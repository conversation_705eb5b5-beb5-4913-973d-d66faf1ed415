<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.mogujie.detail</groupId>
    <artifactId>detail-web</artifactId>
    <packaging>pom</packaging>
    <version>1.1.0.dsl</version>
    <modules>
        <module>core</module>
        <module>module</module>
        <module>web</module>
        <module>spi</module>
        <module>before</module>
    </modules>

    <properties>
        <detail.version>1.1.0.dsl</detail.version>
        <diana.api.version>1.1.6</diana.api.version>
        <pay.modou.api.version>1.1.14</pay.modou.api.version>
    </properties>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.16.4</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <!--配置生成源码包-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.1</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>2.17.0</version>
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>2.17.0</version>
            </dependency>
            <dependency>
                <groupId>com.mogujie</groupId>
                <artifactId>mogu-biz-bom</artifactId>
                <version>1.0.32</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.mogujie</groupId>
                <artifactId>mogu-middleware-bom</artifactId>
                <version>1.0.32</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.mogujie</groupId>
                <artifactId>mogu-third-lib-bom</artifactId>
                <version>1.0.32</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.70</version>
            </dependency>

            <!-- 会员福利列表-->
            <dependency>
                <groupId>com.mogujie.service.diana</groupId>
                <artifactId>diana-api</artifactId>
                <version>${diana.api.version}</version>
            </dependency>

            <!-- 用户相关-->
            <dependency>
                <groupId>com.mogujie.service</groupId>
                <artifactId>pay-modou-api</artifactId>
                <version>${pay.modou.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.mogujie.live</groupId>
                <artifactId>prada-api</artifactId>
                <version>1.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.mogujie.darwin</groupId>
                <artifactId>darwin-client</artifactId>
                <version>1.1.6.4</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.mogujie.search.ara.abtest</groupId>
                        <artifactId>abtest-cli</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.mogujie.search.ara.abtest</groupId>
                        <artifactId>abtest-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.mogujie.metabase</groupId>
                        <artifactId>metabase-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.mogujie.metabase</groupId>
                        <artifactId>metabase-admin</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.mogujie.dragon.bigdata</groupId>
                        <artifactId>CDNBalance</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>


    </dependencyManagement>
    <!--<distributionManagement>-->
        <!--<repository>-->
            <!--<id>snapshots</id>-->
            <!--<name>snapshots</name>-->
            <!--<url>http://maven.mogujie.org/nexus/service/local/repositories/snapshots/content/</url>-->
        <!--</repository>-->
    <!--</distributionManagement>-->

</project>