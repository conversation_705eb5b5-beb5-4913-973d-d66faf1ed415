package com.mogujie.detail.spi.mgj.shop;


import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.spi.BizSpi;
import com.mogujie.detail.module.shop.spi.IShopSalesProvider;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.trade.response.Response;
import com.mogujie.trade.sales.api.dto.ShopSalesQueryDto;
import com.mogujie.trade.sales.query.client.ShopSalesQueryClient;
import com.mogujie.trade.sales.query.domain.App;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by xiaoyao on 16/5/23.
 */
@BizSpi
public class MgjShopSalesProvider implements IShopSalesProvider {

    private static final Logger LOGGER = LoggerFactory.getLogger(MgjShopSalesProvider.class);

    @Autowired
    protected ShopSalesQueryClient shopSalesQueryClient;

    @Override
    public Long getShopSales(DetailContext context) {
        try {
            ItemDO item = context.getItemDO();
            ShopSalesQueryDto shopSalesQueryDto = new ShopSalesQueryDto();
            shopSalesQueryDto.setSellerUserId(item.getUserId());
            App appInfo = new App("appdetail-mgj", "xiaoyao");
            Response<Long> ret = shopSalesQueryClient.queryShopSalesById(shopSalesQueryDto, appInfo);
            if (null != ret && ret.isSuccess()) {
                return ret.getData();
            }
        } catch (Throwable e) {
            LOGGER.error("get Shop Sales failed : {}, {}", context.getItemDO().getShopId(), e);
        }
        return 0L;
    }
}
