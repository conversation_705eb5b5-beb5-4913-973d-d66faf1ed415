package com.mogujie.detail.spi.mgj.shop;


import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.constant.SwitchKey;
import com.mogujie.detail.core.spi.BizSpi;
import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.module.shop.domain.ShopDO;
import com.mogujie.detail.module.shop.domain.ShopDsr;
import com.mogujie.detail.module.shop.spi.IShopDsrProvider;
import com.mogujie.dts.api.query.Query;
import com.mogujie.dts.api.query.condition.Conditions;
import com.mogujie.dts.api.query.constant.Operator;
import com.mogujie.dts.api.result.QueryResult;
import com.mogujie.dts.api.result.RowResult;
import com.mogujie.dts.api.result.Value;
import com.mogujie.dts.api.service.DtsQueryService;
import com.mogujie.dts.utils.TokenUtil;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by xiaoyao on 16/5/21.
 */
@BizSpi
public class MgjShopDsrProvider implements IShopDsrProvider {

    private static final Logger logger = LoggerFactory.getLogger(MgjShopDsrProvider.class);

    private DtsQueryService dtsQueryService;

    private MgjServiceConfProvider mgjServiceConfProvider;

    @Autowired
    private CommonSwitchUtil commonSwitchUtil;

    @PostConstruct
    public void init() {
        try {
            dtsQueryService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(DtsQueryService.class);
            mgjServiceConfProvider = new MgjServiceConfProvider();
        } catch (Throwable e) {
            logger.error("init DSR provider failed.", e);
        }
    }

    @Override
    public List<ShopDsr> listShopDsr(DetailContext context, ShopDO shopDO) {
        if (!commonSwitchUtil.isOn(SwitchKey.OCEAN_GET_KPI_BY_SHOP_ID)) {
            return Collections.EMPTY_LIST;
        }

        ItemDO item = context.getItemDO();
        if (0 == item.getShopId()) {
            return null;
        }
        try {
            Map<String, String> shopDsr = new LinkedHashMap<>();
            Map<String, String> avgDsr = new LinkedHashMap<>();

            // new, 走dts
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
            Calendar calendar = Calendar.getInstance();
            String todayStr = dateFormat.format(calendar.getTime());
            calendar.add(Calendar.DATE, -2);
            String twoDayBeforeStr = dateFormat.format(calendar.getTime());

            Query query = new Query();
            //选择要查询哪些指标（这些指标必须订阅且审核通过才可以查询）
            query.addColumn("007_tag_desc_dsr");
            query.addColumn("007_tag_price_dsr");
            query.addColumn("007_tag_quality_dsr");
            query.addColumn("007_desc_dsr");
            query.addColumn("007_price_dsr");
            query.addColumn("007_quality_dsr");
            //设置查询条件（在同一个query中进行查询的指标，查询条件都是相同的）
            query.setCondition(Conditions.and(
                    Conditions.condition("shopid", Operator.EQ, item.getShopId()),
                    Conditions.condition("visit_date_i", Operator.GE, twoDayBeforeStr),
                    Conditions.condition("visit_date_i", Operator.LE, todayStr)
            ));

            Double quality = null, price = null, desc = null;
            Double avgQuality = null, avgPrice = null, avgDesc = null;
            try {
                //执行查询
                String token = TokenUtil.produce("detail", "detail_dts");
                QueryResult queryResult = dtsQueryService.query(query, "detail", token);
                List<RowResult> list = queryResult.getRowResults();
                if (CollectionUtils.isEmpty(list)) {
                    return null;
                }
                String visitDate = null;
                for (RowResult row : list) {
                    Map<String, Value> map = row.getRowDatas();
                    if (map == null || map.isEmpty()) {
                        continue;
                    }
                    String date = map.get("visit_date_i").asString();
                    if (StringUtils.isBlank(date)) {
                        continue;
                    }
                    if (StringUtils.isBlank(visitDate) || date.compareTo(visitDate) > 0) {
                        visitDate = date;
                        quality = map.get("007_quality_dsr").asDouble();
                        price = map.get("007_price_dsr").asDouble();
                        desc = map.get("007_desc_dsr").asDouble();

                        avgQuality = map.get("007_tag_quality_dsr").asDouble();
                        avgPrice = map.get("007_tag_price_dsr").asDouble();
                        avgDesc = map.get("007_tag_desc_dsr").asDouble();
                    }
                }
            } catch (Throwable e) {
                logger.error("get shop dsr data error.", e);
            }

            shopDsr.put("desc", desc == null ? "5" : desc.toString());
            shopDsr.put("price", price == null ? "5" : price.toString());
            shopDsr.put("quality", quality == null ? "5" : quality.toString());

            avgDsr.put("desc", avgDesc == null ? "4.5" : avgDesc.toString());
            avgDsr.put("price", avgPrice == null ? "4.5" : avgPrice.toString());
            avgDsr.put("quality", avgQuality == null ? "4.5" : avgQuality.toString());

            List<ShopDsr> shopDsrList = new ArrayList<>();

            Set<String> keys = shopDsr.keySet();
            for (String key : keys) {
                if ("style".equals(key) || "service".equals(key)) {
                    continue;
                }

                String name = mgjServiceConfProvider.getServiceName(context, key);
                if (StringUtils.isBlank(name)) {
                    continue;
                }

                ShopDsr dsr = new ShopDsr();
                dsr.setName(name);
                dsr.setScore(Double.parseDouble(String.format("%.2f", Double.valueOf(shopDsr.get(key)))));
                boolean isBetter = Double.parseDouble(shopDsr.get(key)) >= Double.parseDouble(avgDsr.get(key));
                dsr.setIsBetter(isBetter);
                shopDsrList.add(dsr);
            }

            // todo 后期再优化先查出来30天平均发货时长
            Query query2 = new Query();
            SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
            Calendar calendar2 = Calendar.getInstance();
            String todayStr2 = dateFormat.format(calendar2.getTime());
            calendar.add(Calendar.DATE, -2);
            String twoDayBeforeStr2 = dateFormat2.format(calendar.getTime());
            query2.addColumn("0132_avg_ship_time_30d");
            query2.setCondition(Conditions.and(
                    Conditions.condition("shopid", Operator.EQ, item.getShopId()),
                    Conditions.condition("visit_date", Operator.GE, twoDayBeforeStr2),
                    Conditions.condition("visit_date", Operator.LE, todayStr2)
            ));
            String token = TokenUtil.produce("detail", "detail_dts");
            QueryResult queryResult = dtsQueryService.query(query2, "detail", token);
            List<RowResult> list = queryResult.getRowResults();
            if (CollectionUtils.isNotEmpty(list)) {
                String visitDate = null;
                for (RowResult row : list) {
                    Map<String, Value> map = row.getRowDatas();
                    if (map == null || map.isEmpty()) {
                        continue;
                    }
                    String date = map.get("visit_date").asString();
                    if (StringUtils.isBlank(date)) {
                        continue;
                    }
                    if (StringUtils.isBlank(visitDate) || date.compareTo(visitDate) > 0) {
                        visitDate = date;
                        String originData = MapUtils.getString(map, "0132_avg_ship_time_30d");
                        if (StringUtils.isNotBlank(originData)) {
                            double doubleData = Double.parseDouble(originData);
                            shopDO.setShopAvgDeliveryTime(new Double(doubleData).longValue());
                        }
                    }
                }
            }

            return shopDsrList;
        } catch (Exception e) {
            logger.warn("get shop dsr from http error!");
            return new ArrayList<>();
        }
    }
}
