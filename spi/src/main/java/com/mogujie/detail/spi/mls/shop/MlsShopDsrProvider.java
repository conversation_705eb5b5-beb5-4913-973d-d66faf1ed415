package com.mogujie.detail.spi.mls.shop;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.SwitchKey;
import com.mogujie.detail.core.spi.BizSpi;
import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.module.shop.domain.ShopDO;
import com.mogujie.detail.module.shop.domain.ShopDsr;
import com.mogujie.detail.module.shop.spi.IShopDsrProvider;
import com.mogujie.dts.api.query.Query;
import com.mogujie.dts.api.query.condition.Conditions;
import com.mogujie.dts.api.query.constant.Operator;
import com.mogujie.dts.api.result.QueryResult;
import com.mogujie.dts.api.result.RowResult;
import com.mogujie.dts.api.result.Value;
import com.mogujie.dts.api.service.DtsQueryService;
import com.mogujie.dts.utils.TokenUtil;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by xiaoyao on 16/5/21.
 */
@BizSpi(app = App.MLS)
public class MlsShopDsrProvider implements IShopDsrProvider {

    private static final Logger logger = LoggerFactory.getLogger(MlsShopDsrProvider.class);

    private DtsQueryService dtsQueryService;

    private MlsServiceConfProvider mlsServiceConfProvider;

    @Autowired
    private CommonSwitchUtil commonSwitchUtil;

    @PostConstruct
    public void init() {
        try {
            dtsQueryService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(DtsQueryService.class);
            mlsServiceConfProvider = new MlsServiceConfProvider();
        } catch (Throwable e) {
            logger.error("init DSR provider failed.", e);
        }
    }

    @Override
    public List<ShopDsr> listShopDsr(DetailContext context, ShopDO shopDO) {
        DetailItemDO commonItem = context.getItemDO();
        if (0 == commonItem.getShopId()) {
            return null;
        }
        try {
            Map<String, String> shopDsr = new LinkedHashMap<>();
            Map<String, String> avgDsr = new LinkedHashMap<>();

            Integer type = shopDO.getType();
            if (null != type && type == 4 && commonSwitchUtil.isOn(SwitchKey.MLSOCEAN_GET_KPI_BY_SHOP_ID)) {
                // new, 走dts
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
                Calendar calendar = Calendar.getInstance();
                String todayStr = dateFormat.format(calendar.getTime());
                calendar.add(Calendar.DATE, -2);
                String twoDayBeforeStr = dateFormat.format(calendar.getTime());

                Query query = new Query();
                //选择要查询哪些指标（这些指标必须订阅且审核通过才可以查询）
                query.addColumn("0024_tag_desc_dsr");
                query.addColumn("0024_tag_price_dsr");
                query.addColumn("0024_tag_quality_dsr");
                query.addColumn("0024_desc_dsr");
                query.addColumn("0024_price_dsr");
                query.addColumn("0024_quality_dsr");
                //设置查询条件（在同一个query中进行查询的指标，查询条件都是相同的）
                query.setCondition(Conditions.and(
                        Conditions.condition("shopid", Operator.EQ, commonItem.getShopId()),
                        Conditions.condition("visit_date_i", Operator.GE, twoDayBeforeStr),
                        Conditions.condition("visit_date_i", Operator.LE, todayStr)
                ));

                Double quality = null, price = null, desc = null;
                Double avgQuality = null, avgPrice = null, avgDesc = null;

                try {
                    //执行查询
                    String token = TokenUtil.produce("detail", "detail_dts");
                    QueryResult queryResult = dtsQueryService.query(query, "detail", token);
                    List<RowResult> list = queryResult.getRowResults();
                    String visitDate = null;
                    for (RowResult row : list) {
                        Map<String, Value> map = row.getRowDatas();
                        if (map == null || map.isEmpty()) {
                            continue;
                        }
                        String date = map.get("visit_date_i").asString();
                        if (org.apache.commons.lang3.StringUtils.isBlank(date)) {
                            continue;
                        }
                        if (org.apache.commons.lang3.StringUtils.isBlank(visitDate) || date.compareTo(visitDate) > 0) {
                            visitDate = date;
                            quality = map.get("0024_quality_dsr").asDouble();
                            price = map.get("0024_price_dsr").asDouble();
                            desc = map.get("0024_desc_dsr").asDouble();

                            avgQuality = map.get("0024_tag_quality_dsr").asDouble();
                            avgPrice = map.get("0024_tag_price_dsr").asDouble();
                            avgDesc = map.get("0024_tag_desc_dsr").asDouble();
                        }
                    }
                } catch (Throwable e) {
                    logger.error("get shop dsr data error.", e);
                }

                shopDsr.put("desc", desc == null ? "5" : desc.toString());
                shopDsr.put("price", price == null ? "5" : price.toString());
                shopDsr.put("quality", quality == null ? "5" : quality.toString());

                avgDsr.put("desc", avgDesc == null ? "4.5" : avgDesc.toString());
                avgDsr.put("price", avgPrice == null ? "4.5" : avgPrice.toString());
                avgDsr.put("quality", avgQuality == null ? "4.5" : avgQuality.toString());
            } else {
                // new, 走dts
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
                Calendar calendar = Calendar.getInstance();
                String todayStr = dateFormat.format(calendar.getTime());
                calendar.add(Calendar.DATE, -2);
                String twoDayBeforeStr = dateFormat.format(calendar.getTime());

                Query query = new Query();
                //选择要查询哪些指标（这些指标必须订阅且审核通过才可以查询）
                query.addColumn("007_tag_desc_dsr");
                query.addColumn("007_tag_price_dsr");
                query.addColumn("007_tag_quality_dsr");
                query.addColumn("007_desc_dsr");
                query.addColumn("007_price_dsr");
                query.addColumn("007_quality_dsr");
                //设置查询条件（在同一个query中进行查询的指标，查询条件都是相同的）
                query.setCondition(Conditions.and(
                        Conditions.condition("shopid", Operator.EQ, commonItem.getShopId()),
                        Conditions.condition("visit_date_i", Operator.GE, twoDayBeforeStr),
                        Conditions.condition("visit_date_i", Operator.LE, todayStr)
                ));

                Double quality = null, price = null, desc = null;
                Double avgQuality = null, avgPrice = null, avgDesc = null;

                try {
                    //执行查询
                    String token = TokenUtil.produce("detail", "detail_dts");
                    QueryResult queryResult = dtsQueryService.query(query, "detail", token);
                    List<RowResult> list = queryResult.getRowResults();
                    String visitDate = null;
                    for (RowResult row : list) {
                        Map<String, Value> map = row.getRowDatas();
                        if (map == null || map.isEmpty()) {
                            continue;
                        }
                        String date = map.get("visit_date_i").asString();
                        if (org.apache.commons.lang3.StringUtils.isBlank(date)) {
                            continue;
                        }
                        if (org.apache.commons.lang3.StringUtils.isBlank(visitDate) || date.compareTo(visitDate) > 0) {
                            visitDate = date;
                            quality = map.get("007_quality_dsr").asDouble();
                            price = map.get("007_price_dsr").asDouble();
                            desc = map.get("007_desc_dsr").asDouble();

                            avgQuality = map.get("007_tag_quality_dsr").asDouble();
                            avgPrice = map.get("007_tag_price_dsr").asDouble();
                            avgDesc = map.get("007_tag_desc_dsr").asDouble();
                        }
                    }
                } catch (Throwable e) {
                    logger.error("get shop dsr data error.", e);
                }


                shopDsr.put("desc", desc == null ? "5" : desc.toString());
                shopDsr.put("price", price == null ? "5" : price.toString());
                shopDsr.put("quality", quality == null ? "5" : quality.toString());

                avgDsr.put("desc", avgDesc == null ? "4.5" : avgDesc.toString());
                avgDsr.put("price", avgPrice == null ? "4.5" : avgPrice.toString());
                avgDsr.put("quality", avgQuality == null ? "4.5" : avgQuality.toString());
            }
            List<ShopDsr> shopDsrList = new ArrayList<>();

            Set<String> keys = shopDsr.keySet();
            for (String key : keys) {
                if ("style".equals(key) || "service".equals(key)) {
                    continue;
                }

                String name = mlsServiceConfProvider.getServiceName(context, key);
                if (org.apache.commons.lang3.StringUtils.isBlank(name)) {
                    continue;
                }

                ShopDsr dsr = new ShopDsr();
                dsr.setName(name);
                dsr.setScore(Double.parseDouble(String.format("%.2f", Double.valueOf(shopDsr.get(key)))));
                boolean isBetter = Double.parseDouble(shopDsr.get(key)) >= Double.parseDouble(avgDsr.get(key));
                dsr.setIsBetter(isBetter);
                shopDsrList.add(dsr);
            }
            return shopDsrList;
        } catch (Throwable e) {
            logger.warn("get shop dsr from http error! {}", e);
            return new ArrayList<>();
        }
    }
}
