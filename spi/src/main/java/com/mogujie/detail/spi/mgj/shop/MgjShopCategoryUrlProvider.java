package com.mogujie.detail.spi.mgj.shop;

import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailContextHolder;
import com.mogujie.detail.core.spi.BizSpi;
import com.mogujie.detail.core.util.StrategyUpUtil;
import com.mogujie.detail.module.shop.spi.IShopCategoryUrlProvider;

/**
 * Created by xiaoyao on 16/6/3.
 */
@BizSpi
public class MgjShopCategoryUrlProvider implements IShopCategoryUrlProvider {
    @Override
    public String getShopCategoryUrl(DetailContext context, Integer cid) {
        if ("m".equals(DetailContextHolder.get().getParam("appPlat"))) {
            return StrategyUpUtil.upUrl("//shop.mogujie.com/")+ IdConvertor.idToUrl(context.getItemDO().getShopId())+"/list/index?categoryId="+cid;
        }else {
            return StrategyUpUtil.upUrl("http://shop.mogujie.com/")+ IdConvertor.idToUrl(context.getItemDO().getShopId())+"/list/index?categoryId="+cid;
        }
    }
}
