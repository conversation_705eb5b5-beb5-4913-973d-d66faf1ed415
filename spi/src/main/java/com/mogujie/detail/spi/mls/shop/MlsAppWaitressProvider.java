package com.mogujie.detail.spi.mls.shop;

import com.google.gson.Gson;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.SwitchKey;
import com.mogujie.detail.core.spi.BizSpi;
import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.core.util.ImageUtil;
import com.mogujie.detail.module.shop.domain.ShopService;
import com.mogujie.detail.module.shop.domain.ShopServicesDO;
import com.mogujie.detail.module.shop.spi.IWaitressProvider;
import com.mogujie.metabase.spring.client.MetabaseClient;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.waitress.platform.api.PlatformIconUrlService;
import com.mogujie.service.waitress.platform.api.PlatformItemServiceService;
import com.mogujie.service.waitress.platform.common.ServiceDetailEnum;
import com.mogujie.service.waitress.platform.common.TradeSourceEnum;
import com.mogujie.service.waitress.platform.domain.ResultSupport;
import com.mogujie.service.waitress.platform.domain.entity.ItemServiceDetail;
import com.mogujie.service.waitress.platform.domain.query.ItemServiceQuery;
import com.mogujie.stable.spirit.EntryUtil;
import com.mogujie.stable.spirit.exception.BlockException;
import com.mogujie.stable.spirit.limit.Entry;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
 * Created by xiaoyao on 16/4/10.
 */
@BizSpi(app = App.MLS)
public class MlsAppWaitressProvider implements IWaitressProvider {

    private static final Logger LOGGER = LoggerFactory.getLogger(MlsAppWaitressProvider.class);

    protected Gson gson;

    protected static final String YUSHOU_CODE = "18";

    // 商品服务体系接口
    protected PlatformItemServiceService platformItemServiceService;

    protected PlatformIconUrlService platformIconUrlService;

    private static final String DEFAULT_ICON = "/p1/160607/upload_ie4tkmbtgqztomjqhezdambqgqyde_44x44.png";

    private static final String NO_SUPPORT_NO_REASON_REFOUND_ICON = "/p1/160607/upload_ie4tkmbtgqztomjqhezdambqgqyde_44x44.png";

    private ShopService beautyService;

    @Autowired
    private CommonSwitchUtil commonSwitchUtil;

    @Resource(name = "confMetabaseClient")
    protected MetabaseClient metabaseClient;

    @PostConstruct
    public void init() {
        gson = new Gson();
        try {
            platformItemServiceService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(PlatformItemServiceService.class);
            platformIconUrlService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(PlatformIconUrlService.class);
        } catch (Exception e) {
            LOGGER.error("init service failed : {}", e);
        }

        beautyService = new ShopService();
        beautyService.setIcon(ImageUtil.img(DEFAULT_ICON));
        beautyService.setLink("");
        beautyService.setName("白付美");
    }


    @Override
    public ShopServicesDO listService(DetailContext context) {
        ItemServiceQuery itemServiceQuery = this.buildItemServiceQuery(context.getItemDO());
        try {
            Entry entry = null;
            ResultSupport<List<ItemServiceDetail>> result = null;
            try {
                entry = EntryUtil.entry("com.mogujie.service.waitress.api.ItemServiceService:getItemService");
                result = platformItemServiceService.getItemService(itemServiceQuery);
            } catch (BlockException e) {
                LOGGER.error("spirit blocked, {}", e);
            } catch (Throwable e) {
                LOGGER.error("tesla calling failed. ", e);
            } finally {
                if (entry != null) {
                    entry.exit();
                }
            }
            if (result == null || result.getModule() == null || CollectionUtils.sizeIsEmpty(result.getModule())) {
                return null;
            }

            boolean withRefoundTag = false;
            Map<Long, ShopService> shopServices = new HashMap<>(result.getModule().size());
            for (ItemServiceDetail itemService : result.getModule()) {
                ShopService shopService = new ShopService();
                shopService.setName(itemService.getServiceDetailTitle());
                shopService.setDesc(itemService.getServiceDetailContent());
                shopService.setIcon(ImageUtil.img(DEFAULT_ICON));
                shopService.setLink(itemService.getLink());
                shopServices.put(itemService.getServiceDetailId(), shopService);
                if (ServiceDetailEnum.FH_410.getHeadId() == itemService.getServiceHeadId()) {
                    withRefoundTag = true;
                }
            }

            List<ShopService> services = new ArrayList<>(shopServices.values());
            if (context.getItemDO().getVerticalMarket() == 11L) {
                boolean containsBeauty = false;
                for (ShopService service : shopServices.values()) {
                    if (service.getName().contains("白付美")) {
                        containsBeauty = true;
                    }
                }
                if (!containsBeauty) {
                    services.add(beautyService);
                }
            }
            if (!withRefoundTag && commonSwitchUtil.isOn(SwitchKey.NO_REASON_REFOUND)) {
                ShopService shopService = new ShopService();
                shopService.setIcon(ImageUtil.img(NO_SUPPORT_NO_REASON_REFOUND_ICON));
                String text = metabaseClient.get("noRefoundText");
                shopService.setName(StringUtils.isBlank(text) ? "不支持无理由退货" : text);
                if (commonSwitchUtil.isOn(SwitchKey.NO_REASON_REFOUND_FIRST)) {
                    services.add(0, shopService);
                } else {
                    services.add(shopService);
                }
            }
            ShopServicesDO shopServicesDO = new ShopServicesDO();
            shopServicesDO.setNormalServices(services);
            shopServicesDO.setGoodItemServices(null);
            return shopServicesDO;
        } catch (Exception e) {
            LOGGER.error("get itemServiceService error!", e);
            return null;
        }
    }

    /**
     * 构建商品服务体系查询条件
     *
     * @param item
     * @return
     */
    protected ItemServiceQuery buildItemServiceQuery(ItemDO item) {
        ItemServiceQuery itemServiceQuery = new ItemServiceQuery();

        Long tradeItemId = item.getItemId();
        itemServiceQuery.setTradeItemId(tradeItemId);

        String cids = this.formatCid(item.getCids());
        itemServiceQuery.setCids(cids);

        Long shopId = item.getShopId();
        itemServiceQuery.setShopId(shopId);
        //type 4 标识美丽说
        itemServiceQuery.setType(4);


        Integer tradeSource = TradeSourceEnum.NORMAL.getType();
        try {
            String extra = item.getJsonExtra();
            Map<String, String> map = gson.fromJson(extra, HashMap.class);
            String tags = map.get("tags");
            if (tags == null) {
                itemServiceQuery.setItemTag("");
            } else {
                itemServiceQuery.setItemTag(tags);
                String[] list = tags.split(",");
                if (Arrays.asList(list).contains(YUSHOU_CODE)) {
                    tradeSource = TradeSourceEnum.YUS_HOU.getType();
                }
            }
        } catch (Exception e) {
            LOGGER.debug("This item do not have tag! itemId: " + tradeItemId);
        }
        itemServiceQuery.setItemTag(item.getJsonExtra());
        itemServiceQuery.setTradeSource(tradeSource);
        return itemServiceQuery;
    }

    /**
     * 格式化cid，将 "#3856# #1734# #999# #2946#"  转为： "3856,1734,999,2946"
     *
     * @param cids
     * @return
     */
    private String formatCid(String cids) {
        if (StringUtils.isBlank(cids)) {
            return "";
        }

        if (!cids.contains("#")) {
            return cids;
        }

        String newCids = cids.replace("# #", ",");
        return newCids.replace("#", "");
    }
}
