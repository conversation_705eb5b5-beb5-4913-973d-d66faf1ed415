package com.mogujie.detail.spi.mgj.detail;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.spi.BizSpi;
import com.mogujie.detail.core.util.ImageUtil;
import com.mogujie.detail.module.detail.domain.DetailVideo;
import com.mogujie.detail.module.detail.domain.DetailVideoTag;
import com.mogujie.detail.module.detail.spi.IDetailVideoProvider;
import com.mogujie.service.item.api.basic.ItemExtraService;
import com.mogujie.service.item.domain.basic.ItemExtraDO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.util.Map;

/**
 * Created by anshi on 17/3/31.
 */
@BizSpi
public class MgjDetailVideoProvider implements IDetailVideoProvider {
    private static final Logger LOGGER = LoggerFactory.getLogger(MgjDetailVideoProvider.class);

    @PostConstruct
    public void init() {
    }

    @Override
    public DetailVideo getDetailVideo(DetailContext context) {
        try {
            DetailItemDO itemDO = context.getItemDO();
            Map<String, String> features = itemDO.getFeatures();
            if (features == null) {
                return null;
            }

            // 图文详情视频 @乱道、@凌云
            String detailVideoStr = (String) features.get("detailVideo");
            if (StringUtils.isBlank(detailVideoStr)) {
                return null;
            }
            Gson gson = new Gson();
            try {
                DetailVideoTag detailVideoTag = (DetailVideoTag) gson.fromJson(detailVideoStr, DetailVideoTag.class);
                if (detailVideoTag != null && detailVideoTag.getAuditStatus() == 2) {
                    DetailVideo videoInfo = new DetailVideo();
                    videoInfo.setCover(ImageUtil.img(detailVideoTag.getCoverImage()));
                    videoInfo.setVideoId(detailVideoTag.getVideoId());
                    videoInfo.setWidth(detailVideoTag.getWidth());
                    videoInfo.setHeight(detailVideoTag.getHeight());
                    return videoInfo;
                }
            } catch (JsonSyntaxException var7) {
                LOGGER.error("{}", var7);
            }
        } catch (Throwable e) {
            LOGGER.error("getVideoInfo failed : {}", e);
        }
        return null;
    }
}
