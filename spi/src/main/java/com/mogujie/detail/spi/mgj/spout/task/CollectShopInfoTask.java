package com.mogujie.detail.spi.mgj.spout.task;

import com.google.gson.Gson;
import com.meili.service.shopcenter.domain.entity.ShopInfo;
import com.meili.service.shopcenter.result.Result;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.task.AbstractCollectDataTask;
import com.mogujie.service.shopcenter.client.ShopReadServiceClient;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by xiaoyao on 16/10/20.
 */
public class CollectShopInfoTask extends AbstractCollectDataTask {

    private static final Logger LOGGER = LoggerFactory.getLogger(CollectShopInfoTask.class);


    public CollectShopInfoTask(DetailContext context) throws DetailException {
        super(context);
    }

    @Override
    public void collect() {
        try {
            DetailItemDO itemDO = context.getItemDO();
            Long oneCentShopId = getOneCentShopId(itemDO.getJsonExtra());
            if (oneCentShopId != null) {
                itemDO.setShopId(oneCentShopId);
            }
            Result<ShopInfo> ret = ShopReadServiceClient.getShopByShopId(oneCentShopId == null ? itemDO.getShopId() : oneCentShopId);
            if (null != ret && ret.isSuccess()) {
                itemDO.setShopInfo(ret.getData());
            }
        } catch (Throwable e) {
            LOGGER.error("collect shopinfo failed : {}", e);
        }
    }

    private Long getOneCentShopId(String extra) {
        if (StringUtils.isEmpty(extra)) {
            return null;
        }
        try {
            Gson gson = new Gson();
            Map<String, String> extraInfo = gson.fromJson(extra, HashMap.class);
            if (null == extraInfo || extraInfo.isEmpty()) {
                return null;
            }
            String oneCentStr = extraInfo.get("yf");
            if (StringUtils.isEmpty(oneCentStr)) {
                return null;
            }

            String[] oneCentPairs = oneCentStr.split("\\|");
            if (oneCentPairs.length < 1) {
                return null;
            }
            for (String oneCentPair : oneCentPairs) {
                String[] oneCent = oneCentPair.split(":");
                if (oneCent.length != 2) {
                    continue;
                }
                if ("sh".equals(oneCent[0])) {
                    return Long.parseLong(oneCent[1]);
                }
            }
            return null;
        } catch (Throwable e) {
            LOGGER.error("parse extra failed : {}. {}", extra, e);
        }

        return null;
    }
}
