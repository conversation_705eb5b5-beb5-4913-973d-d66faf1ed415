package com.mogujie.detail.spi.mgj.shop;


import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.constant.SwitchKey;
import com.mogujie.detail.core.spi.BizSpi;
import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.module.shop.spi.IShopCollectInfoProvider;
import com.mogujie.service.relation.api.RelationReadFacade;
import com.mogujie.service.relation.domain.RelationDto;
import com.mogujie.service.relation.domain.enums.AppIdEnums;
import com.mogujie.service.relation.domain.enums.AssociationsTypeEnums;
import com.mogujie.service.relation.domain.response.RpcResult;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/6/27.
 */
@BizSpi
public class MgjShopCollectInfoProvider implements IShopCollectInfoProvider {

    private static final Logger LOGGER = LoggerFactory.getLogger(MgjShopCollectInfoProvider.class);

    @Autowired
    private CommonSwitchUtil commonSwitchUtil;

    private RelationReadFacade relationReadFacade;

    @PostConstruct
    public void init() {
        try {
            relationReadFacade = TeslaServiceConsumerFactory.getTeslaServiceConsumer(RelationReadFacade.class);
        } catch (Exception e) {
            LOGGER.error("init service failed : {}", e);
        }

    }


    @Override
    public Boolean isCollected(DetailContext context) {

        if (!commonSwitchUtil.isOn(SwitchKey.SHOPFAVORITE_CHECK_RELATION)) {
            return false;
        }
        Long loginUserId = context.getLoginUserId();
        if (null == loginUserId) {
            return false;
        }
        try {
            RelationDto relationDto = new RelationDto();
            relationDto.setFromId(loginUserId);
            relationDto.setToId(context.getItemDO().getShopId());
            relationDto.setAppId(AppIdEnums.MoGuJie);
            relationDto.setAssociationsType(AssociationsTypeEnums.SHOP_LIKE);
            RpcResult<Boolean> result = relationReadFacade.queryRelation(relationDto);
            if(!result.isSuccess()){
                return Boolean.FALSE;
            }
            return result.getValue();
        } catch (Throwable e) {
            LOGGER.error("get data from relationReadFacade.queryRelation failed : {}", e);
        }
        return false;
    }
}
