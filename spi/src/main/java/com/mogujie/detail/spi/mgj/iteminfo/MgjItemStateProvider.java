package com.mogujie.detail.spi.mgj.iteminfo;


import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.spi.BizSpi;
import com.mogujie.service.item.domain.basic.ItemDO;

/**
 * Created by <PERSON><PERSON><PERSON> on 16/4/27.
 */
@BizSpi
public class MgjItemStateProvider extends BaseItemStateProvider {
    @Override
    public int getItemState(DetailContext context) {
        ItemDO item = context.getItemDO();
        // 预售商品时，未开始阶段，直接设置为1
//        if (item.getItemPreSaleDO() != null) {
//            int currentTime = (int)(System.currentTimeMillis() / 1000);
//            if (currentTime < item.getItemPreSaleDO().getStart()) {
//                return 1;
//            }
//        }

        return super.getItemState(context);
    }
}
