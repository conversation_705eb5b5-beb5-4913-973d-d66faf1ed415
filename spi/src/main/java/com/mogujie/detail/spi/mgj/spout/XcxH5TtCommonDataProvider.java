package com.mogujie.detail.spi.mgj.spout;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.Platform;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.spi.BizSpi;
import com.mogujie.detail.core.task.AbstractCollectDataTask;
import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.module.spout.spi.ICommonDataProvider;
import com.mogujie.detail.spi.mgj.spout.task.CollectShopInfoTask;
import com.mogujie.detail.spi.mgj.spout.task.CollectTtPromotionInfoTask;
import com.mogujie.metabase.spring.client.MetabaseClient;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by nanyang on 17/3/8.
 */
@BizSpi(app = App.XCX, platform = Platform.H5, bizType = BizType.TTNORMAL)
public class XcxH5TtCommonDataProvider implements ICommonDataProvider {

    @Resource(name = "confMetabaseClient")
    private MetabaseClient metabaseClient;

    @Autowired
    private CommonSwitchUtil commonSwitchUtil;

    @Override
    public List<AbstractCollectDataTask> listStaticCollectDataTask(DetailContext context) throws DetailException {
        List<AbstractCollectDataTask> tasks = new ArrayList<>(2);
        tasks.add(new CollectTtPromotionInfoTask(context, metabaseClient, commonSwitchUtil));
        tasks.add(new CollectShopInfoTask(context));
        return tasks;
    }

    @Override
    public List<AbstractCollectDataTask> listDynCollectDataTask(DetailContext context) throws DetailException {
        List<AbstractCollectDataTask> tasks = new ArrayList<>(2);
        tasks.add(new CollectTtPromotionInfoTask(context, metabaseClient, commonSwitchUtil));
        tasks.add(new CollectShopInfoTask(context));
        return tasks;
    }
}
