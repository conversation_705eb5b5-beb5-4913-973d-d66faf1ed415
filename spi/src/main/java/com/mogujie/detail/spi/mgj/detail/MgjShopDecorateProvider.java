package com.mogujie.detail.spi.mgj.detail;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.meili.service.shopcenter.util.ShopInfoTagsUtil;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.Platform;
import com.mogujie.detail.core.constant.SwitchKey;
import com.mogujie.detail.core.spi.BizSpi;
import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.core.util.ImageUtil;
import com.mogujie.detail.module.detail.domain.ShopDecorate;
import com.mogujie.detail.module.detail.domain.ShopDecorateTag;
import com.mogujie.detail.module.detail.spi.IShopDecorateProvider;
import com.mogujie.item.adt.PlainResult;
import com.mogujie.service.ibs.api.DetailDCModuleService;
import com.mogujie.service.ibs.api.ItemRelationDataService;
import com.mogujie.service.ibs.domain.DetailDCModule;
import com.mogujie.service.ibs.domain.ItemRelationData;
import com.mogujie.service.ibs.enums.ItemRelationType;
import com.mogujie.service.tagcenter.api.read.ItemTagReadService;
import com.mogujie.service.tagcenter.api.read.ShopTagReadService;
import com.mogujie.service.tagcenter.domain.entity.query.ItemTagQueryOption;
import com.mogujie.service.tagcenter.domain.entity.query.ShopTagQueryOption;
import com.mogujie.service.tagcenter.domain.entity.result.BaseResultDO;
import com.mogujie.service.tagcenter.domain.entity.result.ItemTagDO;
import com.mogujie.service.tagcenter.domain.entity.result.ShopTagDO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import com.mogujie.tesla.common.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;

/**
 * Created by anshi on 17/3/31.
 */
@BizSpi
public class MgjShopDecorateProvider implements IShopDecorateProvider {

    private static final Logger LOGGER = LoggerFactory.getLogger(MgjShopDecorateProvider.class);

    private static final int DECORATE_NUM_TAG = 9999;
    private static final String DECORATE_TAG_KEY = "DetailDC";

    private Gson gson;

    @Autowired
    private CommonSwitchUtil commonSwitchUtil;


    private ItemTagReadService itemTagReadService;

    private ShopTagReadService shopTagReadService;

    private ItemRelationDataService itemRelationDataService;

    private DetailDCModuleService detailDCModuleService;

    @PostConstruct
    public void init() {
        gson = new Gson();
        try {
            itemTagReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ItemTagReadService.class);
            shopTagReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ShopTagReadService.class);

            itemRelationDataService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ItemRelationDataService.class);
            detailDCModuleService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(DetailDCModuleService.class);
        } catch (Exception e) {
            LOGGER.error("init ShopTagReadService failed");
        }
    }

    @Override
    public ShopDecorate getDecorate(DetailContext context) {
        if (!commonSwitchUtil.isOn(SwitchKey.PALLAS_CLIENT)) {
            return null;
        }

        try {
            DetailItemDO itemDO = context.getItemDO();
            if (commonSwitchUtil.isOn(SwitchKey.SWT_USE_NEW_SHOP_DECORATE)) {
                //新装修接口，@伏念 提供
                PlainResult<ItemRelationData> plainResult = itemRelationDataService.queryByItemIdAndType(context.getItemId(), ItemRelationType.detailDCModuleId);
                if (plainResult != null && plainResult.isSuccess() && plainResult.getData() != null) {
                    PlainResult<DetailDCModule> ret = detailDCModuleService.queryByModuleId(plainResult.getData().getRelationId());
                    if (ret != null && ret.isSuccess() && ret.getData() != null) {
                        DetailDCModule detailDCModule = ret.getData();
                        int now = (int) (System.currentTimeMillis() / 1000);
                        int start = detailDCModule.getStart();
                        int end = detailDCModule.getEnd();
                        // 审核状态通过
                        if (detailDCModule.getAble() == 1 && detailDCModule.getStatus() != 2 && start < now && now < end) {
                            ShopDecorate shopDecorate = new ShopDecorate();
                            shopDecorate.setImg(ImageUtil.img(detailDCModule.getPath()));
                            String linkStr = detailDCModule.getLink();
                            JSONObject jsonObject = JSON.parseObject(linkStr);
                            shopDecorate.setLink(jsonObject.getString("link"));
                            shopDecorate.setXcxLink(jsonObject.getString("xcxLink"));
                            //非小程序店铺(!600)，在小程序平台上，不展示custom装修模块
                            if ((context.getRouteInfo().getApp() == App.XCX
                                    || context.getRouteInfo().getPlatform() == Platform.XCX)
                                    && !ShopInfoTagsUtil.stringToSet(itemDO.getShopInfo().getTags()).contains(600)
                                    && "custom".equals(jsonObject.get("type"))) {
                                return null;
                            }
                            //小程序店铺，在蘑菇街APP上，不展示otherPage装修模块
                            if (context.getRouteInfo().getPlatform() == Platform.APP
                                    && ShopInfoTagsUtil.stringToSet(itemDO.getShopInfo().getTags()).contains(600)
                                    && "otherPage".equals(jsonObject.get("type"))) {
                                return null;
                            }
                            return shopDecorate;
                        }
                    }
                }
            } else {
                //老装修接口
                String extra = null;
                // 先查商品标，如果商品带有9999标则继续查店铺标
                ItemTagQueryOption queryOption = new ItemTagQueryOption();
                queryOption.setItemId(itemDO.getItemId());
                queryOption.setQueryNewDB(true);
                BaseResultDO<List<ItemTagDO>> resultBase = itemTagReadService.queryItemTag(queryOption);
                if (resultBase != null && resultBase.isSuccess() && strTagContains(resultBase.getResult(), DECORATE_NUM_TAG + "")) {
                    ShopTagQueryOption shopTagQueryOption = new ShopTagQueryOption();
                    shopTagQueryOption.setShopId(itemDO.getShopId());
                    shopTagQueryOption.setTagKey(DECORATE_TAG_KEY);
                    BaseResultDO<List<ShopTagDO>> shopTagResult = shopTagReadService.queryShopTag(shopTagQueryOption);
                    if (shopTagResult != null && shopTagResult.isSuccess()
                            && !CollectionUtils.isEmpty(shopTagResult.getResult())) {
                        extra = shopTagResult.getResult().get(0).getTagValue();
                    }
                }
                if (StringUtils.isBlank(extra)) {
                    return null;
                }
                ShopDecorateTag shopDecorateTag = gson.fromJson(extra, ShopDecorateTag.class);
                int now = (int) (System.currentTimeMillis() / 1000);
                int start = shopDecorateTag.getStart();
                int end = shopDecorateTag.getEnd();
                String status = shopDecorateTag.getStatus();
                Integer imgStatus = shopDecorateTag.getImageStatus();
                if ((imgStatus == null || imgStatus != 2) && "open".equals(status) && start < now && now < end) {
                    ShopDecorate shopDecorate = new ShopDecorate();
                    shopDecorate.setImg(ImageUtil.img(shopDecorateTag.getPath()));
                    Map<String, String> linkMap = shopDecorateTag.getLink();
                    shopDecorate.setLink(linkMap == null ? null : linkMap.get("link"));
                    shopDecorate.setXcxLink(linkMap == null ? null : linkMap.get("xcxLink"));
                    //非小程序店铺(!600)，在小程序平台上，不展示custom装修模块
                    if ((context.getRouteInfo().getApp() == App.XCX
                            || context.getRouteInfo().getPlatform() == Platform.XCX)
                            && !ShopInfoTagsUtil.stringToSet(itemDO.getShopInfo().getTags()).contains(600)
                            && "custom".equals(linkMap.get("type"))) {
                        return null;
                    }
                    return shopDecorate;
                }
            }
        } catch (JsonSyntaxException e) {
            LOGGER.error("shop decorate json parse error. {}", e);
        } catch (Exception e) {
            LOGGER.error("get shop decorate error. {}", e);
        }
        return null;
    }

    private boolean strTagContains(List<ItemTagDO> tagDOList, String key) {
        if (CollectionUtils.isEmpty(tagDOList)) {
            return false;
        }
        for (ItemTagDO tag : tagDOList) {
            if (tag.getTagValue().contains(key)) {
                return true;
            }
        }
        return false;
    }
}
