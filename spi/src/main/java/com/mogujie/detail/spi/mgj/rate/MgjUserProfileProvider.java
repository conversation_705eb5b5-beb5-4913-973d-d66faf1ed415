package com.mogujie.detail.spi.mgj.rate;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.spi.BizSpi;
import com.mogujie.detail.module.rate.spi.IUserProfileProvider;
import com.mogujie.detail.module.rate.util.Constants;

/**
 * Created by xiaoyao on 16/5/19.
 */
@BizSpi
public class MgjUserProfileProvider implements IUserProfileProvider {

    @Override
    public String getProfilePrefix(DetailContext context) {
        return Constants.APP_PROFILE_URL_PREFIX;
    }

    public String getAnonymousImg(DetailContext context, long userId) {
        return Constants.ANONYMOUS_ICON[(int) (userId % 6)];
    }
}
