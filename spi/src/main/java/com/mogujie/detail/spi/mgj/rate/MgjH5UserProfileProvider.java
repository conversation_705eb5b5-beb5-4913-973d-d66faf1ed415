package com.mogujie.detail.spi.mgj.rate;


import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailContextHolder;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.Platform;
import com.mogujie.detail.core.spi.BizSpi;
import com.mogujie.detail.core.util.StrategyUpUtil;
import com.mogujie.detail.module.rate.spi.IUserProfileProvider;
import com.mogujie.detail.module.rate.util.Constants;

/**
 * Created by xiaoyao on 16/5/19.
 */
@BizSpi(app = App.MGJ, platform = Platform.H5)
public class MgjH5UserProfileProvider implements IUserProfileProvider {

    @Override
    public String getProfilePrefix(DetailContext context) {
        if ("m".equals(DetailContextHolder.get().getParam("appPlat"))) {
            return StrategyUpUtil.upUrl(Constants.H5_PROFILE_URL_PREFIX_REMOVE_PROTOCOL);
        }else {
            return StrategyUpUtil.upUrl(Constants.H5_PROFILE_URL_PREFIX);
        }
    }


    public String getAnonymousImg(DetailContext context, long userId) {
        return Constants.ANONYMOUS_ICON[(int) (userId % 6)];
    }
}
