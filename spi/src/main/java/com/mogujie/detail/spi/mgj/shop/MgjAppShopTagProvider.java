package com.mogujie.detail.spi.mgj.shop;


import com.meili.service.shopcenter.domain.entity.IconDTO;
import com.meili.service.shopcenter.domain.entity.ShopInfo;
import com.meili.service.shopcenter.enums.TagInfoTypeEnum;
import com.meili.service.shopcenter.result.Result;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.constant.SwitchKey;
import com.mogujie.detail.core.spi.BizSpi;
import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.core.util.ImageUtil;
import com.mogujie.detail.module.shop.spi.IShopTagProvider;
import com.mogujie.service.shopcenter.client.ShopTagServiceClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by xiaoyao on 16/8/4.
 */
@BizSpi
public class MgjAppShopTagProvider implements IShopTagProvider {

    @Autowired
    private CommonSwitchUtil commonSwitchUtil;

    private static final Logger LOGGER = LoggerFactory.getLogger(MgjAppShopTagProvider.class);

    @PostConstruct
    public void init() {
    }

    @Override
    public String getShopTag(DetailContext context, ShopInfo shopInfo) {
        if (!commonSwitchUtil.isOn(SwitchKey.SHOPCENTER_GET_IMG)) {
            return null;
        }
        try {
            List<ShopInfo> shopInfoList = new ArrayList<>(1);
            shopInfoList.add(shopInfo);
            Result<Map<Long, IconDTO>> ret = ShopTagServiceClient.mGetShopsShowIcon(shopInfoList, TagInfoTypeEnum.APP.getType(), null);
            if (ret.isSuccess() && null != ret.getData() && !ret.getData().isEmpty()) {
                IconDTO iconDTO = ret.getData().get(shopInfo.getShopId());
                if (null != iconDTO) {
                    return ImageUtil.img(iconDTO.getImg());
                }
            }
        } catch (Throwable e) {
            LOGGER.error("get shopTag failed");
        }
        return null;
    }
}
