package com.mogujie.detail.spi.mls.itemInfo;


import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.SwitchKey;
import com.mogujie.detail.core.spi.BizSpi;
import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.module.itemBase.spi.IFavInfoProvider;
import com.mogujie.service.relation.api.RelationReadFacade;
import com.mogujie.service.relation.domain.RelationDto;
import com.mogujie.service.relation.domain.enums.AppIdEnums;
import com.mogujie.service.relation.domain.enums.AssociationsTypeEnums;
import com.mogujie.service.relation.domain.response.RpcResult;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/5/14.
 */
@BizSpi(app = App.MLS)
public class MlsFavInfoProvider implements IFavInfoProvider {

    private static final Logger LOGGER = LoggerFactory.getLogger(MlsFavInfoProvider.class);

    private RelationReadFacade relationReadFacade;

    @Autowired
    private CommonSwitchUtil commonSwitchUtil;

    @PostConstruct
    public void init() {
        try {
            relationReadFacade = TeslaServiceConsumerFactory.getTeslaServiceConsumer(RelationReadFacade.class);
        } catch (Exception e) {
            LOGGER.error("init service failed : {}", e);
        }
    }

    @Override
    public Boolean isFaved(DetailContext context) {
        if (!commonSwitchUtil.isOn(SwitchKey.RELATIONSERVICE_READ_QUERY_RELATION)) {
            return false;
        }
        Long loginUserId = context.getLoginUserId();
        if (null == loginUserId) {
            return false;
        }

        try {
            RelationDto relationDto = new RelationDto();
            relationDto.setAppId(AppIdEnums.MeiLi);
            relationDto.setAssociationsType(AssociationsTypeEnums.ITEM_LIKE);
            relationDto.setFromId(loginUserId);
            relationDto.setToId(context.getItemId());
            RpcResult<Boolean> result = relationReadFacade.queryRelation(relationDto);
            if (null != result && result.isSuccess() && result.getValue() != null) {
                return result.getValue();
            }
        } catch (Throwable e) {
            LOGGER.error("get favinfo failed : {}", e);
        }
        return false;
    }
}
