package com.mogujie.detail.spi.mgj.spout;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.Platform;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.spi.BizSpi;
import com.mogujie.detail.core.task.AbstractCollectDataTask;
import com.mogujie.detail.module.spout.spi.ICommonDataProvider;
import com.mogujie.detail.spi.mgj.spout.task.CollectSeckillPromotionInfoTask;
import com.mogujie.detail.spi.mgj.spout.task.CollectShopInfoTask;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Created by anshi on 17/3/8.
 */
@BizSpi(app = App.XCX, platform = Platform.H5, bizType = BizType.SECKILL)
public class XcxH5SecKillCommonDataProvider implements ICommonDataProvider {

    @Override
    public List<AbstractCollectDataTask> listStaticCollectDataTask(DetailContext context) throws DetailException {
        List<AbstractCollectDataTask> tasks = new ArrayList<>(2);
        tasks.add(new CollectSeckillPromotionInfoTask(context));
        tasks.add(new CollectShopInfoTask(context));
        return tasks;
    }

    @Override
    public List<AbstractCollectDataTask> listDynCollectDataTask(DetailContext context) throws DetailException {
        return Collections.EMPTY_LIST;
    }
}
