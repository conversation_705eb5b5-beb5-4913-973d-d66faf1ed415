package com.mogujie.detail.spi.mgj.spout;

import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.Platform;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.spi.BizSpi;
import com.mogujie.detail.core.task.AbstractCollectDataTask;
import com.mogujie.detail.module.spout.spi.ICommonDataProvider;
import com.mogujie.detail.spi.mgj.spout.task.CollectChannelPromotionInfoTask;
import com.mogujie.detail.spi.mgj.spout.task.CollectShopInfoTask;
import com.mogujie.marketing.minicooper.domain.entity.AuditItemDTO;
import com.mogujie.marketing.veyron.api.CombItemServiceApi;
import com.mogujie.marketing.veyron.result.Result;
import com.mogujie.service.hummer.constains.RequestConstants;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by anshi on 17/3/21.
 */
@BizSpi(app=App.XCX, platform = Platform.H5, bizType = BizType.PTG)
public class XcxH5PtgComonDataProvider implements ICommonDataProvider {

    private CombItemServiceApi combItemServiceApi;

    private static final Logger LOGGER = LoggerFactory.getLogger(CombItemServiceApi.class);

    public XcxH5PtgComonDataProvider() {
        try {
            combItemServiceApi = TeslaServiceConsumerFactory.getTeslaServiceConsumer(CombItemServiceApi.class);
        } catch (Exception e) {
            LOGGER.error("init combItemServiceApi failed");
        }
    }

    @Override
    public List<AbstractCollectDataTask> listStaticCollectDataTask(DetailContext context) throws DetailException {
        List<AbstractCollectDataTask> tasks = new ArrayList<>(2);
        String activityIdStr = context.getParam("activityId");
        if (!StringUtils.isEmpty(activityIdStr)) {
            Long activityId = IdConvertor.urlToId(activityIdStr);
            Result<AuditItemDTO> ret = combItemServiceApi.findByCache(activityId);
            if (null != ret && ret.success()) {
                context.addContext("ptgInfo", ret.getValue());
            }
        }
        tasks.add(new CollectChannelPromotionInfoTask(context, 2019, RequestConstants.OutType.BRAND));
        tasks.add(new CollectShopInfoTask(context));
        return tasks;
    }

    @Override
    public List<AbstractCollectDataTask> listDynCollectDataTask(DetailContext context) throws DetailException {
        List<AbstractCollectDataTask> tasks = new ArrayList<>(2);
        String activityIdStr = context.getParam("activityId");
        if (!StringUtils.isEmpty(activityIdStr)) {
            Long activityId = IdConvertor.urlToId(activityIdStr);
            Result<AuditItemDTO> ret = combItemServiceApi.findByCache(activityId);
            if (null != ret && ret.success()) {
                context.addContext("ptgInfo", ret.getValue());
            }
        }
        tasks.add(new CollectChannelPromotionInfoTask(context, 2019, RequestConstants.OutType.BRAND));
        tasks.add(new CollectShopInfoTask(context));
        return tasks;
    }
}
