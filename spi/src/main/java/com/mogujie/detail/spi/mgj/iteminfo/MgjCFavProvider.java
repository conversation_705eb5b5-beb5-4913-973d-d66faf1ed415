package com.mogujie.detail.spi.mgj.iteminfo;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.SwitchKey;
import com.mogujie.detail.core.spi.BizSpi;
import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.module.itemBase.spi.ICFavProvider;
import com.mogujie.service.relation.api.RelationReadFacade;
import com.mogujie.service.relation.domain.RelationDto;
import com.mogujie.service.relation.domain.enums.AppIdEnums;
import com.mogujie.service.relation.domain.enums.AssociationsTypeEnums;
import com.mogujie.service.relation.domain.enums.CounterType;
import com.mogujie.service.relation.domain.enums.Direction;
import com.mogujie.service.relation.domain.response.RpcResult;
import com.mogujie.service.relation.domain.response.counter.RelationCounterReponseDto;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.List;

/**
 * Created by xiaoyao on 16/6/6.
 */
@BizSpi
public class MgjCFavProvider implements ICFavProvider {

    private RelationReadFacade relationReadFacade;

    private static final Logger LOGGER = LoggerFactory.getLogger(MgjCFavProvider.class);

    @Autowired
    private CommonSwitchUtil commonSwitchUtil;

    @PostConstruct
    public void init() {
        try {
            relationReadFacade = TeslaServiceConsumerFactory.getTeslaServiceConsumer(RelationReadFacade.class);
        } catch (Exception e) {
            LOGGER.error("init service failed : {}", e);
        }
    }

    @Override
    public Long getCFav(DetailContext context) {
        if (!commonSwitchUtil.isOn(SwitchKey.RELATIONSERVICE_READ_LIKES_COUNT)
                || context.getRouteInfo().getBizType() == BizType.SKU) {
            return 0L;
        }

        try {
            RelationDto relationDto = new RelationDto();
            relationDto.setAssociationsType(AssociationsTypeEnums.ITEM_LIKE);
            relationDto.setAppId(AppIdEnums.MoGuJie);
            relationDto.setToId(context.getItemId());
            RpcResult<List<RelationCounterReponseDto>> result = relationReadFacade.getCounterMuliter(Arrays.asList(relationDto), Direction.TO2FROM, CounterType.PV);
            if (result != null && result.isSuccess() && result.getValue() != null) {
                for (RelationCounterReponseDto data : result.getValue()) {
                    return data.getCount();
                }
            }
        } catch (Throwable e) {
            LOGGER.error("get cfav failed : {}", e);
        }
        return 0L;
    }
}
