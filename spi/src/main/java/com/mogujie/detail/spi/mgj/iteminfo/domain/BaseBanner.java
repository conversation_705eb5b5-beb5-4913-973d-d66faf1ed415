package com.mogujie.detail.spi.mgj.iteminfo.domain;

import com.mogujie.metabase.utils.StringUtils;

import java.io.Serializable;

public class BaseBanner implements Serializable {
    private static final long serialVersionUID = -5894754143216253589L;
    private String bgImg;
    private String link;
    private String acm;

    public BaseBanner() {
    }

    public String getBgImg() {
        return this.bgImg;
    }

    public void setBgImg(String bgImg) {
        this.bgImg = bgImg;
    }

    public String getLink() {
        return this.link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public String getAcm() {
        return this.acm;
    }

    public void setAcm(String acm) {
        this.acm = acm;
    }

    public boolean isEmpty() {
        return StringUtils.isEmpty(this.link) && StringUtils.isEmpty(this.bgImg);
    }

    public String toString() {
        return "BaseBanner{acm=\'" + this.acm + '\'' + ", bgImg=\'" + this.bgImg + '\'' + ", link=\'" + this.link + '\'' + '}';
    }
}