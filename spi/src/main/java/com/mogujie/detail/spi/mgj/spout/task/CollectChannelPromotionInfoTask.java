package com.mogujie.detail.spi.mgj.spout.task;

import com.mogujie.commons.utils.EnvUtil;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.darwin.util.CollectionUtil;
import com.mogujie.detail.core.adt.ChannelMeta;
import com.mogujie.detail.core.adt.ChannelTag;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.constant.Platform;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.task.AbstractCollectDataTask;
import com.mogujie.detail.core.util.ContextUtil;
import com.mogujie.detail.core.util.MetabaseTool;
import com.mogujie.detail.core.util.NumUtil;
import com.mogujie.detail.core.util.StringUtils;
import com.mogujie.detail.core.util.TagUtil;
import com.mogujie.detail.module.shopSeckill.domain.SkTagInfo;
import com.mogujie.ecommerce.common.MetadataManager;
import com.mogujie.marketing.minicooper.domain.entity.AuditItemDTO;
import com.mogujie.service.hummer.api.PromotionReadService;
import com.mogujie.service.hummer.constains.RequestConstants;
import com.mogujie.service.hummer.domain.dto.CampaignInfo;
import com.mogujie.service.hummer.domain.dto.InvokeInfo;
import com.mogujie.service.hummer.domain.dto.ItemDetailPromotion;
import com.mogujie.service.hummer.domain.dto.ItemDetailRequestV2;
import com.mogujie.service.hummer.domain.dto.Pbuyer;
import com.mogujie.service.hummer.domain.dto.PitemDetail;
import com.mogujie.service.hummer.domain.dto.Pseller;
import com.mogujie.service.hummer.domain.dto.result.Result;
import com.mogujie.service.hummer.utils.PromotionConvertUtils;
import com.mogujie.service.hummer.utils.SystemDiscountChecker;
import com.mogujie.service.inventory.api.InventoryReadService;
import com.mogujie.service.inventory.domain.ActivityInventoryV2;
import com.mogujie.service.inventory.domain.param.BatchActivityInventoryQueryParamV2;
import com.mogujie.service.inventory.domain.result.MapResult;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import com.mogujie.tesla.core.ReferConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 普通渠道价/渠道库存
 * Created by anshi on 17/3/21.
 */
public class CollectChannelPromotionInfoTask extends AbstractCollectDataTask {
    private static final Logger logger = LoggerFactory.getLogger(CollectChannelPromotionInfoTask.class);

    private InventoryReadService inventoryReadService;

    private PromotionReadService promotionReadService;

    private int channel;

    private Short outType;

    /**
     * 微信中请求的appkey
     */
    private static final String WX_APP_KEY = "100028";

    /**
     * 小程序新人专享价渠道id
     */
    private static final long XCX_XINREN_PRICE_CHANNEL = 2033L;

    public CollectChannelPromotionInfoTask(DetailContext context, int channel, Short outType) throws DetailException {
        super(context);
        this.channel = channel;
        this.outType = outType;
        try {
            this.inventoryReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(InventoryReadService.class);
            ReferConfig<PromotionReadService> referConfig = new ReferConfig<>(PromotionReadService.class);
            if (EnvUtil.isOnlineEnv()) {
                referConfig.setGroup("DETAIL-GROUP");
            }
            referConfig.setTimeout(200);
            this.promotionReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(referConfig);
        } catch (Exception e) {
            throw new DetailException(e);
        }
    }

    @Override
    public void collect() {
        // 初始化活动ID
        Long activityId = null;
        try {
            Object activityIdObj = context.getParam("activityId");
            activityId = activityIdObj != null ? IdConvertor.urlToId(activityIdObj.toString()) : null;
        } catch (Exception e) {
            logger.error("activity id parse error, id: {}", activityId, e);
        }
        // 设置渠道活动库存
        try {
            int marketValue = ContextUtil.getMarketByContext(context);
            if (MetadataManager.isIsolateStock(marketValue, channel)) {
                decorateActivitySku(context.getItemDO(), channel, activityId);
            }
//            if (channel != 2023) {
//                decorateActivitySku(context.getItemDO(), channel, activityId);
//            }
        } catch (Throwable e) {
            logger.error("set activity stock error", e);
        }

        if (context.getChannelTag() == null && null != context.getChannelMeta() && context.getChannelMeta().isNormalPriceDefault()) {
            //某些渠道，在活动未生效时，显示日常售卖价
            decorateNormalSkuAndPrice(context);
        } else {
            // 设置渠道价格
            decorateChannelSkuAndPrice(context, channel, activityId);
        }
    }

    private void decorateActivitySku(DetailItemDO item, Integer channelId, Long activityId) {
        List<Long> skuIdList = new ArrayList<>(item.getItemSkuDOList().size());
        for (ItemSkuDO sku : item.getItemSkuDOList()) {
            skuIdList.add(sku.getSkuId());
        }
        try {
            BatchActivityInventoryQueryParamV2 param = new BatchActivityInventoryQueryParamV2();
            param.setSkuIds(skuIdList);
            param.setChannelId(channelId);
            param.setActivityId(activityId);
            MapResult<Long, ActivityInventoryV2> inventoryMapResult = inventoryReadService.batchQueryActivityInventoryV3(param);

            if (null != inventoryMapResult && inventoryMapResult.isSuccess()) {
                Map<Long, ActivityInventoryV2> inventoryMap = inventoryMapResult.getData();
                if (null != inventoryMap) {
                    long originalTotalStock = 0;
                    long remindTotalStock = 0;
                    for (ItemSkuDO sku : item.getItemSkuDOList()) {
                        ActivityInventoryV2 inventory = inventoryMap.get(sku.getSkuId());
                        sku.setQuantity(null == inventory ? 0 : inventory.getStock());
                        if (inventory == null) {
                            continue;
                        }
                        originalTotalStock += null == inventory.getOriginalStock() ? 0 : inventory.getOriginalStock();
                        remindTotalStock += null == inventory.getStock() ? 0 : inventory.getStock();
                    }
                    // 渠道活动的原始报名总库存
                    item.setActOriginalTotalStock(originalTotalStock);
                    item.setRemindTotalStock(remindTotalStock);
                }
            }
        } catch (Exception e) {
            logger.error("get channel inventory failed.", e);
        }
    }

    public void decorateChannelSkuAndPrice(DetailContext context, Integer channel, Long activityId) {
        DetailItemDO item = context.getItemDO();
        try {
            Map<Long, Long> originSkuMap = getSkuPriceMap(item);
            Map<Long, Long> realSkuMap = null;
            Long realPrice = null;
            String decorate = null;

            if (2021 == channel) {
                Object obj = context.getContext("shopSeckillInfo");
                if (null != obj) {
                    SkTagInfo skTagInfo = (SkTagInfo) obj;
                    Integer nowTime = (int) (System.currentTimeMillis() / 1000);
                    //活动未开始，刷价格
                    if (skTagInfo.getStartTime() > nowTime) {
                        Long price = skTagInfo.getPrice().longValue();
                        realSkuMap = new HashMap<>();
                        for (ItemSkuDO sku : context.getItemDO().getItemSkuDOList()) {
                            realSkuMap.put(sku.getSkuId(), price);
                            if (null == realPrice || realPrice > price) {
                                realPrice = price;
                            }
                        }
                    }
                }
            } else if (2019 == channel) {
                Object obj = context.getContext("ptgInfo");
                if (null != obj) {
                    AuditItemDTO auditItemDTO = (AuditItemDTO) obj;
                    Integer nowTime = (int) (System.currentTimeMillis() / 1000);
                    if (null != auditItemDTO && nowTime < auditItemDTO.getStartTime()) {
                        boolean isAllOnePrice = auditItemDTO.getActivityDiscount() == 1000;
                        realSkuMap = new HashMap<>();
                        for (ItemSkuDO sku : context.getItemDO().getItemSkuDOList()) {
                            Long nowPrice = isAllOnePrice ? auditItemDTO.getPromotionPrice() : auditItemDTO.getActivityDiscount() * sku.getPrice() / 1000;
                            realSkuMap.put(sku.getSkuId(), nowPrice);
                            if (null == realPrice || realPrice > nowPrice) {
                                realPrice = nowPrice;
                            }
                        }
                    }
                }
            } else if (context.getChannelTag() != null) {
                // 通用渠道，活动未开始，强制刷价格
                ChannelTag channelTag = context.getChannelTag();
                ChannelMeta channelMeta = context.getChannelMeta();
                Integer nowTime = (int) (System.currentTimeMillis() / 1000);
                if (nowTime < channelTag.getStartTime()
                        || channelMeta.isForceChannelPrice()) {
                    Long price = channelTag.getPrice();
                    realSkuMap = new HashMap<>();
                    for (ItemSkuDO sku : context.getItemDO().getItemSkuDOList()) {
                        Long nowPrice = null;
                        if (price != null) {
                            //一口价
                            nowPrice = price;
                        } else if (channelTag.getDiscount() != null) {
                            //折扣区间价
                            nowPrice = channelTag.getDiscount() * sku.getPrice() / 1000;
                        } else {
                            //原价
                            nowPrice = sku.getPrice();
                        }
                        realSkuMap.put(sku.getSkuId(), nowPrice);
                        if (null == realPrice || realPrice > nowPrice) {
                            realPrice = nowPrice;
                        }
                    }
                }
            }

            if (null == realSkuMap || null == realPrice) {
                ItemDetailRequestV2 request = new ItemDetailRequestV2();
                Pbuyer pbuyer = new Pbuyer();
                pbuyer.setBuyerId(context.getLoginUserId());
                Pseller pSeller = new Pseller();
                pSeller.setSellerId(item.getUserId());
                PitemDetail pitemDetail = new PitemDetail();
                pitemDetail.setExtra(item.getJsonExtra());
                pitemDetail.setItemId(item.getItemId());
                pitemDetail.setSkuPriceMap(originSkuMap);
                pitemDetail.setItemPrice(getHighestPrice(item.getItemSkuDOList()));
                pitemDetail.setItemTagList(PromotionConvertUtils.convertItemTagToItemProTag(context.getItemDO().getItemTags()));
                InvokeInfo invokeInfo = new InvokeInfo();
                invokeInfo.setChannel(channel == null ? (int) RequestConstants.Channel.UNKNOW : channel);
                invokeInfo.setMarket(ContextUtil.getMarketByContext(context));
                invokeInfo.setSource(RequestConstants.Source.DETAIL);
                int terminal = RequestConstants.Terminal.APP;
                switch (context.getRouteInfo().getPlatform()) {
                    case PC:
                        terminal = RequestConstants.Terminal.PC;
                        break;
                    default:
                        terminal = RequestConstants.Terminal.APP;
                        break;
                }
                String appkey = context.getParam("appkey");
                // 如果是小程序新人专享价渠道。
                if (channel == XCX_XINREN_PRICE_CHANNEL && StringUtils.isNotEmpty(appkey) && WX_APP_KEY.equals(appkey)) {
                    terminal = RequestConstants.Terminal.WX;
                }
                invokeInfo.setTerminal(terminal);
                if (outType != null) {
                    invokeInfo.setOutId(activityId != null ? activityId.toString() : null);
                    invokeInfo.setOutType(outType);
                }
                try {
                    Object auctionIdObj = context.getParam("auctionId");
                    if (auctionIdObj != null) {
                        String auctionIdStr = (String) auctionIdObj;
                        invokeInfo.setActivityId(auctionIdStr); // 拍卖id
                    }
                } catch (Exception e) {
                    logger.error("activity id parse error, id: {}", activityId, e);
                }

                request.setPitemDetail(pitemDetail);
                request.setSeller(pSeller);
                request.setPbuyer(pbuyer);
                request.setInvokeInfo(invokeInfo);

                Result<ItemDetailPromotion> ret = promotionReadService.calcForItemDetailPromotion(request);
                if (null != ret && ret.isSuccess() && null != ret.getData() && null != ret.getData().getItemRealPrice()) {
                    realSkuMap = ret.getData().getSkuRealPriceMap();
                    realPrice = ret.getData().getItemRealPrice();
                    decorate = ret.getData().getDecorate();
                    item.setPriceChannel(channel);
                    if (ret.getData().getCampaigninfo() != null
                            && ret.getData().getCampaigninfo().getPromotionMark() != null) {
                        item.setPromotionCode(ret.getData().getCampaigninfo().getPromotionMark().getPromotionCode());
                    }
                }
            }
            if (null != realSkuMap && null != realPrice) {
                item.setDiscountDesc(decorate);
                filterSku(item, realSkuMap, realPrice);
            } else {
                item.setDiscountDesc("");
                filterSku(item, originSkuMap, item.getReservePrice().longValue());
            }
            if (2031 == channel && realPrice != null) {
                // 直播拍卖直接把促销返回的价格设置到LowNowPriceVal
                item.setLowNowPriceVal(realPrice);
                item.setHighNowPriceVal(realPrice);
                for (ItemSkuDO sku : item.getItemSkuDOList()) {
                    sku.setNowPrice(realPrice.intValue());
                }
            }
        } catch (Throwable e) {
            logger.error("get discount info failed : {}", e);
            filterSku(item, new HashMap<Long, Long>(), null);
        }
    }

    public void decorateNormalSkuAndPrice(DetailContext context) {
        DetailItemDO item = context.getItemDO();
        try {
            Map<Long, Long> originSkuMap = getSkuPriceMap(item);
            Map<Long, Long> realSkuMap = null;
            Long realPrice = null;
            String decorate = null;
            if (!TagUtil.isFlItem(item.getJsonExtra()) && useLocalDiscount()) {
                SystemDiscountChecker.SystemDiscountRes ret = SystemDiscountChecker.calcSystemDiscount(item.getJsonExtra(), item.getReservePrice(), false, (int) RequestConstants.Market.MOGUJIE, originSkuMap);
                if (null != ret) {
                    realSkuMap = ret.getSkuRalPrice();
                    realPrice = ret.getRealPrice();
                    decorate = ret.getName();
                }
            }

            if (null == realSkuMap || null == realPrice) {
                ItemDetailRequestV2 request = new ItemDetailRequestV2();
                Pbuyer pbuyer = new Pbuyer();
                pbuyer.setBuyerId(context.getLoginUserId());
                Pseller pSeller = new Pseller();
                pSeller.setSellerId(item.getUserId());
                PitemDetail pitemDetail = new PitemDetail();
                pitemDetail.setExtra(item.getJsonExtra());
                pitemDetail.setItemId(item.getItemId());
                pitemDetail.setSkuPriceMap(originSkuMap);
                pitemDetail.setItemPrice(getHighestPrice(item.getItemSkuDOList()));
                String skuNum = context.getParam("skuNum");
                if (org.apache.commons.lang3.StringUtils.isNotBlank(skuNum)) {
                    pitemDetail.setNumber(Long.parseLong(skuNum));
                } else {
                    pitemDetail.setNumber(1L);
                }
                pitemDetail.setItemTagList(PromotionConvertUtils.convertItemTagToItemProTag(context.getItemDO().getItemTags()));
                InvokeInfo invokeInfo = new InvokeInfo();
                invokeInfo.setChannel((int) RequestConstants.Channel.UNKNOW);
                invokeInfo.setMarket(ContextUtil.getMarketByContext(context));
                invokeInfo.setSource(RequestConstants.Source.DETAIL);
                invokeInfo.setTerminal(context.getRouteInfo().getPlatform() == Platform.APP ? RequestConstants.Terminal.APP : RequestConstants.Terminal.PC);
                request.setPitemDetail(pitemDetail);
                request.setSeller(pSeller);
                request.setPbuyer(pbuyer);
                request.setInvokeInfo(invokeInfo);

                Result<ItemDetailPromotion> ret = promotionReadService.calcForItemDetailPromotion(request);
                if (null != ret && ret.isSuccess() && null != ret.getData()) {
                    CampaignInfo campaignInfo = ret.getData().getCampaigninfo();
                    if (null != campaignInfo) {
                        Map<String, String> parameter = campaignInfo.getParameter();
                        if (null != parameter && !parameter.isEmpty()) {
                            String limitNum = parameter.get("itemCountLimit");
                            if (!org.apache.commons.lang3.StringUtils.isBlank(limitNum)) {
                                item.setLimitNum(Integer.parseInt(limitNum));
                            }
                        }
                    }
                    realSkuMap = ret.getData().getSkuRealPriceMap();
                    realPrice = ret.getData().getItemRealPrice();
                    decorate = ret.getData().getDecorate();
                }
            }
            if (null != realSkuMap && null != realPrice) {
                item.setDiscountDesc(decorate);
                filterSku(item, realSkuMap, realPrice);
            } else {
                item.setDiscountDesc("");
                filterSku(item, originSkuMap, item.getReservePrice().longValue());
            }
        } catch (Throwable e) {
            logger.error("get discount info failed : {}", e);
            filterSku(item, new HashMap<Long, Long>(), null);
        }
    }

    private boolean useLocalDiscount() {
        try {
            Boolean useLocal = Boolean.parseBoolean(MetabaseTool.getValue("discount_useLocal"));
            if (null != useLocal) {
                return useLocal;
            }
        } catch (Exception e) {
            ;
        }
        return true;
    }

    private Map<Long, Long> getSkuPriceMap(ItemDO item) {
        Map<Long, Long> skuPriceMap = new HashMap<>();
        for (ItemSkuDO sku : item.getItemSkuDOList()) {
            skuPriceMap.put(sku.getSkuId(), sku.getPrice().longValue());
        }
        return skuPriceMap;
    }

    private static void filterSku(DetailItemDO item, Map<Long, Long> realPriceMap, Long realPrice) {
        long lowPrice = item.getReservePrice();
        long highPrice = item.getReservePrice();
        long lowNowPrice = null == realPrice ? item.getReservePrice() : realPrice;
        long highNowPrice = null == realPrice ? item.getReservePrice() : realPrice;
        long totalStock = 0;
        for (ItemSkuDO sku : item.getItemSkuDOList()) {

            if (sku.getPrice() < lowPrice) {
                lowPrice = sku.getPrice();
            }

            if (sku.getPrice() > highPrice) {
                highPrice = sku.getPrice();
            }
            Long skuRealPrice = realPriceMap.get(sku.getSkuId());
            if (null == skuRealPrice) {
                skuRealPrice = sku.getPrice().longValue();
            }
            sku.setNowPrice(skuRealPrice.intValue());
            if (skuRealPrice < lowNowPrice) {
                lowNowPrice = skuRealPrice;
            }

            if (skuRealPrice > highNowPrice) {
                highNowPrice = skuRealPrice;
            }

            totalStock += sku.getQuantity();
        }
        item.setLowPrice(NumUtil.formatNum(lowPrice / 100D));
        item.setLowNowPrice(NumUtil.formatNum(lowNowPrice / 100D));
        item.setHighPrice(NumUtil.formatNum(highPrice / 100D));
        item.setHighNowPrice(NumUtil.formatNum(highNowPrice / 100D));
        item.setHighNowPriceVal(highNowPrice);
        item.setLowNowPriceVal(lowNowPrice);
        item.setTotalStock(totalStock);
        item.setReservePrice(lowPrice);
    }

    private Long getHighestPrice(final List<ItemSkuDO> skuList) {
        if (CollectionUtil.isEmpty(skuList)) {
            return 0L;
        }
        Long highestPrice = skuList.get(0).getPrice().longValue();
        for (ItemSkuDO sku : skuList) {
            if (sku.getPrice().longValue() > highestPrice) {
                highestPrice = sku.getPrice().longValue();
            }
        }
        return highestPrice;
    }

}
