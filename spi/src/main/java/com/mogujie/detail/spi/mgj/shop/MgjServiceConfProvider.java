package com.mogujie.detail.spi.mgj.shop;


import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.spi.BizSpi;
import com.mogujie.detail.module.shop.spi.IServiceConfProvider;

/**
 * Created by <PERSON>iaoyao on 16/5/12.
 */
@BizSpi
public class MgjServiceConfProvider implements IServiceConfProvider {

    private static final String STYLE_NAME = "款式新颖";
    private static final String QUALITY_NAME = "质量满意";
    private static final String PRICE_NAME = "价格合理";
    private static final String SERVICE_NAME = "服务周到";
    private static final String DESC_NAME = "描述相符";

    public String getServiceName(DetailContext context, String key) {
        if ("style".equals(key)) {
            return STYLE_NAME;
        } else if ("quality".equals(key)) {
            return QUALITY_NAME;
        } else if ("price".equals(key)) {
            return PRICE_NAME;
        } else if ("service".equals(key)) {
            return SERVICE_NAME;
        } else if ("desc".equals(key)) {
            return DESC_NAME;
        } else {
            return "";
        }
    }
}
