package com.mogujie.detail.spi.mgj.shop;

import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.constant.Platform;
import com.mogujie.detail.core.spi.BizSpi;
import com.mogujie.detail.module.shop.spi.IShopUrlProvider;


/**
 * Created by <PERSON><PERSON>oya<PERSON> on 16/4/10.
 */
@BizSpi(platform = Platform.APP)
public class MgjAppShopUrlProvider implements IShopUrlProvider {

    @Override
    public String getShopUrl(DetailContext detailContext) {
        return "mgj://shop?shopId=" + IdConvertor.idToUrl(detailContext.getItemDO().getShopId());
    }

    @Override
    public String getShopAllGoodsUrl(DetailContext detailContext) {
        return "mgj://shopgoodswall/moshop/goodsall?shopId=" + IdConvertor.idToUrl(detailContext.getItemDO().getShopId()) + "&title=全部商品";
    }
}