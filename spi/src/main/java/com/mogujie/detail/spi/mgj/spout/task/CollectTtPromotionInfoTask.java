package com.mogujie.detail.spi.mgj.spout.task;

import com.mogujie.commons.utils.EnvUtil;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.darwin.util.CollectionUtil;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.OtherPrice;
import com.mogujie.detail.core.adt.RouteInfo;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.ContextKeys;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.task.AbstractCollectDataTask;
import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.core.util.ContextUtil;
import com.mogujie.detail.core.util.NumUtil;
import com.mogujie.metabase.spring.client.MetabaseClient;
import com.mogujie.service.hummer.api.PromotionReadService;
import com.mogujie.service.hummer.constains.RequestConstants;
import com.mogujie.service.hummer.domain.dto.*;
import com.mogujie.service.hummer.domain.dto.result.Result;
import com.mogujie.service.hummer.utils.PromotionConvertUtils;
import com.mogujie.service.inventory.api.InventoryReadService;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import com.mogujie.tesla.core.ReferConfig;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by xiaoyao on 16/10/20.
 */
public class CollectTtPromotionInfoTask extends AbstractCollectDataTask {

    private static final Logger LOGGER = LoggerFactory.getLogger(CollectTtPromotionInfoTask.class);


    private InventoryReadService inventoryReadService;

    private PromotionReadService promotionReadService;


    private MetabaseClient metabaseClient;

    private CommonSwitchUtil commonSwitchUtil;


    public CollectTtPromotionInfoTask(DetailContext detailContext, MetabaseClient metabaseClient, CommonSwitchUtil commonSwitchUtil) throws DetailException {
        super(detailContext);
        this.commonSwitchUtil = commonSwitchUtil;

        this.metabaseClient = metabaseClient;
        try {
            this.inventoryReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(InventoryReadService.class);
            ReferConfig<PromotionReadService> referConfig = new ReferConfig<>(PromotionReadService.class);
            if (EnvUtil.isOnlineEnv()) {
                referConfig.setGroup("DETAIL-GROUP");
            }
            referConfig.setTimeout(200);
            this.promotionReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(referConfig);
        } catch (Exception e) {
            throw new DetailException(e);
        }
    }

    @Override
    public void collect() {
        decorateSkuAndPrice(this.context);
    }



    private Map<Long, Long> getSkuPriceMap(ItemDO item) {
        Map<Long, Long> skuPriceMap = new HashMap<>();
        for (ItemSkuDO sku : item.getItemSkuDOList()) {
            skuPriceMap.put(sku.getSkuId(), sku.getPrice().longValue());
        }
        return skuPriceMap;
    }

    public void decorateSkuAndPrice(DetailContext context) {
        DetailItemDO item = context.getItemDO();
        try {
            Map<Long, Long> originSkuMap = getSkuPriceMap(item);
            Map<Long, Long> realSkuMap = null;
            Long realPrice = null;
            String decorate = null;
            int current = (int) (System.currentTimeMillis() / 1000);

            ItemDetailRequestV2 request = new ItemDetailRequestV2();
            Pbuyer pbuyer = new Pbuyer();
            pbuyer.setBuyerId(context.getLoginUserId());
            Pseller pSeller = new Pseller();
            pSeller.setSellerId(item.getUserId());
            PitemDetail pitemDetail = new PitemDetail();
            pitemDetail.setExtra(item.getJsonExtra());
            pitemDetail.setItemId(item.getItemId());
            pitemDetail.setSkuPriceMap(originSkuMap);
            pitemDetail.setItemPrice(getHighestPrice(item.getItemSkuDOList()));
            String skuNum = context.getParam("skuNum");
            if (StringUtils.isNotBlank(skuNum)) {
                pitemDetail.setNumber(Long.parseLong(skuNum));
            } else {
                pitemDetail.setNumber(1L);
            }
            pitemDetail.setItemTagList(PromotionConvertUtils.convertItemTagToItemProTag(context.getItemDO().getItemTags()));
            InvokeInfo invokeInfo = new InvokeInfo();
            invokeInfo.setChannel((int) RequestConstants.Channel.UNKNOW);
            invokeInfo.setMarket(ContextUtil.getMarketByContext(context));
            RouteInfo routeInfo = context.getRouteInfo();
            if ((routeInfo.getBizType() == BizType.SKU && routeInfo.getVersion().startsWith("live.")/* 直播sku接口 */)
                    || "live".equals(context.getParam("from"))) {
                invokeInfo.setSource(RequestConstants.Source.LIVE_DETAIL);
            } else {
                invokeInfo.setSource(RequestConstants.Source.DETAIL);
            }
            int terminal = RequestConstants.Terminal.APP;
            switch (context.getRouteInfo().getPlatform()) {
                case PC:
                    terminal = RequestConstants.Terminal.PC;
                    break;
                default:
                    terminal = RequestConstants.Terminal.APP;
                    break;
            }
            invokeInfo.setTerminal(terminal);
            request.setPitemDetail(pitemDetail);
            request.setSeller(pSeller);
            request.setPbuyer(pbuyer);
            invokeInfo.setClientName(RequestConstants.ClientName.BYTE_BEATING);

            request.setInvokeInfo(invokeInfo);

            Result<ItemDetailPromotion> ret = promotionReadService.calcForItemDetailPromotion(request);
            if (null != ret && ret.isSuccess() && null != ret.getData()) {
                ItemDetailPromotion itemDetailPromotion = ret.getData();
                CampaignInfo campaignInfo = itemDetailPromotion.getCampaigninfo();
                if (null != campaignInfo) {
                    Map<String, String> parameter = campaignInfo.getParameter();
                    if (null != parameter && !parameter.isEmpty()) {
                        String limitNum = parameter.get("itemCountLimit");
                        if (!StringUtils.isBlank(limitNum)) {
                            item.setLimitNum(Integer.parseInt(limitNum));
                        }
                    }
                    //bizType为702，且promotionCode为discount，则表示该价格为"直播新人专享价"
                    //具体含义咨询 @桌子
                    if (campaignInfo.getBizType() == 702 && "discount".equals(campaignInfo.getPromotionMark().getPromotionCode())) {
                        context.addContext(ContextKeys.IS_LIVE_NEWCOMER_PRICE, true);
                    }
                }
                //设置商品的会员价和普通用户价（目前只有买手店有这个业务）
                BuyerItemPrice buyerItemPrice = itemDetailPromotion.getBuyerItemPrice();
                if (buyerItemPrice != null) {
                    item.setMemberPrice(buyerItemPrice.getItemMemberSkuPrice());
                    item.setNormalUserPrice(buyerItemPrice.getItemPromotionSkuPrice());
                }
                //其他价格，如'直播分享价'等
                List<ItemOtherPrice> otherPrices = itemDetailPromotion.getItemOtherPrices();
                boolean isPresale = ContextUtil.isPresaleItem(item);
                if (otherPrices != null) {
                    List<OtherPrice> otherPriceList = otherPrices.stream()
                            .map(price -> {
                                OtherPrice otherPrice = new OtherPrice();
                                otherPrice.setPriceType(price.getPriceType());
                                otherPrice.setItemRealPrice(price.getItemRealPrice());
                                Map<String, Long> skuPriceMap = new HashMap<>();
                                if (price.getSkuRealPriceMap() != null) {
                                    otherPrice.setMaxPrice(Collections.max(price.getSkuRealPriceMap().values()));
                                    otherPrice.setMinPrice(Collections.min(price.getSkuRealPriceMap().values()));
                                    price.getSkuRealPriceMap().entrySet().stream()
                                            .forEach(entry ->
                                                    skuPriceMap.put(IdConvertor.idToUrl(entry.getKey()), entry.getValue())
                                            );
                                    otherPrice.setSkuRealPriceMap(skuPriceMap);
                                }
                                return otherPrice;
                            })
                            //对预售商品，屏蔽直播分享价
                            .filter(otherPrice -> !(isPresale && otherPrice.getPriceType() == 1))
                            .collect(Collectors.toList());
                    item.setOtherPrices(otherPriceList);
                }
                realSkuMap = itemDetailPromotion.getSkuRealPriceMap();
                realPrice = itemDetailPromotion.getItemRealPrice();
                decorate = itemDetailPromotion.getDecorate();
            }

            if (null != realSkuMap && null != realPrice) {
                item.setDiscountDesc(decorate);
                filterSku(item, realSkuMap, realPrice);
            } else {
                item.setDiscountDesc("");
                filterSku(item, originSkuMap, item.getReservePrice().longValue());
            }
        } catch (Throwable e) {
            LOGGER.error("get discount info failed : {}", e);
            filterSku(item, new HashMap<Long, Long>(), null);
        }
    }

    private static void filterSku(DetailItemDO item, Map<Long, Long> realPriceMap, Long realPrice) {
        long lowPrice = item.getReservePrice();
        long highPrice = item.getReservePrice();
        long lowNowPrice = null == realPrice ? item.getReservePrice() : realPrice;
        long highNowPrice = null == realPrice ? item.getReservePrice() : realPrice;
        long totalStock = 0;
        for (ItemSkuDO sku : item.getItemSkuDOList()) {

            if (sku.getPrice() < lowPrice) {
                lowPrice = sku.getPrice();
            }

            if (sku.getPrice() > highPrice) {
                highPrice = sku.getPrice();
            }
            Long skuRealPrice = realPriceMap.get(sku.getSkuId());
            if (null == skuRealPrice) {
                skuRealPrice = sku.getPrice().longValue();
            }
            sku.setNowPrice(skuRealPrice.intValue());
            if (skuRealPrice < lowNowPrice) {
                lowNowPrice = skuRealPrice;
            }

            if (skuRealPrice > highNowPrice) {
                highNowPrice = skuRealPrice;
            }

            totalStock += sku.getQuantity();
        }
        item.setLowPrice(NumUtil.formatNum(lowPrice / 100D));
        item.setLowNowPrice(NumUtil.formatNum(lowNowPrice / 100D));
        item.setHighPrice(NumUtil.formatNum(highPrice / 100D));
        item.setHighNowPrice(NumUtil.formatNum(highNowPrice / 100D));
        item.setHighNowPriceVal(highNowPrice);
        item.setLowNowPriceVal(lowNowPrice);
        item.setTotalStock(totalStock);
        item.setReservePrice(lowPrice);
    }

    private Long getHighestPrice(final List<ItemSkuDO> skuList) {
        if (CollectionUtil.isEmpty(skuList)) {
            return 0L;
        }
        Long highestPrice = skuList.get(0).getPrice().longValue();
        for (ItemSkuDO sku : skuList) {
            if (sku.getPrice().longValue() > highestPrice) {
                highestPrice = sku.getPrice().longValue();
            }
        }
        return highestPrice;
    }
}
