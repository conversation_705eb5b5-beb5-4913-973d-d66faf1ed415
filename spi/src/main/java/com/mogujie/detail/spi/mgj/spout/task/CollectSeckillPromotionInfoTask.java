package com.mogujie.detail.spi.mgj.spout.task;

import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.task.AbstractCollectDataTask;
import com.mogujie.detail.core.util.NumUtil;
import com.mogujie.marketing.ares.api.SecKillDataService;
import com.mogujie.marketing.ares.common.Result;
import com.mogujie.marketing.ares.domain.entity.SecKillInfo;
import com.mogujie.marketing.ares.domain.entity.SecKillSku;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by anshi on 17/3/8.
 */
public class CollectSeckillPromotionInfoTask extends AbstractCollectDataTask {

    private static final Logger LOGGER = LoggerFactory.getLogger(CollectSeckillPromotionInfoTask.class);

    private SecKillDataService secKillDataService;

    public CollectSeckillPromotionInfoTask(DetailContext context) throws DetailException {
        super(context);
        this.context = context;
        try {
            secKillDataService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(SecKillDataService.class);
        } catch (Exception e) {
            throw new DetailException(e);
        }
    }

    /**
     * 避免缓存错误数据
     * context.isError=true
     */
    @Override
    public void collect() {
        String seckillIdStr = context.getParam("activityId");
        Long seckillId = IdConvertor.urlToId(seckillIdStr);
        if (seckillId == null) {
            LOGGER.error("sec kill id is empty!");
            context.setError(true);
            return;
        }
        try {
            Result<SecKillInfo> secResult = secKillDataService.getInfoById(seckillId);
            if (null != secResult && secResult.isSuccess()) {
                SecKillInfo secKillInfo = secResult.getValue();
                if (secKillInfo != null) {
                    context.addContext("seckillInfo", secKillInfo);
                    Result<List<SecKillSku>> skuResult = secKillDataService.fetchSecKillCodesCnt(seckillId);
                    if (skuResult != null && skuResult.isSuccess()) {
                        // 过滤sku并设定库存
                        decorateSeckillSku(context.getItemDO(), skuResult.getValue());
                        // 设置价格
                        decorateSeckillSkuAndPrice(context.getItemDO(), secKillInfo);
                        return;
                    }
                }
            }
        } catch (Throwable e) {
            LOGGER.error("init sec kill info error.", e);
        }
        LOGGER.error("sec kill service calling failed.");
        context.setError(true);
    }

    /**
     * 1. 过滤秒杀sku
     * 2. 设置秒杀sku库存
     *
     * @param itemDO
     * @param secKillSkus
     */
    private void decorateSeckillSku(DetailItemDO itemDO, List<SecKillSku> secKillSkus) {
        List<ItemSkuDO> itemSkuDOList = itemDO.getItemSkuDOList();
        Map<String, SecKillSku> secKillSkuMap = new HashMap<>();
        for (SecKillSku secKillSku : secKillSkus) {
            String secKillCode = secKillSku.getSecKillCode();
            if (StringUtils.isBlank(secKillCode)) {
                continue;
            }
            String[] codes = secKillCode.split("_");
            if (codes == null || codes.length != 3) {
                continue;
            }
            Long skuId = Long.parseLong(codes[2]);
            secKillSkuMap.put(skuId.toString(), secKillSku);
        }
        List<ItemSkuDO> finalSkuDOList = new ArrayList<>();
        for (ItemSkuDO skuDO : itemSkuDOList) {
            SecKillSku secKillSku = secKillSkuMap.get(String.valueOf(skuDO.getSkuId()));
            if (secKillSku != null) {
                skuDO.setQuantity((int) secKillSku.getStock());
                finalSkuDOList.add(skuDO);
            }
        }
        itemDO.setItemSkuDOList(finalSkuDOList);
    }

    /**
     * 秒杀价格设定
     *
     * @param item
     * @param secKillInfo
     */
    private void decorateSeckillSkuAndPrice(DetailItemDO item, SecKillInfo secKillInfo) {
        if (item == null
                || item.getItemSkuDOList() == null
                || item.getItemSkuDOList().size() == 0
                || secKillInfo == null) {
            return;
        }
        long realPrice = secKillInfo.getPrice();
        long lowPrice = item.getReservePrice();
        long highPrice = item.getReservePrice();
        long lowNowPrice = realPrice;
        long highNowPrice = realPrice;
        long totalStock = 0;
        for (ItemSkuDO sku : item.getItemSkuDOList()) {
            if (sku.getPrice() < lowPrice) {
                lowPrice = sku.getPrice();
            }
            if (sku.getPrice() > highPrice) {
                highPrice = sku.getPrice();
            }
            sku.setNowPrice((int) realPrice);
            totalStock += sku.getQuantity();
        }
        item.setLowPrice(NumUtil.formatNum(lowPrice / 100D));
        item.setLowNowPrice(NumUtil.formatNum(lowNowPrice / 100D));
        item.setHighPrice(NumUtil.formatNum(highPrice / 100D));
        item.setHighNowPrice(NumUtil.formatNum(highNowPrice / 100D));
        item.setHighNowPriceVal(highNowPrice);
        item.setLowNowPriceVal(lowNowPrice);
        item.setTotalStock(totalStock);
        item.setReservePrice(lowPrice);
    }
}
