package com.mogujie.detail.spi.dslutils;

import com.alibaba.fastjson.JSON;
import com.meili.service.shopcenter.domain.entity.ShopInfo;
import com.meili.service.shopcenter.util.ShopInfoTagsUtil;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailContextHolder;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.RouteInfo;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.ContextKeys;
import com.mogujie.detail.core.util.ContextUtil;
import com.mogujie.detail.core.util.MetabaseTool;
import com.mogujie.detail.core.util.TagUtil;
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO;
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO;
import com.mogujie.detail.module.pintuan.domain.PinTuanDO;
import com.mogujie.detail.module.shop.domain.ShopDO;
import com.mogujie.detail.urlhub.DetailUrlProvider;
import com.mogujie.detail.urlhub.constants.App;
import com.mogujie.detail.urlhub.constants.Biz;
import com.mogujie.detail.urlhub.constants.Platform;
import com.mogujie.detail.urlhub.params.DetailUrlQueryOptions;
import com.mogujie.marketing.veyron.BizTagUtils;
import com.mogujie.marketing.veyron.enums.BizTagTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 通用工具辅助方法
 * Created by anshi on 17/3/31.
 */
@Component
public class Tools {

    //商品sku前端缓存时间
    private static final String CLIENT_CACHE_EXPIRESECONDS_SKUS = "client_cache_expireseconds_skus";

    private static final String MEDICAL_BEAUTY_CATEGORIES_KEY = "medical_beauty_categories";

    private static final Integer BONDED_ITEM_TAG = 1498;

    private static final Integer CROSS_BORDER_ITEM_TAG = 1548;

    private static final Integer VIRTUAL_COUPON_ITEM_TAG = 1065;

    private static final Integer LIVE_SUPPLY_CHAIN_TEMP_ITEM_TAG = 1246;

    private static final Integer LIVE_SUPPLY_CHAIN_ITEM_TAG = 1963;

    private static final String JD_SKU_KEY = "jdSpuId";

    //商品自营标
    private static final Integer SELF_EMPLOY_ITEM_TAG = 1561;

    //店铺自营标
    private static final Integer SELF_EMPLOY_SHOP_TAG = 9991;

    //直播供应链特供商品标(供应给特定的主播)
    private static final Integer SPECIAL_SUPPLY_ITEM_TAG = 1553;

    /**
     * 返回sku选择器的前端缓存时间
     *
     * @return
     */
    public int getCacheExpireSeconds() {
        try {
            return Integer.parseInt(MetabaseTool.getValue(CLIENT_CACHE_EXPIRESECONDS_SKUS));
        } catch (Throwable e) {
            return 0;
        }
    }

    /**
     * 是否为良品商品
     *
     * @param itemBaseDO
     * @param shopDO
     */
    public static boolean isGoodItem(ItemBaseDO itemBaseDO, ShopDO shopDO) {
        try {
            List<Integer> itemTags = new ArrayList<>();
            List<Integer> shopTags = null;
            if (itemBaseDO != null && itemBaseDO.getNumTags() != null && itemBaseDO.getNumTags().size() > 0) {
                int length = itemBaseDO.getNumTags().size();
                for (int i = 0; i < length; i++) {
                    String tag = itemBaseDO.getNumTags().get(i);
                    itemTags.add(Integer.parseInt(tag));
                }
            }
            if (shopDO != null) {
                shopTags = shopDO.getTags();
            } else {
                shopTags = Collections.EMPTY_LIST;
            }
            return BizTagUtils.checkBizTag(BizTagTypeEnum.GOOD_ITEM, itemTags, shopTags);
        } catch (Throwable e) {
            return false;
        }
//        if (itemBaseDO?.itemTags?.contains(ItemTag.GOOD)
//                || ItemTag.INSTORE in itemBaseDO?.itemTags
//                || ItemTag.TRANSFER in itemBaseDO?.itemTags) {
//            return true
//        }
    }

    /**
     * 是否为U质团
     *
     * @param groupbuyingDO
     */
    public static boolean isUZhi(GroupbuyingDO groupbuyingDO) {
        return false;
    }

    static String getH5Url(Long iid) {
        DetailUrlQueryOptions options = new DetailUrlQueryOptions();
        options.setApp(App.MGJ);  // 默认 MGJ
        options.setBiz(Biz.NORMAL);  // 默认普通详情页
        options.setPlatform(Platform.H5); //默认APP
        options.setItemId(iid); // 必填项
        options.setIsExternal(false); // 默认为站内详情页
        options.setNeedEncode(true); // 默认为true, 表示需要对itemId做id2url转换
        return DetailUrlProvider.getInstance().getDetailUrl(options);
    }

    /**
     * 获取上下文中配置的DSL组件名
     *
     * @return
     */
    public static List<String> getDSLComponents() {
        return getDSLComponents(DetailContextHolder.get());
    }

    /**
     * 获取上下文中配置的DSL组件名
     *
     * @param context
     * @return
     */
    public static List<String> getDSLComponents(DetailContext context) {
        if (context == null || CollectionUtils.isEmpty(context.getComponentIds())) {
            return Collections.EMPTY_LIST;
        }
        return context.getComponentIds().stream()
                .map(namespacedComponent -> StringUtils.substringAfterLast(namespacedComponent, "."))
                .collect(Collectors.toList());
    }

    /**
     * 是否为拼团
     *
     * @param itemBaseDO
     * @param pintuanDO
     * @return
     */
    public static boolean isPintuan(ItemBaseDO itemBaseDO, PinTuanDO pintuanDO) {
        if (itemBaseDO == null || pintuanDO == null || pintuanDO.getSkuInfo() == null) {
            return false;
        }
        if (pintuanDO != null /*拼团DO存在*/
                && itemBaseDO.getSaleType() == 0 /*非预售商品*/
                && pintuanDO.getSkuInfo().getTotalStock() > 0 /*商品有库存*/
                && itemBaseDO.getState() != 3 /*非待售商品*/
                && itemBaseDO.getState() != 1 /*非下架商品*/) {
            return true;
        }
        // 若为京东商品，库存为0也可以展示拼团样式
        if (pintuanDO != null
                && pintuanDO.getSkuInfo().getTotalStock() == 0
                && isJdItem()) {
            return true;
        }
        return false;
    }

    public static boolean isSystemPintuan(ItemBaseDO itemBaseDO, PinTuanDO pinTuanDO) {
        try {
            return isPintuan(itemBaseDO, pinTuanDO) && pinTuanDO.getSystem() && (pinTuanDO.getRemainTime() > 0);
        } catch (Throwable e) {
            return false;
        }
    }

    /**
     * 是否为保税商品
     *
     * @return
     */
    public static boolean isBondedItem() {
        return isBondedItem(DetailContextHolder.get());
    }

    /**
     * 是否为保税商品
     *
     * @return
     */
    public static boolean isBondedItem(DetailContext context) {
        try {
            return TagUtil.isContainsTag(context.getItemDO(), BONDED_ITEM_TAG);
        } catch (Throwable e) {
        }
        return false;
    }

    /**
     * 是否为海外跨境商品
     *
     * @return
     */
    public static boolean isCrossBorderItem() {
        try {
            return isCrossBorderItem(DetailContextHolder.get());
        } catch (Throwable e) {
        }
        return false;
    }

    /**
     * 是否为海外跨境商品
     *
     * @return
     */
    public static boolean isCrossBorderItem(DetailContext context) {
        try {
            return TagUtil.isContainsTag(context.getItemDO(), CROSS_BORDER_ITEM_TAG);
        } catch (Throwable e) {
        }
        return false;
    }

    /**
     * 是否为医疗美容商品
     *
     * @param context
     * @return
     */
    public static boolean isMedicalBeautyItem(DetailContext context) {
        DetailItemDO itemDO = context.getItemDO();
        String[] categories = MetabaseTool.getValue(MEDICAL_BEAUTY_CATEGORIES_KEY).split(",");
        for (String category : categories) {
            if (itemDO.getCids().contains("#" + category + "#")) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否为医疗美容商品
     *
     * @return
     */
    public static boolean isMedicalBeautyItem() {
        DetailContext context = DetailContextHolder.get();
        return isMedicalBeautyItem(context);
    }

    /**
     * 是否为虚拟优惠券商品
     *
     * @return
     */
    public static boolean isVirtualCouponItem(DetailContext context) {
        try {
            return TagUtil.isContainsTag(context.getItemDO(), VIRTUAL_COUPON_ITEM_TAG);
        } catch (Throwable e) {
        }
        return false;
    }

    /**
     * 是否为虚拟优惠券商品
     *
     * @return
     */
    public static boolean isVirtualCouponItem() {
        return isVirtualCouponItem(DetailContextHolder.get());
    }

    /**
     * 是否为京东商品
     *
     * @param context
     * @return
     */
    public static boolean isJdItem(DetailContext context) {
        try {
            RouteInfo routeInfo = context.getRouteInfo();
            if (Boolean.parseBoolean(MetabaseTool.getValue("swt_live_sku_no_jd"))
                    && routeInfo.getBizType() == BizType.SKU && routeInfo.getVersion().startsWith("live.")/* 直播sku接口 */) {
                return false;
            }
            DetailItemDO itemDO = context.getItemDO();
            return itemDO != null && itemDO.getJsonExtra() != null && JSON.parseObject(itemDO.getJsonExtra()).containsKey(JD_SKU_KEY);
        } catch (Throwable e) {
            return false;
        }
    }

    /**
     * 是否为京东商品
     *
     * @return
     */
    public static boolean isJdItem() {
        return isJdItem(DetailContextHolder.get());
    }

    /**
     * 是否为自营商品
     *
     * @param
     * @return
     */
    public static boolean isSelfEmployedItem() {
        return isSelfEmployedItem(DetailContextHolder.get());
    }

    /**
     * 是否为自营商品
     *
     * @param context
     * @return
     */
    public static boolean isSelfEmployedItem(DetailContext context) {
        if (context == null) {
            return false;
        }

        try {
            if (TagUtil.isContainsTag(context.getItemDO(), SELF_EMPLOY_ITEM_TAG)) {
                return true;
            }

            ShopInfo shopInfo = context.getItemDO().getShopInfo();
            if (shopInfo != null && StringUtils.isNotBlank(shopInfo.getTags())) {
                if (ShopInfoTagsUtil.stringToSet(shopInfo.getTags()).contains(SELF_EMPLOY_SHOP_TAG)) {
                    return true;
                }
            }
        } catch (Throwable e) {
        }
        return false;
    }

    /**
     * 当前价格是否为直播新人专享价
     *
     * @return
     */
    public static boolean isLiveNewcomerPrice() {
        return isLiveNewcomerPrice(DetailContextHolder.get());
    }

    /**
     * 当前价格是否为直播新人专享价
     *
     * @param context
     * @return
     */
    public static boolean isLiveNewcomerPrice(DetailContext context) {
        Object obj = context.getContext(ContextKeys.IS_LIVE_NEWCOMER_PRICE);
        if (obj != null && (boolean) obj) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 是否为直播供应链商品
     *
     * @param context
     * @return
     */
    public static boolean isLiveSupplyChainItem(DetailContext context) {
        return TagUtil.isContainsTag(context.getItemDO().getItemTags(), LIVE_SUPPLY_CHAIN_ITEM_TAG);
    }

    /**
     * 是否为直播供应链商品
     *
     * @return
     */
    public static boolean isLiveSupplyChainItem() {
        return isLiveSupplyChainItem(DetailContextHolder.get());
    }

    /**
     * 是否为直播供应链模板商品
     *
     * @param context
     * @return
     */
    public static boolean isLiveSupplyChainTempalteItem(DetailContext context) {
        return TagUtil.isContainsTag(context.getItemDO().getItemTags(), LIVE_SUPPLY_CHAIN_TEMP_ITEM_TAG);
    }

    /**
     * 是否为直播供应链模板商品
     *
     * @return
     */
    public static boolean isLiveSupplyChainTempalteItem() {
        return isLiveSupplyChainTempalteItem(DetailContextHolder.get());
    }

    /**
     * 是否为直播供应链特供商品
     *
     * @param context
     * @return
     */
    public static boolean isSpecialSupplyItem(DetailContext context) {
        return TagUtil.isContainsTag(context.getItemDO().getItemTags(), SPECIAL_SUPPLY_ITEM_TAG);
    }

    /**
     * 是否为直播供应链特供商品
     *
     * @return
     */
    public static boolean isSpecialSupplyItem() {
        return isSpecialSupplyItem(DetailContextHolder.get());
    }

    /**
     * 当前时间是否为春节打烊期间
     *
     * @return
     */
    public static boolean inSpringFestival() {
        return ContextUtil.isInSpringFestival();
    }

    /**
     * 春节打烊最晚发货时间
     *
     * @return
     */
    public static Integer springDeliveryEndTime(){
        return ContextUtil.springDeliveryEndTime();
    }

    /**
     * 当前商品是否打烊(做了春节打烊时间的判断)
     *
     * @return
     */
    public static boolean isSpringFestivalShutdownItem() {
        return ContextUtil.isSpringFestivalShutdownItem(DetailContextHolder.get().getItemDO());
    }

    /**
     * 商品是否订阅了30天发货服务
     *
     * @return
     */
    public static boolean contain30DayDeliveryService() {
        return ContextUtil.contain30dayDeliveryService(DetailContextHolder.get());
    }


    /**
     * 当前商品是否可使用购物津贴
     *
     * @return
     */
    public static boolean isBonusItem(DetailContext context) {
        return ContextUtil.isBonusItem(context);
    }

    /**
     * 当前商品是否可使用购物津贴
     *
     * @return
     */
    public static boolean isBonusItem() {
        return ContextUtil.isBonusItem(DetailContextHolder.get());
    }
}