package com.mogujie.detail.spi.mgj.shop;

import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.constant.SwitchKey;
import com.mogujie.detail.core.spi.BizSpi;
import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.core.util.ImageUtil;
import com.mogujie.detail.core.util.SkuUtil;
import com.mogujie.detail.module.shop.domain.ShopService;
import com.mogujie.detail.module.shop.domain.ShopServicesDO;
import com.mogujie.detail.module.shop.spi.IWaitressProvider;
import com.mogujie.detail.module.shop.util.WaitressUtil;
import com.mogujie.detail.spi.dslutils.Tools;
import com.mogujie.metabase.spring.client.MetabaseClient;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.service.waitress.platform.api.PlatformIconUrlService;
import com.mogujie.service.waitress.platform.api.PlatformItemServiceService;
import com.mogujie.service.waitress.platform.common.ServiceDetailEnum;
import com.mogujie.service.waitress.platform.domain.ResultSupport;
import com.mogujie.service.waitress.platform.domain.entity.IconUrl;
import com.mogujie.service.waitress.platform.domain.entity.ItemServiceDetail;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Created by xiaoyao on 16/4/10.
 */
@BizSpi
public class MgjAppWaitressProvider implements IWaitressProvider {

    private static final Logger LOGGER = LoggerFactory.getLogger(MgjAppWaitressProvider.class);

    protected Gson gson;

    private static final String NO_SUPPORT_NO_REASON_REFOUND_ICON = "/p1/170309/idid_ifrwmnjqgntgeojymuzdambqhayde_60x60.png";

    protected PlatformItemServiceService platformItemServiceService;

    protected PlatformIconUrlService platformIconUrlService;

    @Autowired
    private CommonSwitchUtil commonSwitchUtil;

    @Resource(name = "confMetabaseClient")
    private MetabaseClient metabaseClient;

    @PostConstruct
    public void init() {
        gson = new Gson();
        try {
            platformItemServiceService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(PlatformItemServiceService.class);
            platformIconUrlService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(PlatformIconUrlService.class);
        } catch (Exception e) {
            LOGGER.error("init platformItemServiceService service failed : {}", e);
        }
    }


    @Override
    public ShopServicesDO listService(DetailContext context) {
        DetailItemDO item = context.getItemDO();
        boolean orderSnap = context.isOrderSnap();
        if (orderSnap) {
            List<ShopService> services = buildServicesFromOrderSnap(context, item);
            ShopServicesDO shopServicesDO = new ShopServicesDO();
            shopServicesDO.setNormalServices(services);
            shopServicesDO.setGoodItemServices(services);
            return shopServicesDO;
        }
        try {
            //-------------有延迟发货,则过滤发货相关服务信息-------------
            boolean hasDelayedDelivery = false;
            for (ItemSkuDO sku : context.getItemDO().getItemSkuDOList()) {
                if ((sku.getQuantity() > 0 || context.isOrderSnap()) && hasDelayDelivery(sku)) {
                    hasDelayedDelivery = true;
                    break;
                }
            }
            //-------------春节打烊，则过滤发货相关服务信息中除了30天延迟发货的-------------
            boolean inSpringAndShutDown = Tools.isSpringFestivalShutdownItem();
            List<ItemServiceDetail> serviceDetails = WaitressUtil.getItemServices(context);
            if (CollectionUtils.sizeIsEmpty(serviceDetails)) {
                return null;
            }

            // 普通标
            Map<Long, ShopService> shopServices = new HashMap<>(serviceDetails.size());
            // 良品标
            Map<Long, ShopService> goodShopServices = new HashMap<>(serviceDetails.size());
            ShopServicesDO shopServicesDO = new ShopServicesDO();
            boolean withRefoundTag = false;//是否包含退货相关服务
            for (ItemServiceDetail itemService : serviceDetails) {
                if (ServiceDetailEnum.FH_410.getHeadId() == itemService.getServiceHeadId()) {
                    // 根据服务体系解析出该商品秒级别的具体发货时长。
                    this.parseItemPromiseDeliveryTime(itemService, shopServicesDO);
                    // 有设置延迟发货,则发货相关服务承诺不返回
                    if (hasDelayedDelivery) {
                        continue;
                    }
                    // 春节打烊，则过滤发货相关服务信息中除了30天延迟发货的
                    if (ServiceDetailEnum.FH_470.getDetailId() != itemService.getServiceDetailId() && inSpringAndShutDown) {
                        continue;
                    }
                }
                //详情页保税仓商品（1498标）和海外跨境商品（cbInfo）不展示"发货后不支持退货"
                Map<String, String> features = item.getFeatures();
                if (ServiceDetailEnum.FHBZCTH_1510.getDetailId() == itemService.getServiceDetailId()
                        && (Tools.isBondedItem(context) || (features != null && features.containsKey("cbInfo")))) {
                    continue;
                }
                //普通标
                ShopService shopService = new ShopService();
                shopService.setName(itemService.getServiceDetailTitle());
                shopService.setDesc(itemService.getServiceDetailContent());
                shopService.setIcon(ImageUtil.img(itemService.getUrl()));
                shopService.setLink(itemService.getLink());
                shopService.setServiceDetailId(itemService.getServiceDetailId());
                shopService.setServiceHeadId(itemService.getServiceHeadId());
                shopServices.put(itemService.getServiceDetailId(), shopService);
                // 良品标
                ShopService goodShopService = new ShopService();
                goodShopService.setName(itemService.getServiceDetailTitle());
                goodShopService.setDesc(itemService.getServiceDetailContent());
                goodShopService.setServiceHeadId(itemService.getServiceHeadId());
                goodShopService.setServiceDetailId(itemService.getServiceDetailId());
                goodShopServices.put(itemService.getServiceDetailId(), goodShopService);
                if (ServiceDetailEnum.TH_110.getHeadId() == itemService.getServiceHeadId()) {
                    withRefoundTag = true;
                }
            }

            // 良品标
            if (MapUtils.isNotEmpty(goodShopServices)) {
                ResultSupport<Map<Long, IconUrl>> iconUrlResult = platformIconUrlService.getIconByMultiDetailAndType(goodShopServices.keySet(), 9);
                // 良品标会过滤一部分新版app不需要的标,iconUrl为空
                if (iconUrlResult != null && MapUtils.isNotEmpty(iconUrlResult.getModule())) {
                    Iterator<Map.Entry<Long, ShopService>> iterator = goodShopServices.entrySet().iterator();
                    while (iterator.hasNext()) {
                        Map.Entry<Long, ShopService> entry = iterator.next();
                        IconUrl iconUrl = iconUrlResult.getModule().get(entry.getKey());
                        if (null != iconUrl) {
                            ShopService service = entry.getValue();
                            service.setIcon(ImageUtil.img(iconUrl.getUrl()));
                            service.setLink(iconUrl.getFaqUrl());
                        } else {
                            iterator.remove();
                        }
                    }
                }
            }

            List<ShopService> services = new ArrayList<>(shopServices.values());
            if (!withRefoundTag && commonSwitchUtil.isOn(SwitchKey.NO_REASON_REFOUND)) {
                ShopService shopService = new ShopService();
                shopService.setIcon(ImageUtil.img(NO_SUPPORT_NO_REASON_REFOUND_ICON));
                String text = metabaseClient.get("noRefoundText");
                shopService.setName(StringUtils.isBlank(text) ? "不支持无理由退货" : text);
                if (commonSwitchUtil.isOn(SwitchKey.NO_REASON_REFOUND_FIRST)) {
                    services.add(0, shopService);
                } else {
                    services.add(shopService);
                }
            }
            shopServicesDO.setNormalServices(services);
            shopServicesDO.setGoodItemServices(new ArrayList<>(goodShopServices.values()));
            return shopServicesDO;
        } catch (Exception e) {
            LOGGER.error("get itemServiceService error!", e);
            return null;
        }
    }

    /**
     * 构建来自交易快照的服务体系列表
     *
     * @param context
     * @param item
     * @return
     */
    private List<ShopService> buildServicesFromOrderSnap(DetailContext context, DetailItemDO item) {
        List<ShopService> services = Lists.newArrayList();
        Map<String, Map> serviceMap = (null != context.getContext("service")) ? (Map<String, Map>) context.getContext("service") : null;
        if (null == serviceMap) {
            return services;
        }
        try {
            Boolean isHide610 = null != context.getContext("isHide610") ? (boolean) context.getContext("isHide610") : false;
            Map<Long, ShopService> shopServices = new HashMap<>(serviceMap.size());
            for (Map.Entry<String, Map> entry : serviceMap.entrySet()) {
                if ("610".equals(entry.getKey())) {
                    if (item.getVerticalMarket() == 9 || isHide610) {
                        continue;
                    }
                    Map<String, Object> parameter = gson.fromJson(entry.getValue().get("parameter").toString(), Map.class);
                    Object subscribe = parameter.get("subscribe");
                    if (null != subscribe && subscribe.equals(false)) {
                        continue;
                    }
                }
                Long detailId = Long.parseLong(entry.getKey());
                ShopService shopService = new ShopService();
                shopService.setName(entry.getValue().get("serviceDetailTitle").toString());
                ResultSupport<IconUrl> iconUrlResultSupport = platformIconUrlService.getIconByDetailAndType(detailId, 0);
                if (iconUrlResultSupport != null && iconUrlResultSupport.getModule() != null) {
                    shopService.setIcon(ImageUtil.img(iconUrlResultSupport.getModule().getUrl()));
                    shopService.setLink(iconUrlResultSupport.getModule().getFaqUrl());
                }
                shopServices.put(detailId, shopService);
            }
            services.addAll(shopServices.values());
            return services;
        } catch (Exception e) {
            LOGGER.error("get itemServiceService error!", e);
            return services;
        }
    }

    /**
     * 是否有设置延迟发货
     *
     * @param itemSkuDO sku信息
     */
    private boolean hasDelayDelivery(ItemSkuDO itemSkuDO) {
        //v2版本的延迟发货设置判断
        if (null != SkuUtil.getDelayDeliveryPeriod(itemSkuDO.getExtra())) {
            return true;
        }
        //v1版本的延迟发货设置判断
        if (null != SkuUtil.getDelayedDeliveryTimeWithoutNewYear(itemSkuDO)) {
            return true;
        }
        return false;
    }

    /**
     * 根据商品发货的服务体系解析出具体的发货时长
     *
     * @param itemService    发货相关的服务体系
     * @param shopServicesDO 服务体系封装对象
     */
    private void parseItemPromiseDeliveryTime(ItemServiceDetail itemService, ShopServicesDO shopServicesDO) {
        Long serviceDetailId = itemService.getServiceDetailId();
        ServiceDetailEnum detailEnum = ServiceDetailEnum.getByDetailId(serviceDetailId);
        Integer time = null;
        switch (detailEnum) {
            case FH_410:
                time = 24 * 60 * 60;
                break;
            case FH_420:
                time = 36 * 60 * 60;
                break;
            case FH_430:
                time = 72 * 60 * 60;
                break;
            case FH_448:
                time = 48 * 60 * 60;
                break;
            case FH_435:
                time = 5 * 24 * 60 * 60;
                break;
            case FH_440:
                time = 7 * 24 * 60 * 60;
                break;
            case FH_450:
                time = 10 * 24 * 60 * 60;
                break;
            case FH_460:
                time = 15 * 24 * 60 * 60;
                break;
            case FH_465:
                time = 20 * 24 * 60 * 60;
                break;
            case FH_470:
                time = 30 * 24 * 60 * 60;
            default:
                break;
        }
        if (time != null) {
            shopServicesDO.setItemPromiseDeliveryTime(time);
        }
    }
}
