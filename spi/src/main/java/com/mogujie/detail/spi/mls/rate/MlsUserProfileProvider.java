package com.mogujie.detail.spi.mls.rate;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.spi.BizSpi;
import com.mogujie.detail.module.rate.spi.IUserProfileProvider;

/**
 * Created by <PERSON><PERSON>oya<PERSON> on 16/5/19.
 */
@BizSpi(app = App.MLS)
public class MlsUserProfileProvider implements IUserProfileProvider {

    private static final String ANONYMOUS_IMG = "/b7/pic/160530/1idj40_ifrgimzxmfrtan3ehazdambqmeyde_140x140.jpg";

    private static final String AVATARS[] = {
            "/p2/161214/103488673_527fecjlbb7f05fihi1aggde73jb8_140x140.png",
            "/p2/161214/103488673_65bf62g5e98f4g9b8dk1j5le13e34_140x140.png",
            "/p2/161214/103488673_0l1ff9kf2hbc32fe4a2g05l77d89d_140x140.png",
            "/p2/161214/103488673_825c06kdj394515kkkjhhi0el8j86_140x141.png",
            "/p2/161214/103488673_8ff131b8g07810e345268cgcda6ef_140x141.png",
            "/p2/161214/103488673_8da6lhkf9a5h32ei6bd297523c35d_140x140.png"};

    @Override
    public String getProfilePrefix(DetailContext context) {
        return "mls://mezone?user_type=mogujie&uid=";
    }

    @Override
    public String getAnonymousImg(DetailContext context, long userId) {
        return AVATARS[(int) (userId % 6)];
    }
}
