package com.mogujie.detail.spi.mgj.spout.channel;

import com.google.gson.Gson;
import com.mogujie.detail.core.adt.ChannelMeta;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.Platform;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.spi.BizSpi;
import com.mogujie.detail.core.task.AbstractCollectDataTask;
import com.mogujie.detail.module.spout.spi.ICommonDataProvider;
import com.mogujie.detail.spi.mgj.spout.task.CollectChannelPromotionInfoTask;
import com.mogujie.detail.spi.mgj.spout.task.CollectShopInfoTask;
import com.mogujie.metabase.spring.client.MetabaseClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by anshi on 18/1/15.
 */
@BizSpi(app = App.MGJ, platform = Platform.APP, bizType = BizType.CHANNEL)
public class MgjAppChannelCommonDataProvider implements ICommonDataProvider {

    @Override
    public List<AbstractCollectDataTask> listStaticCollectDataTask(DetailContext context) throws DetailException {
        ChannelMeta channelMeta = context.getChannelMeta();
        List<AbstractCollectDataTask> tasks = new ArrayList<>(2);
        tasks.add(new CollectChannelPromotionInfoTask(context, channelMeta.getChannelId(), channelMeta.getOutType()));
        tasks.add(new CollectShopInfoTask(context));
        return tasks;
    }

    @Override
    public List<AbstractCollectDataTask> listDynCollectDataTask(DetailContext context) throws DetailException {
        ChannelMeta channelMeta = context.getChannelMeta();
        List<AbstractCollectDataTask> tasks = new ArrayList<>(2);
        tasks.add(new CollectChannelPromotionInfoTask(context, channelMeta.getChannelId(), channelMeta.getOutType()));
        tasks.add(new CollectShopInfoTask(context));
        return tasks;
    }
}
