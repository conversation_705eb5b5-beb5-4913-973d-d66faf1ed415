package com.mogujie.detail.spi.mls.shop;

import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.Platform;
import com.mogujie.detail.core.spi.BizSpi;
import com.mogujie.detail.module.shop.spi.IShopUrlProvider;
import com.mogujie.metabase.spring.client.MetabaseClient;

import javax.annotation.Resource;

/**
 * Created by xiaoyao on 16/4/10.
 */
@BizSpi(app = App.MLS, platform = Platform.WX)
public class MlsWxShopUrlProvider implements IShopUrlProvider {

    @Resource(name = "confMetabaseClient")
    protected MetabaseClient metabaseClient;

    @Override
    public String getShopUrl(DetailContext context) {
        if (useHttps()) {
            return "//weixin.meilishuo.com/wx/shop/" + IdConvertor.idToUrl(context.getItemDO().getShopId());
        } else {
            return "http://weixin.meilishuo.com/wx/shop/" + IdConvertor.idToUrl(context.getItemDO().getShopId());
        }
    }

    @Override
    public String getShopAllGoodsUrl(DetailContext context) {
        return "";
    }

    private boolean useHttps() {
        try {
            Boolean useLocal = metabaseClient.getBoolean("use_https");
            if (null != useLocal) {
                return useLocal;
            }
        } catch (Exception e) {
            ;
        }
        return false;
    }
}
