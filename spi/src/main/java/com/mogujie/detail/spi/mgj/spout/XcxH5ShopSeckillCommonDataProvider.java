package com.mogujie.detail.spi.mgj.spout;

import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.Platform;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.spi.BizSpi;
import com.mogujie.detail.core.task.AbstractCollectDataTask;
import com.mogujie.detail.module.shopSeckill.domain.SkTagInfo;
import com.mogujie.detail.module.spout.spi.ICommonDataProvider;
import com.mogujie.detail.spi.mgj.spout.task.CollectChannelPromotionInfoTask;
import com.mogujie.detail.spi.mgj.spout.task.CollectShopInfoTask;
import com.mogujie.service.tagcenter.api.read.ItemTagReadService;
import com.mogujie.service.tagcenter.domain.entity.query.ItemTagQueryOption;
import com.mogujie.service.tagcenter.domain.entity.result.BaseResultDO;
import com.mogujie.service.tagcenter.domain.entity.result.ItemTagDO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by anshi on 17/12/19.
 */
@BizSpi(app = App.XCX, platform = Platform.H5, bizType = BizType.SHOPSECKILL)
public class XcxH5ShopSeckillCommonDataProvider implements ICommonDataProvider {

    private ItemTagReadService itemTagReadService;

    private static final Logger LOGGER = LoggerFactory.getLogger(XcxH5ShopSeckillCommonDataProvider.class);

    @PostConstruct
    public void init() {
        try {
            itemTagReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ItemTagReadService.class);
        } catch (Exception e) {
            LOGGER.error("init itemTagReadService failed");
        }
    }

    @Override
    public List<AbstractCollectDataTask> listStaticCollectDataTask(DetailContext context) throws DetailException {
        List<AbstractCollectDataTask> tasks = new ArrayList<>(2);
        try {
            String activityIdStr = context.getParam("activityId");
            if (!StringUtils.isEmpty(activityIdStr)) {
                Long activityId = IdConvertor.urlToId(activityIdStr);
                ItemTagQueryOption option = new ItemTagQueryOption();
                option.setItemId(context.getItemId());
                option.setTagKey("sk");
                option.setIncludedFutureTags(true);
                BaseResultDO<List<ItemTagDO>> ret = itemTagReadService.queryItemTag(option);
                if (null != ret && ret.isSuccess() && CollectionUtils.isNotEmpty(ret.getResult())) {
                    for (ItemTagDO tag : ret.getResult()) {
                        if ("sk".equals(tag.getTagKey())) {
                            SkTagInfo skTagInfo = getSkTagInfo(tag.getTagValue());
                            if (skTagInfo != null && skTagInfo.getActivityId().equals(activityId)) {
                                context.addContext("shopSeckillInfo", skTagInfo);
                                break;
                            }
                        }
                    }
                }
            }
            tasks.add(new CollectChannelPromotionInfoTask(context, 2021, (short) 10));
            tasks.add(new CollectShopInfoTask(context));
        } catch (Exception e) {
            throw new DetailException(e);
        }
        return tasks;
    }

    @Override
    public List<AbstractCollectDataTask> listDynCollectDataTask(DetailContext context) throws DetailException {
        List<AbstractCollectDataTask> tasks = new ArrayList<>(2);
        try {
            String activityIdStr = context.getParam("activityId");
            if (!StringUtils.isEmpty(activityIdStr)) {
                Long activityId = IdConvertor.urlToId(activityIdStr);
                ItemTagQueryOption option = new ItemTagQueryOption();
                option.setItemId(context.getItemId());
                option.setTagKey("sk");
                option.setIncludedFutureTags(true);
                BaseResultDO<List<ItemTagDO>> ret = itemTagReadService.queryItemTag(option);
                if (null != ret && ret.isSuccess() && CollectionUtils.isNotEmpty(ret.getResult())) {
                    for (ItemTagDO tag : ret.getResult()) {
                        if ("sk".equals(tag.getTagKey())) {
                            SkTagInfo skTagInfo = getSkTagInfo(tag.getTagValue());
                            if (skTagInfo != null && skTagInfo.getActivityId().equals(activityId)) {
                                context.addContext("shopSeckillInfo", skTagInfo);
                            }
                            break;
                        }
                    }
                }
            }
            tasks.add(new CollectChannelPromotionInfoTask(context, 2021, (short) 10));
            tasks.add(new CollectShopInfoTask(context));
        }catch (Exception e){
            throw new DetailException(e);
        }
        return tasks;
    }

    private SkTagInfo getSkTagInfo(String tagValue) {
        if (StringUtils.isEmpty(tagValue)) {
            return null;
        }
        Integer startTime = null, endTime = null, price = null;
        Long activityId = null;
        String[] tagPairs = tagValue.split(";");
        for (String tagPair : tagPairs) {
            String[] sk = tagPair.split(":");
            if (sk.length != 2) {
                continue;
            }
            if ("st".equals(sk[0])) {
                startTime = Integer.parseInt(sk[1]);
            } else if ("et".equals(sk[0])) {
                endTime = Integer.parseInt(sk[1]);
            } else if ("rp".equals(sk[0])) {
                price = Integer.parseInt(sk[1]);
            } else if ("aid".equals(sk[0])) {
                activityId = Long.parseLong(sk[1]);
            }
        }
        if (startTime != null && endTime != null && price != null && activityId != null) {
            SkTagInfo skTagInfo = new SkTagInfo();
            skTagInfo.setStartTime(startTime);
            skTagInfo.setEndTime(endTime);
            skTagInfo.setActivityId(activityId);
            skTagInfo.setPrice(price);
            return skTagInfo;
        } else {
            return null;
        }
    }
}