package com.mogujie.detail.spi.mgj.iteminfo;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.Platform;
import com.mogujie.detail.core.util.ContextUtil;
import com.mogujie.detail.module.itemBase.spi.IItemStateProvider;
import com.mogujie.detail.spi.dslutils.Tools;
import com.mogujie.metabase.spring.client.MetabaseClient;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 16/4/27.
 */
public class BaseItemStateProvider implements IItemStateProvider {
    private final static Logger LOGGER = LoggerFactory.getLogger(IItemStateProvider.class);

    @Resource(name = "confMetabaseClient")
    private MetabaseClient metabaseClient;

    /**
     * 0: 正常售卖
     * 1: 下架
     * 2: 库存不足
     * 3: 待开售
     *
     * @param context
     * @return
     */
    public int getItemState(DetailContext context) {
        int state = 0;
        try {
            if (ContextUtil.offlineItemWithContext(context)) {
                return 1;
            }
            DetailItemDO item = context.getItemDO();
            int isShelf = item.getIsShelf();
            long totalStock = item.getTotalStock() == null ? 0 : item.getTotalStock();
//        int goodsType = item.getItemType()==null?0:item.getItemType();
            String virtualCateRoot = "#" + metabaseClient.get("virtual_category_root") + "#";
            boolean isMedicalBeautyItem = Tools.isMedicalBeautyItem(context);
            List<String> virtualCates = new ArrayList<>();
            try {
                String virtualCategotryTypes = metabaseClient.get("virtual_categories");
                String[] virtualCateIds = virtualCategotryTypes.split(",");
                for (String cateId : virtualCateIds) {
                    virtualCates.add(cateId.split(":")[0]);
                }
            } catch (Exception e) {
                LOGGER.error("get virtual categories failed");
            }
            if (null != item.getStarts()) {
                long nowTime = System.currentTimeMillis();
                if (nowTime < item.getStarts().getTime()) {
                    return 3;
                }
            }
            //是否为虚拟充值商品（话费、流量、Q币）
            boolean virtualPay = false;
            for (String catId : virtualCates) {
                if (item.getCids().contains("#" + catId + "#")) {
                    virtualPay = true;
                    break;
                }
            }
            //app版本号
            Integer version = null;
            if (StringUtils.isNotBlank(context.getParam("_av"))) {
                try {
                    version = Integer.parseInt(context.getParam("_av"));
                } catch (Throwable e) {
                }
            }
            boolean inWhiteList = false;
            String[] userIds = metabaseClient.get("virtualOrderGraySwitch").split(",");
            for (String userId : userIds) {
                if (context.getLoginUserId() != null && context.getLoginUserId() == Long.parseLong(userId)) {
                    inWhiteList = true;
                    break;
                }
            }
            //总开关为false、不在白名单、且为虚拟商品||医疗美容商品，一律处理为下架
            if (!"true".equalsIgnoreCase(metabaseClient.get("virtualOrderSwitch"))
                    && !inWhiteList
                    && item.getCids().contains(virtualCateRoot)) {
                state = 1;
            }
            //老版本app，充值中心类目，需要显示为下架
            else if (context.getRouteInfo().getPlatform() == Platform.APP
                    && item.getCids().contains(virtualCateRoot)
                    && version != null && version < 1020) {
                state = 1;
            } else if (context.getRouteInfo().getPlatform() == Platform.APP
                    && isMedicalBeautyItem
                    && version != null && version < 1030) {
                //老版本app，医疗美容类目，需要显示为下架
                state = 1;
            } else if (item.getCids().contains(virtualCateRoot)
                    && ((context.getRouteInfo().getApp() != App.MGJ && context.getRouteInfo().getApp() != App.XCX)
                    || (!ContextUtil.isNormalBizDetail(context) && context.getRouteInfo().getBizType() != BizType.PINTUAN && context.getRouteInfo().getBizType() != BizType.JIAJIAGOU))) {
                // 只有蘑菇街、女装小程序的普通、拼团、加价购详情页才可以下单，其他场景显示为下架
                state = 1;
            } else if ((isMedicalBeautyItem)
                    && ((context.getRouteInfo().getApp() != App.MGJ && context.getRouteInfo().getApp() != App.XCX)
                    || (!ContextUtil.isNormalBizDetail(context) && context.getRouteInfo().getBizType() != BizType.PINTUAN && context.getRouteInfo().getBizType() != BizType.JIAJIAGOU && context.getRouteInfo().getBizType() != BizType.FASTBUY && !"live".equals(context.getRouteInfo().getChannelType())))) {
                // 只有蘑菇街、女装小程序的普通、拼团、加价购详情页才可以下单，其他场景显示为下架
                state = 1;
            } else if (isMedicalBeautyItem
                    && (context.getRouteInfo().getApp() == App.XCX || context.getRouteInfo().getPlatform() == Platform.XCX)
                    && StringUtils.isBlank(context.getParam("smbi"))) {
                // 医美商品、小程序详情页，但是没有传smbi参数（只有最新版本小程序才会传），需要显示为下架
                state = 1;
            } else if (item.getCids().contains(virtualCateRoot)
                    && !virtualPay) {
                //充值中心类目，但并非话费、流量、Q币，显示为下架
                state = 1;
            } else if (isInvalidOrderItems(context)) {
                state = 1;
            } else if (isShelf == 1/*|| goodsType == 1*/) {
                state = 1;
            } else if (totalStock == 0) {
                state = 2;
            }
        } catch (Throwable e) {
        }
        return state;
    }

    private boolean isInvalidOrderItems(DetailContext context) {
        return Arrays.asList(metabaseClient.get("invalid_order_items").split(",")).contains(context.getItemId().toString());
    }
}
