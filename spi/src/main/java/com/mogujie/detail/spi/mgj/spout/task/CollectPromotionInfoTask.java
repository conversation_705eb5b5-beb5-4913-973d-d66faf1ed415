package com.mogujie.detail.spi.mgj.spout.task;

import com.alibaba.fastjson.JSON;
import com.meili.service.shopcenter.domain.entity.ShopInfo;
import com.mogujie.commons.utils.EnvUtil;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.darwin.util.CollectionUtil;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.OtherPrice;
import com.mogujie.detail.core.adt.PromotionDecorate;
import com.mogujie.detail.core.adt.RouteInfo;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.ContextKeys;
import com.mogujie.detail.core.constant.SwitchKey;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.task.AbstractCollectDataTask;
import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.core.util.ContextUtil;
import com.mogujie.detail.core.util.NumUtil;
import com.mogujie.detail.core.util.TagUtil;
import com.mogujie.detail.module.detail.util.CategoryTypeUtil;
import com.mogujie.marketing.ferrari.api.RushInfoForDetailService;
import com.mogujie.marketing.ferrari.api.dto.RushInfoDTO;
import com.mogujie.marketing.ferrari.api.dto.RushInfoResultDTO;
import com.mogujie.marketing.ferrari.api.request.RushInfoRequest;
import com.mogujie.metabase.spring.client.MetabaseClient;
import com.mogujie.service.hummer.api.PromotionReadService;
import com.mogujie.service.hummer.constains.PromotionConstants;
import com.mogujie.service.hummer.constains.RequestConstants;
import com.mogujie.service.hummer.domain.dto.BuyerItemPrice;
import com.mogujie.service.hummer.domain.dto.CampaignInfo;
import com.mogujie.service.hummer.domain.dto.InvokeInfo;
import com.mogujie.service.hummer.domain.dto.ItemDetailPromotion;
import com.mogujie.service.hummer.domain.dto.ItemDetailRequestV2;
import com.mogujie.service.hummer.domain.dto.ItemOtherPrice;
import com.mogujie.service.hummer.domain.dto.Pbuyer;
import com.mogujie.service.hummer.domain.dto.PitemDetail;
import com.mogujie.service.hummer.domain.dto.Pseller;
import com.mogujie.service.hummer.domain.dto.result.Result;
import com.mogujie.service.hummer.utils.InvokeExtraUtil;
import com.mogujie.service.hummer.utils.PromotionConvertUtils;
import com.mogujie.service.hummer.utils.SystemDiscountChecker;
import com.mogujie.service.inventory.api.InventoryReadService;
import com.mogujie.service.inventory.domain.ActivityInventoryV2;
import com.mogujie.service.inventory.domain.param.BatchActivityInventoryQueryParamV2;
import com.mogujie.service.inventory.domain.result.MapResult;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.service.tagcenter.api.read.ShopTagReadService;
import com.mogujie.service.tagcenter.domain.entity.query.ShopTagQueryOption;
import com.mogujie.service.tagcenter.domain.entity.result.BaseResultDO;
import com.mogujie.service.tagcenter.domain.entity.result.ShopTagDO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import com.mogujie.tesla.core.ReferConfig;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by xiaoyao on 16/10/20.
 */
public class CollectPromotionInfoTask extends AbstractCollectDataTask {

    private static final Logger LOGGER = LoggerFactory.getLogger(CollectPromotionInfoTask.class);

    private RushInfoForDetailService rushInfoForDetailService;

    private InventoryReadService inventoryReadService;

    private PromotionReadService promotionReadService;

    private ShopTagReadService shopTagReadService;

    private String fastbuyId;

    private MetabaseClient metabaseClient;

    private CommonSwitchUtil commonSwitchUtil;

    //1350班车版本
    private String APP_VERSION = "app_version";

    //女装直播小程序appkey，属于微信小程序
    private static List<String> wxAppkey = new ArrayList<>();

    //抖音/头条直播小程序appkey，
    private static List<String> ttAppkey = new ArrayList<>();

    static {
        //女装
        wxAppkey.add("100063");
        //直播
        wxAppkey.add("100060");

        //抖音小程序
        ttAppkey.add("100196");
        //头条小程序
        ttAppkey.add("100195");

    }


    public CollectPromotionInfoTask(DetailContext detailContext, MetabaseClient metabaseClient, CommonSwitchUtil commonSwitchUtil) throws DetailException {
        super(detailContext);
        this.commonSwitchUtil = commonSwitchUtil;
        //sku选择器中的快抢参数为activityId
        if (detailContext.getRouteInfo().getBizType() == BizType.SKU
                && detailContext.getRouteInfo().getVersion().startsWith("live.fastbuy")) {
            this.fastbuyId = IdConvertor.urlToId(context.getParam("activityId")).toString();
        } else {
            this.fastbuyId = context.getParam("fastbuyId");
        }


        this.metabaseClient = metabaseClient;
        try {
            this.rushInfoForDetailService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(RushInfoForDetailService.class);
            this.inventoryReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(InventoryReadService.class);
            this.shopTagReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ShopTagReadService.class);
            ReferConfig<PromotionReadService> referConfig = new ReferConfig<>(PromotionReadService.class);
            if (EnvUtil.isOnlineEnv()) {
                referConfig.setGroup("DETAIL-GROUP");
            }
            referConfig.setTimeout(200);
            this.promotionReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(referConfig);
        } catch (Exception e) {
            throw new DetailException(e);
        }
    }

    @Override
    public void collect() {
        RushInfoDTO rushInfo = null;
        Long fastId = !StringUtils.isNumeric(fastbuyId) ? null : Long.parseLong(fastbuyId);
        if (null != fastId && commonSwitchUtil.isOn(SwitchKey.FERRARI_GET_RUSH_INFO)) {
            RushInfoRequest rushInfoRequest = RushInfoRequest.of(fastId, context.getLoginUserId());
            RushInfoResultDTO rushRet = rushInfoForDetailService.getRushInfoByUser(rushInfoRequest);
            if (null != rushRet && rushRet.isSucc()) {
                rushInfo = rushRet.getRushInfoDTO();
            }
            context.addContext("fastbuyInfo", rushInfo);
            decorateFastbuySku(context.getItemDO(), fastId);
        }
        decorateSkuAndPrice(this.context, rushInfo);
    }

    private void decorateFastbuySku(ItemDO item, Long fastbuyId) {
        List<Long> skuIdList = new ArrayList<>(item.getItemSkuDOList().size());
        for (ItemSkuDO sku : item.getItemSkuDOList()) {
            skuIdList.add(sku.getSkuId());
        }
        try {
            BatchActivityInventoryQueryParamV2 param = new BatchActivityInventoryQueryParamV2();
            param.setSkuIds(skuIdList);
            param.setChannelId(1);
            param.setActivityId(fastbuyId);
            MapResult<Long, ActivityInventoryV2> inventoryMapResult = inventoryReadService.batchQueryActivityInventoryV3(param);
            if (null != inventoryMapResult && inventoryMapResult.isSuccess()) {
                Map<Long, ActivityInventoryV2> inventoryMap = inventoryMapResult.getData();
                if (null != inventoryMap) {
                    for (ItemSkuDO sku : item.getItemSkuDOList()) {
                        ActivityInventoryV2 inventory = inventoryMap.get(sku.getSkuId());
                        sku.setQuantity(null == inventory ? 0 : inventory.getStock());
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("get fastbuy inventory failed : {}", e.getMessage());
        }
    }

    private boolean useLocalDiscount() {
        try {
            Boolean useLocal = metabaseClient.getBoolean("discount_useLocal");
            if (null != useLocal) {
                return useLocal;
            }
        } catch (Exception e) {
            ;
        }
        return true;
    }

    private Map<Long, Long> getSkuPriceMap(ItemDO item) {
        Map<Long, Long> skuPriceMap = new HashMap<>();
        for (ItemSkuDO sku : item.getItemSkuDOList()) {
            skuPriceMap.put(sku.getSkuId(), sku.getPrice().longValue());
        }
        return skuPriceMap;
    }

    public void decorateSkuAndPrice(DetailContext context, RushInfoDTO rushInfo) {
        DetailItemDO item = context.getItemDO();
        try {
            Map<Long, Long> originSkuMap = getSkuPriceMap(item);
            Map<Long, Long> realSkuMap = null;
            Long realPrice = null;
            String decorate = null;
            boolean fastbuy = false;
            int current = (int) (System.currentTimeMillis() / 1000);
            if (null != rushInfo && current <= rushInfo.getEndTime()) {
                realSkuMap = new HashMap<>(item.getItemSkuDOList().size());
                for (ItemSkuDO sku : item.getItemSkuDOList()) {
                    Long rp = rushInfo.getActivityDiscount() != 1000 ?
                            Long.valueOf((new BigDecimal(sku.getPrice()).multiply(new BigDecimal(rushInfo.getActivityDiscount())).divide(new BigDecimal(1000), RoundingMode.DOWN).longValue())) : rushInfo.getSalePrice();
                    realSkuMap.put(sku.getSkuId(), rp);
                }
                realPrice = (long) rushInfo.getSalePrice();
                fastbuy = metabaseClient.getBoolean("fastbuy_show_discountprice");
            } else if (!TagUtil.isFlItem(item.getJsonExtra()) && useLocalDiscount()) {
                SystemDiscountChecker.SystemDiscountRes ret = SystemDiscountChecker.calcSystemDiscount(item.getJsonExtra(), item.getReservePrice(), false, (int) RequestConstants.Market.MOGUJIE, originSkuMap);
                if (null != ret && !ContextUtil.isLiveInWallItem(context)) { //如果不是特殊商品，也不判断
                    realSkuMap = ret.getSkuRalPrice();
                    realPrice = ret.getRealPrice();
                    decorate = ret.getName();
                }
            }

            if (null == realSkuMap || null == realPrice || fastbuy) {
                //app版本号
                Integer version = null;
                if (StringUtils.isNotBlank(context.getParam("_av"))) {

                    version = Integer.parseInt(context.getParam("_av"));

                }
                ItemDetailRequestV2 request = new ItemDetailRequestV2();
                Pbuyer pbuyer = new Pbuyer();
                pbuyer.setBuyerId(context.getLoginUserId());
                Pseller pSeller = new Pseller();
                pSeller.setSellerId(item.getUserId());
                PitemDetail pitemDetail = new PitemDetail();
                pitemDetail.setExtra(item.getJsonExtra());
                pitemDetail.setItemId(item.getItemId());
                pitemDetail.setSkuPriceMap(originSkuMap);
                //1350班车详情页领券需要用sku最低价格去计算sku优惠价格，之前是为什么用最高价不知道原因，
                // 因此加个开关，有问题通过开关走原来逻辑
                if (commonSwitchUtil.isOn(SwitchKey.SWT_LOW_SKU_PRICE)) {
                    pitemDetail.setItemPrice(getLowPrice(item.getItemSkuDOList()));

                } else {
                    pitemDetail.setItemPrice(getHighestPrice(item.getItemSkuDOList()));
                }
                //如果是直播商品进图强的商品，要单独传参数。
                if (ContextUtil.isLiveInWallItem(context)) {
                    pitemDetail.setBuySource(RequestConstants.BuySource.LIVE_HUAYRA);
                }

                String skuNum = context.getParam("skuNum");
                if (StringUtils.isNotBlank(skuNum)) {
                    pitemDetail.setNumber(Long.parseLong(skuNum));
                } else {
                    pitemDetail.setNumber(1L);
                }
                pitemDetail.setItemTagList(PromotionConvertUtils.convertItemTagToItemProTag(context.getItemDO().getItemTags()));
                //支持品类券
                CategoryTypeUtil util = new CategoryTypeUtil(item.getCids());
                List<String> cids = util.cids().stream().map(String::valueOf).collect(Collectors.toList());
                pitemDetail.setCids(cids);

                InvokeInfo invokeInfo = new InvokeInfo();
                invokeInfo.setChannel((int) RequestConstants.Channel.UNKNOW);
                if (fastbuy) {
                    invokeInfo.setChannel(RequestConstants.Channel.FASTBUY);
                    invokeInfo.setOutType(RequestConstants.OutType.FASTBUY);
                    invokeInfo.setOutId(fastbuyId);
                    invokeInfo.setNowTime((long) (Math.max(current, rushInfo.getStartTime())));
                }
                invokeInfo.setMarket(ContextUtil.getMarketByContext(context));
                RouteInfo routeInfo = context.getRouteInfo();
                //在直播间里看到的sku不用计算优惠的逻辑。从直播间跳过来的商品详情页要计算优惠的逻辑
                if ((routeInfo.getBizType() == BizType.SKU && routeInfo.getVersion().startsWith("live.")/* 直播sku接口 */)) {
                    invokeInfo.setSource(RequestConstants.Source.LIVE_DETAIL);

                }//直播间跳过来的商品详情是普通详情页
                else if ("live".equals(context.getParam("from"))) {
                    invokeInfo.setSource(RequestConstants.Source.LIVE_DETAIL);
                    InvokeExtraUtil.setDetailPromotionPrice(invokeInfo);
                    this.appendShopInfo(request, item);
                } else {
                    //1350班车开始普通商详需要传RequestConstants.Source.DETAIL_PARALLEL,要计算优惠的逻辑,
                    if (version != null && useNewSourceByAppVersion(version) && routeInfo.getBizType() != BizType.SKU) {
                        invokeInfo.setSource(RequestConstants.Source.DETAIL_PARALLEL);
                    } else {
                        //从购物车过来的传RequestConstants.Source.DETAIL，不要计算优惠的逻辑
                        invokeInfo.setSource(RequestConstants.Source.DETAIL);
                    }
                    this.appendShopInfo(request, item);
                }
                int terminal = RequestConstants.Terminal.APP;
                switch (context.getRouteInfo().getPlatform()) {
                    case PC:
                        terminal = RequestConstants.Terminal.PC;
                        break;
                    default:
                        terminal = RequestConstants.Terminal.APP;
                        break;
                }
                //100063 女装小程序 100060 直播小程序 100196 抖音小程序  100195 头条小程序
                String appkey = context.getParam("appkey");
                if (wxAppkey.contains(appkey)) {
                    terminal = RequestConstants.Terminal.WX;
                    invokeInfo.setClientName(RequestConstants.ClientName.WX_MGJ_MIN_PROGRAM);
                } else if (ttAppkey.contains(appkey)) {
                    terminal = RequestConstants.Terminal.UNKNOW;
                    invokeInfo.setClientName(RequestConstants.ClientName.BYTE_BEATING);
                }

                invokeInfo.setTerminal(terminal);
                request.setPitemDetail(pitemDetail);
                request.setSeller(pSeller);
                request.setPbuyer(pbuyer);
                request.setInvokeInfo(invokeInfo);

                if (metabaseClient.getBoolean("swt_cal_promotion")) {
                    String routeInfoStr = String.format("RouteInfo{app=%s, platform=%s, bizType=%s, channel=%s, version=%s}",
                            routeInfo.getApp().name(), routeInfo.getPlatform().name(), routeInfo.getBizType().name(),
                            routeInfo.getChannelType(), routeInfo.getVersion());
                    LOGGER.info("swt_cal_promotion request:{}, context:{}, routeInfo:{}, version:{}",
                            JSON.toJSONString(request), JSON.toJSONString(context), routeInfoStr, version);
                }

                Result<ItemDetailPromotion> ret = promotionReadService.calcForItemDetailPromotion(request);
                if (metabaseClient.getBoolean("swt_cal_promotion")) {
                    LOGGER.info("swt_cal_promotion ret:{}", JSON.toJSONString(ret));
                }

                if (null != ret && ret.isSuccess() && null != ret.getData()) {
                    ItemDetailPromotion itemDetailPromotion = ret.getData();
                    CampaignInfo campaignInfo = itemDetailPromotion.getCampaigninfo();
                    if (null != campaignInfo) {
                        Map<String, String> parameter = campaignInfo.getParameter();
                        if (null != parameter && !parameter.isEmpty()) {
                            String limitNum = parameter.get("itemCountLimit");
                            if (!StringUtils.isBlank(limitNum)) {
                                item.setLimitNum(Integer.parseInt(limitNum));
                            }
                        }
                        //bizType为702，且promotionCode为discount，则表示该价格为"直播新人专享价"
                        //具体含义咨询 @桌子
                        if (campaignInfo.getBizType() == 702 && "discount".equals(campaignInfo.getPromotionMark().getPromotionCode())) {
                            context.addContext(ContextKeys.IS_LIVE_NEWCOMER_PRICE, true);
                        }
                    }
                    //设置商品的会员价和普通用户价（目前只有买手店有这个业务）
                    BuyerItemPrice buyerItemPrice = itemDetailPromotion.getBuyerItemPrice();
                    if (buyerItemPrice != null) {
                        item.setMemberPrice(buyerItemPrice.getItemMemberSkuPrice());
                        item.setNormalUserPrice(buyerItemPrice.getItemPromotionSkuPrice());
                    }
                    //其他价格，如'直播分享价'等
                    List<ItemOtherPrice> otherPrices = itemDetailPromotion.getItemOtherPrices();
                    boolean isPresale = ContextUtil.isPresaleItem(item);
                    if (otherPrices != null) {
                        List<OtherPrice> otherPriceList = otherPrices.stream()
                                .map(price -> {
                                    OtherPrice otherPrice = new OtherPrice();
                                    otherPrice.setPriceType(price.getPriceType());
                                    otherPrice.setItemRealPrice(price.getItemRealPrice());
                                    Map<String, Long> skuPriceMap = new HashMap<>();
                                    if (price.getSkuRealPriceMap() != null) {
                                        otherPrice.setMaxPrice(Collections.max(price.getSkuRealPriceMap().values()));
                                        otherPrice.setMinPrice(Collections.min(price.getSkuRealPriceMap().values()));
                                        price.getSkuRealPriceMap().entrySet().stream()
                                                .forEach(entry ->
                                                        skuPriceMap.put(IdConvertor.idToUrl(entry.getKey()), entry.getValue())
                                                );
                                        otherPrice.setSkuRealPriceMap(skuPriceMap);
                                    }
                                    return otherPrice;
                                })
                                //对预售商品，屏蔽直播分享价
                                .filter(otherPrice -> !(isPresale && otherPrice.getPriceType() == 1))
                                .collect(Collectors.toList());
                        item.setOtherPrices(otherPriceList);
                    }
                    realSkuMap = itemDetailPromotion.getSkuRealPriceMap();
                    realPrice = itemDetailPromotion.getItemRealPrice();
                    decorate = itemDetailPromotion.getDecorate();
                    item.setPromotionPrice(itemDetailPromotion.getExpectPayPrice());
                    //返回到手价的明细，这里没有顺序，完全按照促销给的顺序来，只有店铺优惠+平台优惠。
                    item.setPromotionPriceDetail(getPromotionDecorates(itemDetailPromotion.getSkuPromotionDecorates()));
                }
            }
            if (null != realSkuMap && null != realPrice) {
                item.setDiscountDesc(decorate);
                filterSku(item, realSkuMap, realPrice);
            } else {
                item.setDiscountDesc("");
                filterSku(item, originSkuMap, item.getReservePrice().longValue());
            }
        } catch (Throwable e) {
            LOGGER.error("get discount info failed : {}", e);
            filterSku(item, new HashMap<Long, Long>(), null);
        }
    }

    /**
     * 促销计价请求添加店铺相关参数，支持促销店铺标判断。如解决虚拟商品计价不能使用平台优惠券需求。
     *
     * @param request 促销请求对象
     * @param item    原始是商品信息
     */
    private void appendShopInfo(ItemDetailRequestV2 request, DetailItemDO item) {
        ShopInfo shopInfo = item.getShopInfo();
        List<Integer> tagList;
        if (null == shopInfo) {
            // 店铺数据获取任务CollectShopInfoTask未执行完毕，调用标签中心获取店铺标
            ShopTagQueryOption queryOption = new ShopTagQueryOption();
            queryOption.setShopId(item.getShopId());
            queryOption.setTagKey("tags");
            BaseResultDO<List<ShopTagDO>> resultDO = shopTagReadService.queryShopTag(queryOption);
            if (null == resultDO || !resultDO.isSuccess() || CollectionUtils.isEmpty(resultDO.getResult())) {
                return;
            }
            tagList = resultDO.getResult()
                    .stream()
                    .filter(shopTag -> null != shopTag && StringUtils.isNotBlank(shopTag.getTagValue()) && StringUtils.isNumeric(shopTag.getTagValue()))
                    .map(shopTagDO -> Integer.valueOf(shopTagDO.getTagValue()))
                    .distinct()
                    .collect(Collectors.toList());
        } else {
            // 店铺数据获取任务CollectShopInfoTask执行完毕。直接从shopInfo中获取店铺标。
            String tags = shopInfo.getTags();
            if (StringUtils.isBlank(tags)) {
                return;
            }
            tagList = Arrays
                    .stream(tags.split(","))
                    .filter(idStr -> StringUtils.isNotBlank(idStr) && StringUtils.isNumeric(idStr))
                    .map(Integer::new)
                    .distinct()
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(tagList)) {
            return;
        }
        com.mogujie.service.hummer.domain.dto.ShopInfo requestShopInfo = new com.mogujie.service.hummer.domain.dto.ShopInfo();
        requestShopInfo.setShopTagIdList(tagList);
        request.setShopInfo(requestShopInfo);
    }

    private static void filterSku(DetailItemDO item, Map<Long, Long> realPriceMap, Long realPrice) {
        long lowPrice = item.getReservePrice();
        long highPrice = item.getReservePrice();
        long lowNowPrice = null == realPrice ? item.getReservePrice() : realPrice;
        long highNowPrice = null == realPrice ? item.getReservePrice() : realPrice;
        long totalStock = 0;
        for (ItemSkuDO sku : item.getItemSkuDOList()) {

            if (sku.getPrice() < lowPrice) {
                lowPrice = sku.getPrice();
            }

            if (sku.getPrice() > highPrice) {
                highPrice = sku.getPrice();
            }
            Long skuRealPrice = realPriceMap.get(sku.getSkuId());
            if (null == skuRealPrice) {
                skuRealPrice = sku.getPrice().longValue();
            }
            sku.setNowPrice(skuRealPrice.intValue());
            if (skuRealPrice < lowNowPrice) {
                lowNowPrice = skuRealPrice;
            }

            if (skuRealPrice > highNowPrice) {
                highNowPrice = skuRealPrice;
            }

            totalStock += sku.getQuantity();
        }
        item.setLowPrice(NumUtil.formatNum(lowPrice / 100D));
        item.setLowNowPrice(NumUtil.formatNum(lowNowPrice / 100D));
        item.setHighPrice(NumUtil.formatNum(highPrice / 100D));
        item.setHighNowPrice(NumUtil.formatNum(highNowPrice / 100D));
        item.setHighNowPriceVal(highNowPrice);
        item.setLowNowPriceVal(lowNowPrice);
        item.setTotalStock(totalStock);
        item.setReservePrice(lowPrice);
    }

    private Long getHighestPrice(final List<ItemSkuDO> skuList) {
        if (CollectionUtil.isEmpty(skuList)) {
            return 0L;
        }
        Long highestPrice = skuList.get(0).getPrice().longValue();
        for (ItemSkuDO sku : skuList) {
            if (sku.getPrice().longValue() > highestPrice) {
                highestPrice = sku.getPrice().longValue();
            }
        }
        return highestPrice;
    }

    private Long getLowPrice(final List<ItemSkuDO> skuList) {
        if (CollectionUtil.isEmpty(skuList)) {
            return 0L;
        }
        Long lowPrice = skuList.get(0).getPrice().longValue();
        for (ItemSkuDO sku : skuList) {
            if (sku.getPrice().longValue() < lowPrice) {
                lowPrice = sku.getPrice().longValue();
            }
        }
        return lowPrice;
    }

    private Boolean useNewSourceByAppVersion(int version) {
        try {
            Integer appVersion = metabaseClient.getInteger(APP_VERSION);
            if (appVersion != null && version >= appVersion) {
                return Boolean.TRUE;
            }
        } catch (Exception e) {
            ;
        }
        return Boolean.FALSE;
    }

    private List<PromotionDecorate> getPromotionDecorates(List<ItemDetailPromotion.SkuPromotionDecorate> skuPromotionDecorates) {
        if (CollectionUtils.isEmpty(skuPromotionDecorates)) {
            return Collections.emptyList();
        }

        List<PromotionDecorate> promotionDecorates = new ArrayList<>();
        for (ItemDetailPromotion.SkuPromotionDecorate skuPromotionDecorate: skuPromotionDecorates) {
            String name = "";
            Integer order = null;
            if (skuPromotionDecorate.getPromotionType() == 1) {
                name = "店铺券";
                order = 3;
            } else if (skuPromotionDecorate.getPromotionType() == 2) {
                name = "店铺优惠";
                order = 4;
            } else {
                //这里面对应了promotionType=3和4的场景，4针对了购物金。3包含活动券、跨店满减。
                if (skuPromotionDecorate.getPromotionMark().getPromotionCode().equals(PromotionConstants.PromotionCode.PLATFORM_COUPON)) {
                    name = "活动券";
                    order = 5;
                } else if (skuPromotionDecorate.getPromotionMark().getPromotionCode().equals(PromotionConstants.PromotionCode.PLATFORM_BONUS)) {
                    name = "购物金";
                    order = 2;
                } else if (skuPromotionDecorate.getPromotionMark().getPromotionCode().equals(PromotionConstants.PromotionCode.CROSS_REACH_REDUCE)) {
                    name = "跨店满减";
                    order = 1;
                }
            }

            if (StringUtils.isNotBlank(name)) {
                promotionDecorates.add(new PromotionDecorate(name, skuPromotionDecorate.getSkuDecorate(),
                        skuPromotionDecorate.getCutPrice().intValue(), order));
            }
        }
        return promotionDecorates.stream().sorted(Comparator.comparing(PromotionDecorate::getOrder)).collect(Collectors.toList());
    }
}
