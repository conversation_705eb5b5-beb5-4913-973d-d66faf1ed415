package com.mogujie.detail.spi.mgj.shop;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.meili.service.shopcenter.domain.entity.ShopInfo;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.constant.ShopLabelEnum;
import com.mogujie.detail.core.spi.BizSpi;
import com.mogujie.detail.module.shop.domain.ShopDO;
import com.mogujie.detail.module.shop.domain.ShopLabel;
import com.mogujie.detail.module.shop.spi.IShopLabelProvider;
import com.mogujie.dts.api.query.Query;
import com.mogujie.dts.api.query.condition.Conditions;
import com.mogujie.dts.api.query.constant.Operator;
import com.mogujie.dts.api.result.QueryResult;
import com.mogujie.dts.api.result.RowResult;
import com.mogujie.dts.api.result.Value;
import com.mogujie.dts.api.service.DtsQueryService;
import com.mogujie.dts.utils.TokenUtil;
import com.mogujie.mars.api.domain.MarsResult;
import com.mogujie.mars.api.domain.entity.seller.SellerBusinessInfoDTO;
import com.mogujie.mars.api.seller.SellerBusinessInfoService;
import com.mogujie.metabase.spring.client.MetabaseClient;
import com.mogujie.service.muser.Result;
import com.mogujie.service.muser.api.v1.UserService;
import com.mogujie.service.muser.domain.entity.v1.UsersExtraInfo;
import com.mogujie.service.trade.microservice.order.api.query.shoporder.ShopOrderQueryService;
import com.mogujie.service.trade.microservice.order.domain.dto.query.req.QueryOrderIdsByBuyerReqDTO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import com.mogujie.trade.response.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @AUTUOR: lengshan
 * @DESCRIPTION: 店铺标签相关
 * @DATE: 2020/1/14 下午2:22
 */
@BizSpi
public class MgjShopLabelProviderProvider implements IShopLabelProvider {

    private static final Logger logger = LoggerFactory.getLogger(MgjShopLabelProviderProvider.class);

    private DtsQueryService dtsQueryService;


    private SellerBusinessInfoService sellerBusinessInfoService;

    private UserService userService;

    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    private ShopOrderQueryService shopOrderQueryService;

    @Resource(name = "confMetabaseClient")
    private MetabaseClient metabaseClient;

    @PostConstruct
    public void init() {
        try {
            dtsQueryService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(DtsQueryService.class);
            sellerBusinessInfoService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(SellerBusinessInfoService.class);
            userService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(UserService.class);
        } catch (Throwable e) {
            logger.error("init Label provider failed.", e);
        }
    }

    @Override
    public void listLabel(DetailContext context, ShopDO shopDO) {
        try {
            List<ShopLabel> dynLabels = new ArrayList<>();
            List<ShopLabel> labels = new ArrayList<>();
            ShopInfo baseShopInfo = context.getItemDO().getShopInfo();
            Long sellerId = context.getItemDO().getUserId();

            Long nowTime = System.currentTimeMillis();
            Calendar calendar = Calendar.getInstance();

            calendar.setTimeInMillis(nowTime);
            calendar.add(Calendar.YEAR, -1);
            long ago_1 = calendar.getTimeInMillis();

            calendar.setTimeInMillis(nowTime);
            calendar.add(Calendar.YEAR, -5);
            long ago_5 = calendar.getTimeInMillis();

            calendar.setTimeInMillis(nowTime);
            calendar.add(Calendar.YEAR, -3);
            long ago_3 = calendar.getTimeInMillis();

            calendar.setTimeInMillis(nowTime);
            calendar.add(Calendar.DATE, -1);
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String oneDayBeforeStr = dateFormat.format(calendar.getTime());

            Map<Integer, ShopLabel> labelTextMap = new HashMap<>();
            String labelTextConfig = metabaseClient.get("label_text_config");
            if (StringUtils.isNotBlank(labelTextConfig)){
                labelTextMap = JSON.parseObject(labelTextConfig, new TypeReference<Map<Integer, ShopLabel>>() {});
            }

            // 一、个人相关标签
            if (shopDO.getIsMarked() && labelTextMap.get(ShopLabelEnum.MY_FAVORITE.getIndex()) != null){
                dynLabels.add(labelTextMap.get(ShopLabelEnum.MY_FAVORITE.getIndex()));
            }
            if (context.getLoginUserId() != null) {
                QueryOrderIdsByBuyerReqDTO orderReqDTO = new QueryOrderIdsByBuyerReqDTO();
                orderReqDTO.setCreateFrom(ago_1 / 1000);
                orderReqDTO.setSellerUserId(sellerId);
                orderReqDTO.setPage(1);
                orderReqDTO.setPageSize(1);
                orderReqDTO.setBuyerUserId(context.getLoginUserId());
                Response<List<Long>> ret = shopOrderQueryService.getShopOrderIdsByBuyerAndSeller(orderReqDTO);
                if (ret.isSuccess() && CollectionUtils.isNotEmpty(ret.getData()) && labelTextMap.get(ShopLabelEnum.MY_BUY.getIndex()) != null) {
                    dynLabels.add(labelTextMap.get(ShopLabelEnum.MY_BUY.getIndex()));
                }
            }

            // 同城好店
            setSameCity(context, dynLabels, labelTextMap, sellerId);

            // 除个人相关标签，其他标签需要静态化
            if (context.isDyn()) {
                shopDO.setDynLabels(dynLabels);
                return;
            }

            // 二、关注人数相关标签
            if (shopDO.getCFans() != null && labelTextMap.get(ShopLabelEnum.SHOP_CFANS.getIndex()) != null && shopDO.getCFans() > 100){
                String n = shopDO.getCFans().toString();
                if (shopDO.getCFans() > 10000) {
                    DecimalFormat decimalFormat = new DecimalFormat(".0");
                    decimalFormat.setRoundingMode(RoundingMode.DOWN);
                    n = decimalFormat.format((double) shopDO.getCFans() / 10000) + "万";
                }
                ShopLabel shopLabel = labelTextMap.get(ShopLabelEnum.SHOP_CFANS.getIndex());
                shopLabel.setName(n + shopLabel.getName());
                labels.add(shopLabel);
            }
            // 三、销量相关标签（按照商品主营类目与店铺主营类目对应关系算，如果不对应，则该店铺不算在排名内）、 复购相关
            setSaleLabel(context, labels, shopDO, oneDayBeforeStr, labelTextMap);

            // 四、店铺复购相关标签（店铺主营类目）、五星好店
            if(shopDO.getLevel() != null && shopDO.getLevel() == 5 && labelTextMap.get(ShopLabelEnum.FIVE_LEVEL.getIndex()) != null){
                labels.add(labelTextMap.get(ShopLabelEnum.FIVE_LEVEL.getIndex()));
            }
            setRepeatLabel(labels, shopDO, oneDayBeforeStr, labelTextMap);

            // 五、发货率相关标签
            setShipRate(labels, shopDO, oneDayBeforeStr, labelTextMap);

            // 六、开店时间相关标签
            if (null != baseShopInfo) {
                Long enterTime = baseShopInfo.getEnterTime();
                if (enterTime != null){
                    if (enterTime < ago_5 / 1000){
                        if (labelTextMap.get(ShopLabelEnum.FIVE_YEAR.getIndex()) != null){
                            labels.add(labelTextMap.get(ShopLabelEnum.FIVE_YEAR.getIndex()));
                        }
                    }else if (enterTime < ago_3 / 1000){
                        if (labelTextMap.get(ShopLabelEnum.THREE_YEAR.getIndex()) != null){
                            labels.add(labelTextMap.get(ShopLabelEnum.THREE_YEAR.getIndex()));
                        }
                    }
                }
            }

            shopDO.setDynLabels(dynLabels);
            shopDO.setLabels(labels);

        } catch (Throwable e) {
            logger.error("get order info fail , ", e);
        }
    }

    private void setSaleLabel(DetailContext context, List<ShopLabel> labels, ShopDO shopDO, String visit_date, Map<Integer, ShopLabel> labelTextMap) {

        DetailItemDO detailItemDO = context.getItemDO();
        ShopInfo shopInfo = detailItemDO.getShopInfo();
        boolean isNeedTop10 = false;
        if (shopInfo != null){
            List<Integer> cidList = formatCid(detailItemDO.getCids());
            Integer tagId = shopInfo.getTagId();
            if (CollectionUtils.isNotEmpty(cidList) && tagId != null){
                Map<Integer, Integer> top10ConfigMap = new HashMap<>();
                String top10Config = metabaseClient.get("top10_config_config");
                if (StringUtils.isNotBlank(top10Config)){
                    top10ConfigMap = JSON.parseObject(top10Config, new TypeReference<Map<Integer, Integer>>() {});
                }
                for (Integer cid : cidList){
                    if (MapUtils.isNotEmpty(top10ConfigMap) && top10ConfigMap.get(cid) != null && tagId.intValue() == top10ConfigMap.get(cid).intValue()){
                        isNeedTop10 = true;
                        break;
                    }
                }
            }
        }

        List<ShopLabel> shopLabelList = new ArrayList<>();
        // 销量top10
        if (isNeedTop10){
            shopLabelList.addAll(getLabelDts("0181_tag_name", IdConvertor.urlToId(shopDO.getShopId()), visit_date, ShopLabelEnum.SHOP_SALE_TOP10, labelTextMap, null));
        }
        // 销量王
        shopLabelList.addAll(getLabelDts("0182_tag_name", IdConvertor.urlToId(shopDO.getShopId()), visit_date, ShopLabelEnum.SHOP_SALE_KING, labelTextMap, detailItemDO.getCategoryId()));
        labels.addAll(shopLabelList);

        if (CollectionUtils.isEmpty(shopLabelList)){
            // 计算累计销量
            Integer cSells = shopDO.getCSells();
            if (cSells != null && labelTextMap.get(ShopLabelEnum.SHOP_CSELLS.getIndex()) != null){
                String text = null;
                // 超过1万单位变成万
                if (cSells > 10000){
                    DecimalFormat decimalFormat = new DecimalFormat(".0");
                    decimalFormat.setRoundingMode(RoundingMode.DOWN);
                    text = decimalFormat.format((double) cSells / 10000) + "万+";
                }else if (cSells == 10000){
                    text = "1万";
                }else if (cSells >= 1000){
                    text = cSells.toString();
                }
                if (StringUtils.isNotBlank(text)){
                    ShopLabel shopLabel = labelTextMap.get(ShopLabelEnum.SHOP_CSELLS.getIndex());
                    shopLabel.setName(shopLabel.getName() + text);
                    labels.add(shopLabel);
                }
            }
        }

    }

    private void setRepeatLabel(List<ShopLabel> labels, ShopDO shopDO, String visit_date, Map<Integer, ShopLabel> labelTextMap) {
        labels.addAll(getLabelDts("0183_tag_name", IdConvertor.urlToId(shopDO.getShopId()), visit_date, ShopLabelEnum.SHOP_REPEAT, labelTextMap, null));
    }

    private List<ShopLabel> getLabelDts(String column, Long shopId, String visit_date, ShopLabelEnum shopLabelEnum, Map<Integer, ShopLabel> labelTextMap, Integer categoryId) {

        List<ShopLabel> labels = new ArrayList<>();

        try {
            Query query = new Query();
            //选择要查询哪些指标（这些指标必须订阅且审核通过才可以查询）
            query.addColumn(column);
            //设置查询条件（在同一个query中进行查询的指标，查询条件都是相同的）
            if (categoryId == null){
                query.setCondition(Conditions.and(
                        Conditions.condition("shopid", Operator.EQ, shopId),
                        Conditions.condition("visit_date", Operator.EQ, visit_date)
                ));
            }else{
                query.setCondition(Conditions.and(
                        Conditions.condition("shopid", Operator.EQ, shopId),
                        Conditions.condition("cid", Operator.EQ, categoryId),
                        Conditions.condition("visit_date", Operator.EQ, visit_date)
                ));

            }

            //执行查询
            String token = TokenUtil.produce("detail", "detail_dts");
            QueryResult queryResult = dtsQueryService.query(query, "detail", token);
            if (!queryResult.isSuccess() || CollectionUtils.isEmpty(queryResult.getRowResults())){
                return labels;
            }
            List<RowResult> list = queryResult.getRowResults();
            for (RowResult row : list) {
                Map<String, Value> map = row.getRowDatas();
                if (map == null || map.isEmpty()) {
                    continue;
                }
                String date = map.get("visit_date").asString();
                if (StringUtils.isBlank(date)) {
                    continue;
                }

                String label = map.get(column).asString();

                if (StringUtils.isNotBlank(label) && labelTextMap.get(shopLabelEnum.getIndex()) != null){
                    ShopLabel shopLabel = labelTextMap.get(shopLabelEnum.getIndex());
                    ShopLabel newShopLabel = new ShopLabel();
                    newShopLabel.setName(label);
                    newShopLabel.setSort(shopLabel.getSort());
                    labels.add(newShopLabel);
                }
            }

        } catch (Throwable e) {
            logger.error("getLabelDts error.", e);
        }

        return labels;
    }

    private void setShipRate(List<ShopLabel> labels, ShopDO shopDO, String visit_date, Map<Integer, ShopLabel> labelTextMap) {

        Query query = new Query();
        //选择要查询哪些指标（这些指标必须订阅且审核通过才可以查询）
        query.addColumn("0158_ship_rate_30d");
        //设置查询条件（在同一个query中进行查询的指标，查询条件都是相同的）
        query.setCondition(Conditions.and(
                Conditions.condition("shopid", Operator.EQ, IdConvertor.urlToId(shopDO.getShopId())),
                Conditions.condition("visit_date", Operator.EQ, visit_date)
        ));
        try {
            //执行查询
            String token = TokenUtil.produce("detail", "detail_dts");
            QueryResult queryResult = dtsQueryService.query(query, "detail", token);
            if (!queryResult.isSuccess() || CollectionUtils.isEmpty(queryResult.getRowResults())){
                return;
            }
            List<RowResult> list = queryResult.getRowResults();
            for (RowResult row : list) {
                Map<String, Value> map = row.getRowDatas();
                if (map == null || map.isEmpty()) {
                    continue;
                }
                String date = map.get("visit_date").asString();
                if (StringUtils.isBlank(date)) {
                    continue;
                }

                Double shipRate = map.get("0158_ship_rate_30d").asDouble();
                if (shipRate != null && shipRate == 1 && labelTextMap.get(ShopLabelEnum.SHIP_RATE.getIndex()) != null){
                    labels.add(labelTextMap.get(ShopLabelEnum.SHIP_RATE.getIndex()));
                }
            }
        } catch (Throwable e) {
            logger.error("get dts data error.", e);
        }

    }

    private void setSameCity(DetailContext context, List<ShopLabel> dynLabels, Map<Integer, ShopLabel> labelTextMap, Long sellerId) {


        try {
            MarsResult<SellerBusinessInfoDTO> marsResult = sellerBusinessInfoService.getAuditSimpleSellerBusinessByUserId(sellerId);
            if (marsResult != null && marsResult.isSuccess() && marsResult.getResult() != null) {
                SellerBusinessInfoDTO sellerBusinessInfoDTO = marsResult.getResult();
                String province = sellerBusinessInfoDTO.getProvince();
                String city = sellerBusinessInfoDTO.getCity();

                if (StringUtils.isNotBlank(province) && StringUtils.isNotBlank(city) && labelTextMap.get(ShopLabelEnum.SAME_CITY.getIndex()) != null){
                    // 用户授权匹配
                    if (province.equals(context.getParam("province")) && city.equals(context.getParam("city"))) {
                        dynLabels.add(labelTextMap.get(ShopLabelEnum.SAME_CITY.getIndex()));
                        return;
                    }
                    // 用户个人信息
                    if (context.getLoginUserId() != null) {
                        Result<UsersExtraInfo> result = userService.queryUserExtraInfoByUserId(context.getLoginUserId());
                        if (result != null && result.getValue() != null && result.getValue().getExtra() != null && result.getValue().getExtra().get("mogujie") != null){
                            UsersExtraInfo usersExtraInfo = result.getValue();
                            Map<String, Map<String, Object>> extra = usersExtraInfo.getExtra();
                            Map<String, Object> map = extra.get("mogujie");
                            if (province.equals(map.get("province")) && city.equals(map.get("city"))) {
                                dynLabels.add(labelTextMap.get(ShopLabelEnum.SAME_CITY.getIndex()));
                                return;
                            }
                        }
                    }
                }
            }

        } catch (Throwable e) {
            logger.error("get dts data error.", e);
        }

    }

    /**
     * 格式化cid，将 "#3856# #1734# #999# #2946#"  转为list
     *
     * @param cids
     * @return
     */
    private static List<Integer> formatCid(String cids) {
        if (StringUtils.isBlank(cids)) {
            return null;
        }

        if (!cids.contains("#")) {
            return null;
        }
        try{
            String newCids = cids.replace("# #", ",");
            newCids = newCids.replace("#", "");
            List<Integer> cidList = new ArrayList<>();
            for (String cid : newCids.split(",")){
                cidList.add(Integer.valueOf(cid));
            }
            return cidList;
        }catch (Exception e){
            logger.error("formatCid error.", e);
        }
        return null;
    }

}
