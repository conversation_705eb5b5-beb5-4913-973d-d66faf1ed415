package com.mogujie.detail.spi.mgj.sku;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.module.sku.domain.SkuDO;
import com.mogujie.detail.spi.mgj.SpiBaseTest;
import com.mogujie.service.item.api.SkuService;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import junit.framework.Assert;
import org.junit.Before;
import org.junit.Test;

/**
 * Created by xiaoyao on 16/9/6.
 */
public class MgjSkuParserTest extends SpiBaseTest<MgjSkuParser> {

    private SkuService skuService;

    @Before
    public void setUp() throws Exception {
        super.init(MgjSkuParser.class);
        skuService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(SkuService.class);
    }

    @Test
    public void testParseSku() throws Exception {
        DetailContext context = getDetailContext();
        long totalStock = 0L;
        for(ItemSkuDO sku : context.getItemDO().getItemSkuDOList()) {
            sku.setNowPrice(sku.getPrice().intValue());
            totalStock += sku.getQuantity();
        }
        context.getItemDO().setTotalStock(totalStock);
        SkuDO skuDO = new SkuDO();
        spiImpl.parseSku(context, skuDO);
        Assert.assertTrue(skuDO.getSkus().size() > 0);
    }
}