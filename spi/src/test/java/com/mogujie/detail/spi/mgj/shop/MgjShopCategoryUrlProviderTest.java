package com.mogujie.detail.spi.mgj.shop;

import com.mogujie.detail.spi.mgj.SpiBaseTest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

/**
 * Created by xiaoyao on 16/9/6.
 */
public class MgjShopCategoryUrlProviderTest extends SpiBaseTest<MgjShopCategoryUrlProvider> {

    @Before
    public void setUp() throws Exception {
        super.init(MgjShopCategoryUrlProvider.class);
    }

    @Test
    public void testGetShopCategoryUrl() throws Exception {
        Assert.assertNotNull(spiImpl.getShopCategoryUrl(getDetailContext(), 1160));
    }
}