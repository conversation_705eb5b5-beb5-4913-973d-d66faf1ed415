//package com.mogujie.detail.spi.mgj.shop;
//
//import com.mogujie.detail.spi.mgj.SpiBaseTest;
//import com.mogujie.service.hummer.domain.dto.QueryPartPlatformCouponParam;
//import com.mogujie.service.ocean.api.ShopKpiService;
//import com.mogujie.service.ocean.result.ResultSupport;
//import junit.framework.Assert;
//import mockit.Capturing;
//import mockit.Expectations;
//import mockit.integration.junit4.JMockit;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.ApplicationContext;
//import org.springframework.context.support.ClassPathXmlApplicationContext;
//
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * Created by xiaoyao on 16/9/6.
// */
//@RunWith(JMockit.class)
//public class MgjShopDsrProviderTest extends SpiBaseTest<MgjShopDsrProvider> {
//
//    private MgjShopDsrProvider mgjShopDsrProvider;
//
//    @Before
//    public void setUp() throws Exception {
//        ApplicationContext context = new ClassPathXmlApplicationContext("classpath*:shop-test.xml");
//        super.init();
//        mgjShopDsrProvider = (MgjShopDsrProvider) context.getBean("mgjShopDsrProvider");
//    }
//
//    @Test
//    public void testListShopDsr(@Capturing final ShopKpiService shopKpiService) throws Exception {
//        final ResultSupport<Map<String, Object>> ret = new ResultSupport<>();
//        ret.setSuccess(true);
//        Map<String, Object> map = new HashMap();
//        Map<String, String> map1 = new HashMap<>();
//        Map<String, String> map2 = new HashMap<>();
//        map.put("shop", map1);
//        map.put("tag", map2);
//        map1.put("style", "1.1");
//        map1.put("quality", "2.0");
//        map1.put("price", "3.1");
//        map1.put("service", "5.0");
//        map1.put("desc", "4.5");
//        map2.put("style", "1.1");
//        map2.put("quality", "2.0");
//        map2.put("price", "3.1");
//        map2.put("service", "5.0");
//        map2.put("desc", "4.5");
//
//        ret.setModule(map);
//        new Expectations() {{
//            shopKpiService.getKpiByShopId((Long) any);
//            result = ret;
//        }};
//        Assert.assertNotNull(mgjShopDsrProvider.listShopDsr(getDetailContext(), 1));
//    }
//}