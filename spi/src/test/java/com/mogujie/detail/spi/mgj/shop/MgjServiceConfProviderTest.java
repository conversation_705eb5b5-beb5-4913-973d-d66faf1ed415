package com.mogujie.detail.spi.mgj.shop;

import com.mogujie.detail.spi.mgj.SpiBaseTest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

/**
 * Created by xiaoyao on 16/9/6.
 */
public class MgjServiceConfProviderTest extends SpiBaseTest<MgjServiceConfProvider> {

    @Before
    public void setUp() throws Exception {
        super.init(MgjServiceConfProvider.class);
    }

    @Test
    public void testGetServiceName() throws Exception {
        Assert.assertNotNull(spiImpl.getServiceName(getDetailContext(), "style"));
    }

    @Test
    public void testGetServiceName1() throws Exception {
        Assert.assertNotNull(spiImpl.getServiceName(getDetailContext(), "quality"));
    }

    @Test
    public void testGetServiceName2() throws Exception {
        Assert.assertNotNull(spiImpl.getServiceName(getDetailContext(), "price"));
    }

    @Test
    public void testGetServiceName3() throws Exception {
        Assert.assertNotNull(spiImpl.getServiceName(getDetailContext(), "desc"));
    }

    @Test
    public void testGetServiceName4() throws Exception {
        Assert.assertEquals("", spiImpl.getServiceName(getDetailContext(), "other"));
    }
}