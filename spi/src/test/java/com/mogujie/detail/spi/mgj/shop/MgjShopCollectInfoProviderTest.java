package com.mogujie.detail.spi.mgj.shop;

import com.mogujie.detail.spi.mgj.SpiBaseTest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * Created by x<PERSON><PERSON><PERSON> on 16/9/6.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:shop-test.xml"})
public class MgjShopCollectInfoProviderTest extends SpiBaseTest<MgjShopCollectInfoProvider> {

    @Autowired
    private MgjShopCollectInfoProvider mgjShopCollectInfoProvider;

    @Before
    public void setUp() throws Exception {
        super.init(MgjShopCollectInfoProvider.class);
    }

    @Test
    public void testIsCollected() throws Exception {
        Assert.assertFalse(mgjShopCollectInfoProvider.isCollected(getDetailContext()));
    }
}