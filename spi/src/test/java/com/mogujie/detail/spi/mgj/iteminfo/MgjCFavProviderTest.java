package com.mogujie.detail.spi.mgj.iteminfo;

import com.mogujie.detail.spi.mgj.SpiBaseTest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * Created by x<PERSON><PERSON><PERSON> on 16/9/6.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:spi-test.xml"})
public class MgjCFavProviderTest extends SpiBaseTest<MgjCFavProvider>{

    @Autowired
    private MgjCFavProvider mgjCFavProvider;

    @Before
    public void setUp() throws Exception {
        super.init(MgjCFavProvider.class);
        spiImpl.init();
    }

    @Test
    public void testGetCFav() throws Exception {
        Assert.assertTrue(mgjCFavProvider.getCFav(getDetailContext()) == 0);
    }
}