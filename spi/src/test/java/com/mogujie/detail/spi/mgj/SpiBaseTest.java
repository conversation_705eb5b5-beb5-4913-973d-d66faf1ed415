package com.mogujie.detail.spi.mgj;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.RouteInfo;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.Platform;
import com.mogujie.service.item.api.basic.ItemReadService;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.external.IcException;
import com.mogujie.service.item.domain.query.QueryItemOptions;
import com.mogujie.service.item.domain.result.BaseResultDO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by xia<PERSON><PERSON> on 16/6/30.
 */
public abstract class SpiBaseTest<T> {

    private static final Logger LOGGER = LoggerFactory.getLogger(SpiBaseTest.class);

    protected ItemReadService itemReadService;

    protected T spiImpl;

    protected static final Integer ITEM_ID = 201293545;

    protected void init() throws Exception {
        itemReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ItemReadService.class);
    }

    protected void init(Class spiClass) throws Exception {
        init();
        spiImpl = (T) spiClass.newInstance();
    }

    protected DetailContext getDetailContext() {
        QueryItemOptions queryItemOptions = new QueryItemOptions();
        queryItemOptions.setIncludeItemExtraInfo(true);
        queryItemOptions.setQueryBasicItem(true);
        queryItemOptions.setQueryInventory(true);
        queryItemOptions.setQuerySkuInfo(true);
        queryItemOptions.setIncludeDeleted(false);
        queryItemOptions.setQueryItemDetail(true);
        queryItemOptions.setQueryPresaleDO(true);
        queryItemOptions.setQuerySkuAttribute(true);
//        queryItemOptions.setQueryTagCenter(true);
        try {
            BaseResultDO<ItemDO> ret = itemReadService.queryItemById(ITEM_ID, queryItemOptions);
            DetailContext context = new DetailContext(new RouteInfo(App.MGJ, Platform.ALL, BizType.FASTBUY, "", ""), null, null, null);
            context.setItemDO(new DetailItemDO(ret.getResult()));
            return context;
        } catch (IcException e) {
            LOGGER.error("get item failed : {}", e);
        }
        return null;
    }
}
