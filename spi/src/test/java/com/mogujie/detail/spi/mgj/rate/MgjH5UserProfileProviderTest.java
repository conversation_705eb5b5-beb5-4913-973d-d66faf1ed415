package com.mogujie.detail.spi.mgj.rate;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.spi.mgj.SpiBaseTest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

/**
 * Created by xiaoyao on 16/9/6.
 */
public class MgjH5UserProfileProviderTest extends SpiBaseTest<MgjH5UserProfileProvider>{

    @Before
    public void setUp() throws Exception {
        super.init(MgjH5UserProfileProvider.class);
    }

    @Test
    public void testGetProfilePrefix() throws Exception {
        Assert.assertNotNull(spiImpl.getProfilePrefix(getDetailContext()));
    }

    @Test
    public void testGetAnonymousImg() throws Exception {
        DetailContext context = getDetailContext();
        Assert.assertNotNull(spiImpl.getAnonymousImg(context, context.getItemDO().getUserId()));
    }
}