package com.mogujie.detail.spi.mgj.rate;

import com.meili.service.shopcenter.api.ShopReadService;
import com.meili.service.shopcenter.domain.entity.ShopInfo;
import com.meili.service.shopcenter.result.Result;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.spi.mgj.SpiBaseTest;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import com.mogujie.tesla.core.ReferConfig;
import org.apache.commons.lang.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 * Created by xiaoyao on 16/9/6.
 */
public class MgjUserProfileProviderTest extends SpiBaseTest<MgjUserProfileProvider> {

    @Before
    public void setUp() throws Exception {
        super.init(MgjUserProfileProvider.class);
    }

    @Test
    public void testGetProfilePrefix() throws Exception {
        Assert.assertNotNull(spiImpl.getProfilePrefix(getDetailContext()));
    }

    @Test
    public void testGetAnonymousImg() throws Exception {
        DetailContext context = getDetailContext();
        Assert.assertNotNull(spiImpl.getAnonymousImg(context, context.getItemDO().getUserId()));
    }

    @Test
    public void testString(){
        if(StringUtils.startsWith("htt", "http://")){
            System.out.println("yes");
        }
    }

    @Test
    public void export() throws Exception {
        ReferConfig referConfig = new ReferConfig(ShopReadService.class);
        referConfig.setTargetAddress("************:20031");
        ShopReadService shopReadService = (ShopReadService) TeslaServiceConsumerFactory
                .getTeslaServiceConsumer(referConfig);
        Result<ShopInfo> result = shopReadService.queryShopByUserId(98055883L);
        System.out.println(result.getData());
    }
}