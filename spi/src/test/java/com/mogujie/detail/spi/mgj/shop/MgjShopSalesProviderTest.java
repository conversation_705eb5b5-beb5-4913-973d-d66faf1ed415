package com.mogujie.detail.spi.mgj.shop;

import com.mogujie.detail.spi.mgj.SpiBaseTest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * Created by xiaoyao on 16/9/6.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:spi-test.xml"})
public class MgjShopSalesProviderTest extends SpiBaseTest<MgjShopSalesProvider> {

    @Before
    public void setUp() throws Exception {
        super.init(MgjShopSalesProvider.class);
    }

    @Test
    public void testGetShopSales() throws Exception {
        Assert.assertNotNull(spiImpl.getShopSales(getDetailContext()));
    }
}