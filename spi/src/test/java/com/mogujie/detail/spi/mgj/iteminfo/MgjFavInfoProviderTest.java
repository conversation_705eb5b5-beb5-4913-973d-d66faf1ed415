package com.mogujie.detail.spi.mgj.iteminfo;

import com.mogujie.detail.spi.mgj.SpiBaseTest;
import junit.framework.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * Created by x<PERSON><PERSON><PERSON> on 16/9/6.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:spi-test.xml"})
public class MgjFavInfoProviderTest extends SpiBaseTest<MgjFavInfoProvider>{

    @Autowired
    private MgjFavInfoProvider mgjFavInfoProvider;

    @Before
    public void setUp() throws Exception {
        super.init(MgjFavInfoProvider.class);
        spiImpl.init();
    }

    @Test
    public void testIsFaved() throws Exception {
        Assert.assertFalse(mgjFavInfoProvider.isFaved(getDetailContext()));
    }
}