package com.mogujie.detail.spi.mgj.shop;

import com.meili.service.shopcenter.api.ShopReadService;
import com.meili.service.shopcenter.domain.entity.ShopInfo;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.spi.mgj.SpiBaseTest;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * Created by xiaoyao on 16/9/6.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:spi-test.xml"})
public class MgjAppShopTagProviderTest extends SpiBaseTest<MgjAppShopTagProvider> {

    private ShopReadService shopReadService;

    @Autowired
    private MgjAppShopTagProvider mgjAppShopTagProvider;



    @Before
    public void setUp() throws Exception {
        shopReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ShopReadService.class);
        super.init(MgjAppShopTagProvider.class);
    }

    @Test
    public void testGetShopTag() throws Exception {
        DetailContext context = getDetailContext();
        ShopInfo shopInfo = shopReadService.queryShopByShopId(context.getItemDO().getShopId()).getData();
        Assert.assertNull(mgjAppShopTagProvider.getShopTag(context, shopInfo));
    }
}