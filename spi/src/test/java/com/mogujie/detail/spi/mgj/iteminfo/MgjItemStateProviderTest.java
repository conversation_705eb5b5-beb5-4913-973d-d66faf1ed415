package com.mogujie.detail.spi.mgj.iteminfo;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.spi.mgj.SpiBaseTest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * Created by xiaoya<PERSON> on 16/9/6.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:spi-test.xml"})
public class MgjItemStateProviderTest extends SpiBaseTest<MgjItemStateProvider>{

    @Before
    public void setUp() throws Exception {
        super.init(MgjItemStateProvider.class);
    }

    @Test
    public void testGetItemState() throws Exception {
        DetailContext context = getDetailContext();
        context.getItemDO().setTotalStock(100L);
        Assert.assertTrue(spiImpl.getItemState(context) == 0);
    }
}