<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:beans="http://www.springframework.org/schema/mvc"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

       <bean id="confMetabaseClient" class="com.mogujie.metabase.spring.client.MetabaseClient">
              <property name="appName" value="detailConf"/>
       </bean>

       <bean id="commonSwitchUtil" class="com.mogujie.detail.core.util.CommonSwitchUtil"/>

       <import resource="classpath*:spring-salesquery.xml"/>

       <bean id="shopSalesQueryClient" class="com.mogujie.trade.sales.query.client.ShopSalesQueryClient"/>

       <bean id="itemSalesQueryClient" class="com.mogujie.trade.sales.query.client.ItemSalesQueryClient"/>

       <bean id="preItemSalesQueryClient" class="com.mogujie.trade.sales.query.client.PreItemSalesQueryClient"/>

       <bean id="mgjCFavProvider" class="com.mogujie.detail.spi.mgj.iteminfo.MgjCFavProvider"/>

       <bean id="mgjFavInfoProvider" class="com.mogujie.detail.spi.mgj.iteminfo.MgjFavInfoProvider"/>

       <bean id="mgjAppShopTagProvider" class="com.mogujie.detail.spi.mgj.shop.MgjAppShopTagProvider"/>

</beans>