<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>detail-web</artifactId>
        <groupId>com.mogujie.detail</groupId>
        <version>1.1.0.dsl</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <version>1.1.0.dsl</version>
    <artifactId>spi</artifactId>

    <properties>
        <jmockit.version>1.18</jmockit.version>
        <dts.version>1.0.3.1</dts.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.mogujie.mst</groupId>
            <artifactId>kvstore-client</artifactId>
            <version>1.2.2</version>
        </dependency>
        <dependency>
            <groupId>com.mogujie.marketing</groupId>
            <artifactId>veyron-tools</artifactId>
            <version>1.0.8.2</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mogujie.metabase</groupId>
                    <artifactId>metabase-admin</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.metabase</groupId>
                    <artifactId>metabase-spring-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.metabase</groupId>
                    <artifactId>metabase-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.metabase</groupId>
                    <artifactId>metabase-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.moguboot</groupId>
                    <artifactId>mogu-boot-autoconfigure</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mogujie.dts</groupId>
            <artifactId>dts-api</artifactId>
            <version>${dts.version}</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.detail</groupId>
            <artifactId>module</artifactId>
            <version>${detail.version}</version>
        </dependency>

        <dependency>
            <groupId>org.jmockit</groupId>
            <artifactId>jmockit</artifactId>
            <version>${jmockit.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>


</project>