<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>detail-web</artifactId>
        <groupId>com.mogujie.detail</groupId>
        <version>1.1.0.dsl</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>core</artifactId>


    <properties>
        <ic.version>2.0.4.3</ic.version>
        <junit.version>4.11</junit.version>
        <inventory.version>1.0.24</inventory.version>
        <hummer.version>3.7.21</hummer.version>
        <ferrari.version>1.7-RELEASE</ferrari.version>
        <shopcenter.version>1.3.0.7</shopcenter.version>
        <shopcenter-api.version>1.3.0.9</shopcenter-api.version>
        <jmockit.version>1.18</jmockit.version>
        <logback-plugin.version>1.0.1</logback-plugin.version>
        <timeclient.version>1.0.5</timeclient.version>
        <tag-center-api.version>2.1.3</tag-center-api.version>
        <metadata.version>1.0.21</metadata.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.mogujie.metabase</groupId>
            <artifactId>metabase-spring-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mogujie.mst</groupId>
            <artifactId>kvstore-client</artifactId>
            <version>1.2.2</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.metabase</groupId>
            <artifactId>metabase-admin</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>shopcenter-api</artifactId>
            <version>${shopcenter-api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>shopcenter-client</artifactId>
            <version>${shopcenter.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mogujie.tesla</groupId>
                    <artifactId>tesla-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.tesla</groupId>
                    <artifactId>tesla-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.metabase</groupId>
                    <artifactId>metabase-spring-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.sentry</groupId>
                    <artifactId>sentry-client</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>kvstore-client</artifactId>
                    <groupId>com.mogujie.mst</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mogujie.sentry</groupId>
            <artifactId>sentry-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mogujie.stable</groupId>
            <artifactId>spirit</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.mogujie.metabase</groupId>
                    <artifactId>metabase-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.metabase</groupId>
                    <artifactId>metabase-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-beans</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-aop</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-aspects</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.jmockit</groupId>
            <artifactId>jmockit</artifactId>
            <version>${jmockit.version}</version>
            <scope>test</scope>
        </dependency>

        <!--<dependency>-->
            <!--<groupId>com.mogujie.service</groupId>-->
            <!--<artifactId>item-center-api</artifactId>-->
            <!--<version>${ic.version}</version>-->
        <!--</dependency>-->

        <!--<dependency>-->
            <!--<groupId>com.mogujie.item</groupId>-->
            <!--<artifactId>itemcenter-client</artifactId>-->
            <!--<version>4.0.6.1</version>-->
        <!--</dependency>-->

        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>itemcenter-common</artifactId>
            <version>1.0.6</version>
        </dependency>
        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>category-service-api</artifactId>
            <version>2.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>item-center-api</artifactId>
            <version>2.1.0.20</version>
        </dependency>

        <!--<dependency>-->
        <!--<groupId>com.mogujie.service</groupId>-->
        <!--<artifactId>itemcenter-client</artifactId>-->
        <!--<version>${itemcenter-client.verison}</version>-->
        <!--</dependency>-->

        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>inventory-center-api</artifactId>
            <version>${inventory.version}</version>
        </dependency>

        <!--spring 相关依赖-->

        <!--spring 相关依赖-->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aspects</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-expression</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>log4j-over-slf4j</artifactId>
        </dependency>

        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <version>2.4.8</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.darwin</groupId>
            <artifactId>darwin-client</artifactId>
            <version>1.1.6.4</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mogujie.search.ara.abtest</groupId>
                    <artifactId>abtest-cli</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.search.ara.abtest</groupId>
                    <artifactId>abtest-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.metabase</groupId>
                    <artifactId>metabase-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.metabase</groupId>
                    <artifactId>metabase-admin</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.dragon.bigdata</groupId>
                    <artifactId>CDNBalance</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.mogujie.dragon.bigdata</groupId>
            <artifactId>CDNBalance</artifactId>
        </dependency>

        <!--<dependency>-->
            <!--<groupId>com.mogujie.tesla</groupId>-->
            <!--<artifactId>tesla-spring-bootstrap</artifactId>-->
            <!--<exclusions>-->
                <!--<exclusion>-->
                    <!--<groupId>org.mogujie.trace</groupId>-->
                    <!--<artifactId>trace-lurkeragent</artifactId>-->
                <!--</exclusion>-->
            <!--</exclusions>-->
        <!--</dependency>-->

        <dependency>
            <groupId>com.mogujie.tesla</groupId>
            <artifactId>tesla-spring-server</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.mogujie.trace</groupId>
                    <artifactId>trace-lurkeragent</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.mogujie</groupId>
            <artifactId>costa-server</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.mogujie</groupId>
                    <artifactId>actionlet.session</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- rxjava -->
        <dependency>
            <groupId>io.reactivex</groupId>
            <artifactId>rxjava</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mogujie.session</groupId>
            <artifactId>session-filter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mogujie.trace</groupId>
            <artifactId>trace-lurkeragent</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mogujie.cayenne</groupId>
            <artifactId>cayenne-timeclient</artifactId>
            <version>${timeclient.version}</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.service.hummer</groupId>
            <artifactId>hummer-api</artifactId>
            <version>${hummer.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mogujie</groupId>
                    <artifactId>mogu-commons</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.themis</groupId>
                    <artifactId>themis-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.mogujie.marketing</groupId>
            <artifactId>ferrari-api</artifactId>
            <version>${ferrari.version}</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.dragon.bigdata</groupId>
            <artifactId>CDNBalance</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mogujie</groupId>
            <artifactId>actionlet.mwp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mogujie.detail</groupId>
            <artifactId>detail-logback-plugin</artifactId>
            <version>${logback-plugin.version}</version>
        </dependency>
        <dependency>
            <groupId>com.mogujie.detail</groupId>
            <artifactId>groovy.loader</artifactId>
            <version>1.0.6</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>tag-center-api</artifactId>
            <version>${tag-center-api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.ecommerce</groupId>
            <artifactId>metadata</artifactId>
            <version>${metadata.version}</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.brandcenter</groupId>
            <artifactId>brandcenter-api</artifactId>
            <version>1.4.7</version>
        </dependency>
        <dependency>
            <groupId>com.mougjie.search.acm</groupId>
            <artifactId>sdk</artifactId>
            <version>2.0.21</version>
        </dependency>
        <dependency>
            <groupId>org.mogujie.marketing</groupId>
            <artifactId>kamaz-client</artifactId>
            <version>1.3.2</version>
        </dependency>
        <dependency>
            <groupId>com.mogujie</groupId>
            <artifactId>slardar-client</artifactId>
        </dependency>
    </dependencies>


</project>