package com.mogujie.detail.core.manager;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.ModuleDO;
import com.mogujie.detail.core.adt.RouteInfo;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.Platform;
import com.mogujie.service.item.api.basic.ItemReadService;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.external.IcException;
import com.mogujie.service.item.domain.query.QueryItemOptions;
import com.mogujie.service.item.domain.result.BaseResultDO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * Created by xiaoyao on 16/9/8.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath*:test-context-manager.xml")
public class TranslatorManagerTest {

    private static final Logger LOGGER = LoggerFactory.getLogger(TranslatorManagerTest.class);

    @Autowired
    private TranslatorManager translatorManager;

    private ItemReadService itemReadService;

    protected static final Integer ITEM_ID = 201293545;

    @Before
    public void setUp() throws Exception {
        itemReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ItemReadService.class);
    }

    protected DetailContext getDetailContext() {
        QueryItemOptions queryItemOptions = new QueryItemOptions();
        queryItemOptions.setIncludeItemExtraInfo(true);
        queryItemOptions.setQueryBasicItem(true);
        queryItemOptions.setQueryInventory(true);
        queryItemOptions.setQuerySkuInfo(true);
        queryItemOptions.setIncludeDeleted(false);
        queryItemOptions.setQueryItemDetail(true);
        queryItemOptions.setQueryPresaleDO(true);
        queryItemOptions.setQuerySkuAttribute(true);
//        queryItemOptions.setQueryTagCenter(true);
        try {
            BaseResultDO<ItemDO> ret = itemReadService.queryItemById(ITEM_ID, queryItemOptions);
            List<String> componentIds = new ArrayList<>(3);
            componentIds.add("summary");
            DetailContext context = new DetailContext(new RouteInfo(App.MGJ, Platform.ALL, BizType.FASTBUY, "", ""), componentIds, ITEM_ID.longValue(), TranslatorManager.getTranslatorMappingHolder());
            DetailItemDO itemDO = new DetailItemDO(ret.getResult());
            itemDO.setTotalStock(1000L);
            context.setItemDO(itemDO);
            return context;
        } catch (IcException e) {
            LOGGER.error("get item failed : {}", e);
        }
        return null;
    }

    @Test
    public void testTranslate() throws Exception {
        Map<Class, Future<ModuleDO>> translateMap = new HashMap<>();
        translateMap.put(ModuleDO.class, new Future<ModuleDO>() {
            @Override
            public boolean cancel(boolean mayInterruptIfRunning) {
                return false;
            }

            @Override
            public boolean isCancelled() {
                return false;
            }

            @Override
            public boolean isDone() {
                return false;
            }

            @Override
            public ModuleDO get() throws InterruptedException, ExecutionException {
                return null;
            }

            @Override
            public ModuleDO get(long timeout, TimeUnit unit) throws InterruptedException, ExecutionException, TimeoutException {
                return null;
            }
        });
        Assert.assertNotNull(translatorManager.translate(translateMap, getDetailContext()));
    }
}