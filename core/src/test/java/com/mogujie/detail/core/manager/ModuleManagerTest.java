package com.mogujie.detail.core.manager;

import org.junit.Before;
import org.junit.Test;
import rx.Observable;
import rx.Subscriber;
import rx.functions.Action0;
import rx.functions.Action1;
import rx.functions.Func1;
import rx.schedulers.Schedulers;

/**
 * Created by xiaoyao on 16/8/9.
 */
public class ModuleManagerTest {

    @Before
    public void setUp() throws Exception {

    }

    @Test
    public void rxjavaTest() {
        flatMapExampleAsync();
    }

//    private static void mergingAsync() {
//        Observable.merge(getDataAsync(1), getDataAsync(2)).toBlocking().forEach(System.out::println);
//    }
//
//    private static void mergingSync() {
//        // here you'll see the delay as each is executed synchronously
//        Observable.merge(getDataSync(1), getDataSync(2)).toBlocking().forEach(System.out::println);
//    }
//
//    private static void mergingSyncMadeAsync() {
//        // if you have something synchronous and want to make it async, you can schedule it like this
//        // so here we see both executed concurrently
//        Observable.merge(getDataSync(1).subscribeOn(Schedulers.io()), getDataSync(2).subscribeOn(Schedulers.io())).toBlocking().forEach(System.out::println);
//    }

    private static void flatMapExampleAsync() {
        Observable.from(new Long[]{1L, 2L, 3L, 4L}).flatMap(new Func1<Long, Observable<?>>() {
            public Observable<?> call(Long i) {
                return getDataAsync(i);
            }
        }).doOnCompleted(new Action0() {
            public void call() {
                System.out.println("aaa");
            }
        }).toBlocking().forEach(new Action1<Object>() {
            public void call(Object s) {
                System.out.println(s);
            }
        });
        System.out.println(123);
    }

//    private static void flatMapExampleSync() {
//        Observable.range(0, 5).flatMap(i -> {
//            return getDataSync(i);
//        }).toBlocking().forEach(System.out::println);
//    }
//
//    private static void flatMapBufferedExampleAsync() {
//        Observable.range(0, 5000).buffer(500).flatMap(i -> {
//            return Observable.from(i).subscribeOn(Schedulers.computation()).map(item -> {
//                // simulate computational work
//                try {
//                    Thread.sleep(1);
//                } catch (Exception e) {
//                }
//                return item + " processed " + Thread.currentThread();
//            });
//        }).toBlocking().forEach(System.out::println);
//    }
//
//    private static void flatMapWindowedExampleAsync() {
//        Observable.range(0, 5000).window(500).flatMap(work -> {
//            return work.observeOn(Schedulers.computation()).map(item -> {
//                // simulate computational work
//                try {
//                    Thread.sleep(1);
//                } catch (Exception e) {
//                }
//                return item + " processed " + Thread.currentThread();
//            });
//        }).toBlocking().forEach(System.out::println);
//    }

    // artificial representations of IO work
    static Observable<Long> getDataAsync(long i) {
        return getDataSync(i).subscribeOn(Schedulers.io());
    }

    static Observable<Long> getDataSync(long i) {
        return Observable.create(new Observable.OnSubscribe<Long>() {
            public void call(Subscriber<? super Long> subscriber) {
                try {
                    Thread.sleep(1000);
                    subscriber.onNext(System.currentTimeMillis());
                    subscriber.onCompleted();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }
}