package com.mogujie.detail.core.spi;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.RouteInfo;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.Platform;
import com.mogujie.service.item.api.basic.ItemReadService;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.external.IcException;
import com.mogujie.service.item.domain.query.QueryItemOptions;
import com.mogujie.service.item.domain.result.BaseResultDO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import junit.framework.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by xiaoyao on 16/9/9.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath*:test-context-manager.xml")
public class SpiManagerTest {

    @Autowired
    private SpiManager spiManager;

    @Autowired
    private TestModule testModule;

    protected static final Integer ITEM_ID = 201293545;

    private ItemReadService itemReadService;

    @Before
    public void setUp() throws Exception {
        itemReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ItemReadService.class);
    }

    protected DetailContext getDetailContext() {
        QueryItemOptions queryItemOptions = new QueryItemOptions();
        queryItemOptions.setIncludeItemExtraInfo(true);
        queryItemOptions.setQueryBasicItem(true);
        queryItemOptions.setQueryInventory(true);
        queryItemOptions.setQuerySkuInfo(true);
        queryItemOptions.setIncludeDeleted(false);
        queryItemOptions.setQueryItemDetail(true);
        queryItemOptions.setQueryPresaleDO(true);
        queryItemOptions.setQuerySkuAttribute(true);
//        queryItemOptions.setQueryTagCenter(true);
        try {
            BaseResultDO<ItemDO> ret = itemReadService.queryItemById(ITEM_ID, queryItemOptions);
            List<String> componentIds = new ArrayList<>(3);
            componentIds.add("summary");
            DetailContext context = new DetailContext(new RouteInfo(App.MGJ, Platform.ALL, BizType.FASTBUY, "", ""), componentIds, ITEM_ID.longValue(), null);
            DetailItemDO itemDO = new DetailItemDO(ret.getResult());
            itemDO.setTotalStock(1000L);
            context.setItemDO(itemDO);
            return context;
        } catch (IcException e) {
            ;
        }
        return null;
    }

    @Test
    public void testSpi() {
        Assert.assertNotNull(spiManager.getDefaultImpl(ITestService.class));
        Assert.assertNotNull(testModule.hello(getDetailContext()));
    }

}