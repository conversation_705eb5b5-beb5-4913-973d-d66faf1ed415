package com.mogujie.detail.core.cache.impl;

import com.google.common.cache.Cache;
import com.mogujie.detail.core.adt.Status;
import com.mogujie.detail.core.cache.ICacheUtil;
import mockit.Capturing;
import mockit.Expectations;
import mockit.integration.junit4.JMockit;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

/**
 * Created by xiaoyao on 16/9/8.
 */
@RunWith(JMockit.class)
public class GuavaCacheTest {

    private ICacheUtil cacheUtil;

    @Before
    public void setUp() {
        ApplicationContext context = new ClassPathXmlApplicationContext("classpath*:test-context.xml");
        cacheUtil = (ICacheUtil) context.getBean("cacheUtil");
    }

    @Test
    public void testGetAndSetObj() throws Exception {
        Status status = new Status();
        status.setCode(1001);
        status.setMsg("haha");
        cacheUtil.setObject("status", status, 1000);
        Assert.assertNotNull(cacheUtil.getObject("status", Status.class));
    }

    @Test
    public void testExpire() throws Exception {
        Status status = new Status();
        status.setCode(1001);
        status.setMsg("haha");
        cacheUtil.setObject("abc", status, 1000);
        Assert.assertTrue(cacheUtil.expire("abc"));
    }


    @Test
    public void testException(@Capturing final Cache cache) throws Exception {
        new Expectations() {{
            cache.put((String)any, (String)any);
            result = new Exception("error");
            cache.getIfPresent((String)any);
            result = new Exception("error");
        }};
        Status status = new Status();
        status.setCode(1001);
        status.setMsg("haha");
        cacheUtil.setObject("cccc", status, 1000);
        cacheUtil.getObject("cccc", Status.class);
        cacheUtil.expire("cccc");
    }

    @Test
    public void testGetAndSetString() throws Exception {
        cacheUtil.setString("ttt", "12312", 1000);
        Assert.assertNotNull(cacheUtil.getString("ttt"));

    }
}