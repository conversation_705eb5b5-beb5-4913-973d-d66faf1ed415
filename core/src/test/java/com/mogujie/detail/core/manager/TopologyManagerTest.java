package com.mogujie.detail.core.manager;

import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.RouteInfo;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.Platform;
import com.mogujie.service.item.api.basic.ItemReadService;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.external.IcException;
import com.mogujie.service.item.domain.query.QueryItemOptions;
import com.mogujie.service.item.domain.result.BaseResultDO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import junit.framework.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by xiaoyao on 16/9/8.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath*:test-context-manager.xml")
public class TopologyManagerTest {

    private static final Logger LOGGER = LoggerFactory.getLogger(TopologyManagerTest.class);

    protected static final Integer ITEM_ID = 201293545;

    @Autowired
    private TopologyManager manager;

    private ItemReadService itemReadService;



    protected DetailContext getDetailContext() {
        QueryItemOptions queryItemOptions = new QueryItemOptions();
        queryItemOptions.setIncludeItemExtraInfo(true);
        queryItemOptions.setQueryBasicItem(true);
        queryItemOptions.setQueryInventory(true);
        queryItemOptions.setQuerySkuInfo(true);
        queryItemOptions.setIncludeDeleted(false);
        queryItemOptions.setQueryItemDetail(true);
        queryItemOptions.setQueryPresaleDO(true);
        queryItemOptions.setQuerySkuAttribute(true);
//        queryItemOptions.setQueryTagCenter(true);
        try {
            BaseResultDO<ItemDO> ret = itemReadService.queryItemById(ITEM_ID, queryItemOptions);
            List<String> componentIds = new ArrayList<>(3);
            componentIds.add("summary");
            DetailContext context = new DetailContext(new RouteInfo(App.MGJ, Platform.ALL, BizType.FASTBUY, "", ""), componentIds, ITEM_ID.longValue(), null);
            DetailItemDO itemDO = new DetailItemDO(ret.getResult());
            itemDO.setTotalStock(1000L);
            context.setItemDO(itemDO);
            return context;
        } catch (IcException e) {
            LOGGER.error("get item failed : {}", e);
        }
        return null;
    }

    @Before
    public void setUp() throws Exception {
        itemReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ItemReadService.class);
    }

    @Test
    public void testEmit() throws Exception {
        DetailContext context = getDetailContext();
        context.addParam("fastbuyId", IdConvertor.urlToId("1pa").toString());
        Assert.assertNotNull(manager.emit(context));
    }
}