package com.mogujie.detail.core.util;

import com.alibaba.fastjson.JSON;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.service.item.api.basic.ItemImageService;
import com.mogujie.service.item.domain.basic.ItemImageDO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import com.mogujie.tesla.core.ReferConfig;
import org.junit.Assert;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Created by xiaoyao on 16/9/6.
 */
public class CodecUtilTest {

    @Test
    public void testEncryptWithCache() throws Exception {
        Assert.assertTrue(CodecUtil.encryptWithCache("123").equals(CodecUtil.encrypt("123")));
    }

    @Test
    public void testEncrypt() throws Exception {
        String testStr = "abc";
        String encryptedStr = CodecUtil.encrypt(testStr);
    }

    @Test
    public void testId(){
        String ids = "622257742,620327108,623518751,626725703,620311990,620417893,626335798,618092564,618092564,618528650,619103588,625632845,618093448,618941428,618941428,618941428,620322514,625865821,625865821,626232607,626441830,626441830,619040654,624780080,624780080,624780080,624620629,619110209,619110209,617999835,617999835,617999835,617999835,617999835,620194440,618529162,624132697,624132697,623394590,623394590,625745561,617978607,625646085,625734978,618988183,619228906,620261413,624775552,624775552,617976719,624132197,624132197,624132197,618729507,623787525,626163003,626163003,619463836,624620008,618093902,620326181,620326181";
        String[] idStrs = ids.split(",");
        for (String id:idStrs){
            System.out.println(IdConvertor.idToUrl(Integer.parseInt(id)));
        }
    }

    @Test
    public void testIC(){
        ReferConfig<ItemImageService> referConfig = new ReferConfig<>(ItemImageService.class);
        referConfig.setTargetAddress("*************:20031");
        try {
            ItemImageService service = TeslaServiceConsumerFactory.getTeslaServiceConsumer(referConfig);
            Map<Long, List<ItemImageDO>> longListMap = service.listItemImages(Arrays.asList(676266724L));
            System.out.println(JSON.toJSONString(longListMap));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}