package com.mogujie.detail.core.util;

import com.mogujie.service.item.api.basic.ItemReadService;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.query.QueryItemOptions;
import com.mogujie.service.item.domain.result.BaseResultDO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by xiaoyao on 16/9/6.
 */
public class TagUtilTest {

    private ItemReadService itemReadService;

    private static final Long ITEM_ID = 201293545L;

    @Before
    public void setUp() throws Exception {
//        itemReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ItemReadService.class);
    }

    @Test
    public void testIsContainsTag() throws Exception {
        QueryItemOptions options = new QueryItemOptions();
        options.setQueryBasicItem(true);
        BaseResultDO<ItemDO> ret = itemReadService.queryItemById(ITEM_ID, options);
        ItemDO itemDO = ret.getResult();
        Assert.assertFalse(TagUtil.isContainsTag(itemDO, 72));
    }

    @Test
    public void testStream() {
        List<String> list = new ArrayList<>();
        List<String> listB = Arrays.asList("a", "b");
        listB = list.stream()
                .filter(s -> "s".equals(s))
                .collect(Collectors.toList());
        for (String s : listB) {
            System.out.println(s);
        }
    }
}