package com.mogujie.detail.core.util;

import lombok.Getter;
import lombok.Setter;
import org.junit.Assert;
import org.junit.Test;

/**
 * Created by xiaoyao on 16/9/6.
 */
public class JsonUtilTest {

    @Test
    public void testToJson() throws Exception {

        TestOBJ obj = new TestOBJ();
        obj.setCode(111);
        obj.setMsg("ahah");
        String seriObjStr = JsonUtil.toJson(obj);
        Assert.assertEquals(obj, JsonUtil.fromJson(seriObjStr, TestOBJ.class));

    }

    private static class TestOBJ {

        @Getter
        @Setter
        private int code;

        @Getter
        @Setter
        private String msg;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (!(o instanceof TestOBJ)) return false;

            TestOBJ testOBJ = (TestOBJ) o;

            if (getCode() != testOBJ.getCode()) return false;
            return getMsg() != null ? getMsg().equals(testOBJ.getMsg()) : testOBJ.getMsg() == null;

        }

        @Override
        public int hashCode() {
            int result = getCode();
            result = 31 * result + (getMsg() != null ? getMsg().hashCode() : 0);
            return result;
        }
    }
}