<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:beans="http://www.springframework.org/schema/mvc" xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

       <bean id="cacheUtil" class="com.mogujie.detail.core.cache.impl.GuavaCache"/>

       <context:component-scan base-package="com.mogujie.detail"/>
       <context:annotation-config/>
       <beans:annotation-driven/>

       <bean id="detailTemplate" class="com.mogujie.metabase.spring.client.MetabaseClient">
              <property name="appName" value="detail_template"/>
       </bean>

       <bean id="topologyManager" class="com.mogujie.detail.core.manager.TopologyManager">
              <property name="slowThreadPoolShare" value="2"/>
              <property name="taskQueueSize" value="128"/>
       </bean>

       <bean id="detailSwitchConf" class="com.mogujie.metabase.spring.client.MetabaseClient">
              <property name="appName" value="detail-all"/>
       </bean>
       <bean id="confMetabaseClient" class="com.mogujie.metabase.spring.client.MetabaseClient">
              <property name="appName" value="detailConf"/>
       </bean>

       <bean id="spiInterceptor" class="com.mogujie.detail.core.spi.SPIInterceptor"/>
       <aop:config>
              <aop:aspect ref="spiInterceptor">
                     <aop:around pointcut="@within(com.mogujie.detail.core.spi.BizSpi)" method="intercept" />
              </aop:aspect>
       </aop:config>
</beans>