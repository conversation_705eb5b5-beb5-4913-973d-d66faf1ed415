<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

    <!--<bean class="com.mogujie.itemcenter.client.spring.ItemCenterClientSpringInitializer">-->
        <!--<property name="env" value="${ic_env}"/>-->
    <!--</bean>-->

    <!--<bean id="itemReadServiceClient" class=" com.mogujie.itemcenter.client.service.impl.ItemReadServiceClientImpl"/>-->

    <bean id="spiInterceptor" class="com.mogujie.detail.core.spi.SPIInterceptor"/>
    <aop:config>
        <aop:aspect ref="spiInterceptor">
            <aop:around pointcut="@within(com.mogujie.detail.core.spi.BizSpi)" method="intercept" />
        </aop:aspect>
    </aop:config>
    <bean id="commonSwitchUtil" class="com.mogujie.detail.core.util.CommonSwitchUtil"/>

    <bean id="listener" class="com.mogujie.detail.core.manager.TranslatorManager" />
    <bean id="groovyLoader" class="com.mogujie.detail.groovy.NamespacedGroovyLoader">
        <property name="baseDir" value="/groovy" />
        <property name="commonScriptDir" value="common" />
        <property name="checkInterval" value="10000" />
        <property name="groovyRefreshedListener" ref="listener" />
    </bean>
</beans>