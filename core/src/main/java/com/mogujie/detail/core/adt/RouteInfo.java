package com.mogujie.detail.core.adt;

import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.Platform;

/**
 * 路由信息
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/8/8.
 */
public class RouteInfo {

    private App app;

    private Platform platform;

    private BizType bizType;

    private String channelType;

    private String version;

    public RouteInfo(App app, Platform platform, BizType bizType, String channelType, String version) {
        this.app = app;
        this.platform = platform;
        this.bizType = bizType;
        this.channelType = channelType;
        this.version = version;
    }

    public App getApp() {
        return app;
    }

    public void setApp(App app) {
        this.app = app;
    }

    public Platform getPlatform() {
        return platform;
    }

    public void setPlatform(Platform platform) {
        this.platform = platform;
    }

    public BizType getBizType() {
        return bizType;
    }

    public void setBizType(BizType bizType) {
        this.bizType = bizType;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public RouteInfo getDefaultBizRoute() {
        return new RouteInfo(this.app, this.platform, BizType.NORMAL, null,"1.0.0");
    }

    public RouteInfo getDefaultPlatformAndBizRoute() {
        return new RouteInfo(this.app, Platform.ALL, BizType.NORMAL, null,"1.0.0");
    }

    public RouteInfo getAllDefaultRoute() {
        return new RouteInfo(App.MGJ, Platform.ALL, BizType.NORMAL, null,"1.0.0");
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof RouteInfo)) return false;

        RouteInfo routeInfo = (RouteInfo) o;

        if (getApp() != routeInfo.getApp()) return false;
        if (getPlatform() != routeInfo.getPlatform()) return false;
        return getBizType() == routeInfo.getBizType();

    }

    @Override
    public int hashCode() {
        int result = getApp() != null ? getApp().hashCode() : 0;
        result = 31 * result + (getPlatform() != null ? getPlatform().hashCode() : 0);
        result = 31 * result + (getBizType() != null ? getBizType().hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "RouteInfo{" +
                "app=" + app +
                ", platform=" + platform +
                ", bizType=" + bizType +
                '}';
    }
}
