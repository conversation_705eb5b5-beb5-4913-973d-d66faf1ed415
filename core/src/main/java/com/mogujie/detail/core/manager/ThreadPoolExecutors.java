package com.mogujie.detail.core.manager;

import io.netty.util.concurrent.DefaultThreadFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @author: 桌子 <EMAIL>
 * @datetime: 2020-09-16 17:26
 */
public class ThreadPoolExecutors {

    private static final Logger LOGGER = LoggerFactory.getLogger(ThreadPoolExecutors.class);

    public static ThreadPoolExecutor translateExecutorService;

    public static ThreadPoolExecutor fastExecutorService;

    public static ThreadPoolExecutor commonExecutorService;

    static  {
        translateExecutorService = new ThreadPoolExecutor(
                8,
                32,
                180,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<Runnable>(256),
                new DefaultThreadFactory("translate-run-thread"),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
        LOGGER.error("初始化TranslatorManager...");


        fastExecutorService = new ThreadPoolExecutor(
                32,
                64,
                180,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<Runnable>(256),
                new DefaultThreadFactory("fast-module-run-thread"),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
        LOGGER.error("初始化fastExecutorService...");

        commonExecutorService = new ThreadPoolExecutor(
                16,
                64,
                60,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<Runnable>(256),
                new DefaultThreadFactory("itemdataservice-thread"),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
        LOGGER.error("初始化commonExecutorService...");
    }
}
