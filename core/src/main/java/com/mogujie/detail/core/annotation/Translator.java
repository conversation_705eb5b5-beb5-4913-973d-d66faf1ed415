package com.mogujie.detail.core.annotation;

import com.mogujie.detail.core.constant.DefaultType;
import com.mogujie.detail.groovy.annotation.GroovyBean;

import java.lang.annotation.*;

/**
 * Created by xiaoyao on 16/8/8.
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@GroovyBean
public @interface Translator {

    /**
     * 翻译器名称
     * @return
     */
    String name() default "";

    /**
     * 翻译器ID
     * @return
     */
    String id();

    /**
     * 翻译器默认返回值
     * @return
     */
    DefaultType defaultValue() default DefaultType.NULL;
}
