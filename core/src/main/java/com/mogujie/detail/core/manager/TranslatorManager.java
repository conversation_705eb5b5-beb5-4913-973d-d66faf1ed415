package com.mogujie.detail.core.manager;

import com.alibaba.fastjson.JSON;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailContextHolder;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.adt.ModuleDO;
import com.mogujie.detail.core.adt.TranslatorMappingHolder;
import com.mogujie.detail.core.annotation.Translator;
import com.mogujie.detail.core.constant.AlarmGroup;
import com.mogujie.detail.core.constant.DefaultType;
import com.mogujie.detail.core.constant.DetailAlarmType;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.translator.ITranslator;
import com.mogujie.detail.core.util.AlarmUtil;
import com.mogujie.detail.core.util.ContextUtil;
import com.mogujie.detail.groovy.listener.GroovyRefreshedEvent;
import com.mogujie.detail.groovy.listener.GroovyRefreshedListener;
import com.mogujie.metabase.spring.client.MetabaseClient;
import com.mogujie.sentry.type.AlarmType;
import com.mogujie.trace.threadable.LurkerRunnableAdaptor;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.log4j.MDC;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.Advised;
import org.springframework.beans.factory.BeanCreationException;
import org.springframework.beans.factory.BeanFactoryUtils;
import org.springframework.context.ApplicationContext;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Created by xiaoyao on 16/8/8.
 */
public class TranslatorManager implements GroovyRefreshedListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(TranslatorManager.class);

    private final AtomicBoolean loaded = new AtomicBoolean(false);

    private static TranslatorMappingHolder translatorMappingHolder;

    private Map<IModuleDOProvider, Class> moduleDoMap;

    private Map<Class, IModuleDOProvider> doModuleMap;

    private static boolean INITED = false;

    private static final Integer INIT_MODULE_TIMEOUT = 5000;

    private static final Integer MODULE_TIMEOUT = 1000;

    private static final String TRANSLATE_METHOD_NAME = "translate";

    @Resource(name = "confMetabaseClient")
    private MetabaseClient metabaseClient;

    @Override
    public void groovyRefreshed(GroovyRefreshedEvent groovyRefreshedEvent) {
        try {
            if (loaded.compareAndSet(false, true)) {
                doModuleMap = new ConcurrentHashMap<>(10);
                moduleDoMap = new ConcurrentHashMap<>(10);
                scanAllModule(groovyRefreshedEvent.getAncestorContext());
            }
            scanAllTranslator(groovyRefreshedEvent.getNamespacedContextMap());
        } catch (DetailException e) {
            AlarmUtil.sentryAlarm(metabaseClient.get(AlarmGroup.INTERNAL_ALARM_MEMBER),
                    "详情页dsl层translator映射关系刷新失败", e.getMessage()
                    , AlarmType.TT, DetailAlarmType.GroovyMappingError);
            LOGGER.error(e.getMessage());
            throw new BeanCreationException(e.getMessage());
        }
    }

    public Map<String, Object> translate(Map<Class, Future<ModuleDO>> doMap, DetailContext context) {
        List<String> translateIdList = context.getComponentIds();
        TranslatorMappingHolder mappingHolder = context.getTranslatorMappingHolder();
        if (!CollectionUtils.isEmpty(translateIdList)) {
            ConcurrentMap<String, Object> voMap = new ConcurrentHashMap<>(translateIdList.size());
            CountDownLatch latch = new CountDownLatch(translateIdList.size());
            for (String translateId : translateIdList) {
                Method method = mappingHolder.getTranslateMethodMap().get(translateId);
                ITranslator translator = mappingHolder.getTranslatorMap().get(translateId);
                DefaultType defaultType = mappingHolder.getTranslatorDefaultMap().get(translateId);
                if (null == method || null == translator) {
                    AlarmUtil.sentryAlarm(AlarmGroup.DSL_ALARM_MEMBER, "详情页dsl无效的translator, ", "invalid translator id: " + translateId, AlarmType.TT, DetailAlarmType.InvalidTranslator);
                    LOGGER.warn("invalid translatorId : {}", translateId);
                    latch.countDown();
                    continue;
                }
                List<Class> dependDoClsList = mappingHolder.getTranslatorDoMap().get(translateId);
                Future<ModuleDO>[] doArr = null;
                if (!CollectionUtils.isEmpty(dependDoClsList)) {
                    doArr = new Future[dependDoClsList.size()];
                    for (int i = 0; i < dependDoClsList.size(); i++) {
                        doArr[i] = doMap.get(dependDoClsList.get(i));
                    }
                }
                TranslateParam translateParam = new TranslateParam(translateId, translator, defaultType, method, doArr, latch, voMap, context, metabaseClient.get(AlarmGroup.DSL_ALARM_MEMBER));
                TranslateTask task = new TranslateTask(translateParam);
                ThreadPoolExecutors.translateExecutorService.submit(new LurkerRunnableAdaptor(task));
            }
            try {
                latch.await(3, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                LOGGER.error("wait for async emit failed {}", e);
            }
            return voMap;
        }
        return null;
    }

    public Map<Class, IModuleDOProvider> getAllDependModules(DetailContext context) {
        List<String> translateIdList = context.getComponentIds();  //mgj/app/nomal.itemInfo
        TranslatorMappingHolder mappingHolder = context.getTranslatorMappingHolder();
        if (!CollectionUtils.isEmpty(translateIdList)) {
            Map<Class, IModuleDOProvider> moduleMap = new HashMap<>(translateIdList.size());
            for (String id : translateIdList) {
                List<IModuleDOProvider> dependModuleList = mappingHolder.getTranslatorModuleMap().get(id);
                if (null != dependModuleList) {
                    for (IModuleDOProvider module : dependModuleList) {
                        Class doCls = moduleDoMap.get(module);
                        if (!moduleMap.containsKey(doCls)) {
                            moduleMap.put(doCls, module);
                        }
                    }
                }
            }
            return moduleMap;
        }
        return null;
    }

    private void scanAllModule(final ApplicationContext context) throws DetailException {
        Map<String, IModuleDOProvider> moduleMap = context.getBeansOfType(IModuleDOProvider.class);
        for (Map.Entry<String, IModuleDOProvider> beanEntry : moduleMap.entrySet()) {
            IModuleDOProvider module = beanEntry.getValue();
            module.init();
            Type type = module.getClass().getGenericInterfaces()[0];
            Type doType = ((ParameterizedType) type).getActualTypeArguments()[0];
            doModuleMap.put((Class) doType, module);
            moduleDoMap.put(module, (Class) doType);
        }
    }

    private void scanAllTranslator(final Map<String, ApplicationContext> contextMap) throws DetailException {
        TranslatorMappingHolder translatorMappingHolder = new TranslatorMappingHolder();
        translatorMappingHolder.setTranslatorMap(new ConcurrentHashMap<String, ITranslator>(10));
        translatorMappingHolder.setTranslatorModuleMap(new ConcurrentHashMap<String, List<IModuleDOProvider>>(10));
        translatorMappingHolder.setTranslateMethodMap(new ConcurrentHashMap<String, Method>(10));
        translatorMappingHolder.setTranslatorDoMap(new ConcurrentHashMap<String, List<Class>>(10));
        translatorMappingHolder.setTranslatorDefaultMap(new ConcurrentHashMap<String, DefaultType>(10));

        for (Map.Entry<String, ApplicationContext> entry : contextMap.entrySet()) {
            String namespace = entry.getKey();
            if (namespace.endsWith("/vo") || namespace.endsWith("/common") || namespace.equals("/base")) {
                continue;
            }
            ApplicationContext context = entry.getValue();
            Map<String, ITranslator> translatorBeanMap = BeanFactoryUtils.beansOfTypeIncludingAncestors(context, ITranslator.class);
            for (Map.Entry<String, ITranslator> beanEntry : translatorBeanMap.entrySet()) {
                ITranslator translator = beanEntry.getValue();
                Class targetTranslatorCls;
                if (Advised.class.isAssignableFrom(translator.getClass())) {
                    targetTranslatorCls = ((Advised) translator).getTargetSource().getTargetClass();
                } else {
                    targetTranslatorCls = translator.getClass();
                }
                Translator translatorAnno = (Translator) targetTranslatorCls.getAnnotation(Translator.class);

                if (null == translatorAnno) {
                    throw new DetailException("translator" + translator.getClass() + "doesn't annotate with @Translator");
                }
                String traslatorKey = namespace + "." + translatorAnno.id();
                if (translatorMappingHolder.getTranslatorMap().containsKey(traslatorKey)) {
                    throw new DetailException("translator " + traslatorKey + " alread exist");
                }
                translatorMappingHolder.getTranslatorMap().put(traslatorKey, translator);
                translatorMappingHolder.getTranslatorDefaultMap().put(traslatorKey, translatorAnno.defaultValue());
                Type type = targetTranslatorCls.getGenericInterfaces()[0];
                Type[] parameterTypes = ((ParameterizedType) type).getActualTypeArguments();
                List<IModuleDOProvider> dependentModuleList = new ArrayList<>(parameterTypes.length);
                List<Class> dependDoList = new ArrayList<>(parameterTypes.length);
                for (int i = 0; i < parameterTypes.length - 1; i++) {
                    IModuleDOProvider module = doModuleMap.get(parameterTypes[i]);
                    if (null == module) {
                        throw new DetailException("cann't found dependent module for do : " + parameterTypes[i]);
                    }
                    dependDoList.add((Class) parameterTypes[i]);
                    dependentModuleList.add(module);
                }
                for (Method method : translator.getClass().getMethods()) {
                    if (method.getName().equals(TRANSLATE_METHOD_NAME)) {
                        translatorMappingHolder.getTranslateMethodMap().put(traslatorKey, method);
                    }
                }
                LOGGER.error("traslatorKey:" + traslatorKey + " dependentModuleList" + JSON.toJSONString(dependentModuleList) + " parameterTypes:" + JSON.toJSONString(parameterTypes));
                translatorMappingHolder.getTranslatorDoMap().put(traslatorKey, dependDoList);
                translatorMappingHolder.getTranslatorModuleMap().put(traslatorKey, dependentModuleList);
            }
        }
        TranslatorManager.translatorMappingHolder = translatorMappingHolder;
    }

    public static TranslatorMappingHolder getTranslatorMappingHolder() {
        return translatorMappingHolder;
    }

    public static class TranslateTask implements Runnable {

        private TranslateParam param;

        public TranslateTask(TranslateParam param) {
            this.param = param;
        }

        @Override
        public void run() {
            Object value = null;
            String traslatorId = param.getTranslatorId();
            String traslatorVOKey = traslatorId.substring(traslatorId.indexOf(".") + 1, traslatorId.length());

            Object[] args = null;
            try {
                MDC.put("topo", param.getContext().getParam("template"));
                MDC.put("iid", IdConvertor.idToUrl(param.getContext().getItemId()));
                MDC.put("position", param.getTranslatorId());
                DetailContextHolder.set(param.getContext());
                args = getReadArgs(param.getArgs());
                if (ContextUtil.isDebugMode(param.getContext()) && args != null && args.length > 0) {
                    Type type = param.getMethod().getDeclaringClass().getGenericInterfaces()[0];
                    Type[] parameterTypes = ((ParameterizedType) type).getActualTypeArguments();
                    for (int i = 0; i < args.length; i++) {
                        param.getContext().addDebugObject(((Class) parameterTypes[i]).getSimpleName(), args[i] == null ? "null" : args[i]);
                    }
                }

                value = param.getMethod().invoke(param.getTranslator(), args);
                if (null != value) {
                    this.param.getVoMap().put(traslatorVOKey, value);
                }
            } catch (Throwable e) {
                //dsl层报警提示异常的位置、异常栈
                String eStr = e.getMessage();
                if (e.getCause() != null) {
                    eStr += "\nCaused by: " + e.getCause().toString();
                    for (StackTraceElement stackTraceElement : e.getCause().getStackTrace()) {
                        eStr += "\n    " + stackTraceElement.toString();
                    }
                }
                AlarmUtil.sentryAlarm(param.getDslAlarmMember(),
                        "详情页dsl执行异常",
                        "iid: " + MDC.get("iid")
                                + ", template: " + MDC.get("topo")
                                + ", translator id: " + param.getTranslatorId() + ".\n" + eStr
                        , AlarmType.TT, DetailAlarmType.DslExecuteError);
                LOGGER.error("itemId : " + param.getContext().getItemId() + ", " + param.getTranslatorId()
                        + " translate failed  method:{}, args:{}", JSON.toJSONString(param.getMethod()),JSON.toJSONString(args), e);
            } finally {
                //设置dsl脚本的默认返回值
                if (value == null) {
                    Object defaultVal = null;
                    switch (param.defaultType) {
                        case EMPTY_MAP:
                            defaultVal = Collections.EMPTY_MAP;
                            break;
                        case EMPTY_STRING:
                            defaultVal = "";
                            break;
                        case FALSE:
                            defaultVal = false;
                            break;
                    }
                    if (defaultVal != null) {
                        this.param.getVoMap().put(traslatorVOKey, defaultVal);
                    }
                }
                MDC.clear();
                DetailContextHolder.clear();
            }
            this.param.getLatch().countDown();
        }

        private Object[] getReadArgs(Future<ModuleDO>[] futureArgs) {
            if (null == futureArgs || futureArgs.length == 0) {
                return null;
            }
            Object[] args = new Object[futureArgs.length];
            for (int i = 0; i < futureArgs.length; i++) {
                Future<ModuleDO> futureArg = futureArgs[i];
                if (null == futureArg) {
                    args[i] = null;
                    continue;
                }
                Object arg = null;
                try {
                    arg = futureArg.get(INITED ? MODULE_TIMEOUT : INIT_MODULE_TIMEOUT, TimeUnit.MILLISECONDS);
                } catch (InterruptedException | ExecutionException | TimeoutException e) {
                    LOGGER.error("Get Module DO failed : {}", futureArg, e);
                }
                args[i] = arg;
            }
            INITED = true;
            return args;
        }
    }

    private class TranslateParam {

        public TranslateParam(String translatorId, ITranslator translator, DefaultType defaultType, Method method, Future[] args, CountDownLatch latch, ConcurrentMap<String, Object> voMap, DetailContext context, String dslAlarmMember) {
            this.translatorId = translatorId;
            this.translator = translator;
            this.defaultType = defaultType;
            this.method = method;
            this.args = args;
            this.voMap = voMap;
            this.latch = latch;
            this.context = context;
            this.dslAlarmMember = dslAlarmMember;
        }

        @Getter
        @Setter
        private String translatorId;

        @Getter
        @Setter
        private ITranslator translator;

        @Getter
        @Setter
        private DefaultType defaultType;

        @Getter
        @Setter
        private Method method;

        @Getter
        @Setter
        private Future[] args;

        @Getter
        @Setter
        private CountDownLatch latch;

        @Getter
        @Setter
        private ConcurrentMap<String, Object> voMap;

        @Getter
        @Setter
        private DetailContext context;

        @Getter
        @Setter
        private String dslAlarmMember;
    }
}
