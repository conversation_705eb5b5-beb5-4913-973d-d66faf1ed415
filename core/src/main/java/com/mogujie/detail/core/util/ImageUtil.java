package com.mogujie.detail.core.util;

import com.mogujie.detail.core.adt.DetailContextHolder;
import com.mogujie.dragon.bigdata.CDNBalance.CDNBalance;
import com.mogujie.dragon.bigdata.CDNBalance.CDNException;
import com.mogujie.metabase.client.MetaClient;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by xiaoyao on 15/11/24.
 */
public class ImageUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(ImageUtil.class);

    private static MetaClient metaClient;

    static {
        try {
            metaClient = MetaClient.client("detailConf");
        } catch (Throwable e) {
            LOGGER.error("init meta client failed : ", e);
        }
    }

    public static String img(String srcFile) {
        if (StringUtils.isBlank(srcFile)) {
            return "";
        }

        try {
            boolean useHttps = useHttps();
            String path = CDNBalance.smartGetImg(srcFile, useHttps);
//            if (useHttps) {
//                return path.replaceFirst("https:", "");
//            }
            return path;
        } catch (CDNException e) {
            LOGGER.error("get realPath failed : {} , ", srcFile, e);
        }
        return "";
    }

    public static final Boolean useHttps() {
        try {
            if (null != metaClient.getConfigurations()) {
                if (null == DetailContextHolder.get() || null == DetailContextHolder.get().getRouteInfo()) {
                    return false;
                }
                String platform = DetailContextHolder.get().getRouteInfo().getPlatform().toString();
                String value = metaClient.getConfigurations().get("imgUseHttps_" + platform);
                if (!StringUtils.isEmpty(value)) {
                    return Boolean.valueOf(value);
                } else if ("m".equals(DetailContextHolder.get().getParam("appPlat"))) {
                    return true;
                }
            }
        } catch (Throwable e) {
            LOGGER.warn("get key error : ", e);
        }
        return false;
    }

}

