package com.mogujie.detail.core.util;

import com.mogujie.commons.utils.EnvUtil;
import com.mogujie.detail.core.constant.DetailAlarmType;
import com.mogujie.sentry.type.AlarmType;
import com.mogujie.sentry.util.Alarm;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;

/**
 * 同一种报警30秒只报警一次
 * Created by anshi on 17/6/30.
 */
public class AlarmUtil {
    private static ArrayList<Long> lastAlertTime = new ArrayList<>();

    private static final long alertInterval = 60000;

    static {
        for (int i = 0; i < DetailAlarmType.values().length; i++) {
            lastAlertTime.add(0L);
        }
    }

    public static void sentryAlarm(String to, String title, String message, AlarmType type, DetailAlarmType detailAlarmType) {
        long lastAlert = lastAlertTime.get(detailAlarmType.getIndex());
        if (EnvUtil.isOfflineEnv() && type == AlarmType.TT) {
            type = AlarmType.MOBILE;
        }
        // 时间到了才尝试获取锁，避免无效的锁竞争
        if (lastAlert + alertInterval < System.currentTimeMillis()) {
            synchronized (detailAlarmType) {
                lastAlert = lastAlertTime.get(detailAlarmType.getIndex());
                if (lastAlert + alertInterval < System.currentTimeMillis()) {
                    String env = "";
                    try {
                        env = "【" + EnvUtil.currentEnv().toString() + ": " + InetAddress.getLocalHost().getHostAddress() + "】";
                    } catch (UnknownHostException e) {
                    }
                    Alarm.getInstance().sentryAlarm(to, env + title, message, type);
                    lastAlertTime.set(detailAlarmType.getIndex(), System.currentTimeMillis());
                }
            }
        }
    }
}
