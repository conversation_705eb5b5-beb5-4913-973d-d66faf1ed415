package com.mogujie.detail.core.task;

import com.mogujie.detail.core.adt.DetailContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CountDownLatch;

/**
 * Created by xiaoyao on 16/10/20.
 */
public abstract class AbstractCollectDataTask implements Runnable {

    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractCollectDataTask.class);

    protected CountDownLatch countDownLatch;

    protected DetailContext context;

    public AbstractCollectDataTask(DetailContext context) {
        this.context = context;
    }

    public void setCountDownLatch(CountDownLatch countDownLatch) {
        this.countDownLatch = countDownLatch;
    }

    @Override
    public void run() {
        try {
            collect();
        } catch (Throwable e) {
            LOGGER.error("run collect data task failed {}", e);
        } finally {
            this.countDownLatch.countDown();
        }
    }

    public abstract void collect();
}
