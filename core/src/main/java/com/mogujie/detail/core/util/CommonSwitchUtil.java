package com.mogujie.detail.core.util;

import com.mogujie.metabase.spring.client.MetabaseClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.ThreadLocalRandom;

/**
 * Created by anshi on 16/10/10.
 */
@Component
public class CommonSwitchUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommonSwitchUtil.class);

    @Resource(name = "confMetabaseClient")
    private MetabaseClient metabaseClient;

    public boolean isOn(String key) {
        try {
            Boolean ret = metabaseClient.getBoolean(key);
            return null == ret ? true : ret;
        } catch (Throwable e) {
            LOGGER.warn("get value from metabase failed, key : {}", key);
        }
        return true;
    }

    /**
     * 0~99的随机数n，返回是否value > n
     * 配置<=0，即必定返回false
     * 配置>=100，即必定返回true
     *
     * @param key
     * @return
     */
    public boolean isRatioOn(String key) {
        try {
            int val = metabaseClient.getInteger(key);
            int testVal = ThreadLocalRandom.current().nextInt(100);
            return val > testVal;
        } catch (Throwable e) {
            LOGGER.warn("get value from metabase failed, key : {}", key);
        }
        return true;
    }
}