package com.mogujie.detail.core.adt;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Method;
import java.util.Map;

/**
 * 修改translator的依赖关系只能重启应用
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/8/8.
 */
public class Topology {

    private static final Logger LOGGER = LoggerFactory.getLogger(Topology.class);

    public final Map<String, Method> moduleMap;

    public final Map<String, Method> translatorMap;

    public String key;

    public Topology(Map<String, Method> moduleMap, Map<String, Method> translatorMap) {
        this.moduleMap = moduleMap;
        this.translatorMap = translatorMap;
        this.key = getKey();
    }

    private String getKey() {
        if (null == translatorMap || translatorMap.isEmpty()) {
            LOGGER.error("empty topology");
            return "";
        }
        return StringUtils.join(translatorMap.keySet(), "_");
    }


}
