package com.mogujie.detail.core.cache;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 15/12/15.
 */
public interface ICacheUtil {

    /**
     * 获取函数返回值对象
     *
     * @param key
     * @param clz
     * @return
     */
    public <T> T getObject(String key, Class<T> clz);


    /**
     * 设置函数返回值
     *
     * @param key
     * @param value
     * @param expireSeconds
     * @return
     */
    public boolean setObject(String key, Object value, int expireSeconds);

    /**
     * 获取string类型的value
     *
     * @param key
     * @return
     */
    public String getString(String key);

    /**
     * 设置string类型的value
     *
     * @param key
     * @param value
     * @param expireSeconds
     * @return
     */
    public boolean setString(String key, String value, int expireSeconds);

    /**
     * 失效键值
     *
     * @param key
     * @return
     */
    public boolean expire(String key);


}
