package com.mogujie.detail.core.adt;

/**
 * Created by anshi on 18/1/15.
 */
public class ChannelMeta {

    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 渠道id
     */
    private Integer channelId;

    private Short outType;

    private String promotionCode;

    /**
     * 标key
     */
    private String tagKey;

    /**
     * 渠道下单id
     */
    private String orderChannelId;

    /**
     * 渠道价格描述
     */
    private String priceDesc;

    /**
     * 该渠道是否需要普通售卖价（放在channelInfoDO中）
     */
    private boolean needNormalPrice;

    /**
     * （渠到标的情况下）itemBaseDO、skuDO中的价格是否强制刷成渠道价
     */
    private boolean forceChannelPrice;

    /**
     * 若活动未生效（取不到标的情况），itemBaseDO、skuDO中的价格设置为普通现售价
     */
    private boolean normalPriceDefault;

    /**
     * 是否提前获取未来生效的商品标（预热等场景需要）
     */
    private boolean queryFutureTag;

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public Integer getChannelId() {
        return channelId;
    }

    public void setChannelId(Integer channelId) {
        this.channelId = channelId;
    }

    public Short getOutType() {
        return outType;
    }

    public void setOutType(Short outType) {
        this.outType = outType;
    }

    public String getPromotionCode() {
        return promotionCode;
    }

    public void setPromotionCode(String promotionCode) {
        this.promotionCode = promotionCode;
    }

    public String getTagKey() {
        return tagKey;
    }

    public void setTagKey(String tagKey) {
        this.tagKey = tagKey;
    }

    public String getOrderChannelId() {
        return orderChannelId;
    }

    public void setOrderChannelId(String orderChannelId) {
        this.orderChannelId = orderChannelId;
    }

    public String getPriceDesc() {
        return priceDesc;
    }

    public void setPriceDesc(String priceDesc) {
        this.priceDesc = priceDesc;
    }

    public boolean isNeedNormalPrice() {
        return needNormalPrice;
    }

    public void setNeedNormalPrice(boolean needNormalPrice) {
        this.needNormalPrice = needNormalPrice;
    }

    public boolean isForceChannelPrice() {
        return forceChannelPrice;
    }

    public void setForceChannelPrice(boolean forceChannelPrice) {
        this.forceChannelPrice = forceChannelPrice;
    }

    public boolean isNormalPriceDefault() {
        return normalPriceDefault;
    }

    public void setNormalPriceDefault(boolean normalPriceDefault) {
        this.normalPriceDefault = normalPriceDefault;
    }

    public boolean isQueryFutureTag() {
        return queryFutureTag;
    }

    public void setQueryFutureTag(boolean queryFutureTag) {
        this.queryFutureTag = queryFutureTag;
    }
}
