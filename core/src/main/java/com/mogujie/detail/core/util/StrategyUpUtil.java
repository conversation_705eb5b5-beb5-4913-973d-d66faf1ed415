package com.mogujie.detail.core.util;

import org.apache.commons.lang3.StringUtils;

/**
 * 战略升级
 *
 * @auther huasheng
 * @time 19/3/29 16:38
 */
public class StrategyUpUtil {


    public static String upUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return url;
        }
        boolean isUp = DetailWebMetabase.getBoolean("strategy_up_switch", false);
        if (isUp) {
            url = url.replace("mogujie.com", "mogu.com");
        }
        return url;

    }
}
