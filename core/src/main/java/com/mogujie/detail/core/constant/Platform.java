package com.mogujie.detail.core.constant;

/**
 * Created by xiaoyao on 16/3/24.
 */
public enum Platform {

    /**
     * pc
     */
    PC(1),

    /**
     * h5
     */
    H5(2),

    /**
     * 全平台可用
     */
    ALL(7),

    /**
     * app
     */
    APP(3),

    /**
     * 微信
     */
    WX(5),

    /**
     * xcx
     */
    XCX(6);

    private int code;

    private Platform(int code) {
        this.code = code;
    }

    public static Platform getPlatform(int code) {
        switch (code) {
            case 1:
                return PC;
            case 2:
                return H5;
            case 3:
            case 4:
                return APP;
            case 5:
                return WX;
            case 6:
                return XCX;
            default:
                return ALL;
        }
    }

}
