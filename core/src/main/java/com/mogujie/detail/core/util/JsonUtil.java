package com.mogujie.detail.core.util;

import com.google.gson.Gson;

/**
 * Created by xiaoyao on 15/11/24.
 */
public class JsonUtil {

    /**
     * 对象转到json
     *
     * @param obj
     * @return
     */
    public static String toJson(Object obj) {
        Gson gson = new Gson();
        return gson.toJson(obj);
    }

    /**
     * json转到对象
     *
     * @param jsonStr
     * @param cls
     * @param <T>
     * @return
     */
    public static <T> T fromJson(String jsonStr, Class<T> cls) {
        Gson gson = new Gson();
        return (T) gson.fromJson(jsonStr, cls);
    }

}
