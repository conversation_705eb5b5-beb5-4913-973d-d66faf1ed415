//package com.mogujie.detail.core.task;
//
//import com.mogujie.detail.core.adt.DetailContext;
//import com.mogujie.detail.core.adt.DetailItemDO;
//import com.mogujie.detail.core.exception.DetailException;
//import com.mogujie.service.shopcenter.client.ShopReadServiceClient;
//import com.mogujie.service.shopcenter.domain.entity.ShopInfo;
//import com.mogujie.service.shopcenter.result.Result;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
///**
// * Created by xiaoyao on 16/10/20.
// */
//public class CollectShopInfoTask extends AbstractCollectDataTask {
//
//    private static final Logger LOGGER = LoggerFactory.getLogger(CollectShopInfoTask.class);
//
//
//    public CollectShopInfoTask(DetailContext context) throws DetailException {
//        super(context);
//    }
//
//    @Override
//    public void collect() {
//        try {
//            DetailItemDO itemDO = context.getItemDO();
//            Result<ShopInfo> ret = ShopReadServiceClient.getShopByShopId(itemDO.getShopId());
//            if (null != ret && ret.isSuccess()) {
//                itemDO.setShopInfo(ret.getData());
//            }
//        } catch (Throwable e) {
//            LOGGER.error("collect shopinfo failed : {}", e);
//        }
//    }
//}
