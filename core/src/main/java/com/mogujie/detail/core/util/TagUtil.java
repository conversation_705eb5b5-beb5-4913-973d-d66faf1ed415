package com.mogujie.detail.core.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.basic.ItemTagDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * tag相关
 * Created by xiaoyao on 16/8/16.
 */
public class TagUtil {
    private static final Logger logger = LoggerFactory.getLogger(TagUtil.class);

    public static boolean isContainsTag(List<ItemTagDO> itemTags, Integer numTag) {
        try {
            if (CollectionUtils.isEmpty(itemTags) || numTag == null) {
                return false;
            }
            for (ItemTagDO tag : itemTags) {
                if ("tags".equals(tag.getTagKey()) && numTag.toString().equals(tag.getTagValue())) {
                    return true;
                }
            }
        } catch (Throwable e) {
        }
        return false;
    }

    public static boolean isContainsTag(ItemDO item, Integer tag) {
        if (null != item && null != item.getJsonExtra() && null != tag) {
            String extra = item.getJsonExtra();
            if (extra.equals("[]")) {
                return false;
            } else {
                try {
                    JSONObject e = JSON.parseObject(extra);
                    String tags = e.getString("tags");
                    String[] tagArr = StringUtils.split(tags, ",");
                    if (null != tagArr) {
                        String[] arr$ = tagArr;
                        int len$ = tagArr.length;

                        for (int i$ = 0; i$ < len$; ++i$) {
                            String tagStr = arr$[i$];
                            if (tag.intValue() == NumberUtils.toInt(tagStr, 0)) {
                                return true;
                            }
                        }
                    }
                } catch (Exception var10) {
                    ;
                }

                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * 将extra字段映射为map
     *
     * @param extra
     * @return
     */
    public static Map<String, String> getExtraInfo(String extra) {
        if (org.apache.commons.lang.StringUtils.isEmpty(extra)) {
            return null;
        }
        try {
            Gson gson = new Gson();
            return gson.fromJson(extra, HashMap.class);
        } catch (Exception e) {
            logger.debug("decode extrainfo failed, {}", e);
        }
        return null;
    }

    /**
     * 判断是不是福利购商品
     *
     * @param extra
     * @return
     */
    public static boolean isFlItem(String extra) {
        try {
            if (org.apache.commons.lang.StringUtils.isEmpty(extra)) {
                return false;
            }
            Gson gson = new Gson();
            Map<String, String> extraInfo = gson.fromJson(extra, HashMap.class);
            if (null == extraInfo || extraInfo.isEmpty()) {
                return false;
            }
            String fl = extraInfo.get("fl");
            if (org.apache.commons.lang.StringUtils.isBlank(fl)) {
                return false;
            }

            String[] flPairs = fl.split("\\|");
            for (String flPair : flPairs) {
                String[] pair = flPair.split(":");
                if (pair.length != 2) {
                    continue;
                }
                if ("st".equals(pair[0])) {
                    if ((System.currentTimeMillis() / 1000) < Integer.parseInt(pair[1])) {
                        return false;
                    }
                } else if ("et".equals(pair[0])) {
                    if ((System.currentTimeMillis() / 1000) > Integer.parseInt(pair[1])) {
                        return false;
                    }
                }
            }
            return true;
        } catch (Throwable e) {
            logger.error("invalid extra : {}", extra);
        }
        return false;
    }

    /**
     * 将kv标格式的解析成为map形式，如：shango: "st:1569636000|et:1569895199|ws:0|we:0|ai:20693880|uid:20693880|at:0|pp:6200"
     *
     * @param kvString 原始的kv标字符串
     * @return
     */
    public static Map<String, String> parseKVTags(String kvString) {
        if (StringUtils.isBlank(kvString)) {
            return null;
        }
        List<String> kvList = com.mogujie.detail.core.util.StringUtils.string2List(kvString, "\\|", String.class);
        if (CollectionUtils.isEmpty(kvList)) {
            return null;
        }
        Map<String, String> kvTagMap = Maps.newHashMap();
        kvList.stream()
                .filter(StringUtils::isNotBlank)
                .forEach(singleKV -> {
                    String[] keyAndValue = singleKV.split(":");
                    if (keyAndValue.length != 2) {
                        return;
                    }
                    kvTagMap.put(keyAndValue[0], keyAndValue[1]);
                });
        return kvTagMap;
    }

}
