package com.mogujie.detail.core.adt;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 详情页上下文
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/8/8.
 */
public class DetailContext {

    private static final Logger LOGGER = LoggerFactory.getLogger(DetailContext.class);

    private DetailItemDO itemDO;

    private Long itemId;

    private final ConcurrentMap<String, String> params;

    private final ConcurrentMap<String, Object> context;

    private final List<String> componentIds;

    private Long loginUserId;

    private RouteInfo routeInfo;

    private boolean isDyn;

    private String clientIp;

    private boolean isError;

    private String osVersion;

    private ChannelMeta channelMeta;

    private ChannelTag channelTag;

    private TranslatorMappingHolder translatorMappingHolder;

    /**
     * debug数据
     */
    private Map<String, Object> debugObject;

    /**
     * 是否是订单快照
     */
    private boolean isOrderSnap = Boolean.FALSE;

    public DetailContext(final RouteInfo routeInfo, final List<String> componentIds, final Long itemId, TranslatorMappingHolder translatorMappingHolder) {
        this(routeInfo, componentIds, new ConcurrentHashMap<String, String>(3), itemId, translatorMappingHolder);
    }

    public DetailContext(final RouteInfo routeInfo, final List<String> componentIds, final ConcurrentMap<String, String> params, final Long itemId, TranslatorMappingHolder translatorMappingHolder) {
        this(routeInfo, componentIds, params, new ConcurrentHashMap<String, Object>(3), itemId, translatorMappingHolder);
    }

    public DetailContext(final RouteInfo routeInfo, final List<String> componentIds, final ConcurrentMap<String, String> params, final ConcurrentMap<String, Object> context, final Long itemId, TranslatorMappingHolder translatorMappingHolder) {
        this.itemId = itemId;
        this.routeInfo = routeInfo;
        this.componentIds = componentIds;
        this.params = params;
        this.context = context;
        this.translatorMappingHolder = translatorMappingHolder;
    }

    public DetailContext(final RouteInfo routeInfo, final List<String> componentIds, TranslatorMappingHolder translatorMappingHolder) {
        this.routeInfo = routeInfo;
        this.componentIds = componentIds;
        this.params = new ConcurrentHashMap<>(3);
        this.context = new ConcurrentHashMap<>(3);
        this.translatorMappingHolder = translatorMappingHolder;
    }

    public void addContext(String key, Object val) {
        try {
            this.context.put(key, val);
        } catch (NullPointerException e) {
            LOGGER.error("empty context {}:{}", key, val);
        }
    }

    public void addParam(String key, String value) {
        try {
            this.params.put(key, value);
        } catch (NullPointerException e) {
            LOGGER.error("empty param {}:{}", key, value);
        }
    }

    public Object getContext(String key) {
        return this.context.get(key);
    }

    public String getParam(String key) {
        return this.params.get(key);
    }

    public Map<String, String> getParams() {
        return params;
    }

    public Map<String, Object> getContexts() {
        return context;
    }

    public DetailItemDO getItemDO() {
        return this.itemDO;
    }

    public void setItemDO(DetailItemDO itemDO) {
        this.itemDO = itemDO;
    }

    public List<String> getComponentIds() {
        return componentIds;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public boolean isDyn() {
        return isDyn;
    }

    public void setDyn(boolean dyn) {
        isDyn = dyn;
    }

    public Long getLoginUserId() {
        return loginUserId;
    }

    public void setLoginUserId(Long loginUserId) {
        this.loginUserId = loginUserId;
    }

    public RouteInfo getRouteInfo() {
        return routeInfo;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public boolean isError() {
        return isError;
    }

    public void setError(boolean error) {
        isError = error;
    }

    public String getOsVersion() {
        return osVersion;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion;
    }

    public ChannelMeta getChannelMeta() {
        return channelMeta;
    }

    public void setChannelMeta(ChannelMeta channelMeta) {
        this.channelMeta = channelMeta;
    }

    public ChannelTag getChannelTag() {
        return channelTag;
    }

    public void setChannelTag(ChannelTag channelTag) {
        this.channelTag = channelTag;
    }

    public boolean isOrderSnap() {
        return isOrderSnap;
    }

    public void setOrderSnap(boolean orderSnap) {
        isOrderSnap = orderSnap;
    }

    public TranslatorMappingHolder getTranslatorMappingHolder() {
        return translatorMappingHolder;
    }

    public void setTranslatorMappingHolder(TranslatorMappingHolder translatorMappingHolder) {
        this.translatorMappingHolder = translatorMappingHolder;
    }

    public Map<String, Object> getDebugObject() {
        return debugObject;
    }

    public void setDebugObject(Map<String, Object> debugObject) {
        this.debugObject = debugObject;
    }

    public void addDebugObject(String key, Object obj) {
        if (debugObject == null) {
            synchronized (this) {
                if (debugObject == null) {
                    debugObject = new ConcurrentHashMap<>();
                }
            }
        }
        debugObject.put(key, obj);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        DetailContext that = (DetailContext) o;

        if (isDyn != that.isDyn) return false;
        if (isError != that.isError) return false;
        if (itemDO != null ? !itemDO.equals(that.itemDO) : that.itemDO != null) return false;
        if (itemId != null ? !itemId.equals(that.itemId) : that.itemId != null) return false;
        if (params != null ? !params.equals(that.params) : that.params != null) return false;
        if (context != null ? !context.equals(that.context) : that.context != null) return false;
        if (componentIds != null ? !componentIds.equals(that.componentIds) : that.componentIds != null) return false;
        if (loginUserId != null ? !loginUserId.equals(that.loginUserId) : that.loginUserId != null) return false;
        if (routeInfo != null ? !routeInfo.equals(that.routeInfo) : that.routeInfo != null) return false;
        if (clientIp != null ? !clientIp.equals(that.clientIp) : that.clientIp != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = itemDO != null ? itemDO.hashCode() : 0;
        result = 31 * result + (itemId != null ? itemId.hashCode() : 0);
        result = 31 * result + (params != null ? params.hashCode() : 0);
        result = 31 * result + (context != null ? context.hashCode() : 0);
        result = 31 * result + (componentIds != null ? componentIds.hashCode() : 0);
        result = 31 * result + (loginUserId != null ? loginUserId.hashCode() : 0);
        result = 31 * result + (routeInfo != null ? routeInfo.hashCode() : 0);
        result = 31 * result + (isDyn ? 1 : 0);
        result = 31 * result + (clientIp != null ? clientIp.hashCode() : 0);
        result = 31 * result + (isError ? 1 : 0);
        return result;
    }

    @Override
    public String toString() {
        return "DetailContext{" +
                "itemDO=" + itemDO +
                ", itemId=" + itemId +
                ", params=" + params +
                ", context=" + context +
                ", componentIds=" + componentIds +
                ", loginUserId=" + loginUserId +
                ", routeInfo=" + routeInfo +
                ", isDyn=" + isDyn +
                ", clientIp='" + clientIp + '\'' +
                ", isError=" + isError +
                '}';
    }
}
