package com.mogujie.detail.core.adt;

import com.mogujie.detail.core.constant.DefaultType;
import com.mogujie.detail.core.translator.ITranslator;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;

/**
 * Created by anshi on 17/3/1.
 */
public class TranslatorMappingHolder {
    private Map<String, ITranslator> translatorMap;

    private Map<String, List<IModuleDOProvider>> translatorModuleMap;

    private Map<String, List<Class>> translatorDoMap;

    private Map<String, Method> translateMethodMap;

    private Map<String, DefaultType> translatorDefaultMap;

    public Map<String, ITranslator> getTranslatorMap() {
        return translatorMap;
    }

    public void setTranslatorMap(Map<String, ITranslator> translatorMap) {
        this.translatorMap = translatorMap;
    }

    public Map<String, List<IModuleDOProvider>> getTranslatorModuleMap() {
        return translatorModuleMap;
    }

    public void setTranslatorModuleMap(Map<String, List<IModuleDOProvider>> translatorModuleMap) {
        this.translatorModuleMap = translatorModuleMap;
    }

    public Map<String, List<Class>> getTranslatorDoMap() {
        return translatorDoMap;
    }

    public void setTranslatorDoMap(Map<String, List<Class>> translatorDoMap) {
        this.translatorDoMap = translatorDoMap;
    }

    public Map<String, Method> getTranslateMethodMap() {
        return translateMethodMap;
    }

    public void setTranslateMethodMap(Map<String, Method> translateMethodMap) {
        this.translateMethodMap = translateMethodMap;
    }

    public Map<String, DefaultType> getTranslatorDefaultMap() {
        return translatorDefaultMap;
    }

    public void setTranslatorDefaultMap(Map<String, DefaultType> translatorDefaultMap) {
        this.translatorDefaultMap = translatorDefaultMap;
    }
}