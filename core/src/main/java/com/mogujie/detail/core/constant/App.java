package com.mogujie.detail.core.constant;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/3/18.
 */
public enum App {

    /**
     * 蘑菇街
     */
    MGJ(1),

    /**
     * 美丽说
     */
    MLS(2),

    /**
     * 淘世界
     */
    TSJ(3),

    /**
     * 9块9市场
     */
    GO(4),

    /**
     * 魔豆商城
     */
    MD(5),

    /**
     * 小程序
     */
    XCX(6),


    /**
     * 百货小程序
     */
    BH(7),

    /**
     * 所有平台
     */
    ALL(8),

    /**
     * 美丽买手店
     */
    MSD(9);

    private int code;

    private App(int code) {
        this.code = code;
    }

    public static App getApp(int code) {
        switch (code) {
            case 1:
                return MGJ;
            case 2:
                return MLS;
            case 3:
                return TSJ;
            case 4:
                return GO;
            case 5:
                return MD;
            case 6:
                return XCX;
            case 7:
                return BH;
            case 9:
                return MSD;
            default:
                return ALL;
        }
    }
}
