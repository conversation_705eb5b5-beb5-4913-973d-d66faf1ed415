package com.mogujie.detail.core.util;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.service.hummer.utils.SystemDiscountChecker;
import com.mogujie.service.item.domain.basic.ItemSkuDO;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by anshi on 2019/1/30.
 */
public class BizUtil {

    /**
     * 根据商品标，获取商品的大促价
     *
     * @param context
     * @return
     */
    public static Integer getActivityPriceFromTag(DetailContext context) {
        if (context == null || context.getItemDO() == null || context.getRouteInfo().getBizType() == BizType.TTNORMAL) {
            return null;
        }
        try {
            DetailItemDO itemDO = context.getItemDO();
            SystemDiscountChecker.SystemDiscountReq req = new SystemDiscountChecker.SystemDiscountReq();
            req.setExtra(itemDO.getJsonExtra());
            req.setOrgiPrice(itemDO.getReservePrice());
            req.setIsDisplay(true);
            req.setMarket(ContextUtil.getMarketByContext(context));
            Map<Long, Long> skuPriceMap = new HashMap<>();
            for (ItemSkuDO sku : itemDO.getItemSkuDOList()) {
                skuPriceMap.put(sku.getSkuId(), sku.getPrice().longValue());
            }
            req.setSkuPriceMap(skuPriceMap);
            SystemDiscountChecker.SystemDiscountRes ret = SystemDiscountChecker.calcSystemDiscount(req);
            if (ret != null && ret.getRealPrice() != null) {
                return ret.getRealPrice().intValue();
            }
        } catch (Throwable e) {
        }
        return null;
    }
}
