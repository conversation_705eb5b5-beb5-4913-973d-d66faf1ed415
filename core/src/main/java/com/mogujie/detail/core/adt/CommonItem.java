package com.mogujie.detail.core.adt;

import com.mogujie.metabase.utils.CollectionUtils;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.basic.ItemDetailDO;
import com.mogujie.service.item.domain.basic.ItemExtraDO;
import com.mogujie.service.item.domain.basic.ItemImageDO;
import com.mogujie.service.item.domain.basic.ItemPreSaleDO;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.service.item.domain.entity.Item;
import com.mogujie.service.item.domain.entity.ItemTag;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by x<PERSON>oyao on 15/11/25.
 */
@EqualsAndHashCode
@ToString
public class CommonItem {

    /**
     * 折扣
     */
    @Getter
    @Setter
    private Float discount;

    /**
     * 折扣描述
     */
    @Getter
    @Setter
    private String discountDesc;

    @Getter
    @Setter
    private String lowPrice;

    @Getter
    @Setter
    private String highPrice;

    @Getter
    @Setter
    private String lowNowPrice;

    @Getter
    @Setter
    private String highNowPrice;

    @Getter
    @Setter
    private Integer totalStock;

    // 预售商品
    @Getter
    @Setter
    private ItemPreSaleDO itemPreSale;

    // 判断是否测试流量
    @Getter
    @Setter
    private boolean isTest;

    // 判断是否测试流量
    @Getter
    @Setter
    private List<ItemTag> itemTags;

    @Getter
    @Setter
    private List<com.mogujie.service.item.domain.entity.ItemTag> itemTagList;

    // 扩展参数
    @Getter
    @Setter
    private Map<String, Object> params;

    @Getter
    @Setter
    private Integer priceChannel;

    @Getter
    @Setter
    private Integer limitNum;

    @Getter
    @Setter
    private Map<String, String> features;

    @Getter
    @Setter
    private Long activityId;


    //////////// CompleteItem

//    @Getter
//    @Setter
//    private List<Sku> skus;

    @Getter
    @Setter
    private List<ItemSkuDO> skus;

    @Getter
    @Setter
    private List<ItemImageDO> topImages;

    @Getter
    @Setter
    private ItemDetailDO detail;

    @Getter
    @Setter
    private Long postageId;

    //////////// Item

    /*
     * 商品ID
     */
    @Getter
    @Setter
    protected Long tradeItemId;

    /**
     * 小店itemId
     * FIXME:暂时保留，很快去掉
     */
    @Deprecated
    @Getter
    @Setter
    protected Long xdItemId;

    /*
     * 数据类型：用于区别不同来源的数据
     */
    @Getter
    @Setter
    protected Long type;

    /*
     * 用户Id
     */
    @Getter
    @Setter
    protected Long userId;

    /*
     * 店铺Id
     */
    @Getter
    @Setter
    protected Long shopId;

    /*
     * 商品开始售卖时间
     */
    @Getter
    @Setter
    protected Integer start;

    /*
     * 减库存方式：0付款，1下单减库存
     */
    @Getter
    @Setter
    protected Integer decStockType;

    /*
     * 商品标题
     */
    @Getter
    @Setter
    protected String title;

    /*
     * 一句话描述
     */
    @Getter
    @Setter
    protected String description;

    /*
     * 标签集合，以空格分割
     */
    @Getter
    @Setter
    protected String tags;

    /*
     * 价格,以分为单位
     */
    @Getter
    @Setter
    protected Long price;

    /*
     * 商品编码
     */
    @Getter
    @Setter
    protected String code;

    /*
     * 交易类型，0为全款交易，1为2阶段预售
     */
    @Getter
    @Setter
    protected Integer processType;

    /*
     * 商品状态
     */
    @Getter
    @Setter
    protected Integer status;

    /*
     * 扩展信息,json格式
     */
    @Getter
    @Setter
    protected String extra;

    /**
     * 位置名称
     */
    @Getter
    @Setter
    protected String address;

    /*
     * 商品所属最小类目id
     */
    @Getter
    @Setter
    protected Integer cid;

    /**
     * 商品类型
     */
    @Getter
    @Setter
    private Integer goodsType = 0;

    /**
     * 类目全路径
     */
    @Getter
    @Setter
    private String cids;

    /*
     * 是否下架 1为下架
     */
    @Getter
    @Setter
    protected Integer isShelf;

    /*
     * 扩展属性集合
     */
    @Getter
    @Setter
    protected String properties;

    /*
     * 商品主图
     */
    @Getter
    @Setter
    protected String image;

    /*
     * 店铺内分类ids,逗号分割
     */
    @Getter
    @Setter
    protected String classIds;

    /*
     *
     * 货币单位
     */
    @Getter
    @Setter
    protected String currency;

    /**
     * 品牌编号
     */
    @Getter
    @Setter
    protected Long brandId;

    /**
     * 以分为单位的原价
     * <p>
     * 该字段任何时候都是原价,提供给商品solr增量使用,其他业务方请勿使用.若错误使用,后果自负
     */
    @Getter
    @Setter
    protected Integer originalPrice;

    //乐观锁字段
    @Getter
    @Setter
    protected Long modify;

    //////////// ItemBaseEntity

    @Getter
    @Setter
    protected Integer created;

    @Getter
    @Setter
    protected Integer updated;

    @Getter
    @Setter
    protected Integer isDeleted;

    //快抢业务类型
    @Getter
    @Setter
    private String  fastBizType;

    @Getter
    @Setter
    protected ItemExtraDO itemExtraDO;

    ////////////
    public CommonItem() {

    }

    public CommonItem(Item item) {
        this.tradeItemId = item.getTradeItemId().longValue();
        this.type = item.getType().longValue();
        this.userId = item.getUserId().longValue();
        this.shopId = item.getShopId().longValue();
        this.decStockType = item.getDecStockType();
        this.title = item.getTitle();
        this.description = item.getDescription();
        this.tags = item.getTags();
        this.price = item.getPrice().longValue();
        this.code = item.getCode();
        this.processType = item.getProcessType();
        this.status = item.getStatus();
        this.extra = item.getExtra();
        this.address = item.getAddress();
        this.cid = item.getCid();
        this.setGoodsType(item.getGoodsType());
        this.setCids(item.getCids());
        this.isShelf = item.getIsShelf();
        this.properties = item.getProperties();
        this.image = item.getImage();
        this.classIds = item.getClassIds();
        this.xdItemId = item.getXdItemId().longValue();
        this.isDeleted = item.getIsDeleted();
        this.start = item.getStart();
        this.params = new HashMap<>(2);
        this.brandId = item.getBrandId();
    }

    public CommonItem(ItemDO item) {
        this.tradeItemId = item.getItemId();
        this.type = item.getVerticalMarket();
        this.userId = item.getUserId();
        this.shopId = item.getShopId();
        this.decStockType = item.getInventoryType();
        this.title = item.getTitle();
        this.description = item.getDescription();
        this.tags = item.getTags();
        this.price = item.getReservePrice();
        this.code = item.getCode();
        this.processType = item.getProcessType();
        this.status = item.getStatus();
        this.extra = item.getJsonExtra();
        this.address = item.getAddress();
        this.cid = item.getCategoryId();
        this.setGoodsType(0);
        this.setCids(item.getCids());
        this.isShelf = item.getIsShelf();
        this.properties = item.getPackedProperties();
        this.image = item.getMainImage();
        this.classIds = null;
        this.xdItemId = item.getXdItemId();
        this.isDeleted = item.getIsDeleted();
        this.start = (int) (item.getStarts().getTime() / 1000);
        this.params = new HashMap<>(2);
        this.brandId = item.getBrandId();

        this.skus = item.getItemSkuDOList();
        this.topImages = item.getImages();
        this.detail = item.getItemDetailDO();
        this.itemTagList = this.convertFromICDO(item.getItemTags());
        this.itemExtraDO = item.getItemExtraDO();
    }

    public Object getParam(String key) {
        return this.params.get(key);
    }

    private static List<ItemTag> convertFromICDO(List<com.mogujie.service.item.domain.basic.ItemTagDO> newTags) {
        if (CollectionUtils.isEmpty(newTags)) {
            return null;
        }
        List<ItemTag> oldTagList = new ArrayList<>();
        for (com.mogujie.service.item.domain.basic.ItemTagDO itemTagDO : newTags) {
            ItemTag tag = new ItemTag();
            tag.setBizId(itemTagDO.getBizId());
            tag.setBizType(itemTagDO.getBizType());
            tag.setEndTime(itemTagDO.getEndTime());
            tag.setId(itemTagDO.getId().intValue());
            tag.setItemId(itemTagDO.getItemId().intValue());
            tag.setMarket(itemTagDO.getMarket());
            tag.setStartTime(itemTagDO.getStartTime());
            tag.setSource(itemTagDO.getSource());
            tag.setTagKey(itemTagDO.getTagKey());
            tag.setTagValue(itemTagDO.getTagValue());
            tag.setCreated(itemTagDO.getCreated());
            tag.setUpdated(itemTagDO.getUpdated());
            tag.setIsDeleted(itemTagDO.getIsDeleted());
            oldTagList.add(tag);
        }
        return oldTagList;
    }

}
