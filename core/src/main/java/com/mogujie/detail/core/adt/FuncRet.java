package com.mogujie.detail.core.adt;

/**
 * 函数执行结果
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/8/15.
 */
public class FuncRet<T> {

    boolean isSuccess;

    String message;

    private T data;

    public FuncRet(boolean isSuccess, String message) {
        this(isSuccess, message, null);
    }

    public FuncRet(boolean isSuccess, String message, T data) {
        this.isSuccess = isSuccess;
        this.message = message;
        this.data = data;
    }

    public boolean isSuccess() {
        return isSuccess;
    }

    public void setSuccess(boolean success) {
        isSuccess = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
