package com.mogujie.detail.core.spi;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.RouteInfo;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.ItemTag;
import com.mogujie.detail.core.constant.Platform;
import org.apache.commons.lang.ClassUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.Advised;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Created by xiaoyao on 16/3/24.
 */
@Component
public class SpiManager implements ApplicationListener<ContextRefreshedEvent> {

    private final AtomicBoolean loaded = new AtomicBoolean(false);

    private final ConcurrentMap<Class, Map<RouteInfo, Object>> spiCache = new ConcurrentHashMap<>(10);

    private static final ThreadLocal<Map<Class, Object>> SPI_CACHE = new ThreadLocal<>();

    private static final Logger LOGGER = LoggerFactory.getLogger(SpiManager.class);


    @Override
    public void onApplicationEvent(final ContextRefreshedEvent contextRefreshedEvent) {
        if (loaded.compareAndSet(false, true)) {
            scanSpiPackage(contextRefreshedEvent.getApplicationContext());
            autowiredSpi(contextRefreshedEvent.getApplicationContext());
        }
    }

    private void scanSpiPackage(final ApplicationContext applicationContext) {
        Map<String, Object> spiBeanMap = applicationContext.getBeansWithAnnotation(BizSpi.class);
        for (Map.Entry spiBeanEntry : spiBeanMap.entrySet()) {
            Object spiImpl = spiBeanEntry.getValue();
            Class spiClass = spiImpl.getClass();
            if (Advised.class.isAssignableFrom(spiClass)) {
                spiClass = ((Advised)spiImpl).getTargetSource().getTargetClass();
            }
            LOGGER.error("spiClass : {}", spiClass);
            Annotation anno = spiClass.getAnnotation(BizSpi.class);
            if (null != anno) {
                BizSpi bizSpi = (BizSpi) anno;
                BizType bizType = bizSpi.bizType();
                Platform platform = bizSpi.platform();
                ItemTag itemTag = bizSpi.itemTag();
                App app = bizSpi.app();
                // 用 getInterfaces 不能获取超类实现的接口
                List<Class> classes = ClassUtils.getAllInterfaces(spiClass);
                Class spiInterf = null;
                for (Class interf : classes) {
                    LOGGER.error("interf : {}", interf);
                    if (isSpiInterface(interf)) {
                        spiInterf = interf;
                        break;
                    }
                }
                if (null != spiInterf) {
                    RouteInfo topoRoute = new RouteInfo(app, platform, bizType, null,"");
                    Map<RouteInfo, Object> spiMap = spiCache.get(spiInterf);
                    if (null == spiMap) {
                        spiMap = new HashMap<>();
                        spiCache.put(spiInterf, spiMap);
                    }
                    LOGGER.error("load spi {} for {}", spiImpl, topoRoute.toString() + ":" + itemTag);
                    spiMap.put(topoRoute, spiImpl);
                } else {
                    LOGGER.error("spi interface not found : {}", spiClass);
                }
            }
        }
    }

    public void autowiredSpi(ApplicationContext context) {
        for (String name : context.getBeanDefinitionNames()) {
            Object bean = context.getBean(name);
            Class beanCls = bean.getClass();
            if (Advised.class.isAssignableFrom(beanCls)) {
                beanCls = ((Advised)bean).getTargetSource().getTargetClass();
                try {
                    bean = ((Advised)bean).getTargetSource().getTarget();
                } catch (Exception e) {
                    LOGGER.error("decode bean failed : {}", e);
                }
            }
            List<Field> fields = FieldUtils.getAllFieldsList(beanCls);
            for (Field field : fields) {
                if (field.isAnnotationPresent(SpiAutowired.class)) {
                    LOGGER.info("got spi autowired field : {}", field.getName());
                    field.setAccessible(true);
                    Class spiInterf = field.getType();
                    Object defaultImpl = getDefaultImpl(spiInterf);
                    if (null == defaultImpl) {
                        continue;
                    }
                    try {
                        field.set(bean, defaultImpl);
                    } catch (Throwable e) {
                        LOGGER.error("set default for field {} failed {}", field.getName(), e);
                    }
                    field.setAccessible(false);
                }
            }
        }
    }

    public Object spiCall(Class spiInterface,  Method method, Object... args) throws InvocationTargetException, IllegalAccessException {
        Map<RouteInfo, Object> spiMap = spiCache.get(spiInterface);
        if (null == spiMap) {
            LOGGER.error("Cann't found spiTable for interface : {}", spiInterface);
            return null;
        }
        Object spiImpl = getSpiCache(spiInterface);
        if (null == spiImpl) {
            DetailContext context = (DetailContext)args[0];
            if (null == context) {
                LOGGER.error("spi first parameter is not DetailContext : {}", spiInterface);
                return null;
            }

            RouteInfo originRoute = context.getRouteInfo();
            if (null == originRoute) {
                LOGGER.error("get routeInfo from DetailContext failed");
                return null;
            }
            spiImpl = spiMap.get(originRoute);
            if (null == spiImpl) {
                spiImpl = spiMap.get(originRoute.getDefaultBizRoute());
                if (null == spiImpl) {
                    spiImpl = spiMap.get(originRoute.getDefaultPlatformAndBizRoute());
                    if (null == spiImpl) {
                        spiImpl = spiMap.get(originRoute.getAllDefaultRoute());
                        if (null == spiImpl) {
                            LOGGER.error("event can't found default spiImplMap : {}, {}", originRoute, spiInterface);
                            return null;
                        }
                    }
                }
            }

            setSpiCache(spiInterface, spiImpl);
        }
        Method realMethod = null;
        for(Method m : spiImpl.getClass().getMethods()) {
            if (method.getName().equals(m.getName())) {
                realMethod = m;
                break;
            }
        }
        if (null == realMethod) {
            LOGGER.error("cann't find real method {}, {}", spiImpl, method.getName());
            return null;
        }
        return realMethod.invoke(spiImpl, args);
    }

    public static boolean isSpiInterface(Class cls) {
        if (null == cls) {
            return false;
        }
        return cls.isAnnotationPresent(Exposed.class);
    }

    public <T> T getDefaultImpl(Class spiInterface) {
        if (!spiInterface.isAnnotationPresent(Exposed.class)) {
            Class[] spis = spiInterface.getInterfaces();
            for (Class spi : spis) {
                if (spi.isAnnotationPresent(Exposed.class)) {
                    spiInterface = spi;
                    break;
                }
            }
        }
        if (null == spiInterface) {
            LOGGER.error("spi interface is null");
            return null;
        }
        Map<RouteInfo, Object> spiTable = spiCache.get(spiInterface);
        if (null == spiTable || spiTable.isEmpty()) {
            LOGGER.error("Cann't find impl for spi : {}", spiInterface);
            return null;
        }
        return (T)(spiTable.values().iterator().next());
    }

    public static void setSpiCache(Class spiInterf, Object impl) {
        Map<Class, Object> spiCache = SPI_CACHE.get();
        if (null == spiCache) {
            spiCache = new HashMap<>();
            SPI_CACHE.set(spiCache);
        }
        spiCache.put(spiInterf, impl);
    }

    public static Object getSpiCache(Class spiInterf) {
        if (null == SPI_CACHE.get()) {
            return null;
        } else {
            return SPI_CACHE.get().get(spiInterf);
        }
    }

    public static void clear() {
        SPI_CACHE.remove();
    }
}
