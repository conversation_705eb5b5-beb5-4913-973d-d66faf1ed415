package com.mogujie.detail.core.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.mogujie.actionlet.core.impl.DefaultActionResult;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.RouteInfo;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.metabase.spring.client.MetabaseClient;
import com.mogujie.metabase.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.mogujie.kamaz.client.KamazClient;
import org.mogujie.kamaz.client.KamazClientFactory;
import org.mogujie.kamaz.client.task.impl.DefaultTask;
import org.mogujie.kamaz.common.enums.ContentType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
 * @AUTUOR: lengshan
 * @DESCRIPTION: 容灾
 * @DATE: 2019/9/23 下午5:57
 */
@Component
public class KamazManager {

    private static final Logger LOGGER = LoggerFactory.getLogger(KamazManager.class);

    private static KamazClient kamazClient = KamazClientFactory.client("detailweb");

    private static final String PREFIX = "mwp.detailwebmwp.detail.nocache.api";

    private static final String DELIMITER = "_";

    private static final String DISASTER_CODE = "detailweb_detail.nocache.api";


    public void sample(DetailContext context, Map<String, Object> data) {
        String key = "";
        String value = "";
        try {
            key = PREFIX +
                    DELIMITER + context.getParam("template") +
                    DELIMITER + context.getParam("iid") +
                    DELIMITER + (context.getParam("activityId") == null ? "" : context.getParam("activityId")) +
                    DELIMITER + (context.getParam("fastbuyId") == null ? "" : context.getParam("fastbuyId"));
            value = JSON.toJSONString(new DefaultActionResult(true, "1001", "", data), SerializerFeature.DisableCircularReferenceDetect);
            kamazClient.sample(new DefaultTask(DISASTER_CODE, key,value, ContentType.JSON));
        } catch (Exception e) {
            LOGGER.error("Kamaz sample! key:{}, value:{}", key, value, e);
        }
    }
}
