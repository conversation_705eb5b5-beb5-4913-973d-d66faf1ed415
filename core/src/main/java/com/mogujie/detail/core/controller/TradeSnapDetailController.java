package com.mogujie.detail.core.controller;

import com.mogujie.actionlet.ActionResult;
import com.mogujie.actionlet.annotation.ActionletName;
import com.mogujie.actionlet.core.impl.DefaultActionResult;
import com.mogujie.actionlet.mwp.annotation.MWPApi;
import com.mogujie.actionlet.session.annotation.NeedUserInfo;
import com.mogujie.actionlet.sync.SyncActionlet;
import com.mogujie.costa.server.annotations.RefererCheck;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.FuncRet;
import com.mogujie.detail.core.adt.RouteInfo;
import com.mogujie.detail.core.manager.TemplateManager;
import com.mogujie.detail.core.manager.TopologyManager;
import com.mogujie.detail.core.manager.TranslatorManager;
import com.mogujie.service.category2.api.CategoryService;
import com.mogujie.service.category2.domain.entity.Category;
import com.mogujie.session.SessionContextHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by xiaoyao on 17/3/29.
 */
@Component
@MWPApi(value = "trade.snap.detail", version = "1")
@ActionletName(value = "trade.snap.detail", version = "1")
@RefererCheck({"mogujie.com", "mogu.com"})
@NeedUserInfo
public class TradeSnapDetailController implements SyncActionlet<String, Object>{


    private static final Logger LOGGER = LoggerFactory.getLogger(TradeSnapDetailController.class);

    @Autowired
    private TemplateManager templateManager;

    @Autowired
    private TopologyManager topologyManager;

    @Autowired
    private CategoryService categoryService;

    @Override
    public ActionResult<Object> execute(@Nullable String orderId) {

        try {
            String template = "1-1-detail_normal-1.0.0";
            String[] templateKeys = template.split("-");
            if (templateKeys.length != 4) {
                return getInvalidRet("not complete template key : " + template);
            }
            String bizType = templateKeys[2].replaceAll("detail_", "");
            RouteInfo routeInfo = RouteController.getRouteInfo(Integer.parseInt(templateKeys[0]), bizType, Integer.parseInt(templateKeys[1]), templateKeys[3]);
            if (null == routeInfo) {
                LOGGER.error("invalid route : {}", routeInfo);
                return getInvalidRet("invalid route parameter!");
            }
            List<String> componentIds = templateManager.getComponentIds(routeInfo, templateKeys[3]);
            if (null == componentIds) {
                return getInvalidRet("no component select!");
            }
            DetailContext context = new DetailContext(routeInfo, componentIds, TranslatorManager.getTranslatorMappingHolder());
            context.setDyn(false);
            if (StringUtils.isNotBlank(template)) {
                context.addParam("template", template);
            }
            context.setLoginUserId(SessionContextHolder.getUserId() > 0 ? SessionContextHolder.getUserId() : null);
            FuncRet<Map<String, Object>> ret = topologyManager.snapEmit(context, null, orderId);
            if (!ret.isSuccess()) {
                return getInvalidRet(ret.getMessage());
            }
            Map<String, Object> data = ret.getData();
            this.appendOtherInfo(context.getItemDO(), data);
            return getSuccessRet(ret.getMessage(), data);
        } catch (Exception e) {
            LOGGER.error("get snap error : ", e);
        }
        return new DefaultActionResult(false, "4004", "内部错误", null);
    }

    /**
     * 追加其他的额外信息
     *
     * @param detailItemDO 原始商品信息
     * @param data         最终返回数据
     */
    private void appendOtherInfo(DetailItemDO detailItemDO, Map<String, Object> data) {
        String cids = detailItemDO.getCids();
        if (StringUtils.isEmpty(cids)) {
            return;
        }
        String[] cidArray = StringUtils.split(cids, " ");
        List<Integer> cidList = new ArrayList<>();
        for (String cidStr : cidArray) {
            cidList.add(Integer.parseInt(cidStr.replaceAll("#", "")));
        }
        List<Category> categories = categoryService.listCachedCategory(cidList);
        if (CollectionUtils.isEmpty(categories)) {
            return;
        }
        Map<String, String> cidMap = new HashMap<>();
        for (Category category : categories) {
            cidMap.put(category.getId().toString(), category.getName());
        }
        List<String> cidNames = new ArrayList<>();
        for (Integer cid : cidList) {
            cidNames.add(cidMap.get(cid.toString()));
        }
        String categoryPath = StringUtils.join(cidNames, ">");
        if (StringUtils.isNotBlank(categoryPath)) {
            data.put("categoryPath", categoryPath);
        }
    }

    private ActionResult<Object> getInvalidRet(String msg) {
        return new DefaultActionResult<>(false, "4004", msg);
    }

    private ActionResult<Object> getSuccessRet(String msg, Map<String, Object> data) {
        if (null == data) {
            data = new HashMap();
        }
        return new DefaultActionResult(true, "1001", "", data);
    }
}
