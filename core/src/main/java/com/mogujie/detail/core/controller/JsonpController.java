package com.mogujie.detail.core.controller;

import com.mogujie.actionlet.ActionResult;
import com.mogujie.actionlet.annotation.ActionletName;
import com.mogujie.actionlet.core.impl.DefaultActionResult;
import com.mogujie.actionlet.mwp.MWPContext;
import com.mogujie.actionlet.mwp.annotation.MWPApi;
import com.mogujie.actionlet.session.annotation.NeedUserInfo;
import com.mogujie.actionlet.sync.SyncActionlet;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.costa.core.domain.RequestType;
import com.mogujie.costa.server.annotations.CostaWeb;
import com.mogujie.costa.server.annotations.RefererCheck;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.FuncRet;
import com.mogujie.detail.core.adt.RouteInfo;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.manager.TemplateManager;
import com.mogujie.detail.core.manager.TopologyManager;
import com.mogujie.detail.core.manager.TranslatorManager;
import com.mogujie.detail.core.spi.SpiManager;
import com.mogujie.sentry.SentryClient;
import com.mogujie.sentry.collector.Collector;
import com.mogujie.sentry.type.CollectorType;
import com.mogujie.session.SessionContextHolder;
import com.mogujie.stable.spirit.point.annotation.ClassSpirit;
import com.mogujie.stable.spirit.point.annotation.MethodSpirit;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by
 * xiaoyao on 17/3/20.
 */
@ClassSpirit
@Component
@MWPApi(value = "detail.api", version = "v1")
@ActionletName(value = "detail.api", version = "v1")
@CostaWeb(type = {RequestType.JSONP, RequestType.WITHOUT_TOKEN})
@NeedUserInfo
@RefererCheck({"mogujie.com", "meilishuo.com", "mogu.com", "servicewechat.com", "tmaservice.developer.toutiao.com"})
public class JsonpController implements SyncActionlet<JsonpController.DetailParam, Object>, InitializingBean {

    private static final Logger LOGGER = LoggerFactory.getLogger(JsonpController.class);

    private SentryClient sentryClient;

    @Autowired
    private TopologyManager topologyManager;

    @Autowired
    private TemplateManager templateManager;

    @Override
    public void afterPropertiesSet() throws Exception {
        sentryClient = SentryClient.factory("detail");
        sentryClient.openAutoCollector();
        try {
            Thread.sleep(5000);
        } catch (Throwable e) {
            LOGGER.error("sleep error.", e);
        }
    }


    @Override
    @MethodSpirit
    public ActionResult<Object> execute(@Nullable DetailParam param) {
        try {
            // 设置MDC参数,供logback获取
            MDC.put("topo", param.getTemplate());
            MDC.put("iid", param.getIid());
            MDC.put("position", "spout");
            return realCall(param);
        } catch (Throwable e) {
            LOGGER.error("call error: ", e);
        } finally {
            MDC.clear();
            SpiManager.clear();
        }
        return getInvalidRet("详情页内部错误");
    }

    protected ActionResult<Object> realCall(DetailParam param) throws DetailException {
        long startTime = System.currentTimeMillis();
        String iid = param.getIid();
        String template = param.getTemplate();
        if (iid.endsWith("_1111")) {
            iid = StringUtils.removeEnd(iid, "_1111");
        }

        if (StringUtils.isEmpty(template)) {
            return getInvalidRet("invalid template : " + template);
        }

        String[] templateKeys = template.split("-");
        if (templateKeys.length != 4) {
            return getInvalidRet("not complete template key : " + template);
        }

        Long itemId = IdConvertor.urlToId(iid);
        if (StringUtils.isEmpty(iid) || itemId > Integer.MAX_VALUE || itemId < 0) {
            LOGGER.error("invalid itemId : {}", iid);
            return getInvalidRet("invalid itemId " + iid);
        }
        String bizType = templateKeys[2].replaceAll("detail_", "");
        RouteInfo routeInfo = RouteController.getRouteInfo(Integer.parseInt(templateKeys[0]), bizType, Integer.parseInt(templateKeys[1]), templateKeys[3]);
        if (null == routeInfo) {
            LOGGER.error("invalid route : {}", routeInfo);
            return getInvalidRet("invalid route parameter!");
        }
        List<String> componentIds = templateManager.getComponentIds(routeInfo, templateKeys[3]);
        if (null == componentIds) {
            return getInvalidRet("no component select!");
        }

        qpsAdd(template, routeInfo.getApp().name());

        DetailContext context = new DetailContext(routeInfo, componentIds, itemId, TranslatorManager.getTranslatorMappingHolder());
        context.setDyn(false);
        if (StringUtils.isNotBlank(param.getActivityId())) {
            context.addParam("activityId", param.getActivityId());
        }
        if (StringUtils.isNotBlank(param.getFastbuyId())) {
            context.addParam("fastbuyId", IdConvertor.urlToId(param.getFastbuyId()).toString());
        }
        if (StringUtils.isNotBlank(param.getTemplate())) {
            context.addParam("template", param.getTemplate());
        }
        if (StringUtils.isNotBlank(param.getAppPlat())) {
            context.addParam("appPlat", param.getAppPlat());
        }
        if (StringUtils.isNotBlank(param.getActUserId())) {
            context.addParam("actUserId", param.getActUserId());
        }
        if (StringUtils.isNotBlank(param.getRelatedIds())) {
            context.addParam("relatedIds", param.getRelatedIds());
        }
        if (StringUtils.isNotBlank(param.getDid())) {
            context.addParam("did", IdConvertor.urlToId(param.getDid()).toString());
        }
        if (StringUtils.isNotBlank(param.getAuctionId())) {
            context.addParam("auctionId", IdConvertor.urlToId(param.getAuctionId()).toString());
        }
        if (StringUtils.isNotBlank(param.getFashionId())) {
            context.addParam("fashionId", param.getFashionId());
        }
        if (StringUtils.isNotBlank(param.getAcm())) {
            context.addParam("acm", param.getAcm());
        }
        if (MWPContext.getMWPRequestFrom() != null && StringUtils.isNotBlank(MWPContext.getMWPRequestFrom().getPlatform())) {
            context.addParam("_platform", MWPContext.getMWPRequestFrom().getPlatform());
        }
        try {
            // 设置用户在详情页选择的用于计算运费的收货地址id
            if(StringUtils.isNotBlank(param.getRecvAddressId())){
                context.addParam("recvAddressId",param.getRecvAddressId());
            }
            if (StringUtils.isNotBlank(param.getProvince())) {
                String province = new String(param.getProvince().getBytes("ISO-8859-1"), "UTF-8");
                context.addParam("province", province);
            }
            if (StringUtils.isNotBlank(param.getCity())) {
                String city = new String(param.getCity().getBytes("ISO-8859-1"), "UTF-8");
                context.addParam("city", city);
            }
        } catch (UnsupportedEncodingException e) {
            LOGGER.error("Jsonp get address fault : {}", e);
        }

        context.setLoginUserId(SessionContextHolder.getUserId() > 0 ? SessionContextHolder.getUserId() : null);
        FuncRet<Map<String, Object>> ret = topologyManager.emit(context);

        long endTime = System.currentTimeMillis();
        rtAdd((int) (endTime - startTime), template, routeInfo.getApp().name());
        if (!ret.isSuccess()) {
            return getInvalidRet(ret.getMessage());
        }

        return getSuccessRet(ret.getMessage(), ret.getData());
    }

    private ActionResult<Object> getInvalidRet(String msg) {
        return new DefaultActionResult<>(false, "4004", msg);
    }

    private ActionResult<Object> getSuccessRet(String msg, Map<String, Object> data) {
        if (null == data) {
            data = new HashMap();
        }
        return new DefaultActionResult(true, "1001", "", data);
    }

    private void rtAdd(int rt, String template, String app) {
        Map<String, String> tags = new HashMap<>(2);
        if (StringUtils.isNotBlank(template)) {
            tags.put("url", template.replaceAll("-", "_"));
            tags.put("app", app);
        }
        tags.put("enterance", "jsonp");
        Collector rtCollector = sentryClient.getCollector("detail.rt", tags, 60, CollectorType.AVG);
        rtCollector.put(rt);
    }

    private void qpsAdd(String template, String app) {
        Map<String, String> tags = new HashMap<>(2);
        if (StringUtils.isNotBlank(template)) {
            tags.put("url", template.replaceAll("-", "_"));
            tags.put("app", app);
        }
        tags.put("enterance", "jsonp");
        Collector qpsCollector = sentryClient.getCollector("detail.qps", tags, 1, CollectorType.SUM);
        qpsCollector.put(1);
    }

    public static class DetailParam {

        private String iid;

        private String template;

        private String activityId;

        private String fastbuyId;

        private String appPlat;

        private String actUserId;

        /**
         * 达人ID
         */
        private String did;

        /**
         * 拍卖ID
         */
        private String auctionId;

        /**
         * 抖音关联商品ids
         */
        private String relatedIds;

        private String fashionId;

        /**
         * 用户当前所在省
         */
        private String province;

        /**
         * 用户当前所在城市
         */
        private String city;

        /**
         * 链接中的acm打点参数
         */
        private String acm;

        /**
         * 用户在详情页选择的收获地址id
         */
        private String recvAddressId;


        public String getIid() {
            return iid;
        }

        public void setIid(String iid) {
            this.iid = iid;
        }

        public String getTemplate() {
            return template;
        }

        public void setTemplate(String template) {
            this.template = template;
        }

        public String getActivityId() {
            return activityId;
        }

        public void setActivityId(String activityId) {
            this.activityId = activityId;
        }

        public String getFastbuyId() {
            return fastbuyId;
        }

        public void setFastbuyId(String fastbuyId) {
            this.fastbuyId = fastbuyId;
        }

        public String getAppPlat() {
            return appPlat;
        }

        public void setAppPlat(String appPlat) {
            this.appPlat = appPlat;
        }

        public String getActUserId() {
            return actUserId;
        }

        public void setActUserId(String actUserId) {
            this.actUserId = actUserId;
        }

        public String getDid() {
            return did;
        }

        public void setDid(String did) {
            this.did = did;
        }

        public String getAuctionId() {
            return auctionId;
        }

        public void setAuctionId(String auctionId) {
            this.auctionId = auctionId;
        }

        public String getRelatedIds() {
            return relatedIds;
        }

        public void setRelatedIds(String relatedIds) {
            this.relatedIds = relatedIds;
        }

        public String getFashionId() {
            return fashionId;
        }

        public void setFashionId(String fashionId) {
            this.fashionId = fashionId;
        }

        public String getProvince() {
            return province;
        }

        public void setProvince(String province) {
            this.province = province;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getAcm() {
            return acm;
        }

        public void setAcm(String acm) {
            this.acm = acm;
        }

        public String getRecvAddressId() {
            return recvAddressId;
        }

        public void setRecvAddressId(String recvAddressId) {
            this.recvAddressId = recvAddressId;
        }

        @Override
        public String toString() {
            return "DetailParam{" +
                    "iid='" + iid + '\'' +
                    ", template='" + template + '\'' +
                    ", activityId='" + activityId + '\'' +
                    ", fastbuyId='" + fastbuyId + '\'' +
                    ", appPlat='" + appPlat + '\'' +
                    ", actUserId='" + actUserId + '\'' +
                    ", did='" + did + '\'' +
                    ", auctionId='" + auctionId + '\'' +
                    ", relatedIds='" + relatedIds + '\'' +
                    ", fashionId='" + fashionId + '\'' +
                    ", province='" + province + '\'' +
                    ", city='" + city + '\'' +
                    ", acm='" + acm + '\'' +
                    ", recvAddressId='" + recvAddressId + '\'' +
                    '}';
        }
    }
}
