package com.mogujie.detail.core.spi;

import com.mogujie.sentry.SentryClient;
import com.mogujie.sentry.collector.Collector;
import org.apache.commons.lang.ClassUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by xiaoyao on 16/3/31.
 */
public class SPIInterceptor {

    private static final Logger LOGGER = LoggerFactory.getLogger(SPIInterceptor.class);

    private ConcurrentMap<String, Collector> rtCollectorMap = new ConcurrentHashMap<>(3);

    private ConcurrentMap<String, Collector> exceptionCollectorMap = new ConcurrentHashMap<>(3);

    private ConcurrentMap<String, Collector> qpsCollectorMap = new ConcurrentHashMap<>(3);

    private SentryClient sentryClient;

    private static final ThreadLocal<Map<String, AtomicInteger>> SPI_CALL_COUNTERS = new ThreadLocal<>();

    @Autowired
    private SpiManager spiManager;

    @PostConstruct
    public void init() {
        sentryClient = SentryClient.factory("detail.spi");
        sentryClient.openAutoCollector();
    }

    public Object intercept(ProceedingJoinPoint pjp) throws SpiCallException {
        MethodSignature signature = (MethodSignature)pjp.getSignature();
        Method method = signature.getMethod();
        List<Class> interfaces = ClassUtils.getAllInterfaces(pjp.getTarget().getClass());
        Class spiInterface = null;
        for (Class interf : interfaces) {
            if(SpiManager.isSpiInterface(interf)) {
                spiInterface = interf;
            }
        }
        if (null == spiInterface) {
            LOGGER.error("cann't find interface for class {}", pjp.getTarget().getClass());
        }
        Method[] methods = spiInterface.getMethods();
        boolean isSpiMethod = false;
        for (Method m : methods) {
            if (m.getName().equals(method.getName())) {
                isSpiMethod = true;
                break;
            }
        }
        String interfaceMethodKey = spiInterface.getName()+":"+method.getName()+":";
        if (!isSpiMethod) {
            SPI_CALL_COUNTERS.get().remove(interfaceMethodKey);
            return directCall(pjp);

        }
        if (isSpiProxied(interfaceMethodKey)) {
            SPI_CALL_COUNTERS.get().remove(interfaceMethodKey);
            return directCall(pjp);
        }

        if (null == spiInterface) {
            LOGGER.error("spi interface not found : {}", spiInterface);
        }
        try {
            setSpiProxied(interfaceMethodKey);
            return spiManager.spiCall(spiInterface, method, pjp.getArgs());
        } catch (Exception e) {
            LOGGER.error("call spi impl failed : {}", e);
            return null;
        }
    }

    private Object directCall(ProceedingJoinPoint pjp) {
        try {
            return pjp.proceed(pjp.getArgs());
        } catch (Throwable throwable) {
            LOGGER.error("call method failed : {}", pjp.getTarget().getClass(), throwable);
            return null;
        }
    }

    private static boolean isSpiProxied(String spiMethod) {
        Map<String, AtomicInteger> spiCallCounterMap = SPI_CALL_COUNTERS.get();
        if (null == spiCallCounterMap) {
            spiCallCounterMap = new HashMap<>(10);
            SPI_CALL_COUNTERS.set(spiCallCounterMap);
            return false;
        }
        if (null == spiCallCounterMap.get(spiMethod)) {
            spiCallCounterMap.put(spiMethod, new AtomicInteger(0));
            return false;
        } else {
            boolean ret =  spiCallCounterMap.get(spiMethod).get()%2 == 1;
            spiCallCounterMap.get(spiMethod).incrementAndGet();
            return ret;
        }
    }

    public static void setSpiProxied(String spiMethod) {
        Map<String, AtomicInteger> spiRecords = SPI_CALL_COUNTERS.get();
        if (null == spiRecords) {
            spiRecords = new HashMap<>(10);
            SPI_CALL_COUNTERS.set(spiRecords);
        }
        AtomicInteger counter = spiRecords.get(spiMethod);
        if (null == counter) {
            spiRecords.put(spiMethod, new AtomicInteger(1));
        }
    }

}
