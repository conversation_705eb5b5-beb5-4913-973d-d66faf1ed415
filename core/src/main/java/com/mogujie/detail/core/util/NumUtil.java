package com.mogujie.detail.core.util;


import java.math.BigDecimal;

/**
 * Created by <PERSON>iaoyao on 15/11/26.
 */
public class NumUtil {

    public static String formatNum(double num, int precision) {
        BigDecimal b = new BigDecimal(num);
        return b.setScale(precision, BigDecimal.ROUND_HALF_UP).toString();
    }

    public static String formatNum(double num) {
        return String.format("%.2f", num);
    }

    /**
     * 价格抽屉小数抹零
     * http://wiki.mogujie.org/pages/viewpage.action?pageId=60014762
     * @param priceCent
     * @return
     */
    public static String formatPriceDrawer(int priceCent) {
        if (priceCent % 100 == 0) {
            return (priceCent / 100) + "";
        } else if (priceCent % 10 == 0) {
            return String.format("%.1f", ((double) priceCent) / 100D);
        } else {
            return String.format("%.2f", ((double) priceCent) / 100D);
        }
    }
}
