package com.mogujie.detail.core.constant;

/**
 * @author: 桌子 <EMAIL>
 * @datetime: 2020-04-02 11:45
 */
public interface DetailConstants {

    public interface SourceParamType {
        /**
         * 直播商品进进图墙商品
         */
        int LIVE_ITEM_INTO_WALL_ITEM = 1;
    }

    /**
     * 用来保存商品Extra的key信息
     */
    public interface ItemExtraDOKeys {
        /**
         * 直播商品进图墙，会把这主播以及评论等相关信息放入到该字段对应的extra里面
         * "anchorItemActivityInfo":{
         *         "activityTitle":"卓诗尼2019冬新款加绒豆豆鞋女方头粗跟蝴蝶结低帮棉女", //活动标题，可能为空
         *         "activityPrice":15500, //单位：分
         *         "remarks":[   //评论id列表，0~10条，有序
         *             1002,
         *             1003,
         *             1009
         *         ],
         *         "anchorInfo":{   //主播id和切片链接
         *             "anchorId":"34123456", //主播id，即主播用户id
         *             "explainId":22794, //讲解视频id
         *             "slice":"https://1251964405.vod2.myqcloud.com/....mp4",//讲解视频
         *             "shortSlice":"https://1251964405.vod2.myqcloud.com/...webp"//5s切片
         *         },
         *         "activitySlogan":"主播试穿推荐" //标签
         *     }
         */
        String ANCHOR_ITEM_ACTIVITY_INFO = "anchorItemActivityInfo";
    }

    /**
     * 请求上带的参数key信息
     */
    public interface RequestParamKeys {

        /**
         * 要拿到sourceParams参数，后续需要用到，具体方案要看 https://mogu.feishu.cn/docs/doccnOUCXZe886S8ze6sgecTaPd#nubAeN
         * 具体内容如下 {"actorId":"1er9va0","type":1},后续意义看具体定义了。
         */
        String SOURCE_PARAMS = "sourceParams";
    }
}
