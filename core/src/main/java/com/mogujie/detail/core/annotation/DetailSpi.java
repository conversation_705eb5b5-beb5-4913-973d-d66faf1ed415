package com.mogujie.detail.core.annotation;


import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.ItemTag;
import com.mogujie.detail.core.constant.Platform;
import org.springframework.stereotype.Service;

import java.lang.annotation.*;

/**
 * Created by <PERSON><PERSON>oyao on 16/3/24.
 */


@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Service
public @interface DetailSpi {

    /**
     * 商品标签
     * @return
     */
    public ItemTag itemTag() default ItemTag.DEFAULT;

    /**
     * 业务标签
     * @return
     */
    public BizType bizTag() default BizType.NORMAL;

    /**
     * 平台支持
     * @return
     */
    public Platform platform() default Platform.ALL;

    /**
     * 属于哪个app
     * @return
     */
    public App app() default App.ALL;
}
