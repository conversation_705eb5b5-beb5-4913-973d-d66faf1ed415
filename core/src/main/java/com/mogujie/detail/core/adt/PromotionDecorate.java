package com.mogujie.detail.core.adt;


/**
 * @author: 桌子 <EMAIL>
 * @datetime: 2021-02-19 16:21
 */
public class PromotionDecorate {
    private String name;

    private String desc;

    private Integer cutPrice;

    private Integer order = 1;

    public PromotionDecorate() {}

    public PromotionDecorate(String name, String desc, Integer cutPrice, Integer order) {
        this(name, desc, cutPrice);
        this.order = order;
    }

    public PromotionDecorate(String name, String desc, Integer cutPrice) {
        this.name = name;
        this.desc = desc;
        this.cutPrice = cutPrice;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getCutPrice() {
        return cutPrice;
    }

    public void setCutPrice(Integer cutPrice) {
        this.cutPrice = cutPrice;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    @Override
    public String toString() {
        return "PromotionDecorate{" +
                "name='" + name + '\'' +
                ", desc='" + desc + '\'' +
                ", cutPrice=" + cutPrice +
                ", order=" + order +
                '}';
    }
}
