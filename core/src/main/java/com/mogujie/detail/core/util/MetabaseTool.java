package com.mogujie.detail.core.util;

import com.mogujie.metabase.client.MetaClient;
import com.mogujie.metabase.client.MetabaseConfigure;

/**
 * Created by anshi on 18/3/9.
 */
public class MetabaseTool {

    private static volatile MetaClient metaClient;

    public static MetaClient getMetaClient() {
        if (metaClient == null) {
            synchronized (MetabaseTool.class) {
                if (metaClient == null) {
                    MetabaseConfigure configure = new MetabaseConfigure();
                    configure.setConfigGroup("detailConf");
                    metaClient = MetaClient.client2(configure);
                }
            }
        }
        return metaClient;
    }

    public static String getValue(String key) {
        MetaClient metaClient = getMetaClient();
        return metaClient.getConfiguration(key);
    }

    public static boolean isOn(String key, boolean defaultValue) {
        try {
            String value = getValue(key);
            return Boolean.parseBoolean(value);
        } catch (Throwable e) {
        }
        return defaultValue;
    }
}
