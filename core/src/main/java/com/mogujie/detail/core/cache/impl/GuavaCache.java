package com.mogujie.detail.core.cache.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.mogujie.detail.core.cache.ICacheUtil;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;

/**
 * Created by xiaoyao on 15/12/15.
 */
public class GuavaCache implements ICacheUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(GuavaCache.class);

    private Cache<String, String> cache = CacheBuilder
            .newBuilder()
            .expireAfterWrite(4L, TimeUnit.HOURS)
            .maximumSize(10000L)
            .build();;

    @Setter
    @Getter
    private Long expireHours = 4L;

    @Setter
    @Getter
    private Long maxmumSize = 10000L;


    @Override
    public String getString(String key) {
        if (null == key) {
            throw new IllegalArgumentException("key is null");
        }
        try {
            return cache.getIfPresent(key);
        } catch (Exception e) {
            LOGGER.error("get guava object failed, {}", e);
            return null;
        }
    }

    @Override
    public boolean setString(String key, String value, int expireSeconds) {
        if (null == key || null == value) {
            throw new IllegalArgumentException("key or value is null");
        }
        try {
            cache.put(key, value);
            return true;
        } catch (Exception e) {
            LOGGER.error("set guava object failed, {}", e);
            return false;
        }
    }

    @Override
    public <T> T getObject(String key, Class<T> clazz) {
        if (null == key) {
            throw new IllegalArgumentException("key is null");
        }
        if (clazz == null) {
            throw new IllegalArgumentException("clazz is null");
        }
        try {
            String valStr = cache.getIfPresent(key);
            if (null == valStr) {
                return null;
            }
            return JSON.parseObject(valStr, clazz);
        } catch (Exception e) {
            LOGGER.error("get guava object failed, {}", e);
            return null;
        }
    }

    @Override
    public boolean setObject(String key, Object value, int expireSeconds) {
        if (null == key || null == value) {
            throw new IllegalArgumentException("key or value is null");
        }
        try {
            String valStr = JSON.toJSONString(value);
            cache.put(key, valStr);
            return true;
        } catch (Exception e) {
            LOGGER.error("set guava object failed, {}", e);
            return false;
        }
    }

    @Override
    public boolean expire(String key) {
        if (null == key) {
            throw new IllegalArgumentException("key is null");
        }
        try {
            cache.invalidate(key);
            return true;
        } catch (Exception e) {
            LOGGER.error("expire guava object failed, {}", e);
            return false;
        }
    }
}
