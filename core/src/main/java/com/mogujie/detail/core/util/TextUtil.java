package com.mogujie.detail.core.util;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 文本处理工具类
 * @auther huasheng
 * @time 19/3/19 16:04
 */
public class TextUtil {

    /**
     * https://www.cnblogs.com/rrooyy/p/5349978.html
     * 去除换行符
     * @return
     */
    public static String removeLinebreak(String text){
        if(StringUtils.isEmpty(text)){
            return text;
        }
        try {
            Pattern p = Pattern.compile("\\s*|\t|\r|\n");
            Matcher m = p.matcher(text);
            text = m.replaceAll("");
            text= text.replaceAll("\u2028","");
            return text;

        }catch (Exception ex){
            //not to do
        }
        return text;
    }


}
