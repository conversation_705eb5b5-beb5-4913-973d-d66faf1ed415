package com.mogujie.detail.core.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meili.service.shopcenter.domain.entity.ShopInfo;
import com.meili.service.shopcenter.util.ShopInfoTagsUtil;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.RouteInfo;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.DetailConstants;
import com.mogujie.detail.core.translator.ITranslator;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.basic.ItemExtraDO;
import com.mogujie.service.item.domain.basic.ItemPreSaleDO;
import com.mogujie.service.item.domain.basic.ItemTagDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by anshi on 18/1/15.
 */
public class ContextUtil {

    // 美丽买手店礼包商品数字标
    private static final Integer MAISHOU_GIFT_TAG = 1897;

    //商品隐藏数字标：在详情页显示商品不存在
    private static final Integer HIDE_ITEM_TAG = 1251;

    //店铺隐藏数字标：在详情页显示商品不存在
    private static final Integer HIDE_ITEM_SHOP_TAG = 1908;

    //商品下架展示数字标：在详情页显示商品下架
    private static final Integer OFFLINE_ITEM_TAG = 1252;

    //店铺下架展示数字标：在详情页显示商品下架
    private static final Integer OFFLINE_ITEM_SHOP_TAG = 1486;

    //春节期间不打烊的商品数字标
    private static final Integer SPRING_NO_SHUTDOWN_TAG = 1358;

    //购物津贴数字标
    private static final Integer BONUS_ITEM_TAG = 1618;

    // 直播秒杀中的标
    private static final Integer LIVE_SECKILL_TAG = 8080;

    public static final String SPRING_FESTIVAL_CONFIG_KEY = "spring_festival_shutdown_time";

    public static final String IS_30_DAY_DELIVERY_ITEM = "is30dayDeliveryItem";

    public static String getNumTagString(ItemDO itemDO) {
        return StringUtils.join(getNumTags(itemDO), ",");
    }

    public static List<String> getNumTags(ItemDO itemDO) {
        List<ItemTagDO> itemTagList = itemDO.getItemTags();
        if (itemTagList == null || itemTagList.size() == 0) {
            return Collections.EMPTY_LIST;
        }
        List<String> numTags = new ArrayList<>();
        for (ItemTagDO tag : itemTagList) {
            if ("tags".equals(tag.getTagKey())) {
                numTags.add(tag.getTagValue());
            }
        }
        return numTags;
    }

    /**
     * 是否为直播sku选择器
     *
     * @param context
     * @return
     */
    public static boolean isLiveSkuDetail(DetailContext context) {
        if (context == null) {
            return false;
        }
        RouteInfo routeInfo = context.getRouteInfo();
        if (routeInfo.getBizType() == BizType.SKU && routeInfo.getVersion().startsWith("live.")) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 是否为普通详情页面（非直播、im等sku接口）
     *
     * @return
     */
    public static boolean isNormalPageDetail(DetailContext context) {
        try {
            RouteInfo routeInfo = context.getRouteInfo();
            if (routeInfo.getBizType() == BizType.NORMAL /* 普通Biz */
                    || (routeInfo.getBizType() == BizType.SHARE && StringUtils.equalsIgnoreCase(routeInfo.getVersion(), "normal"))) { /* 普通单车分享页 */
                return true;
            }
        } catch (Throwable e) {
        }
        return false;
    }

    /**
     * 是否为普通详情页、普通单车详情、普通直播详情
     *
     * @return
     */
    public static boolean isNormalBizDetail(DetailContext context) {
        try {
            RouteInfo routeInfo = context.getRouteInfo();
            if (routeInfo.getBizType() == BizType.NORMAL /* 普通Biz */
                    || (routeInfo.getBizType() == BizType.SHARE && StringUtils.equalsIgnoreCase(routeInfo.getVersion(), "normal")) /* 普通单车分享页 */
                    || (routeInfo.getBizType() == BizType.SKU && routeInfo.getVersion().startsWith("live.normal"))/* 普通直播sku接口 */
                    || (routeInfo.getBizType() == BizType.SKU && routeInfo.getVersion().startsWith("normal"))) { /* 普通sku接口 */
                return true;
            }
        } catch (Throwable e) {
        }
        return false;
    }

    public static boolean isMedicalItem(DetailContext context) {
        ItemDO itemDO = context.getItemDO();
        String[] categories = MetabaseTool.getValue("medical_beauty_categories").split(",");
        for (String category : categories) {
            if (itemDO.getCids().contains("#" + category + "#")) {
                return true;
            }
        }
        return false;
    }

    /**
     * 商品在某些场景下隐藏
     * 用于对商品做平台隔离等
     *
     * @param context
     * @param itemDO
     * @return
     */
    public static boolean hideItemWithContext(DetailContext context, ItemDO itemDO) {
        try {
            ShopInfo shopInfo = context.getItemDO().getShopInfo();
            Set<Integer> shopTags = null;
            if (shopInfo == null) {
                shopTags = Collections.EMPTY_SET;
            } else {
                shopTags = ShopInfoTagsUtil.stringToSet(shopInfo.getTags());
            }
            int market = (int) itemDO.getVerticalMarket();
            List<ItemTagDO> itemTags = itemDO.getItemTags();
            //美丽买手店商品只在美丽买手店展示；其他商品不在美丽买手店展示
            if ((market == 14 && context.getRouteInfo().getApp() != App.MSD)
                    || (market != 14 && context.getRouteInfo().getApp() == App.MSD)) {
                return true;
            }
            //非百货平台，则屏蔽百货商品
            if ((market == 12 && context.getRouteInfo().getApp() != App.BH)) {
                return true;
            }
            //天美意商品只在天美意平台展示
            if (market == 13 && !context.getRouteInfo().getVersion().equalsIgnoreCase("teenmix")) {
                return true;
            }

            //一元抽奖商品只能在一元夺宝页面展示
            if (isOneYuanItem(itemDO) && context.getRouteInfo().getBizType() != BizType.ONEYUANTREASURE) {
                return true;
            }
            //商品有屏蔽标，则在详情页显示商品不存在
            if (TagUtil.isContainsTag(itemTags, HIDE_ITEM_TAG)) {
                return true;
            }
            //店铺有屏蔽标，则在详情页展示商品不存在
            if (shopTags.contains(HIDE_ITEM_SHOP_TAG)) {
                return true;
            }
            //买手店普通详情页隐藏礼包商品
//            if (context.getRouteInfo().getApp() == App.MSD
//                    && !"gift".equals(context.getRouteInfo().getVersion())
//                    && TagUtil.isContainsTag(itemTags, MAISHOU_GIFT_TAG)) {
//                return true;
//            }
        } catch (Throwable e) {
        }
        return false;
    }

    /**
     * 是否一元抽奖商品
     *
     * @param itemDO
     * @return
     */
    public static boolean isOneYuanItem(ItemDO itemDO) {
        if (CollectionUtils.isEmpty(itemDO.getItemTags())) {
            return false;
        }

        for (ItemTagDO itemTagDO : itemDO.getItemTags()) {
            if (itemTagDO.getTagKey().equals("tags")) {
                if (itemTagDO.getTagValue().equals("10042")) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 商品在某些时候需要在详情页展示为下架
     *
     * @param context
     * @return
     */
    public static boolean offlineItemWithContext(DetailContext context) {
        try {
            ShopInfo shopInfo = context.getItemDO().getShopInfo();
            Set<Integer> shopTags = null;
            if (shopInfo == null) {
                shopTags = Collections.EMPTY_SET;
            } else {
                shopTags = ShopInfoTagsUtil.stringToSet(shopInfo.getTags());
            }
            if (TagUtil.isContainsTag(context.getItemDO(), OFFLINE_ITEM_TAG)) {
                return true;
            } else if (shopTags.contains(OFFLINE_ITEM_SHOP_TAG)) {
                return true;
            }
            //直播供应链模板商品需要下架
            if (isLiveSupplyTemplateItem(context.getItemDO())) {
                return true;
            }
        } catch (Throwable e) {
        }
        return false;

    }

    /**
     * 根据商品获取其market
     *
     * @param itemDO
     * @return
     */
    public static int getMarketByItem(ItemDO itemDO) {
        try {
            int marketValue = 8;
            switch ((int) itemDO.getVerticalMarket()) {
                case 8://蘑菇街
                    marketValue = 8;
                    break;
                case 100://蘑菇街
                    marketValue = 8;
                    break;
                case 11://美丽说
                    marketValue = 11;
                    break;
                case 12://百货
                    marketValue = 12;
                    break;
                case 13://微信独立小程序
                    marketValue = 13;
                    break;
                case 14://美丽买手店
                    marketValue = 14;
                    break;
                default:
                    marketValue = 8;
                    break;
            }
            return marketValue;
        } catch (Throwable e) {
            return 8;
        }
    }

    /**
     * 根据请求上下文获取当前的market
     *
     * @param context
     * @return
     */
    public static int getMarketByContext(DetailContext context) {
        try {
            int marketValue = 8;
            switch (context.getRouteInfo().getApp()) {
                case MLS:
                    marketValue = 11;
                    break;
                case BH:
                    marketValue = 12;
                    break;
                case MSD:
                    marketValue = 14;
                    break;
                default:
                    marketValue = 8;
                    break;
            }
            return marketValue;
        } catch (Throwable e) {
            return 8;
        }
    }

    /**
     * 当前请求是否为debug模式
     *
     * @param context
     * @return
     */
    public static boolean isDebugMode(DetailContext context) {
        if (context == null) {
            return false;
        }
        return "true".equals(context.getParam("debug"));
    }

    public static Map<String, Object> getContextDebugInfo(DetailContext context) {
        Map<String, Object> debugInfo = new HashMap<>();
        try {
            debugInfo.put("itemDO", context.getItemDO());
            debugInfo.put("params", context.getParams());
            debugInfo.put("thirdObj", context.getContexts());
            debugInfo.put("componentIds", context.getComponentIds());
            debugInfo.put("loginUserId", context.getLoginUserId());
            debugInfo.put("routeInfo", context.getRouteInfo());
            debugInfo.put("isDyn", context.isDyn());
            debugInfo.put("clientIp", context.getClientIp());
            debugInfo.put("isError", context.isError());

            RouteInfo route = context.getRouteInfo();
            String biz = route.getBizType() == BizType.CHANNEL ? route.getChannelType() : route.getBizType().name();
            String namespacefolder = (route.getApp().name() + "/" + route.getPlatform().name() + "/" + biz).toLowerCase();
            Map<String, ITranslator> translatorMap = context.getTranslatorMappingHolder().getTranslatorMap();
            List<String> namespaceTranslators = translatorMap.entrySet().stream().filter(entry -> entry.getKey().startsWith(namespacefolder))
                    .map(entry -> entry.getKey().replace(namespacefolder + ".", ""))
                    .collect(Collectors.toList());
            debugInfo.put(namespacefolder, namespaceTranslators);
            debugInfo.put("debugObject", context.getDebugObject());
        } catch (Throwable e) {
        }
        return debugInfo;
    }

    public static Boolean isPresaleItem(ItemDO item) {
        int nowTime = (int) (System.currentTimeMillis() / 1000);
        if (null != item.getItemPreSaleDO()) {
            ItemPreSaleDO preSale = item.getItemPreSaleDO();
            if (preSale.getStart() < nowTime && nowTime < preSale.getEnd()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 直播供应链模板商品
     *
     * @param itemDO
     * @return
     */
    public static Boolean isLiveSupplyTemplateItem(ItemDO itemDO) {
        return TagUtil.isContainsTag(itemDO.getItemTags(), 1246);
    }

    /**
     * 直播供应链影子商品
     *
     * @param itemDO
     * @return
     */
    public static Boolean isLiveSupplyShadowItem(ItemDO itemDO) {
        return TagUtil.isContainsTag(itemDO.getItemTags(), 1963);
    }

    /**
     * 是否为直播商品进图强商品
     * @param detailContext
     * @return
     */
    public static boolean isLiveInWallItem(DetailContext detailContext) {
        String sourceParams = detailContext.getParam(DetailConstants.RequestParamKeys.SOURCE_PARAMS);
        if (StringUtils.isBlank(sourceParams) || detailContext.getItemDO() == null) {
            return false;
        }

        if(!TagUtil.isContainsTag(detailContext.getItemDO().getItemTags(), 2002)) {
            return false;
        }

        JSONObject sourceObject = JSON.parseObject(sourceParams);
        if (sourceObject == null || !Objects.equals(sourceObject.getInteger("type"), DetailConstants.SourceParamType.LIVE_ITEM_INTO_WALL_ITEM)) {
            return false;
        }

        ItemExtraDO itemExtraDO = detailContext.getItemDO().getItemExtraDO();
        if (itemExtraDO == null || StringUtils.isBlank(itemExtraDO.getFeatures().get(DetailConstants.ItemExtraDOKeys.ANCHOR_ITEM_ACTIVITY_INFO))) {
            return false;
        }
        return true;
    }


    /**
     * 直播供应链大市场原商品
     *
     * @param itemDO
     * @return
     */
    public static Boolean isLiveSupplySourceItem(DetailItemDO itemDO) {
        return itemDO.getFeatures() != null && itemDO.getFeatures().get("supplierCopyItemId") != null;
    }

    /**
     * 根据上下文判断商品是否有30天发货服务
     *
     * @param context
     * @return
     */
    public static Boolean contain30dayDeliveryService(DetailContext context) {
        if (context.getContext(IS_30_DAY_DELIVERY_ITEM) != null) {
            return (Boolean) context.getContext(IS_30_DAY_DELIVERY_ITEM);
        } else {
            return false;
        }
    }

    public static Boolean isInSpringFestival() {
        try {
            int now = (int) (System.currentTimeMillis() / 1000);
            String springFestivalConfig = MetabaseTool.getValue(SPRING_FESTIVAL_CONFIG_KEY);
            if (springFestivalConfig == null) {
                return false;
            }
            JSONObject springShutdownTime = JSON.parseObject(springFestivalConfig);
            //平台时间外，一律不打烊
            if (springShutdownTime == null
                    || springShutdownTime.getInteger("startTime") > now
                    || now >= springShutdownTime.getInteger("endTime")) {
                return false;
            }
            return true;
        } catch (Throwable e) {
            return false;
        }
    }

    /**
     * 春节打烊最晚发货时间
     *
     * @return
     */
    public static Integer springDeliveryEndTime() {
        try {
            String springFestivalConfig = MetabaseTool.getValue(SPRING_FESTIVAL_CONFIG_KEY);
            if (springFestivalConfig == null) {
                return null;
            }
            JSONObject springShutdownTime = JSON.parseObject(springFestivalConfig);
            if (MapUtils.isEmpty(springShutdownTime)){
                return null;
            }
            return springShutdownTime.getInteger("deliveryEndTime");
        } catch (Throwable e) {
            return null;
        }
    }



    /**
     * 春节期间打烊商品
     *
     * @param itemDO
     * @return
     */
    public static Boolean isSpringFestivalShutdownItem(ItemDO itemDO) {
        try {
            if (!isInSpringFestival()) {
                return false;
            }
            //其他情况下，不带标的商品认为是打烊商品
            return !TagUtil.isContainsTag(itemDO.getItemTags(), SPRING_NO_SHUTDOWN_TAG);
        } catch (Throwable e) {
            return false;
        }
    }

    /**
     * 是否为可以使用购物津贴的商品
     *
     * @return
     */
    public static Boolean isBonusItem(DetailContext context) {
        if (context == null || context.getItemDO() == null) {
            return false;
        }
        return TagUtil.isContainsTag(context.getItemDO(), BONUS_ITEM_TAG);
    }

    public static int getClientVersion(DetailContext context){
        Integer version = 0;
        if (StringUtils.isNotBlank(context.getParam("_av"))) {
            try {
                version = Integer.parseInt(context.getParam("_av"));
            } catch (Throwable e) {
                version = 0;
            }
        }
        return version;
    }

    /**
     * 商品是否直播秒杀中
     *
     * @param itemDO 商品数据
     * @return 商品是否直播秒杀中
     */
    public static boolean isLiveSecKilling(ItemDO itemDO) {
        return itemDO != null && TagUtil.isContainsTag(itemDO.getItemTags(), LIVE_SECKILL_TAG);
    }

    /**
     * 是否是搭配购。搭配购首先是闪购，然后在闪购的KV标中的nbt特定是86
     * @param context context
     */
    public static boolean isDaPeiGou(DetailContext context) {
        try {
            List<com.mogujie.service.item.domain.basic.ItemTagDO> tags = context.getItemDO().getItemTags();
            if (CollectionUtils.isEmpty(tags)){
                return false;
            }
            for (com.mogujie.service.item.domain.basic.ItemTagDO tag : tags) {
                if (tag.getTagKey().equals("shango")) {
                    String[] tagValuePairs = tag.getTagValue().split("\\|");
                    for (String tagValuePair : tagValuePairs) {
                        String[] pair = tagValuePair.split(":");
                        if (pair.length != 2) {
                            continue;
                        }
                        if ("nbt".equals(pair[0])) {
                            return "86".equals(pair[1]);
                        }
                    }
                }
            }

            return false;

        } catch (Throwable ignore) {
        }
        return false;
    }
}
