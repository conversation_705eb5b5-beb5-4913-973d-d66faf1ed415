package com.mogujie.detail.core.manager;

import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailContextHolder;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.FuncRet;
import com.mogujie.detail.core.adt.IDetailSpout;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.adt.ModuleDO;
import com.mogujie.detail.core.adt.SnapDetailSpout;
import com.mogujie.detail.core.constant.AlarmGroup;
import com.mogujie.detail.core.constant.DetailAlarmType;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.spi.SpiManager;
import com.mogujie.detail.core.util.AlarmUtil;
import com.mogujie.detail.core.util.ContextUtil;
import com.mogujie.itemcenter.common.async.LurkerCallableAdaptor;
import com.mogujie.metabase.spring.client.MetabaseClient;
import com.mogujie.sentry.SentryClient;
import com.mogujie.sentry.collector.Collector;
import com.mogujie.sentry.type.AlarmType;
import com.mogujie.sentry.type.CollectorType;
import org.apache.commons.lang.StringUtils;
import org.mogujie.cayenne.timeclient.constans.LurkerConsts;
import org.mogujie.cayenne.timeclient.core.LurkerRegister;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;

/**
 * Created by xiaoyao on 16/8/8.
 */
public class TopologyManager {

    private static final Logger LOGGER = LoggerFactory.getLogger(TopologyManager.class);

    @Autowired
    private IDetailSpout detailSpout;

    @Autowired
    private SnapDetailSpout itemSnapDetailSpout;

    @Autowired
    private SnapDetailSpout tradeSnapDetailSpout;

    @Resource(name = "confMetabaseClient")
    private MetabaseClient metabaseClient;

    @Autowired
    private TranslatorManager translatorManager;

    /**
     * 商品信息获取
     */
    public FuncRet<Map<String, Object>> emit(DetailContext context) throws DetailException {
        try {
            if (context.getLoginUserId() != null) {
                LurkerRegister.register(LurkerConsts.CURRENT_USER, IdConvertor.idToUrl(context.getLoginUserId()));
            }
        } catch (Throwable e) {
        }
        detailSpout.decorateItemDO(context);
        DetailItemDO item = context.getItemDO();
        if (null == item || ContextUtil.hideItemWithContext(context, item)) {
            return new FuncRet<>(false, "商品不存在");
        }

        if (CollectionUtils.isEmpty(context.getComponentIds())) {
            AlarmUtil.sentryAlarm(metabaseClient.get(AlarmGroup.INTERNAL_ALARM_MEMBER), "详情页数据异常", "未设置数据拓扑，iid: " + context.getItemId() + ", template: " + context.getRouteInfo().toString(), AlarmType.TT, DetailAlarmType.InvalidTopo);
            return new FuncRet<Map<String, Object>>(true, "未设置数据拓扑");
        }

//        if (App.MGJ.equals(context.getRouteInfo().getApp()) && item.getVerticalMarket() == 11L) {
//            return new FuncRet<>(false, BaseConstants.INVALID_ITEM_SHOW);
//        }
        Map<Class, IModuleDOProvider> moduleMap = translatorManager.getAllDependModules(context);
        Map<Class, Future<ModuleDO>> doMap = new HashMap<>(moduleMap.size());
        if (moduleMap != null && !moduleMap.isEmpty()) {
            for (Map.Entry<Class, IModuleDOProvider> entry : moduleMap.entrySet()) {
                if (!isModuleOn(entry.getKey().getSimpleName())) {
                    continue;
                }
                RunModuleTask task = new RunModuleTask(entry.getValue(), context);
                Future<ModuleDO> moduleDOFuture = ThreadPoolExecutors.fastExecutorService.submit(new LurkerCallableAdaptor<ModuleDO>(task));
                doMap.put(entry.getKey(), moduleDOFuture);
            }
        }
        Map<String, Object> data = translatorManager.translate(doMap, context);
        if (context.isError()) {
            throw new DetailException("数据异常");
        } else {
            return new FuncRet<Map<String, Object>>(true, "", data);
        }
    }

    /**
     * 快照信息获取
     *
     * @param context 请求上下文
     * @param snapId  快照id,获取商品编辑快照时使用
     * @param orderId 订单id,获取交易快照时使用
     * @return
     * @throws DetailException
     */
    public FuncRet<Map<String, Object>> snapEmit(DetailContext context, String snapId, String orderId) throws DetailException {
        try {
            if (context.getLoginUserId() != null) {
                LurkerRegister.register(LurkerConsts.CURRENT_USER, IdConvertor.idToUrl(context.getLoginUserId()));
            }
        } catch (Throwable e) {
        }
        if (StringUtils.isNotBlank(snapId)) {
            // 商品编辑快照组装
            itemSnapDetailSpout.decorateSnapItemDO(context, snapId);
        } else {
            // 交易快照组装
            tradeSnapDetailSpout.decorateSnapItemDO(context, orderId);
        }
        DetailItemDO item = context.getItemDO();
        if (null == item || ContextUtil.hideItemWithContext(context, item)) {
            return new FuncRet<>(false, "商品不存在");
        }

        if (CollectionUtils.isEmpty(context.getComponentIds())) {
            AlarmUtil.sentryAlarm(metabaseClient.get(AlarmGroup.INTERNAL_ALARM_MEMBER), "详情页数据异常", "未设置数据拓扑，iid: " + context.getItemId() + ", template: " + context.getRouteInfo().toString(), AlarmType.TT, DetailAlarmType.InvalidTopo);
            return new FuncRet<Map<String, Object>>(true, "未设置数据拓扑");
        }

        Map<Class, IModuleDOProvider> moduleMap = translatorManager.getAllDependModules(context);
        Map<Class, Future<ModuleDO>> doMap = new HashMap<>(moduleMap.size());
        if (moduleMap != null && !moduleMap.isEmpty()) {
            for (Map.Entry<Class, IModuleDOProvider> entry : moduleMap.entrySet()) {
                if (!isModuleOn(entry.getKey().getSimpleName())) {
                    continue;
                }
                RunModuleTask task = new RunModuleTask(entry.getValue(), context);
                Future<ModuleDO> moduleDOFuture = ThreadPoolExecutors.fastExecutorService.submit(new LurkerCallableAdaptor<ModuleDO>(task));
                doMap.put(entry.getKey(), moduleDOFuture);
            }
        }
        Map<String, Object> data = translatorManager.translate(doMap, context);
        if (context.isError()) {
            throw new DetailException("数据异常");
        } else {
            return new FuncRet<Map<String, Object>>(true, "", data);
        }
    }

    private boolean isModuleOn(String name) {
        try {
            return this.metabaseClient.getBoolean(name);
        } catch (Throwable e) {
            LOGGER.error("{} not set", name);
        }
        return true;
    }

    public static class RunModuleTask implements Callable<ModuleDO> {

        private IModuleDOProvider moduleDOProvider;

        private DetailContext context;

        private String moduleName;

        private static SentryClient sentryClient;

        static {
            sentryClient = SentryClient.factory("detail");
            sentryClient.openAutoCollector();
        }

        public RunModuleTask(IModuleDOProvider moduleDOProvider, DetailContext context) {
            this.moduleDOProvider = moduleDOProvider;
            this.context = context;
            this.moduleName = moduleDOProvider.getClass().getSimpleName();
        }

        @Override
        public ModuleDO call() {
            // 设置MDC参数,供logback获取
            MDC.put("topo", context.getParam("template"));
            MDC.put("iid", IdConvertor.idToUrl(context.getItemId()));
            MDC.put("position", moduleName);
            ModuleDO moduleDO = null;
            long startTime = System.currentTimeMillis();
            try {
                addQps();
                DetailContextHolder.set(this.context);
                moduleDO = moduleDOProvider.emit(context);
            } catch (Throwable e) {
                addException();
                LOGGER.error("get ModuleDO {} failed.", moduleDOProvider, e);
            } finally {
                DetailContextHolder.clear();
                SpiManager.clear();
                MDC.clear();
            }
            long endTime = System.currentTimeMillis();
            addRt((int) (endTime - startTime));
            return moduleDO;
        }

        private void addQps() {
            Map<String, String> tags = new HashMap<>(2);
            tags.put("module", moduleName);
            Collector rtCollector = sentryClient.getCollector("module.qps", tags, 1, CollectorType.SUM);
            rtCollector.put(1);
        }

        private void addRt(int rt) {
            Map<String, String> tags = new HashMap<>(2);
            tags.put("module", moduleName);
            Collector rtCollector = sentryClient.getCollector("module.rt", tags, 10, CollectorType.AVG);
            rtCollector.put(rt);
        }

        private void addException() {
            Map<String, String> tags = new HashMap<>(2);
            tags.put("module", moduleName);
            Collector rtCollector = sentryClient.getCollector("module.exception", tags, 1, CollectorType.SUM);
            rtCollector.put(1);
        }
    }
}
