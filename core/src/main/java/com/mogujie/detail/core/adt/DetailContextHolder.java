package com.mogujie.detail.core.adt;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17/3/20.
 */
public class DetailContextHolder {

    private static final ThreadLocal<DetailContext> contextHolder = new ThreadLocal<>();

    public static DetailContext get() {
        return contextHolder.get();
    }

    public static void set(DetailContext context) {
        contextHolder.set(context);
    }

    public static void clear() {
        contextHolder.remove();
    }

}
