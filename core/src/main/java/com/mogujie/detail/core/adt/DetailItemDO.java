package com.mogujie.detail.core.adt;

import com.meili.service.shopcenter.domain.entity.ShopInfo;
import com.mogujie.detail.core.constant.ItemTag;
import com.mogujie.service.item.domain.basic.ItemDO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * Created by xiaoyao on 16/8/16.
 */
public class DetailItemDO extends ItemDO {

    public DetailItemDO(ItemDO item) {
        this.setAddress(item.getAddress());
        this.setBrandId(item.getBrandId());
        this.setCategoryId(item.getCategoryId());
        this.setCids(item.getCids());
//        this.setCity(item.getCity());
//        this.setClassId(item.getClassId());
        this.setClassIds(item.getClassIds());
        this.setCode(item.getCode());
//        this.setCurrency(item.getCurrency());
        this.setDescription(item.getDescription());
//        this.setEnds(item.getEnds());
        this.setExtra(item.getJsonExtra());
//        this.setFeatureCc(item.getFeatureCc());
//        this.setFeatures(item.getFeatures());
//        this.setFinalPrice(item.getFinalPrice());
//        this.setFirstStartTime(item.getFirstStartTime());
//        this.setImageId(item.getImageId());
//        this.setInventoryId(item.getInventoryId());
        this.setInventoryType(item.getInventoryType());
        this.setIsShelf(item.getIsShelf());
        this.setItemDetailDO(item.getItemDetailDO());
        this.setItemExtraDO(item.getItemExtraDO());
        this.setItemId(item.getItemId());
//        this.setItemInfoId(item.getItemInfoId());
//        this.setItemInventoryDO(item.getItemInventoryDO());
        this.setItemPreSaleDO(item.getItemPreSaleDO());
        this.setItemSkuDOList(item.getItemSkuDOList());
        this.setItemState(item.getItemState());
//        this.setItemTagDTO(item.getItemTagDTO());
//        this.setItemType(item.getItemType());
        this.setJsonExtra(item.getJsonExtra());
//        this.setLabels(item.getLabels());
//        this.setLatitude(item.getLatitude());
//        this.setLongitude(item.getLongitude());
        this.setMainImage(item.getMainImage());
//        this.setPicUrl(item.getPicUrl());
        this.setPostageId(item.getPostageId());
        this.setProcessType(item.getProcessType());
        this.setPackedProperties(item.getPackedProperties());
//        this.setProv(item.getProv());
        this.setQuantity(item.getQuantity());
        this.setReservePrice(item.getReservePrice());
        this.setShopId(item.getShopId());
        this.setStarts(item.getStarts());
        this.setStatus(item.getStatus());
        this.setStringProperty(item.getPackedProperties());
//        this.setSyncVersion(item.getSyncVersion());
//        this.setTagPrice(item.getTagPrice());
        this.setTags(item.getTags());
//        this.setTagsInTagCenter(item.getTagsInTagCenter());
        this.setTitle(item.getTitle());
        this.setImages(item.getImages());
        this.setUserId(item.getUserId());
        this.setVerticalMarket(item.getVerticalMarket());
        this.setXdItemId(item.getXdItemId());
        this.setGmtCreate(item.getGmtCreate());
        this.setGmtModify(item.getGmtModify());
        this.setIsDeleted(item.getIsDeleted());
        this.setFirstOnline((int) (item.getFirstOnLineTime() / 1000));
        this.setItemTags(item.getItemTags());
    }

    /**
     * 折扣描述
     */
    @Getter
    @Setter
    private String discountDesc;

    /**
     * 会员价（会员所能享受的价格，当前用户若不是会员，则无法享受该价格）
     */
    @Getter
    @Setter
    private Map<Long, Long> memberPrice;

    /**
     * 普通用户价
     */
    @Getter
    @Setter
    private Map<Long, Long> normalUserPrice;

    /**
     * 其他价格
     */
    @Getter
    @Setter
    private List<OtherPrice> otherPrices;

    /**
     * 商品标签
     */
    @Getter
    @Setter
    private List<ItemTag> itemBizTags;

    @Getter
    @Setter
    private String lowPrice;

    @Getter
    @Setter
    private String highPrice;

    @Getter
    @Setter
    private String lowNowPrice;

    @Getter
    @Setter
    private String highNowPrice;

    @Getter
    @Setter
    private Long lowNowPriceVal;

    @Getter
    @Setter
    private Long highNowPriceVal;

    @Getter
    @Setter
    private Long totalStock;

    /**
     * 活动原始总库存
     */
    @Getter
    @Setter
    private Long actOriginalTotalStock;

    /**
     * 活动总的剩余库存
     */
    @Getter
    @Setter
    private Long remindTotalStock;

    @Getter
    @Setter
    private ShopInfo shopInfo;

    /**
     * 第一次上架时间(s)
     */
    @Getter
    @Setter
    private int firstOnline;

    /**
     * 限购数量
     */
    @Getter
    @Setter
    private Integer limitNum;

    /**
     * 商品extra
     * com.mogujie.service.item.api.basic.ItemExtraService#queryItemExtraInfo
     * com.mogujie.service.item.domain.basic.ItemExtraDO#features
     */
    @Getter
    @Setter
    private Map<String, String> features;

    /**
     * 价格渠道
     */
    @Getter
    @Setter
    private Integer priceChannel;

    /**
     * 计价接口返回的价格code
     */
    @Getter
    @Setter
    private String promotionCode;

    /**
     * 优惠后价格：
     */
    @Getter
    @Setter
    private Long promotionPrice;

    /**
     * 计算券后价，需要把明细返回回来。
     * @see https://mogu.feishu.cn/docs/doccnAzCJ9MA6DN6xQdhZojW3ke
     */
    @Getter
    @Setter
    private List<PromotionDecorate> promotionPriceDetail;
}
