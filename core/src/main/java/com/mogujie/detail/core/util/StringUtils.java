package com.mogujie.detail.core.util;

import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @auther huasheng
 * @time 19/4/9 15:33
 */
public class StringUtils {

    public static boolean isNotEmpty(String value) {
        return !isEmpty(value);
    }


    public static boolean isEmpty(String value) {
        if (value == null || value.trim().isEmpty()) {
            return true;
        }
        return false;
    }


    /**
     * 去除空格、回车、换行符、制表符
     *
     * @param str
     * @return
     */
    public static String replaceBlank(String str) {
        String dest = "";
        if (str != null) {
            Pattern p = Pattern.compile("\\s*|\t|\r|\n");
            Matcher m = p.matcher(str);
            dest = m.replaceAll("");
        }
        return dest;
    }

    public static String objToString(Object o) {
        if(o==null){
            return org.apache.commons.lang3.StringUtils.EMPTY;
        }
        return o.toString();
    }



    public static Long objToLong(Object o) {
        if(o==null){
            return 0l;
        }
        return NumberUtils.toLong(o.toString());
    }

    public static Integer objToInteger(Object o) {
        if(o==null){
            return 0;
        }
        return NumberUtils.toInt(o.toString());
    }


    public static Float objToFloat(Object o) {
        if(o==null){
            return 0f;
        }
        return NumberUtils.toFloat(o.toString());
    }

    /**
     * 将一个按照固定分隔符分割开的字符串，切分成指定类型的list的工具类
     *
     * @param source    源字符串
     * @param separator 切分字符串的分割符
     * @param clazz     切分后的list的类型
     * @param <T>       指定类型的枚举
     * @return result
     */
    @SuppressWarnings("unchecked")
    public static <T> List<T> string2List(String source, String separator, Class<T> clazz) {
        List<T> result = new ArrayList<>();
        try {
            if (org.apache.commons.lang3.StringUtils.isEmpty(source)) {
                return result;
            }
            String[] temp = org.apache.commons.lang3.StringUtils.split(source, separator);
            for (String s : temp) {
                if (clazz.equals(String.class)) {
                    result.add((T) s);
                } else {
                    result.add((T) ConvertUtils.convert(s, clazz));
                }
            }

        } catch (Exception e) {
            throw new IllegalArgumentException("field can't convert to list, sourceString:" + source);
        }
        return result;
    }

}
