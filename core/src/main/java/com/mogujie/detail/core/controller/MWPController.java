package com.mogujie.detail.core.controller;

import com.google.gson.Gson;
import com.mogujie.actionlet.ActionResult;
import com.mogujie.actionlet.annotation.ActionletName;
import com.mogujie.actionlet.core.impl.DefaultActionResult;
import com.mogujie.actionlet.mwp.MWPContext;
import com.mogujie.actionlet.mwp.annotation.MWPApi;
import com.mogujie.actionlet.session.annotation.NeedUserInfo;
import com.mogujie.actionlet.sync.SyncActionlet;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.FuncRet;
import com.mogujie.detail.core.adt.RouteInfo;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.DetailConstants;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.manager.KamazManager;
import com.mogujie.detail.core.manager.TemplateManager;
import com.mogujie.detail.core.manager.TopologyManager;
import com.mogujie.detail.core.manager.TranslatorManager;
import com.mogujie.detail.core.spi.SpiManager;
import com.mogujie.detail.core.util.ContextUtil;
import com.mogujie.sentry.SentryClient;
import com.mogujie.sentry.collector.Collector;
import com.mogujie.sentry.type.CollectorType;
import com.mogujie.session.SessionContextHolder;
import com.mogujie.stable.spirit.point.annotation.ClassSpirit;
import com.mogujie.stable.spirit.point.annotation.MethodSpirit;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by xiaoyao on 16/8/8.
 */
@ClassSpirit
@Component
@MWPApi(value = "detail.nocache.api", version = "1")
@ActionletName(value = "detail.nocache.api", version = "1")
@NeedUserInfo
public class MWPController implements SyncActionlet<MWPController.DetailParam, Object>, InitializingBean {

    private static final Logger LOGGER = LoggerFactory.getLogger(MWPController.class);

    private static final String ESI_TMPL = "<esi:try><esi:attempt><esi:include src=\'http://%s\'/></esi:attempt><esi:except></esi:except></esi:try>";

    private static final String ESI_POSTFIX = "?$(QUERY_STRING)&isDyn=true";

    private static final String ESI_TAG = "##ESI##";

    private static final String DYN_PATH = "/detail/dynapi";

    private Gson gson;

    private SentryClient sentryClient;

    @Autowired
    private TopologyManager topologyManager;

    @Autowired
    private TemplateManager templateManager;

    @Autowired
    private KamazManager kamazManager;

    @PostConstruct
    public void init() {
        gson = new Gson();
        sentryClient = SentryClient.factory("detail");
        sentryClient.openAutoCollector();
    }

    @Override
    @MethodSpirit
    public ActionResult<Object> execute(@Nullable MWPController.DetailParam param) {
        try {
            // 设置MDC参数,供logback获取
            MDC.put("topo", param.getTemplate());
            MDC.put("iid", param.getIid());
            MDC.put("position", "spout");
            return realCall(param);
        } catch (Throwable e) {
            LOGGER.error("call error: ", e);
        } finally {
            MDC.clear();
            SpiManager.clear();
        }
        return getInvalidRet("详情页内部错误");
    }

    protected ActionResult<Object> realCall(MWPController.DetailParam param) throws DetailException {
        long startTime = System.currentTimeMillis();
        String iid = param.getIid();
        if (StringUtils.isEmpty(param.getIid())) {
            return getInvalidRet("invalid iid : " + iid);
        }
        if (iid.endsWith("_1111")) {
            iid = StringUtils.removeEnd(iid, "_1111");
        }

        if (StringUtils.isEmpty(param.getTemplate())) {
            return getInvalidRet("invalid template : " + param.getTemplate());
        }
        String[] templateKeys = param.getTemplate().split("-");
        if (templateKeys.length != 4) {
            return getInvalidRet("not complete template key : " + param.getTemplate());
        }
        Long itemId = null;
        try {
            itemId = IdConvertor.urlToId(iid);
        } catch (Exception e) {
            LOGGER.error("invalid item id {}", iid);
        }
        if (null == itemId || itemId > Integer.MAX_VALUE || itemId < 0) {
            LOGGER.error("invalid itemId : {}", iid);
            return getInvalidRet("invalid itemId " + iid);
        }
        String bizType = templateKeys[2].replaceAll("detail_", "");
        RouteInfo routeInfo = RouteController.getRouteInfo(Integer.parseInt(templateKeys[0]), bizType, Integer.parseInt(templateKeys[1]), templateKeys[3]);
        if (null == routeInfo) {
            LOGGER.error("invalid route : {}", routeInfo);
            return getInvalidRet("invalid route parameter!");
        }
        List<String> componentIds = templateManager.getComponentIds(routeInfo, templateKeys[3]);
        if (null == componentIds) {
            return getInvalidRet("no component select!");
        }

        qpsAdd(param.getTemplate());

        DetailContext context = new DetailContext(routeInfo, componentIds, itemId, TranslatorManager.getTranslatorMappingHolder());
        context.setDyn(false);
        context.setLoginUserId(SessionContextHolder.getUserId() > 0 ? SessionContextHolder.getUserId() : null);
        try {
            String mwpDeviceInfo = MWPContext.getMWPHeader().get("mw-dinfo");
            if (mwpDeviceInfo != null) {
                String[] ps = mwpDeviceInfo.split("@");
                if (ps != null && ps.length >= 3) {
                    context.setOsVersion(ps[1]);
                }
            }
            String appkey = MWPContext.getMWPHeader().get("mw-appkey");
            if (!StringUtils.isEmpty(appkey)) {
                context.addParam("appkey", appkey);
            }
        } catch (Throwable e) {
        }
        parseParam(routeInfo, param, context);
        // 获取客户端ip, 存在x-forwarded-for头中, 记录了请求链路的所有ip
        try {
            String clientIp = MWPContext.getMWPRequestFrom().getIp();
            if (!StringUtils.isEmpty(clientIp)) {
                context.setClientIp(clientIp);
                if (clientIp.startsWith("10.")) {
//                    LOGGER.error("MWP got local ip {}", clientIp);
                }
            }
        } catch (Throwable e) {
            LOGGER.error("get client ip error.");
        }
        FuncRet<Map<String, Object>> ret = topologyManager.emit(context);

        long endTime = System.currentTimeMillis();
        rtAdd((int) (endTime - startTime), param.getTemplate());
        if (!ret.isSuccess()) {
            return getInvalidRet(ret.getMessage());
        }

        kamazManager.sample(context, ret.getData());
        return getSuccessRet(ret.getMessage(), ret.getData(), context);
    }

    private ActionResult<Object> getInvalidRet(String msg) {
        return new DefaultActionResult<>(false, "4004", msg);
    }

    private ActionResult<Object> getSuccessRet(String msg, Map<String, Object> data, DetailContext context) {
        if (null == data) {
            data = new HashedMap();
        }
        //debug模式
        if ("true".equals(context.getParam("debug"))) {
            data.put("debug", ContextUtil.getContextDebugInfo(context));
        }
        return new DefaultActionResult(true, "1001", "", data);
    }

    private void rtAdd(int rt, String template) {
        Map<String, String> tags = new HashMap<>(2);
        if (StringUtils.isNotBlank(template)) {
            tags.put("url", template.replaceAll("-", "_"));
            tags.put("app", "MGJ");
        }
        tags.put("enterance", "mwp");
        Collector rtCollector = sentryClient.getCollector("detail.rt", tags, 60, CollectorType.AVG);
        rtCollector.put(rt);
    }

    private void qpsAdd(String template) {
        Map<String, String> tags = new HashMap<>(2);
        if (StringUtils.isNotBlank(template)) {
            tags.put("url", template.replaceAll("-", "_"));
            tags.put("app", "MGJ");
        }
        tags.put("enterance", "mwp");
        Collector qpsCollector = sentryClient.getCollector("detail.qps", tags, 1, CollectorType.SUM);
        qpsCollector.put(1);
    }

    private static void parseParam(RouteInfo routeInfo, DetailParam param, DetailContext context) {
        if (StringUtils.isNotBlank(param.getIid())) {
            context.addParam("iid", param.getIid());
        }
        if (StringUtils.isNotBlank(param.getTemplate())) {
            context.addParam("template", param.getTemplate());
        }
        if (StringUtils.isNotBlank(param.getActivityId())) {
            context.addParam("activityId", param.getActivityId());
        }
        if (StringUtils.isNotBlank(param.getFastbuyId())) {
            context.addParam("fastbuyId", param.getFastbuyId());
        }
        if (StringUtils.isNotBlank(param.getAppPlat())) {
            context.addParam("appPlat", param.getAppPlat());
        }
        if (StringUtils.isNotBlank(param.getActUserId())) {
            context.addParam("actUserId", param.getActUserId());
        }
        if (StringUtils.isNotBlank(param.getCaller())) {
            context.addParam("caller", param.getCaller());
        }
        if (StringUtils.isNotBlank(param.getNoPintuan())) {
            context.addParam("noPintuan", param.getNoPintuan());
        }
        if (StringUtils.isNotBlank(param.getIp())) {
            context.addParam("ip", param.getIp());
        }
        if (StringUtils.isNotBlank(param.getAddressId())) {
            context.addParam("addressId", param.getAddressId());
        }
        if (StringUtils.isNotBlank(param.getSkuId())) {
            context.addParam("skuId", param.getSkuId());
        }
        if (StringUtils.isNotBlank(param.getSkuNum())) {
            context.addParam("skuNum", param.getSkuNum());
        }
        if (StringUtils.isNotBlank(param.getRecvAddressId())) {
            context.addParam("recvAddressId", param.recvAddressId);
        }
        if (MWPContext.getMWPRequestFrom() != null) {
            if (StringUtils.isNotBlank(MWPContext.getMWPRequestFrom().getClientShortVersion())) {
                context.addParam("_av", MWPContext.getMWPRequestFrom().getClientShortVersion());
            }
            if (StringUtils.isNotBlank(MWPContext.getMWPRequestFrom().getPlatform())) {
                context.addParam("_platform", MWPContext.getMWPRequestFrom().getPlatform());
            }
        }
        if (StringUtils.isNotBlank(param.getFrom())) {
            context.addParam("from", param.getFrom());
        }
        if (StringUtils.isNotBlank(param.getAcm())) {
            context.addParam("acm", param.getAcm());
        }
        if (routeInfo.getBizType() == BizType.TTNORMAL) {
            String did = param.getDid();
            if (!StringUtils.isEmpty(did)) {
                context.addParam("did", IdConvertor.urlToId(did).toString());
            }
            String relatedIds = param.getRelatedIds();
            context.addParam("relatedIds", relatedIds);
            String fashionId = param.getFashionId();
            context.addParam("fashionId", fashionId);
        }
        if (routeInfo.getBizType() == BizType.FASTBUY || routeInfo.getBizType() == BizType.SHARE) {
            String fastbuyId = param.getFastbuyId();
            if (!StringUtils.isEmpty(fastbuyId)) {
                context.addParam("fastbuyId", IdConvertor.urlToId(fastbuyId).toString());
            }
        }
        if (routeInfo.getBizType() == BizType.SECKILL || routeInfo.getBizType() == BizType.SHARE) {
            String seckillId = param.getSeckillId();
            if (!StringUtils.isEmpty(seckillId)) {
                context.addParam("seckillId", IdConvertor.urlToId(seckillId).toString());
            }
        }
        if (routeInfo.getBizType() == BizType.CHANNEL) {
            String auctionId = param.getAuctionId();
            if (!StringUtils.isEmpty(auctionId)) {
                context.addParam("auctionId", IdConvertor.urlToId(auctionId).toString());
            }
        }
        try {
            if (StringUtils.isNotBlank(param.getProvince())) {
                String province = new String(param.getProvince().getBytes("ISO-8859-1"), "UTF-8");
                context.addParam("province", province);
            }
            if (StringUtils.isNotBlank(param.getCity())) {
                String city = new String(param.getCity().getBytes("ISO-8859-1"), "UTF-8");
                context.addParam("city", city);
            }
            if (StringUtils.isNotBlank(param.getSourceParams())) {
                context.addParam(DetailConstants.RequestParamKeys.SOURCE_PARAMS, param.getSourceParams());
            }
        } catch (UnsupportedEncodingException e) {
            LOGGER.error("MWP get address fault : {}", e);
        }

    }

    @Override
    public void afterPropertiesSet() throws Exception {

    }

    public static class DetailParam {

        private String iid;

        private String template;

        private String activityId;

        private String fastbuyId;

        private String appPlat;

        private String ip;

        private String noPintuan;

        private String skuNum;

        private String skuId;

        private String caller;

        private String addressId;

        private String seckillId;

        private boolean debug;

        private String from;

        private String actUserId;

        /**
         * 达人ID
         */
        private String did;

        /**
         * 拍卖ID
         */
        private String auctionId;

        /**
         * 抖音关联商品ids
         */
        private String relatedIds;

        private String fashionId;

        /**
         * 用户当前所在省
         */
        private String province;

        /**
         * 用户当前所在城市
         */
        private String city;

        /**
         * 链接中的acm打点参数
         */
        private String acm;

        private String sourceParams;

        /**
         * 用户详情页选择的收获地址id
         */
        private String recvAddressId;

        public String getIid() {
            return iid;
        }

        public void setIid(String iid) {
            this.iid = iid;
        }

        public String getTemplate() {
            return template;
        }

        public void setTemplate(String template) {
            this.template = template;
        }

        public String getActivityId() {
            return activityId;
        }

        public void setActivityId(String activityId) {
            this.activityId = activityId;
        }

        public String getFastbuyId() {
            return fastbuyId;
        }

        public void setFastbuyId(String fastbuyId) {
            this.fastbuyId = fastbuyId;
        }

        public String getAppPlat() {
            return appPlat;
        }

        public void setAppPlat(String appPlat) {
            this.appPlat = appPlat;
        }

        public String getIp() {
            return ip;
        }

        public void setIp(String ip) {
            this.ip = ip;
        }

        public String getNoPintuan() {
            return noPintuan;
        }

        public void setNoPintuan(String noPintuan) {
            this.noPintuan = noPintuan;
        }

        public String getSkuNum() {
            return skuNum;
        }

        public void setSkuNum(String skuNum) {
            this.skuNum = skuNum;
        }

        public String getSkuId() {
            return skuId;
        }

        public void setSkuId(String skuId) {
            this.skuId = skuId;
        }

        public String getCaller() {
            return caller;
        }

        public void setCaller(String caller) {
            this.caller = caller;
        }

        public String getAddressId() {
            return addressId;
        }

        public void setAddressId(String addressId) {
            this.addressId = addressId;
        }

        public String getSeckillId() {
            return seckillId;
        }

        public void setSeckillId(String seckillId) {
            this.seckillId = seckillId;
        }

        public boolean isDebug() {
            return debug;
        }

        public void setDebug(boolean debug) {
            this.debug = debug;
        }

        public String getFrom() {
            return from;
        }

        public void setFrom(String from) {
            this.from = from;
        }

        public String getActUserId() {
            return actUserId;
        }

        public void setActUserId(String actUserId) {
            this.actUserId = actUserId;
        }

        public String getDid() {
            return did;
        }

        public void setDid(String did) {
            this.did = did;
        }

        public String getAuctionId() {
            return auctionId;
        }

        public void setAuctionId(String auctionId) {
            this.auctionId = auctionId;
        }

        public String getRelatedIds() {
            return relatedIds;
        }

        public void setRelatedIds(String relatedIds) {
            this.relatedIds = relatedIds;
        }

        public String getFashionId() {
            return fashionId;
        }

        public void setFashionId(String fashionId) {
            this.fashionId = fashionId;
        }

        public String getProvince() {
            return province;
        }

        public void setProvince(String province) {
            this.province = province;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getSourceParams() {
            return sourceParams;
        }

        public void setSourceParams(String sourceParams) {
            this.sourceParams = sourceParams;
        }

        public String getAcm() {
            return acm;
        }

        public void setAcm(String acm) {
            this.acm = acm;
        }

        public String getRecvAddressId() {
            return recvAddressId;
        }

        public void setRecvAddressId(String recvAddressId) {
            this.recvAddressId = recvAddressId;
        }

        @Override
        public String toString() {
            return "DetailParam{" +
                    "iid='" + iid + '\'' +
                    ", template='" + template + '\'' +
                    ", activityId='" + activityId + '\'' +
                    ", fastbuyId='" + fastbuyId + '\'' +
                    ", appPlat='" + appPlat + '\'' +
                    ", ip='" + ip + '\'' +
                    ", noPintuan='" + noPintuan + '\'' +
                    ", skuNum='" + skuNum + '\'' +
                    ", skuId='" + skuId + '\'' +
                    ", caller='" + caller + '\'' +
                    ", addressId='" + addressId + '\'' +
                    ", seckillId='" + seckillId + '\'' +
                    ", debug=" + debug +
                    ", from='" + from + '\'' +
                    ", actUserId='" + actUserId + '\'' +
                    ", did='" + did + '\'' +
                    ", auctionId='" + auctionId + '\'' +
                    ", relatedIds='" + relatedIds + '\'' +
                    ", fashionId='" + fashionId + '\'' +
                    ", province='" + province + '\'' +
                    ", city='" + city + '\'' +
                    ", acm='" + acm + '\'' +
                    ", sourceParams='" + sourceParams + '\'' +
                    ", recvAddressId='" + recvAddressId + '\'' +
                    '}';
        }
    }
}
