//package com.mogujie.detail.core.task;
//
//import com.mogujie.darwin.util.CollectionUtil;
//import com.mogujie.detail.core.adt.DetailContext;
//import com.mogujie.detail.core.adt.DetailItemDO;
//import com.mogujie.detail.core.constant.SwitchKey;
//import com.mogujie.detail.core.exception.DetailException;
//import com.mogujie.detail.core.util.CommonSwitchUtil;
//import com.mogujie.detail.core.util.NumUtil;
//import com.mogujie.marketing.ferrari.api.RushInfoForDetailService;
//import com.mogujie.marketing.ferrari.api.dto.RushInfoDTO;
//import com.mogujie.marketing.ferrari.api.dto.RushInfoResultDTO;
//import com.mogujie.metabase.spring.client.MetabaseClient;
//import com.mogujie.service.hummer.api.PromotionReadService;
//import com.mogujie.service.hummer.constains.RequestConstants;
//import com.mogujie.service.hummer.domain.dto.*;
//import com.mogujie.service.hummer.domain.dto.result.Result;
//import com.mogujie.service.hummer.utils.SystemDiscountChecker;
//import com.mogujie.service.inventory.api.InventoryReadService;
//import com.mogujie.service.inventory.domain.ActivityInventory;
//import com.mogujie.service.inventory.domain.param.BatchActivityInventoryQueryParam;
//import com.mogujie.service.inventory.domain.result.MapResult;
//import com.mogujie.service.item.domain.basic.ItemDO;
//import com.mogujie.service.item.domain.basic.ItemSkuDO;
//import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * Created by xiaoyao on 16/10/20.
// */
//public class CollectPromotionInfoTask extends AbstractCollectDataTask {
//
//    private static final Logger LOGGER = LoggerFactory.getLogger(CollectPromotionInfoTask.class);
//
//    private RushInfoForDetailService rushInfoForDetailService;
//
//    private InventoryReadService inventoryReadService;
//
//    private PromotionReadService promotionReadService;
//
//    private Long fastbuyId;
//
//    private MetabaseClient metabaseClient;
//
//    private CommonSwitchUtil commonSwitchUtil;
//
//    public CollectPromotionInfoTask(DetailContext detailContext, MetabaseClient metabaseClient, Long fastbuyId, CommonSwitchUtil commonSwitchUtil) throws DetailException{
//        super(detailContext);
//        this.commonSwitchUtil = commonSwitchUtil;
//        this.fastbuyId = fastbuyId;
//        this.metabaseClient = metabaseClient;
//        try {
//            this.rushInfoForDetailService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(RushInfoForDetailService.class);
//            this.inventoryReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(InventoryReadService.class);
//            this.promotionReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(PromotionReadService.class);
//        } catch (Exception e) {
//            throw new DetailException(e);
//        }
//    }
//
//    @Override
//    public void collect() {
//        RushInfoDTO rushInfo = null;
//        if (null != fastbuyId && commonSwitchUtil.isOn(SwitchKey.FERRARI_GET_RUSH_INFO)) {
//            RushInfoResultDTO rushRet = rushInfoForDetailService.getRushInfo(fastbuyId);
//            if (null != rushRet && rushRet.isSucc()) {
//                rushInfo = rushRet.getRushInfoDTO();
//            }
//            context.addContext("fastbuyInfo", rushInfo);
//            decorateFastbuySku(this.context.getItemDO(), fastbuyId);
//        }
//        decorateSkuAndPrice(this.context, rushInfo);
//    }
//
//    private void decorateFastbuySku(ItemDO item, Long fastbuyId) {
//        List<Long> skuIdList = new ArrayList<>(item.getItemSkuDOList().size());
//        for (ItemSkuDO sku : item.getItemSkuDOList()) {
//            skuIdList.add(sku.getSkuId());
//        }
//        try {
//            BatchActivityInventoryQueryParam param = new BatchActivityInventoryQueryParam();
//            param.setSkuIds(skuIdList);
//            param.setChannelId(1);
//            param.setActivityId(fastbuyId.intValue());
//            MapResult<Long, ActivityInventory> inventoryMapResult = inventoryReadService.batchQueryActivityInventory(param);
//            if (null != inventoryMapResult && inventoryMapResult.isSuccess()) {
//                Map<Long, ActivityInventory> inventoryMap = inventoryMapResult.getData();
//                if (null != inventoryMap) {
//                    for (ItemSkuDO sku : item.getItemSkuDOList()) {
//                        ActivityInventory inventory = inventoryMap.get(sku.getSkuId());
//                        sku.setQuantity(null == inventory ? 0 : inventory.getStock());
//                    }
//                }
//            }
//        } catch (Exception e) {
//            LOGGER.error("get fastbuy inventory failed : {}", e.getMessage());
//        }
//    }
//
//    private boolean useLocalDiscount() {
//        try {
//            Boolean useLocal = metabaseClient.getBoolean("discount_useLocal");
//            if (null != useLocal) {
//                return useLocal;
//            }
//        } catch (Exception e) {
//            ;
//        }
//        return true;
//    }
//
//    private Map<Long, Long> getSkuPriceMap(ItemDO item) {
//        Map<Long, Long> skuPriceMap = new HashMap<>();
//        for (ItemSkuDO sku : item.getItemSkuDOList()) {
//            skuPriceMap.put(sku.getSkuId(), sku.getPrice().longValue());
//        }
//        return skuPriceMap;
//    }
//
//    public void decorateSkuAndPrice(DetailContext context, RushInfoDTO rushInfo) {
//        DetailItemDO item = context.getItemDO();
//        try {
//            Map<Long, Long> originSkuMap = getSkuPriceMap(item);
//            Map<Long, Long> realSkuMap = null;
//            Long realPrice = null;
//            String decorate = null;
//            if (null != rushInfo) {
//                realSkuMap = new HashMap<>(item.getItemSkuDOList().size());
//                for (ItemSkuDO sku : item.getItemSkuDOList()) {
//                    realSkuMap.put(sku.getSkuId(), (long) rushInfo.getSalePrice());
//                }
//                realPrice = (long) rushInfo.getSalePrice();
//            } else if (useLocalDiscount()) {
//                SystemDiscountChecker.SystemDiscountRes ret = SystemDiscountChecker.calcSystemDiscount(item.getJsonExtra(), item.getReservePrice(), false, (int) RequestConstants.Market.MOGUJIE, originSkuMap);
//                if (null != ret) {
//                    realSkuMap = ret.getSkuRalPrice();
//                    realPrice = ret.getRealPrice();
//                    decorate = ret.getName();
//                }
//            }
//
//            if (null == realSkuMap || null == realPrice) {
//                ItemDetailRequestV2 request = new ItemDetailRequestV2();
//                Pbuyer pbuyer = new Pbuyer();
//                pbuyer.setBuyerId(context.getLoginUserId());
//                Pseller pSeller = new Pseller();
//                pSeller.setSellerId(item.getUserId());
//                PitemDetail pitemDetail = new PitemDetail();
//                pitemDetail.setExtra(item.getJsonExtra());
//                pitemDetail.setItemId(item.getItemId());
//                pitemDetail.setSkuPriceMap(originSkuMap);
//                pitemDetail.setItemPrice(getHighestPrice(item.getItemSkuDOList()));
//                InvokeInfo invokeInfo = new InvokeInfo();
//                invokeInfo.setChannel((int) RequestConstants.Channel.UNKNOW);
//                invokeInfo.setMarket((int) RequestConstants.Market.MOGUJIE);
//                invokeInfo.setSource(RequestConstants.Source.DETAIL);
//                invokeInfo.setTerminal(RequestConstants.Terminal.APP);
//                request.setPitemDetail(pitemDetail);
//                request.setSeller(pSeller);
//                request.setPbuyer(pbuyer);
//                request.setInvokeInfo(invokeInfo);
//
//                Result<ItemDetailPromotion> ret = promotionReadService.calcForItemDetailPromotion(request);
//                if (null != ret && ret.isSuccess() && null != ret.getData()) {
//                    realSkuMap = ret.getData().getSkuRealPriceMap();
//                    realPrice = ret.getData().getItemRealPrice();
//                    decorate = ret.getData().getDecorate();
//                }
//            }
//            if (null != realSkuMap && null != realPrice) {
//                item.setDiscountDesc(decorate);
//                filterSku(item, realSkuMap, realPrice);
//            } else {
//                item.setDiscountDesc("");
//                filterSku(item, originSkuMap, item.getReservePrice().longValue());
//            }
//        } catch (Throwable e) {
//            LOGGER.error("get discount info failed : {}", e);
//            filterSku(item, new HashMap<Long, Long>(), null);
//        }
//    }
//
//    private static void filterSku(DetailItemDO item, Map<Long, Long> realPriceMap, Long realPrice) {
//        long lowPrice = item.getReservePrice();
//        long highPrice = item.getReservePrice();
//        long lowNowPrice = null == realPrice ? item.getReservePrice() : realPrice;
//        long highNowPrice = null == realPrice ? item.getReservePrice() : realPrice;
//        long totalStock = 0;
//        for (ItemSkuDO sku : item.getItemSkuDOList()) {
//
//            if (sku.getPrice() < lowPrice) {
//                lowPrice = sku.getPrice();
//            }
//
//            if (sku.getPrice() > highPrice) {
//                highPrice = sku.getPrice();
//            }
//            Long skuRealPrice = realPriceMap.get(sku.getSkuId());
//            if (null == skuRealPrice) {
//                skuRealPrice = sku.getPrice().longValue();
//            }
//            sku.setNowPrice(skuRealPrice.intValue());
//            if (skuRealPrice < lowNowPrice) {
//                lowNowPrice = skuRealPrice;
//            }
//
//            if (skuRealPrice > highNowPrice) {
//                highNowPrice = skuRealPrice;
//            }
//
//            totalStock += sku.getQuantity();
//        }
//        item.setLowPrice(NumUtil.formatNum(lowPrice / 100D));
//        item.setLowNowPrice(NumUtil.formatNum(lowNowPrice / 100D));
//        item.setHighPrice(NumUtil.formatNum(highPrice / 100D));
//        item.setHighNowPrice(NumUtil.formatNum(highNowPrice / 100D));
//        item.setTotalStock(totalStock);
//        item.setReservePrice(lowPrice);
//    }
//
//    private Long getHighestPrice(final List<ItemSkuDO> skuList) {
//        if (CollectionUtil.isEmpty(skuList)) {
//            return 0L;
//        }
//        Long highestPrice = skuList.get(0).getPrice().longValue();
//        for (ItemSkuDO sku : skuList) {
//            if (sku.getPrice().longValue() > highestPrice) {
//                highestPrice = sku.getPrice().longValue();
//            }
//        }
//        return highestPrice;
//    }
//}
