package com.mogujie.detail.core.filter;

import com.mogujie.discover.client.Publisher;
import com.mogujie.discover.client.PublisherRegister;
import com.mogujie.discover.client.registration.PublisherRegistration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * Created by xy on 2018/4/23.
 */
public class SlbRegister {

    private Publisher publisher;

    private static final Logger LOGGER = LoggerFactory.getLogger(SlbRegister.class);

    private static class SingletonHolder {
        public final static SlbRegister instance = new SlbRegister();
    }

    public static SlbRegister getInstance() {
        return SingletonHolder.instance;
    }

    private SlbRegister() {
    }

    public void start() {
        String ip = "";
        try {
            ip = InetAddress.getLocalHost().getHostAddress();
            this.discoverRegister(ip);
        } catch (UnknownHostException e) {
            LOGGER.error("get local ip failed : {}", e);
        }
    }

    public void close() {
        this.discoverUnRegister();
    }

    /**
     * discover注册
     *
     * @param ip 本机ip
     */
    private void discoverRegister(String ip) {
        String data = ip + ":3056";
        PublisherRegistration registration = new PublisherRegistration("detailweb", "detailwebhost", "detailweb");
        publisher = PublisherRegister.register(registration);
        publisher.publish(data);
    }

    /**
     * discover注销
     */
    private void discoverUnRegister() {
        PublisherRegister.unRegister(publisher);
    }
}
