package com.mogujie.detail.core.util;

import com.mogujie.darwin.application.client.DarwinClient;
import com.mogujie.darwin.application.query.ApplicationLaunchQuery;
import com.mogujie.darwin.application.service.client.ApplicationLaunchClient;
import com.mogujie.detail.core.adt.DetailContextHolder;
import com.mogujie.market.base.model.ResultBase;
import com.mogujie.metabase.client.MetaClient;
import com.mogujie.tesla.common.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by y<PERSON>ui on 16/3/14.
 */
public class MaitUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(MaitUtil.class);

    private static MetaClient metaClient;

    static {
        try {
            metaClient = MetaClient.client("detailConf");
        } catch (Throwable e) {
            LOGGER.error("init meta client failed : ", e);
        }
    }

    public static List<Map<String, Object>> getMaitData(long defId) {
        ResultBase<List<Map<String, Object>>> ans = DarwinClient.getData(defId);
        if (ans == null || !ans.isSuccess() || CollectionUtils.isEmpty(ans.getValue())) {
            return null;
        }
        return ans.getValue();
    }

    public static Map<Long, List<Map<String, Object>>> batchGetMatiData(List<Long> defIds) {
        ResultBase<Map<Long, List<Map<String, Object>>>> ans = DarwinClient.mgetData(defIds);
        if (ans == null || !ans.isSuccess() || CollectionUtils.isEmpty(ans.getValue())) {
            return null;
        }

        return ans.getValue();
    }

    public static List<Map<String, Object>> getTargetedMaitData(Long defId) {
        if (null == defId) {
            return null;
        }else if (!isTargetGetOn(defId)){
            return getMaitData(defId);
        }
        ApplicationLaunchClient launchClient = DarwinClient.getLaunchClient();
        ApplicationLaunchQuery query = new ApplicationLaunchQuery();
        query.setDefinitionId(defId);
        Long loginUserId = DetailContextHolder.get().getLoginUserId();
        String did = DetailContextHolder.get().getParam("_did");
        String av = DetailContextHolder.get().getParam("_av");
        query.setDefinitionId(defId);
        query.setDid(did);
        query.setUid(loginUserId);
        Map<String, Object> extra = new HashMap();
        extra.put("login", null == loginUserId ? "0" : "1");
        extra.put("v", StringUtils.isEmpty(av) ? "0" : av );
        extra.put("channel", "0");
        query.setExtra(extra);
        query.setPlatform(DetailContextHolder.get().getParam("_platform"));


        Map<String, Object> params = new HashMap();
        params.put("iid", DetailContextHolder.get().getItemId());
        query.setParams(params);
        ResultBase<List<Map<String, Object>>> rstBase = launchClient.applicationLaunch(query);
        if (null != rstBase && rstBase.hasSuccessValue() && !CollectionUtils.isEmpty(rstBase.getValue())) {
            return rstBase.getValue();
        }
        return null;
    }

    private static final Boolean isTargetGetOn(Long defId) {
        try {
            String value = metaClient.getConfigurations().get("isTargetGetOn");
            if (!StringUtils.isEmpty(value) && !Boolean.valueOf(value)) {
                return false;
            }
            String singleValue = metaClient.getConfigurations().get("isTargetGetOn_" + defId);
            if (!StringUtils.isEmpty(singleValue)) {
                return  Boolean.valueOf(singleValue);
            }

        } catch (Throwable e) {
            LOGGER.warn("get key error : ", e);
        }
        return true;
    }
}
