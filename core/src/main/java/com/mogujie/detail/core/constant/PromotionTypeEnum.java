package com.mogujie.detail.core.constant;

import lombok.Data;


/**
 * @description: 店铺券 (1) or 店铺活动 (2) or 平台优惠（3） or 购物津贴 （4）
 * @author: wuzhao
 * @create: 2020-02-28 19:49
 **/

public enum  PromotionTypeEnum {
    MORECOUPON(0,"多个优惠"),

    SHOPCOUPON(1,"店铺券"),

    SHOPACTIVITY(2, "店铺活动"),

    PLATFORM(3, "平台优惠"),

    SHOPPRICE(4, "购物津贴");

    int index;
    String desc;

    PromotionTypeEnum(int index, String desc){
        this.index = index;
        this.desc = desc;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
