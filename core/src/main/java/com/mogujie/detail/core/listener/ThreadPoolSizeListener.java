package com.mogujie.detail.core.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mogujie.detail.core.manager.ThreadPoolExecutors;
import com.mogujie.metabase.client.listener.SingleChangedListener;
import com.mogujie.metabase.datastructure.MetaEvent;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * @author: 桌子 <EMAIL>
 * @datetime: 2020-09-17 14:03
 */
public class ThreadPoolSizeListener implements SingleChangedListener {

    private static int numProcessor = Runtime.getRuntime().availableProcessors();

    @Override
    public String getMetaKey() {
        return "thread_pool_config";
    }

    @Override
    public void configChanged(MetaEvent metaEvent) {
        if (metaEvent != null && metaEvent.getMetaValue() != null) {
            String metaValue = metaEvent.getMetaValue();
            JSONObject configMap = JSON.parseObject(metaValue);
            if (configMap.getInteger("translateExecutorService") != null) {
                resetThreadPool(ThreadPoolExecutors.translateExecutorService, configMap.getInteger("translateExecutorService"));
            } else if (configMap.getInteger("commonExecutorService") != null) {
                resetThreadPool(ThreadPoolExecutors.commonExecutorService, configMap.getInteger("commonExecutorService"));
            } else if (configMap.getInteger("fastExecutorService") != null) {
                resetThreadPool(ThreadPoolExecutors.fastExecutorService, configMap.getInteger("fastExecutorService"));
            }
        }
    }

    private void resetThreadPool(ThreadPoolExecutor threadPoolExecutor , Integer config) {
        int max = config % 1000;
        int core = config / 1000;

        if (core >= numProcessor && core <= 256) {
            threadPoolExecutor.setCorePoolSize(core);
        }

        if (max > threadPoolExecutor.getCorePoolSize() && max <= 256) {
            threadPoolExecutor.setMaximumPoolSize(max);
        }
    }
}
