package com.mogujie.detail.core.constant;

/**
 * @AUTUOR: lengshan
 * @DESCRIPTION: 店铺标签（非标签中心）
 * @DATE: 2020/1/15 下午4:18
 */
public enum ShopLabelEnum {

    MY_FAVORITE(101,"我关注的店", 101),

    MY_BUY(102,"我买过的店", 102),

    SHOP_CFANS(201, "万人关注", 201),

    SHOP_SALE_TOP10(301, "销量top10", 301),

    SHOP_SALE_KING(302, "销量王", 302),

    SHOP_CSELLS(303, "累计销量", 303),

    FIVE_LEVEL(401, "五星好店", 401),

    SHOP_REPEAT(402, "复购相关", 402),

    SHIP_RATE(501, "24小时发货百分百", 501),

    SAME_CITY(502, "同城好店", 502),

    FIVE_YEAR(601, "5年金招牌", 601),

    THREE_YEAR(602, "3年老字号", 602);

    int index;
    String desc;
    int sort;

    ShopLabelEnum(int index, String desc, int sort){
        this.index = index;
        this.desc = desc;
        this.sort = sort;
    }

    public int getIndex(){
        return index;
    }

    public String getDesc(){
        return desc;
    }

    public int getSort() {
        return sort;
    }
}
