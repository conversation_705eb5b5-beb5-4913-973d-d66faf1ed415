package com.mogujie.detail.core.adt;

import java.util.Map;

/**
 * 渠道标
 * Created by anshi on 18/1/16.
 */
public class ChannelTag {
    private String tagKey;

    private Long activityId;

    private Integer warmUpTime;

    private Integer startTime;

    private Integer endTime;

    /**
     * 一口价
     */
    private Long price;

    /**
     * 折扣区间价
     */
    private Integer discount;

    /**
     * 商品标中的其他字段
     */
    private Map<String, String> extraMap;

    public String getTagKey() {
        return tagKey;
    }

    public void setTagKey(String tagKey) {
        this.tagKey = tagKey;
    }

    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    public Integer getWarmUpTime() {
        return warmUpTime;
    }

    public void setWarmUpTime(Integer warmUpTime) {
        this.warmUpTime = warmUpTime;
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public Long getPrice() {
        return price;
    }

    public void setPrice(Long price) {
        this.price = price;
    }

    public Integer getDiscount() {
        return discount;
    }

    public void setDiscount(Integer discount) {
        this.discount = discount;
    }

    public Map<String, String> getExtraMap() {
        return extraMap;
    }

    public void setExtraMap(Map<String, String> extraMap) {
        this.extraMap = extraMap;
    }
}
