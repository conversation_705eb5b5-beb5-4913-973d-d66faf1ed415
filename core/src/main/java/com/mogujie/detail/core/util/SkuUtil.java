package com.mogujie.detail.core.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.service.item.utils.SkuUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


import java.util.*;
import java.util.stream.Collectors;

import static com.mogujie.detail.core.util.ContextUtil.SPRING_FESTIVAL_CONFIG_KEY;
import static com.mogujie.detail.core.util.ContextUtil.isSpringFestivalShutdownItem;

/**
 * Created by anshi on 2018/12/11.
 */
public class SkuUtil {

    private static Logger logger= LoggerFactory.getLogger(SkuUtil.class);
    /**
     * 获取商家设置的商品自身延迟发货时间
     *
     * @param itemSkuDO
     * @return
     */
    public static Integer getDelayedDeliveryTimeWithoutNewYear(ItemSkuDO itemSkuDO) {
        int delayedDeliveryTime = SkuUtils.getDelayedDeliveryTime(itemSkuDO);
        int now = (int) (System.currentTimeMillis() / 1000);
        return delayedDeliveryTime > now ? delayedDeliveryTime : null;
    }

    /**
     * 若春节期间不打烊，则使用商品自身的延迟发货时间
     * 春节期间打烊，则返回平台的春节延迟发货时间
     *
     * @param itemDO
     * @return
     */
    public static Integer getDelayedDeliveryTimeWithNewYear(ItemDO itemDO, ItemSkuDO itemSkuDO) {
        int delayedDeliveryTime = 0;
        if (!isSpringFestivalShutdownItem(itemDO)) {
            delayedDeliveryTime = SkuUtils.getDelayedDeliveryTime(itemSkuDO);
        } else {
            String springFestivalConfig = MetabaseTool.getValue(SPRING_FESTIVAL_CONFIG_KEY);
            if (springFestivalConfig != null) {
                JSONObject springShutdownTime = JSON.parseObject(springFestivalConfig);
                delayedDeliveryTime = springShutdownTime.getInteger("deliveryTime");
            }
        }
        int now = (int) (System.currentTimeMillis() / 1000);
        return delayedDeliveryTime > now ? delayedDeliveryTime : null;
    }

    /**
     * 延迟发货时间：获取XX内发货的小时数
     *
     * @return
     */
    public static Integer getDelayDeliveryPeriod(String skuExtra) {
        if (StringUtils.isEmpty(skuExtra)) {
            return null;
        }

        try {
            JSONObject jsonObject = JSON.parseObject(skuExtra);
            Integer deliveryPeriod = jsonObject.getInteger("deliveryPeriod");
            if (null != deliveryPeriod && deliveryPeriod > 0) {
                return deliveryPeriod;
            } else {
                return null;
            }
        } catch (Exception e) {
            logger.error("parse sku exception:{}",e.getMessage());
            return null;
        }
    }

    /**
     * sku限购
     */
    public static Integer getPurchaseSkuLimit(String skuExtra) {
        if (StringUtils.isEmpty(skuExtra)) {
            return null;
        }

        try {
            JSONObject jsonObject = JSON.parseObject(skuExtra);
            Integer purchaseSkuLimit = jsonObject.getInteger("purchaseSkuLimit");
            if (null != purchaseSkuLimit && purchaseSkuLimit > 0) {
                return purchaseSkuLimit;
            } else {
                return null;
            }
        } catch (Exception e) {
            logger.error("parse sku exception:{}",e.getMessage());
            return null;
        }
    }

    /**
     * 延迟发货原因code
     *
     * @return
     */
    public static String getDelayReasonCode(String skuExtra) {
        if (StringUtils.isEmpty(skuExtra)) {
            return null;
        }

        try {
            JSONObject jsonObject = JSON.parseObject(skuExtra);
            String reasonCode = jsonObject.getString("deliveryPeriodReason");
            if(StringUtils.isNotEmpty(reasonCode)){
                return reasonCode;
            }else{
                return null;
            }
        } catch (Exception e) {
            logger.error("parse sku exception:{}",e.getMessage());
            return null;
        }
    }

    /**
     * 延迟发货原因code
     *
     * @return
     */
    public static String getDelayReason(String reasonCode,long maitId) {
        if (StringUtils.isEmpty(reasonCode)) {
            return null;
        }
        try {
            Map<String, String> reasonMap = getReasonMap(maitId);
            if (reasonMap == null) {
                return null;
            }
            String reasonText=reasonMap.get(reasonCode);
            return reasonText;
        } catch (Exception e) {
            logger.error("parse sku exception:{}",e.getMessage());
            return null;
        }
    }

    private static Map<String, String> getReasonMap(long maitId) {
        List<Map<String, Object>> delayReasonData= MaitUtil.getMaitData(maitId);
        if(CollectionUtils.isEmpty(delayReasonData)){
            return null;
        }
        String reasonStr= StringUtils.objToString(delayReasonData.get(0).get("reason"));
        JSONArray reasonArr= JSONObject.parseArray(reasonStr);
        if(reasonArr==null||reasonArr.size()==0){
            return null;
        }
        Map<String,String> reasonMap=new HashMap<>();
        for(int i=0;i<reasonArr.size();i++){
          JSONObject reasonJson=  reasonArr.getJSONObject(i);
          String id=  reasonJson.getString("id");
          String name=reasonJson.getString("name");

          if(StringUtils.isEmpty(id)||StringUtils.isEmpty(name)){
              continue;
          }
           reasonMap.put(id,name);
        }
        return reasonMap;
    }
}
