package com.mogujie.detail.core.controller;

import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.mogujie.actionlet.mwp.MWPRequestFrom;
import com.mogujie.actionlet.mwp.spring.MWPServiceUtil;
import com.mogujie.commons.utils.EnvUtil;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.FuncRet;
import com.mogujie.detail.core.adt.RetData;
import com.mogujie.detail.core.adt.RouteInfo;
import com.mogujie.detail.core.adt.Status;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.Platform;
import com.mogujie.detail.core.constant.StatusCode;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.filter.SlbRegister;
import com.mogujie.detail.core.manager.TemplateManager;
import com.mogujie.detail.core.manager.ThreadPoolExecutors;
import com.mogujie.detail.core.manager.TopologyManager;
import com.mogujie.detail.core.manager.TranslatorManager;
import com.mogujie.detail.core.spi.SpiManager;
import com.mogujie.detail.core.util.ContextUtil;
import com.mogujie.sentry.SentryClient;
import com.mogujie.sentry.collector.Collector;
import com.mogujie.sentry.type.CollectorType;
import com.mogujie.service.session.api.SessionService;
import com.mogujie.service.session.domain.SignInfo;
import com.mogujie.session.SessionContextHolder;
import com.mogujie.stable.spirit.point.annotation.ClassSpirit;
import com.mogujie.stable.spirit.point.annotation.MethodSpirit;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Created by xiaoyao on 16/8/8.
 */
@ClassSpirit
@Controller
public class RouteController {

    private static final Logger LOGGER = LoggerFactory.getLogger(RouteController.class);

    private final AtomicBoolean warmUped = new AtomicBoolean(false);

    private static final String ESI_TMPL = "<esi:try><esi:attempt><esi:include src=\'http://%s\'/></esi:attempt><esi:except></esi:except></esi:try>";

    private static final String ESI_POSTFIX = "?$(QUERY_STRING)&isDyn=true";

    private static final String ESI_TAG = "##ESI##";

    private static final String DYN_PATH = "/detail/dynapi";

    private Gson gson;

    private SentryClient sentryClient;

    @Autowired
    private TopologyManager topologyManager;

    @Autowired
    private TemplateManager templateManager;

    private SessionService sessionService;

    @PostConstruct
    public void init() {
        gson = new Gson();
        sentryClient = SentryClient.factory("detail");
        sentryClient.openAutoCollector();
        try {
            sessionService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(SessionService.class);
        } catch (Exception e) {
            LOGGER.error("init session service failed : {}", e);
        }
    }

    @RequestMapping(value = "/slb_close", produces = {"application/json;charset=UTF-8"})
    @ResponseBody
    public void destroy() {
        try {
            SlbRegister.getInstance().close();
            Thread.sleep(1000L);
        } catch (Throwable e) {
            e.printStackTrace();
        }
        LOGGER.error("slb destroy");
    }

    @RequestMapping(value = "/detail/seckillapi", produces = {"application/json;charset=UTF-8"})
    @ResponseBody
    @MethodSpirit
    public String detailSeckillApi(@RequestParam(value = "iid", required = true) String iid,
                                   @RequestParam(value = "template", required = true) String template,
                                   HttpServletRequest request, HttpServletResponse response) throws DetailException {
        return detailApi(iid, template, request, response);
    }

    @RequestMapping(value = "/detail/api", produces = {"application/json;charset=UTF-8"})
    @ResponseBody
    @MethodSpirit
    public String detailApi(@RequestParam(value = "iid", required = true) String iid,
                            @RequestParam(value = "template", required = true) String template,
                            HttpServletRequest request, HttpServletResponse response) throws DetailException {
        try {
            // 设置MDC参数,供logback获取
            MDC.put("topo", template);
            MDC.put("iid", iid);
            MDC.put("position", "spout");
            return realCall(iid, template, request, false);
        } catch (Throwable e) {
            LOGGER.error("call error: ", e);
        } finally {
            MDC.clear();
            SpiManager.clear();
        }
        response.setStatus(HttpStatus.BAD_GATEWAY.value());
        Status status = new Status(StatusCode.FAIL, "detail internal error");
        RetData<Map> result = new RetData<>();
        result.setStatus(status);
        return gson.toJson(result);
    }

    @RequestMapping(value = "/detail/dynapi", produces = {"application/json;charset=UTF-8"})
    @ResponseBody
    @MethodSpirit
    public String detailDynApi(@RequestParam(value = "iid", required = true) String iid,
                               @RequestParam(value = "template", required = true) String template,
                               HttpServletRequest request, HttpServletResponse response) throws DetailException {
        try {
            // 设置MDC参数,供logback获取
            MDC.put("topo", template);
            MDC.put("iid", iid);
            MDC.put("position", "spout");
            return realCall(iid, template, request, true);
        } catch (Throwable e) {
            LOGGER.error("call error: ", e);
        } finally {
            MDC.clear();
            SpiManager.clear();
        }
        response.setStatus(HttpStatus.BAD_GATEWAY.value());
        Status status = new Status(StatusCode.FAIL, "detail internal error");
        RetData<Map> result = new RetData<>();
        result.setStatus(status);
        return gson.toJson(result);
    }

    @RequestMapping(value = "/detail/comapi", produces = {"application/json;charset=UTF-8"})
    @ResponseBody
    @MethodSpirit
    public String detailComApi(@RequestParam(value = "iid", required = true) String iid,
                               @RequestParam(value = "template", required = true) String template, @RequestParam(value = "comIds", required = true) String comIds,
                               HttpServletRequest request, HttpServletResponse response) throws DetailException {
        try {
            // 设置MDC参数,供logback获取
            MDC.put("topo", template);
            MDC.put("iid", iid);
            MDC.put("position", "spout");
            return getComData(iid, template, request, comIds);
        } catch (Throwable e) {
            LOGGER.error("call error: ", e);
        } finally {
            MDC.clear();
            SpiManager.clear();
        }
        response.setStatus(HttpStatus.BAD_GATEWAY.value());
        Status status = new Status(StatusCode.FAIL, "detail internal error");
        RetData<Map> result = new RetData<>();
        result.setStatus(status);
        return gson.toJson(result);
    }

    protected String getComData(String iid, String template,
                                HttpServletRequest request, String comIds) throws DetailException {
        long startTime = System.currentTimeMillis();
        if (iid.endsWith("_1111")) {
            iid = StringUtils.removeEnd(iid, "_1111");
        }

        if (StringUtils.isEmpty(template)) {
            return getInvalidRet("invalid template : " + template, false);
        }

        String[] templateKeys = template.split("-");
        if (templateKeys.length != 4) {
            return getInvalidRet("not complete template key : " + template, false);
        }

        Long itemId = IdConvertor.urlToId(iid);
        if (StringUtils.isEmpty(iid) || itemId > Integer.MAX_VALUE || itemId < 0) {
            LOGGER.error("invalid itemId : {}", iid);
            return getInvalidRet("invalid itemId " + iid, false);
        }
        String bizType = templateKeys[2].replaceAll("detail_", "");
        RouteInfo routeInfo = getRouteInfo(Integer.parseInt(templateKeys[0]), bizType, Integer.parseInt(templateKeys[1]), templateKeys[3]);
        if (null == routeInfo) {
            LOGGER.error("invalid route : {}", routeInfo);
            return getInvalidRet("invalid route parameter!", false);
        }

        if (StringUtils.isEmpty(comIds)) {
            LOGGER.error("comIds is empty : {}", routeInfo);
            return getInvalidRet("comIds is empty", false);
        }
        List<String> componentIds = Arrays.asList(comIds.split(","));
        if (null == componentIds) {
            return getInvalidRet("no component select!", false);
        }

        List<String> namespacedComponentIds = new ArrayList<>(componentIds.size());
        for (String componentId : componentIds) {
            String biz = null;
            if (routeInfo.getBizType() == BizType.CHANNEL) {
                biz = routeInfo.getChannelType();
            } else {
                biz = routeInfo.getBizType().name().toLowerCase();
            }
            String namespacedId = routeInfo.getApp().name().toLowerCase() + "/" + routeInfo.getPlatform().name().toLowerCase() + "/" + biz + "." + componentId;
            namespacedComponentIds.add(namespacedId);
        }

        qpsAdd(true ? template + "_com" : template, "route.comapi");

        DetailContext context = new DetailContext(routeInfo, namespacedComponentIds, itemId, TranslatorManager.getTranslatorMappingHolder());
        context.setDyn(false);
        context.setLoginUserId(getLoginUserId(routeInfo, request));
        parseParam(routeInfo, request, context);
        // 获取客户端ip, 存在x-forwarded-for头中, 记录了请求链路的所有ip
        try {
            String clientIp = request.getParameter("ip");
            if (!StringUtils.isEmpty(clientIp)) {
                context.setClientIp(clientIp);
                if (clientIp.startsWith("10.")) {
//                    LOGGER.error("MWP got local ip {}", clientIp);
                }
            } else {
                String xForwardedFor = request.getHeader("x-forwarded-for"); //************, 127.0.0.1, 127.0.0.1
                if (!StringUtils.isBlank(xForwardedFor)) {
                    String[] clientIps = xForwardedFor.split(", ");
                    // 广州移动等部分运营商会将客户端ip放在第二个,第一个ip为局域网ip
                    if (clientIps[0].startsWith("10.") && clientIps.length > 1) {
                        context.setClientIp(clientIps[1]);
                    } else {
                        context.setClientIp(clientIps[0]);
                    }
                }
            }
        } catch (Throwable e) {
            LOGGER.error("get client ip error.");
        }
        FuncRet<Map<String, Object>> ret = topologyManager.emit(context);

        long endTime = System.currentTimeMillis();
        rtAdd((int) (endTime - startTime), template + "_com", "route.comapi");
        if (!ret.isSuccess()) {
            return getInvalidRet(ret.getMessage(), false);
        }

        return getSuccessRet(ret.getMessage(), ret.getData(), false, request, context, true);
    }


    protected String realCall(String iid, String template,
                              HttpServletRequest request, boolean isDyn) throws DetailException {
        long startTime = System.currentTimeMillis();
        if (iid.endsWith("_1111")) {
            iid = StringUtils.removeEnd(iid, "_1111");
        }

        if (StringUtils.isEmpty(template)) {
            return getInvalidRet("invalid template : " + template, isDyn);
        }

        String[] templateKeys = template.split("-");
        if (templateKeys.length != 4) {
            return getInvalidRet("not complete template key : " + template, isDyn);
        }
        Long itemId = null;
        try {
            itemId = IdConvertor.urlToId(iid);
        } catch (Exception e) {
            LOGGER.error("invalid item id {}", iid);
        }
        if (null == itemId || itemId > Integer.MAX_VALUE || itemId < 0) {
            LOGGER.error("invalid itemId : {}", iid);
            return getInvalidRet("invalid itemId " + iid, isDyn);
        }
        String bizType = templateKeys[2].replaceAll("detail_", "");
        RouteInfo routeInfo = getRouteInfo(Integer.parseInt(templateKeys[0]), bizType, Integer.parseInt(templateKeys[1]), templateKeys[3]);
        if (null == routeInfo) {
            LOGGER.error("invalid route : {}", routeInfo);
            return getInvalidRet("invalid route parameter!", isDyn);
        }
        List<String> componentIds = templateManager.getComponentIds(routeInfo, templateKeys[3]);
        if (null == componentIds) {
            return getInvalidRet("no component select!", isDyn);
        }

        qpsAdd(isDyn ? template + "_dyn" : template, isDyn ? "route.dynapi" : "route.api");

        DetailContext context = new DetailContext(routeInfo, componentIds, itemId, TranslatorManager.getTranslatorMappingHolder());
        context.setDyn(isDyn);
        context.setLoginUserId(getLoginUserId(routeInfo, request));
        try {
            String mwpDeviceInfo = request.getHeader("mw-dinfo");
            if (mwpDeviceInfo != null) {
                String[] ps = mwpDeviceInfo.split("@");
                if (ps != null && ps.length >= 3) {
                    context.setOsVersion(ps[1]);
                }
            }
            String appkey = request.getHeader("mw-appkey");
            if (!StringUtils.isEmpty(appkey)) {
                context.addParam("appkey", appkey);
            }

            String mwTTid = request.getHeader("mw-ttid");
            if (!StringUtils.isEmpty(mwTTid)) {
                context.addParam("mwTTid", mwTTid);
                MWPRequestFrom requestFrom = new MWPRequestFrom();
                requestFrom.setChannel(mwTTid);
                context.addParam("_platform", requestFrom.getPlatform());
            }

            String mwDid = request.getHeader("mw-did");
            if (!StringUtils.isEmpty(mwDid)) {
                context.addParam("mwDid", mwDid);
            }
        } catch (Throwable e) {
        }
        parseParam(routeInfo, request, context);
        // 获取客户端ip, 存在x-forwarded-for头中, 记录了请求链路的所有ip
        try {
            String clientIp = request.getParameter("ip");
            if (!StringUtils.isEmpty(clientIp)) {
                context.setClientIp(clientIp);
                if (clientIp.startsWith("10.")) {
//                    LOGGER.error("MWP got local ip {}", clientIp);
                }
            } else {
                String xForwardedFor = request.getHeader("x-forwarded-for"); //************, 127.0.0.1, 127.0.0.1
                if (!StringUtils.isBlank(xForwardedFor)) {
                    String[] clientIps = xForwardedFor.split(", ");
                    // 广州移动等部分运营商会将客户端ip放在第二个,第一个ip为局域网ip
                    if (clientIps[0].startsWith("10.") && clientIps.length > 1) {
                        context.setClientIp(clientIps[1]);
                    } else {
                        context.setClientIp(clientIps[0]);
                    }
                }
            }
        } catch (Throwable e) {
            LOGGER.error("get client ip error.");
        }
        FuncRet<Map<String, Object>> ret = topologyManager.emit(context);

        long endTime = System.currentTimeMillis();
        rtAdd((int) (endTime - startTime), isDyn ? template + "_dyn" : template, isDyn ? "route.dynapi" : "route.api");
        if (!ret.isSuccess()) {
            return getInvalidRet(ret.getMessage(), isDyn);
        }

        return getSuccessRet(ret.getMessage(), ret.getData(), isDyn, request, context, false);
    }

    /**
     * 监控检测
     *
     * @return
     */
    @RequestMapping(value = "/status")
    @ResponseBody
    public String status(HttpServletRequest request, HttpServletResponse response) {
        while (TranslatorManager.getTranslatorMappingHolder() == null) {
            try {
                Thread.sleep(1000L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        if (warmUped.compareAndSet(false, true)) {
            try {
                detailApi("1k2ygge", "1-3-detail_normal-2.0.4", request, response);
                detailApi("1k2ygge", "1-3-detail_normal-2.0.4", request, response);
                detailApi("1k2ygge", "1-3-detail_normal-2.0.4", request, response);
                detailApi("1k2ygge", "1-3-detail_normal-2.0.4", request, response);
                Thread.sleep(2000);
                MWPServiceUtil.serviceReadyForLazyExport();
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            Thread.sleep(5000);
                        } catch (InterruptedException e) {
                            LOGGER.error("sleep error : ", e);
                        }
                        SlbRegister.getInstance().start();
                    }
                }).start();
            } catch (Throwable e) {
                LOGGER.error("check state error", e);
            }
            response.setStatus(HttpStatus.OK.value());
        }
        return "SUCCESS";
    }

    /**
     * httpdns
     *
     * @return
     */
    @RequestMapping(value = "/ipavailable.html")
    @ResponseBody
    public String httpdns() {
        return "1001";
    }

    @RequestMapping(value = "/threadPool")
    @ResponseBody
    public Object threadPool() {
        List<String> returnValue = Lists.newArrayList();
        returnValue.add(String.format("%s 线程池 core:%d max:%d now:%d taskCount:%d", "commonExecutorService",
                ThreadPoolExecutors.commonExecutorService.getCorePoolSize(),
                ThreadPoolExecutors.commonExecutorService.getMaximumPoolSize(),
                ThreadPoolExecutors.commonExecutorService.getActiveCount(),
                ThreadPoolExecutors.commonExecutorService.getCompletedTaskCount()));

        returnValue.add(String.format("%s 线程池 core:%d max:%d now:%d taskCount:%d", "translateExecutorService",
                ThreadPoolExecutors.translateExecutorService.getCorePoolSize(),
                ThreadPoolExecutors.translateExecutorService.getMaximumPoolSize(),
                ThreadPoolExecutors.translateExecutorService.getActiveCount(),
                ThreadPoolExecutors.translateExecutorService.getCompletedTaskCount()
        ));

        returnValue.add(String.format("%s 线程池 core:%d max:%d now:%d taskCount:%d", "fastExecutorService",
                ThreadPoolExecutors.fastExecutorService.getCorePoolSize(),
                ThreadPoolExecutors.fastExecutorService.getMaximumPoolSize(),
                ThreadPoolExecutors.fastExecutorService.getActiveCount(),
                ThreadPoolExecutors.fastExecutorService.getCompletedTaskCount()
        ));
        return returnValue;
    }

    private static void parseParam(RouteInfo routeInfo, HttpServletRequest request, DetailContext context) {
        for (Map.Entry<String, String[]> entry : request.getParameterMap().entrySet()) {
            if (entry.getValue().length > 0) {
                context.addParam(entry.getKey(), entry.getValue()[0]);
            }
        }
        if (routeInfo.getBizType() == BizType.FASTBUY || routeInfo.getBizType() == BizType.SHARE) {
            String fastbuyId = request.getParameter("fastbuyId");
            if (!StringUtils.isEmpty(fastbuyId)) {
                context.addParam("fastbuyId", IdConvertor.urlToId(fastbuyId).toString());
            }
        }
        if (routeInfo.getBizType() == BizType.SECKILL || routeInfo.getBizType() == BizType.SHARE) {
            String seckillId = request.getParameter("seckillId");
            if (!StringUtils.isEmpty(seckillId)) {
                context.addParam("seckillId", IdConvertor.urlToId(seckillId).toString());
            }
        }
        if (routeInfo.getBizType() == BizType.CHANNEL) {
            String auctionId = request.getParameter("auctionId");
            if (!StringUtils.isEmpty(auctionId)) {
                context.addParam("auctionId", IdConvertor.urlToId(auctionId).toString());
            }
        }
        if (routeInfo.getBizType() == BizType.TTNORMAL) {
            String did = request.getParameter("did");
            if (!StringUtils.isEmpty(did)) {
                context.addParam("did", IdConvertor.urlToId(did).toString());
            }
            String relatedIds = request.getParameter("relatedIds");
            context.addParam("relatedIds", relatedIds);
            String fashionId = request.getParameter("fashionId");
            context.addParam("fashionId", fashionId);
        }
        String acm = request.getParameter("acm");
        if (StringUtils.isNotBlank(acm)) {
            context.addParam("acm", acm);
        }

        try {
            // 用户在详情页选择的收获地址id
            if (StringUtils.isNotBlank(request.getParameter("recvAddressId"))) {
                context.addParam("recvAddressId", request.getParameter("recvAddressId"));
            }
            if (StringUtils.isNotBlank(request.getParameter("province"))) {
                String province = new String(request.getParameter("province").getBytes("ISO-8859-1"), "UTF-8");
                context.addParam("province", province);
            }
            if (StringUtils.isNotBlank(request.getParameter("city"))) {
                String city = new String(request.getParameter("city").getBytes("ISO-8859-1"), "UTF-8");
                context.addParam("city", city);
            }
        } catch (UnsupportedEncodingException e) {
            LOGGER.error("Route get address fault : {}", e);
        }
    }

    private String getInvalidRet(String msg, boolean isDyn) {
        Status status = new Status(StatusCode.FAIL, msg);
        RetData<Map> result = new RetData<>();
        result.setStatus(status);
        String retStr = gson.toJson(result);
        return isDyn ? StringEscapeUtils.escapeJava(retStr) : retStr;
    }

    private String getSuccessRet(String msg, Map<String, Object> data, boolean isDyn, HttpServletRequest request, DetailContext context, Boolean hideEsi) {
        Status status = new Status(StatusCode.SUCCESS, msg);
        RetData<Map> result = new RetData<>();
        if (null == data) {
            data = new HashedMap();
        }

        if (isDyn && data.isEmpty()) {
            data.put("_", "");
        }
        //debug模式
        if ("true".equals(context.getParam("debug"))) {
            data.put("debug", ContextUtil.getContextDebugInfo(context));
        }
        // don't use esi
//        if (context.getRouteInfo().getBizType() != BizType.SECKILL && !hideEsi) {
//            data.put("esi", ESI_TAG);
//        }
        result.setResult(data);
        result.setStatus(status);
        String retStr = gson.toJson(result);
        if (isDyn) {
            return org.apache.commons.lang.StringEscapeUtils.escapeJava(retStr);
        } else {
//            return StringUtils.replace(retStr, ESI_TAG, getEsiUrl(request));
            return retStr;
        }
    }


    private static String getEsiUrl(HttpServletRequest request) {
        String domain = request.getServerName();
        return String.format(ESI_TMPL, domain + DYN_PATH + ESI_POSTFIX);
    }

    public static RouteInfo getRouteInfo(Integer appCode, String bizTypeName, Integer platformCode, String version) {
        App app;
        try {
            app = App.getApp(appCode);
        } catch (IllegalArgumentException e) {
            LOGGER.error("undefined app : {}", appCode);
            return null;
        }
        Platform platform;
        try {
            platform = Platform.getPlatform(platformCode);
        } catch (IllegalArgumentException e) {
            LOGGER.error("undefined platform : {}", platformCode);
            return null;
        }

        BizType bizType;
        try {
            bizType = BizType.valueOf(bizTypeName.toUpperCase());
        } catch (IllegalArgumentException e) {
            LOGGER.error("undefined bizType : {}", bizTypeName);
            return null;
        }
        if (bizType == BizType.CHANNEL) {
            String channelType = version.split("_")[0];
            return new RouteInfo(app, platform, bizType, channelType, version);
        } else if (bizType == BizType.SKU && version.contains(".channel.")) {
            String channelType = StringUtils.substringBefore(StringUtils.substringAfter(version, ".channel."), ".");
            return new RouteInfo(app, platform, bizType, channelType, version);
        } else {
            return new RouteInfo(app, platform, bizType, null, version);
        }
    }

    private void rtAdd(int rt, String template, String enterance) {
        Map<String, String> tags = new HashMap<>(2);
        if (StringUtils.isNotBlank(template)) {
            tags.put("url", template.replaceAll("-", "_"));
            tags.put("app", "MGJ");
        }
        tags.put("enterance", enterance);
        Collector rtCollector = sentryClient.getCollector("detail.rt", tags, 60, CollectorType.AVG);
        rtCollector.put(rt);
    }

    private void qpsAdd(String template, String enterance) {
        Map<String, String> tags = new HashMap<>(2);
        if (StringUtils.isNotBlank(template)) {
            tags.put("url", template.replaceAll("-", "_"));
            tags.put("app", "MGJ");
        }
        tags.put("enterance", enterance);
        Collector qpsCollector = sentryClient.getCollector("detail.qps", tags, 1, CollectorType.SUM);
        qpsCollector.put(1);
    }

    private Long getLoginUserId(RouteInfo routeInfo, HttpServletRequest request) {
        if (!EnvUtil.isOnlineEnv() && request.getParameter("mockUser") != null) {
            //只有非线上环境，才能使用mock功能
            return Long.parseLong(request.getParameter("mockUser"));
        }
        Long loginUserId = SessionContextHolder.getUserId();
        if (loginUserId != 0 && loginUserId > 0) {
            return loginUserId;
        } else if (routeInfo.getApp().equals(App.MSD) || routeInfo.getApp().equals(App.XCX) || routeInfo.getPlatform().equals(Platform.XCX)) {
            String sid = request.getHeader("mw-sid");
            String appkey = request.getHeader("mw-appkey");
            String did = request.getHeader("mw-did");
            if (!StringUtils.isEmpty(sid) && !StringUtils.isEmpty(did) && !StringUtils.isEmpty(appkey)) {
                SignInfo signInfo = sessionService.checkSignInWmp(sid, did, appkey);
                if (null != signInfo && signInfo.isValid() && signInfo.getUserId() > 0) {
                    return signInfo.getUserId();
                }

            }
        }
        return null;
    }
}
