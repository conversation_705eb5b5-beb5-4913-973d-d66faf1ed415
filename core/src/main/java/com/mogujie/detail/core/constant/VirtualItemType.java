package com.mogujie.detail.core.constant;

/**
 * Created by an<PERSON> on 17/10/13.
 */
public enum VirtualItemType {
    /**
     * 普通商品（非虚拟商品）
     */
    NORMAL(0),

    /**
     * 流量
     */
    TRAFFIC_DATA(1),

    /**
     * 话费
     */
    PHONE_FEE(2),

    /**
     * Q币
     */
    QB(3);

    private int code;

    public int getCode(){
        return code;
    }

    VirtualItemType(int code) {
        this.code = code;
    }

    public static VirtualItemType getType(int code) {
        switch (code) {
            case 1:
                return TRAFFIC_DATA;
            case 2:
                return PHONE_FEE;
            case 3:
                return QB;
            default:
                return NORMAL;
        }
    }

}
