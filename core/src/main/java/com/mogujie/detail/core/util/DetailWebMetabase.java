package com.mogujie.detail.core.util;

import com.mogujie.market.common.util.StringUtil;
import com.mogujie.metabase.client.MetaClient;
import com.mogujie.metabase.client.MetabaseConfigure;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @auther huasheng
 * @time 19/3/29 17:07
 */
public class DetailWebMetabase {

    private static MetaClient metaClient =null;
    private static Logger logger = LoggerFactory.getLogger(DetailWebMetabase.class);


    static{
        MetabaseConfigure configure = new MetabaseConfigure();
        configure.setConfigGroup("detailConf");
        metaClient = MetaClient.client2(configure);
    }

    public static String get(String key) {
        return  metaClient.getConfiguration(key);
    }


    public static Boolean getBoolean(String key, Boolean defaultValue) {
        String value = null;
        try {
            value = get(key);
            if (StringUtil.isNotEmpty(value)) {
                return Boolean.valueOf(value);
            } else {
                return defaultValue;
            }
        } catch (Exception e) {
            logger.error("Exception key: " + key + ", value: " + value, e);
            return defaultValue;
        }
    }

    public static String getString(String key, String defaultValue) {
        String value = null;
        try {
            value = get(key);
            if (StringUtil.isNotEmpty(value)) {
                return value;
            } else {
                return defaultValue;
            }
        } catch (Exception e) {
            logger.error("Exception key: " + key + ", value: " + value, e);
            return defaultValue;
        }
    }


}
