package com.mogujie.detail.core.util;

import com.mogujie.detail.core.cache.ICacheUtil;
import com.mogujie.detail.core.cache.impl.GuavaCache;
import com.mogujie.metabase.utils.StringUtils;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * Created by x<PERSON>oya<PERSON> on 15/12/4.
 */
public class CodecUtil {

    private static final org.slf4j.Logger LOGGER = org.slf4j.LoggerFactory.getLogger(CodecUtil.class);

    private static final String CODEC_KEY = "a132a884faecc0f2";

    private static ICacheUtil cacheUtil = new GuavaCache();

    private static Cipher cipher;

    /**
     * 因为加密算法的实现为spi单例,init开销比较大,所以详情页后端只要支持加密就行了,指有一个cipher实例.
     */
    static {
        SecretKeySpec skey = new SecretKeySpec(CODEC_KEY.getBytes(), "AES");
        try {
            cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, skey);
        } catch (NoSuchAlgorithmException | NoSuchPaddingException | InvalidKeyException e) {
            LOGGER.error("init cipher failed : {}", e);
        }
    }

    public static String encryptWithCache(String input) throws UnsupportedEncodingException {
        String encryptedVal = cacheUtil.getString(input);
        if (StringUtils.isEmpty(encryptedVal)) {
            encryptedVal = encrypt(input);
            if (!StringUtils.isEmpty(encryptedVal)) {
                cacheUtil.setString(input, encryptedVal, 86400);
            }
        }
        return encryptedVal;
    }

    public static String encrypt(String input) throws UnsupportedEncodingException {
        byte[] crypted = null;
        try {
            crypted = cipher.doFinal(input.getBytes());
        } catch (Exception e) {
            LOGGER.error(e.toString());
        }
        if (crypted == null)
            return null;
        return new BASE64Encoder().encode(crypted);
    }

//    public static String decrypt(String input) throws UnsupportedEncodingException {
//        byte[] output = null;
//        Cipher cipher = null;
//        try {
//            cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
//            cipher.init(Cipher.DECRYPT_MODE, skey);
//            output = cipher.doFinal(new BASE64Decoder().decodeBuffer(input));
//        } catch (Exception e) {
//            LOGGER.error(e.toString());
//        }
//        if (output == null)
//            return null;
//        return new String(output, "UTF-8");
//    }
}
