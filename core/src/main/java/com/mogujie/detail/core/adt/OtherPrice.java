package com.mogujie.detail.core.adt;

import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * 商品其他可能需要被透出的价格
 * Created by anshi on 2018/11/5.
 */
public class OtherPrice {

    /**
     * 价格类型
     * 直播分享价格：1
     *
     * @see com.mogujie.service.hummer.constains.PromotionConstants.ItemPriceType
     */
    @Getter
    @Setter
    private Integer priceType;

    /**
     * 商品价格
     */
    @Getter
    @Setter
    private Long itemRealPrice;

    /**
     * sku最高价
     */
    @Getter
    @Setter
    private Long maxPrice;

    /**
     * sku最低价
     */
    @Getter
    @Setter
    private Long minPrice;

    /**
     * 商品sku价格
     */
    @Getter
    @Setter
    private Map<String/**skuId*/, Long/**discountFee*/> skuRealPriceMap;
}
