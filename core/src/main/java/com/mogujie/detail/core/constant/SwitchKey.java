package com.mogujie.detail.core.constant;

/**
 * Created by anshi on 16/10/10.
 */
public interface SwitchKey {

    // 店铺包邮
    String WAITRESS_SHIP_SERVICE = "swt_waitress_shipservice";

    // 蘑豆gift
    String MODOUBUSINESS_MODOU_RULE = "swt_modoubusiness_modouRule";

    // 喜欢数
    String FEEDSTREAM_LIKES_COUNT = "swt_feedstream_likesCounter";

    // 红人信息
    String SOCIALCHANNEL_USER_BY_ITEM = "swt_socialchannel_userByItem";

    // 是否喜欢
    String FEEDSTREAM_IS_USER_LIKE = "swt_feedstream_isUserLike";

    // 店铺标(品质商家等)
    String SHOPCENTER_GET_IMG = "swt_shopcenter_getImg";

    // 蘑菇街店铺DSR
    String OCEAN_GET_KPI_BY_SHOP_ID = "swt_ocean_getKpiByShopId";

    // 判断店铺是否收藏
    String SHOPFAVORITE_CHECK_RELATION = "swt_shopfavorite_checkRelation";

    // 根据类目id获取类目
    String CATEGORYV2_CACHED_CATEGORY = "swt_categoryV2_cachedCategory";

    // 查店铺是否有某标
    String SHOPCENTER_CHECK_SHOP_TAG = "swt_shopcenter_checkShopTag";

    // 查询快抢信息
    String FERRARI_GET_RUSH_INFO = "swt_ferrari_getRushInfo";

    // 检查是否为管理员
    String MUSER_IS_ADMIN = "swt_muser_isAdmin";

    // 查询美丽说twitter id
    String ITEMCENTER_TWITTER_IDS = "swt_itemcenter_twitterIds";

    // 查询商品活动报名信息(蘑豆报名库存)
    String ACTCENTER_GET_BY_ID = "swt_comb_act_center_getById";

    // 查询部分优惠券
    String HUMMER_PART_COUPON_LIST = "swt_hummer_partCouponList";

    // 店铺当前上架商品数
    String ITEMCENTER_COUNT_ONLINE_ITEMS = "swt_itemcenter_countOnlineItems";

    // 店铺粉丝数
    String SHOPFAVORITE_SHOP_FAN_COUNT = "swt_shopfavorite_shopFanCount";

    // 查询店铺类目
    String SHOPCENTER_SHOP_CATEGORY = "swt_shopcenter_shopCategory";

    // 购物车数量
    String CARTSERVICE_SKU_COUNT = "swt_cartMicroService_skuCount";

    // 美丽说店铺DSR
    String MLSOCEAN_GET_KPI_BY_SHOP_ID = "swt_mlsocean_getKpiByShopId";

    String MBSTORE_BARGAIN_DETAIL = "swt_mbstore_bargainDetail";

    String PALLAS_CLIENT = "swt_pallas_client";

    String USERSERVICE_GET_USER_BY_IDS = "swt_muser_getUserByIds";

    // 用户是否可开通白付美分期购
    String PAYMAILO_CHECK_USER_WHITE_LIST = "swt_paymailo_checkUserWhiteList";

    // 用户是否喜欢该商品
    String RELATIONSERVICE_READ_QUERY_RELATION = "swt_relationservice_queryRelation";

    // 商品喜欢数
    String RELATIONSERVICE_READ_LIKES_COUNT = "swt_relationservice_getCounterMuliter";

    // 用户是否可用分期
    String PAYMAILO_GET_USER_STATUS = "swt_paymailo_getUserStatus";

    // 是否展示先试后买
    String PAYMAILO_GET_FORETASTE = "swt_paymailo_getForetaste";

    /**
     * 是否展示保险信息
     */
    String PAYMAILO_GET_INSURANCE = "swt_paymailo_getInsurance";


    String USE_MOBILE_PRICE = "swt_use_mobile_price";

    // 315不支持7天无理由退货开关
    String NO_REASON_REFOUND = "swt_noReasonRefound";

    // 315标展示在最前面
    String NO_REASON_REFOUND_FIRST = "swt_noReasonRefoundFirst";

    // 启用图文详情模块动态解析
    String USE_DYNAMIC_DETAILINFO = "swt_useDynamicDetailInfo";

    // 使用DTS获取DSR数据
    String USE_DTS_DSR = "swt_useDtsDsr";

    // 是否计算邮费
    String CAL_POSTAGE_FEE = "swt_calculate_postage_fee";

    // 根据tangram来为sku属性值排序
    String ORDERED_SKU_ATTRS = "swt_ordered_sku_attrs";

    // 是否显示发货地
    String SHOW_ADDRESS = "swt_show_address";

    // 是否过滤尺码助手只有U质团才展示
    String SIZEHELPER_UZHI_FILTER = "swt_sizehelper_uzhi_filter";

    // ratio：pallas切tag-center-api的比例
    String RTO_SWITCH_TO_TAG_CENTER = "rto_tagcenter_pallas";

    // 是否新人专享
    String SWT_IS_NEW_COMER = "swt_is_new_comer";

    // mait: true走定投/false走通投
    String SWT_TARGET_MAIT = "swt_target_mait";

    // 展示魔豆抵扣
    String SWT_SHOW_MODOU_DISCOUNT = "swt_show_modou_discount";

    // H5大促促销合成图展示开关
    String SWT_SHOW_H5_PROMOTION_IMG = "swt_show_h5_promotion_img";

    // 使用新详情页装修接口 @伏念
    String SWT_USE_NEW_SHOP_DECORATE = "swt_use_new_shop_decorate";

    // 是否查询主播信息窗口
    String SWT_SHOW_LIVE_INFO = "swt_show_live_info";

    // 是否查询当日是否在店铺内加购下单判断私聊弹窗
    String SWT_SHOW_IM_TIPS = "swt_show_im_tips";

    //查询店铺标签开关
    String SWT_SHOW_SHOP_LABELS = "swt_show_shop_labels";

    // 是否查询店铺实时推荐
    String SWT_SHOP_REAL_TIME_RECOMMEND = "swt_shop_real_time_recommend";

    // 是否查询店铺在架商品数
    String SWT_GOODS_TOTAL = "swt_goods_total";

    //是否使用sku最低价调用促销的计价接口，true:sku最低价，false：sku最高价
    String SWT_LOW_SKU_PRICE = "swt_low_sku_price";

    // 是否使用新的快照列表页面;
    String SWT_NEW_SNAP_LIST = "swt_new_snap_list";

    // 是否使用魔豆兑换
    String SWT_MODOU_CONVERT = "swt_modou_convert";

    // 是否展示新版的跨店满减banner信息
    String SWITCH_CROSS_SHOP_DISCOUNT_BANNER = "switch_cross_shop_discount_banner";

    // 是否展示详情页的热卖品类榜单
    String SWITCH_HOT_SALE_RANK_LIST = "switch_hot_sale_rank_list";

    //详情页是否展示买手店信息
    String SWT_CLOSE_BUYER_SHOP_DISPLAY = "swt_close_buyer_shop_display";

    /**
     * 隐藏购买用户信息类目。
     */
    String SWT_HIDE_BUY_USE_CIDS = "swt_hide_buy_use_cids";

    //关闭直播切片商品推荐
    String SWT_CLOSE_LIVE_ITEM_RECOMMEND = "swt_close_live_item_recommend";

    //关闭麦田免息标
    String SWT_CLOSE_INSTALLMENT_ICON = "swt_close_installment_icon";
    //关闭麦田免息标
    String SWT_CLOSE_INSTALLMENT_ICON_DISCOUNT_TAG = "swt_close_installment_icon_discount_tag";

    //官方补贴麦田资源位ID
    String PLATFORM_ALLOWANCE_MAITID = "platform_allowance_maitId";
}
