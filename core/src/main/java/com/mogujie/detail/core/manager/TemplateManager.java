package com.mogujie.detail.core.manager;

import com.mogujie.detail.core.adt.RouteInfo;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.metabase.spring.client.MetabaseClient;
import com.mogujie.metabase.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * Created by xiaoyao on 16/10/27.
 */
@Component
public class TemplateManager {

    private static final Logger LOGGER = LoggerFactory.getLogger(TemplateManager.class);

    @Resource(name = "detailTemplate")
    private MetabaseClient detailTemplate;


    @PostConstruct
    public void init() throws Exception {
    }

    public List<String> getComponentIds(RouteInfo routeInfo, String version) {
        String templateName = routeInfo.getApp().name() + "_" + routeInfo.getPlatform().name() + "_" + routeInfo.getBizType().name() + "_" + version;
        List<String> componentIds = getComponentIds(templateName);
        String biz = null;
        if (routeInfo.getBizType() == BizType.CHANNEL) {
            biz = routeInfo.getChannelType();
        } else {
            biz = routeInfo.getBizType().name().toLowerCase();
        }
        if (!CollectionUtils.isEmpty(componentIds)) {
            List<String> namespacedComponentIds = new ArrayList<>(componentIds.size());
            for (String componentId : componentIds) {
                String namespacedId = routeInfo.getApp().name().toLowerCase() + "/" + routeInfo.getPlatform().name().toLowerCase() + "/" + biz + "." + componentId;
                namespacedComponentIds.add(namespacedId);
            }
            return namespacedComponentIds;
        }
        return null;
    }

    private List<String> getComponentIds(String templateName) {
        try {
            String componentIds = detailTemplate.get(templateName.toLowerCase());
            if (!StringUtils.isEmpty(componentIds)) {
                String[] componentIdArr = componentIds.split(",");
                return Arrays.asList(componentIdArr);
            }
        } catch (Exception e) {
            ;
        }
        return Collections.emptyList();
    }
}
