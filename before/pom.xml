<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>detail-web</artifactId>
        <groupId>com.mogujie.detail</groupId>
        <version>1.1.0.dsl</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>before</artifactId>

    <properties>
        <servlet.version>2.5</servlet.version>
        <junit.version>4.11</junit.version>
        <jetty.version>6.1.26</jetty.version>
        <spirit.version>1.0.2</spirit.version>
        <mogucache.version>0.1.9</mogucache.version>
        <trade-order.version>1.0.46</trade-order.version>
        <hummer.version>3.7.21</hummer.version>
        <shopcenter.version>1.3.0.7</shopcenter.version>
        <ferrari.version>1.7-RELEASE</ferrari.version>
        <tangram-property.version>1.1.9</tangram-property.version>
        <tag.version>1.1.2</tag.version>
        <pay-mailo-api.version>1.5.6</pay-mailo-api.version>
        <inventory.version>1.0.24</inventory.version>
        <tag-center-api.version>2.1.3</tag-center-api.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.mogujie.mst</groupId>
            <artifactId>kvstore-client</artifactId>
            <version>1.2.2</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>muser-api</artifactId>
            <version>1.1.3.0</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie</groupId>
            <artifactId>slardar-client</artifactId>
            <version>0.1.8</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
            <version>${servlet.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>

        <!-- 分期信息接口 -->
        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>pay-mailo-api</artifactId>
            <version>${pay-mailo-api.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>kvstore-client</artifactId>
                    <groupId>com.mogujie.mst</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>tag-center-client</artifactId>
            <version>${tag.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mogujie.discover</groupId>
                    <artifactId>discover-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.algo</groupId>
                    <artifactId>topn-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.discover</groupId>
                    <artifactId>discover-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.search.ara.abtest</groupId>
                    <artifactId>abtest-cli</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>item-center-api</artifactId>
            <version>2.1.0.20</version>
        </dependency>


        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>tag-center-api</artifactId>
            <version>${tag-center-api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.marketing</groupId>
            <artifactId>ferrari-api</artifactId>
            <version>${ferrari.version}</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.tesla</groupId>
            <artifactId>tesla-spring-server</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.mogujie.trace</groupId>
                    <artifactId>trace-lurkeragent</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>tangram-property-client</artifactId>
            <version>${tangram-property.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>kvstore-client</artifactId>
                    <groupId>com.mogujie.mst</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>category-service-api</artifactId>
                    <groupId>com.mogujie.service</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>inventory-center-api</artifactId>
            <version>${inventory.version}</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie</groupId>
            <artifactId>costa-server</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.mogujie</groupId>
                    <artifactId>actionlet.session</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mogujie.stable</groupId>
            <artifactId>spirit</artifactId>
            <version>${spirit.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mogujie.metabase</groupId>
                    <artifactId>metabase-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.metabase</groupId>
                    <artifactId>metabase-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-beans</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-aop</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-aspects</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>trade.service.order-api</artifactId>
            <version>${trade-order.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mogujie.mst</groupId>
                    <artifactId>kvstore-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.corgi</groupId>
                    <artifactId>corgi-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>shopcenter-client</artifactId>
            <version>${shopcenter.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mogujie.tesla</groupId>
                    <artifactId>tesla-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.tesla</groupId>
                    <artifactId>tesla-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.metabase</groupId>
                    <artifactId>metabase-spring-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.sentry</groupId>
                    <artifactId>sentry-client</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>kvstore-client</artifactId>
                    <groupId>com.mogujie.mst</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>shopcenter-api</artifactId>
            <version>1.3.0.9</version>
        </dependency>
        <dependency>
            <groupId>com.mogujie.service.hummer</groupId>
            <artifactId>hummer-api</artifactId>
            <version>${hummer.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mogujie</groupId>
                    <artifactId>mogu-commons</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.themis</groupId>
                    <artifactId>themis-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mogujie</groupId>
            <artifactId>actionlet.session</artifactId>
            <version>1.6.1</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

</project>