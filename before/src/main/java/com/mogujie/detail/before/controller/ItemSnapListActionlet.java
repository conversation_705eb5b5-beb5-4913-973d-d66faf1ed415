package com.mogujie.detail.before.controller;

import com.mogujie.actionlet.ActionResult;
import com.mogujie.actionlet.annotation.ActionletName;
import com.mogujie.actionlet.core.impl.DefaultActionResult;
import com.mogujie.actionlet.mwp.annotation.MWPApi;
import com.mogujie.actionlet.session.annotation.NeedUserInfo;
import com.mogujie.actionlet.sync.SyncActionlet;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.costa.server.annotations.RefererCheck;
import com.mogujie.detail.before.common.adt.SnapQueryParam;
import com.mogujie.item.adt.ListResult;
import com.mogujie.service.item.api.basic.ItemEditHistoryService;
import com.mogujie.service.item.domain.basic.ItemEditHistoryDO;
import com.mogujie.service.muser.Result;
import com.mogujie.service.muser.api.UserService;
import com.mogujie.session.SessionContextHolder;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by xiaoyao on 17/3/29.
 */
@Component
@MWPApi(value = "item.snap.list", version = "1")
@ActionletName(value = "item.snap.list", version = "1")
@RefererCheck({"mogujie.com","mogu.com"})
@NeedUserInfo
public class ItemSnapListActionlet implements SyncActionlet<SnapQueryParam, Object> {


    private static final Logger LOGGER = LoggerFactory.getLogger(ItemSnapListActionlet.class);

    private UserService userService;

    private ItemEditHistoryService itemEditHistoryService;

    @PostConstruct
    public void init() {
        try {
            itemEditHistoryService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ItemEditHistoryService.class);
            userService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(UserService.class);
        } catch (Exception e) {
            LOGGER.error("init service failed : ", e);
        }
    }

    @Override
    public ActionResult<Object> execute(@Nullable SnapQueryParam parameter) {
        if (StringUtils.isBlank(parameter.getIid())) {
            return new DefaultActionResult(false, "4004", "商品ID为空", null);
        }

        if (!isAdmin()) {
            return new DefaultActionResult(false, "4004", "非小仙小侠不能查看", null);
        }
        try {
            Long itemId = IdConvertor.urlToId(parameter.getIid());
            Integer offset = parameter.getOffset() == null ? 0 : parameter.getOffset();
            Integer size = parameter.getSize() == null ? 10 : (parameter.getSize() > 10 ? 10 : parameter.getSize());
            ListResult<ItemEditHistoryDO> ret = itemEditHistoryService.queryByItemId(itemId, offset * size, size);
            if (null != ret && ret.isSuccess() && !CollectionUtils.isEmpty(ret.getData())) {
                Map<String, Object> response = new HashMap<>();
                List<SnapRecord> snapList = new ArrayList<>();
                for (ItemEditHistoryDO history : ret.getData()) {
                    snapList.add(new SnapRecord(IdConvertor.idToUrl(history.getItemEditHistoryId()), (int) (history.getGmtCreate().getTime() / 1000)));
                }
                response.put("snapshots", snapList);
                response.put("offset", offset);
                response.put("size", size);
                response.put("total", ret.getTotal());
                return new DefaultActionResult(true, "1001", "", response);
            }
        } catch (Exception e) {
            LOGGER.error("get snap error : ", e);
        }

        return new DefaultActionResult(false, "4004", "内部错误", null);
    }

    static class SnapRecord {

        private String iid;

        private Integer timestamp;

        public SnapRecord(String iid, Integer timestamp) {
            this.iid = iid;
            this.timestamp = timestamp;
        }

        public String getIid() {
            return iid;
        }

        public void setIid(String iid) {
            this.iid = iid;
        }

        public Integer getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(Integer timestamp) {
            this.timestamp = timestamp;
        }
    }

    private Boolean isAdmin() {
        long loginUserId = SessionContextHolder.getUserId();
        if (0L == loginUserId) {
            return false;
        }
        try {
            Result<Boolean> ret = userService.isAdmin(loginUserId);
            return ret.getValue();
        } catch (Throwable e) {
            LOGGER.error("check is admin failed : {}", e);
        }
        return false;
    }
}
