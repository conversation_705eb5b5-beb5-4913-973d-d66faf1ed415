package com.mogujie.detail.before.controller;

import com.google.gson.Gson;
import com.mogujie.actionlet.ActionResult;
import com.mogujie.actionlet.core.ActionletExecutor;
import com.mogujie.detail.before.common.adt.BaseDomainData;
import com.mogujie.detail.before.common.adt.DetailContextHolder;
import com.mogujie.detail.before.common.adt.DetailParam;
import com.mogujie.detail.before.common.adt.RetData;
import com.mogujie.detail.before.common.adt.Status;
import com.mogujie.detail.before.common.adt.TopoRoute;
import com.mogujie.detail.before.common.constant.App;
import com.mogujie.detail.before.common.constant.BizTag;
import com.mogujie.detail.before.common.constant.Constants;
import com.mogujie.detail.before.common.constant.ItemTag;
import com.mogujie.detail.before.common.constant.Platform;
import com.mogujie.detail.before.common.constant.StatusCode;
import com.mogujie.detail.before.common.exception.DetailException;
import com.mogujie.detail.before.core.adt.AppDetailResponse;
import com.mogujie.detail.before.core.adt.DetailRequest;
import com.mogujie.detail.before.core.constant.DetailStatusCode;
import com.mogujie.sentry.SentryClient;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Created by xiaoyao on 16/1/18.
 *
 * 备注：2020年万象项目，详情页合并，将appDetail和detailSkip两个老的详情页应用中部分接口进行暴力平行迁移，统一放在before module中。
 */

@Controller
public class DetailDispatcherMWP {
    private static final Logger LOGGER = LoggerFactory.getLogger(DetailDispatcherMWP.class);

    private static final String ESI_TMPL = "<esi:try><esi:attempt><esi:include src=\'http://%s\'/></esi:attempt><esi:except></esi:except></esi:try>";

    private static final String ESI_PREFIX = "_dyn?$(QUERY_STRING)";

    private static final String ESI_TAG = "##ESI##";

    private final AtomicBoolean warmUped = new AtomicBoolean(false);

    private SentryClient sentryClient;

    private Gson gson;

    @Autowired
    private ActionletExecutor detailExecutor;

    @PostConstruct
    public void init() {
        gson = new Gson();
        sentryClient = SentryClient.factory("detail");
        sentryClient.openAutoCollector();
    }

    @RequestMapping(value = "/detail/{app}/{v}/{biz}", produces = {"application/json;charset=UTF-8"})
    @ResponseBody
    public String detailApi(@PathVariable("app") String app,
                            @PathVariable("v") String v,
                            @PathVariable("biz") String biz, HttpServletRequest request, HttpServletResponse response) {
        boolean isEsi = biz.endsWith("_dyn");
        String topokey = app + "_" + v + "_" + (isEsi ? StringUtils.removeEnd(biz, "_dyn") : biz);
        DetailRequest req = new DetailRequest();
        req.setActionName(app + "." + biz);
        req.setActionVersion(v);
        DetailParam param = new DetailParam();
        String iid = request.getParameter("iid");
        // 设置MDC参数,供logback获取
        MDC.put("topo", topokey);
        MDC.put("iid", iid);
        MDC.put("position", "spout");
        TopoRoute topoRoute = null;
        try {
            if (StringUtils.isEmpty(iid)) {
                Status status = new Status(StatusCode.FAIL, "iid not set");
                RetData<BaseDomainData> result = new RetData<>();
                result.setStatus(status);
                return gson.toJson(result);
            }
            param.setTest(request.getParameter("test"));
            param.setVersion(request.getParameter("_av"));
            param.setItemId(iid);
            param.setTopoKey(topokey);
            req.setParameter(param);
            AppDetailResponse resp = new AppDetailResponse();
            topoRoute = getTopoRoute(app, StringUtils.removeEnd(biz, "_dyn"));
            String referer = request.getHeader("Referer");
            if (null != referer && referer.contains("onecent")) {
                DetailContextHolder.set("isOnecent", "true");
            }
            String appPlat = request.getParameter("appPlat");
            if (StringUtils.isNotBlank(appPlat)){
                DetailContextHolder.set(DetailContextHolder.APP_PLAT, appPlat);
            }
            DetailContextHolder.setRouteTag(topoRoute, Arrays.asList(ItemTag.DEFAULT));
            DetailContextHolder.setAllRequestParams(request);
            if (null == topoRoute) {
                Status status = new Status(StatusCode.FAIL, "App not found");
                RetData<BaseDomainData> result = new RetData<>();
                result.setStatus(status);
                return gson.toJson(result);
            }
            detailExecutor.execute(req, resp);
            ActionResult<BaseDomainData> ret = resp.getResult();
            if (null != ret && Constants.MLS_ITEM_NOT_SHOW.equals(ret.getReturnMessage())) {
                // 美丽说商品不在蘑菇街展示
                Status status = new Status(Integer.parseInt(ret.getReturnCode()), ret.getReturnMessage());
                RetData<BaseDomainData> result = new RetData<>();
                result.setStatus(status);
                result.setResult(null);
                return gson.toJson(result);
            }

            if (null == ret || !ret.isSuccess()) {
                throw new DetailException("internal error");
            }
            if (null != ret && ret.isSuccess()) {
                if (!DetailStatusCode.SUCCESS.equals(ret.getReturnCode())) {
                    Status status = new Status(StatusCode.FAIL, ret.getReturnMessage());
                    RetData<BaseDomainData> result = new RetData<>();
                    result.setStatus(status);
                    result.setResult(null);
                    return gson.toJson(result);
                } else if (null != ret.getData()) {
                    Status status = new Status(StatusCode.SUCCESS, "");
                    RetData<BaseDomainData> result = new RetData<>();
                    result.setStatus(status);
                    result.setResult(ret.getData());
                    String retStr = gson.toJson(result);
                    if (isEsi) {
                        String escape = request.getParameter("escape");
                        if (null == escape || "true".equals(escape)) {
                            return StringEscapeUtils.escapeJava(retStr);
                        } else {
                            return retStr;
                        }
                    } else {
                        return StringUtils.replace(retStr, ESI_TAG, getEsiUrl(request));
                    }
                } else {
                    Status status = new Status(StatusCode.SUCCESS, ret.getReturnMessage());
                    RetData<BaseDomainData> result = new RetData<>();
                    result.setStatus(status);
                    result.setResult(null);
                    return gson.toJson(result);
                }
            }
        } catch (Exception e) {
            LOGGER.error("detail exception : ", e);
        }

        response.setStatus(HttpStatus.BAD_GATEWAY.value());
        Status status = new Status(StatusCode.FAIL, "detail internal error");
        RetData<BaseDomainData> result = new RetData<>();
        result.setStatus(status);
        return gson.toJson(result);
    }

    private static String getEsiUrl(HttpServletRequest request) {
        String uri = request.getRequestURI();
        String domain = request.getServerName();
        return String.format(ESI_TMPL, domain + uri + ESI_PREFIX);
    }

    public static TopoRoute getTopoRoute(String appName, String biz) {
        App app = null;
        try {
            app = App.valueOf(appName.toUpperCase());
        } catch (IllegalArgumentException e) {
            LOGGER.error("undefined app : {}", appName);
            return null;
        }
        String[] platformBiz = biz.split("_");
        Platform platform;
        BizTag bizTag = null;
        if ("main".equals(platformBiz[0])) {
            platform = Platform.APP;
        } else {
            try {
                platform = Platform.valueOf(platformBiz[0].toUpperCase());
            } catch (IllegalArgumentException e) {
                LOGGER.error("undefined platform : {}", platformBiz[0]);
                platform = Platform.ALL;
            }
        }
        if (platformBiz.length >= 2) {
            try {
                bizTag = BizTag.valueOf(platformBiz[1].toUpperCase());
            } catch (IllegalArgumentException e) {
                LOGGER.error("undefined biz : {}", platformBiz[1]);
                bizTag = BizTag.NORMAL;
            }
        } else {
            bizTag = BizTag.NORMAL;
        }
        return new TopoRoute(app, platform, bizTag);
    }
}
