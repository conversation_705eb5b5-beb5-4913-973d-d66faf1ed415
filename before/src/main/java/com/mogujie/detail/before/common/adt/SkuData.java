package com.mogujie.detail.before.common.adt;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by xiaoya<PERSON> on 15/12/7.
 */
public class SkuData {

    @Setter
    @Getter
    private String stockId;

    @Setter
    @Getter
    private String xdSkuId;

    @Setter
    @Getter
    private Integer price;

    @Setter
    @Getter
    private Integer nowprice;

    @Setter
    @Getter
    private String img;

    @Setter
    @Getter
    private String currency;

    @Setter
    @Getter
    private int stock;

    @Setter
    @Getter
    private String style;

    @Setter
    @Getter
    private String size;

    @Setter
    @Getter
    private int styleId;

    @Setter
    @Getter
    private int sizeId;

    /**
     * 预售定金
     */
    @Setter
    @Getter
    private String mainPriceStr;

    /**
     * 预售总价
     */
    @Setter
    @Getter
    private String subPriceStr;

    /**
     * 分期信息
     */
    @Getter
    @Setter
    private List<InstallmentData> installment;

    /**
     * 延迟发货时间
     */
    @Getter
    @Setter
    private Integer delayTime;
}
