package com.mogujie.detail.before.common.adt;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by <PERSON>iaoya<PERSON> on 16/5/26.
 */
public class SkuInfoData {

    protected SkuInfo data;

    private Boolean isPreSale;

    private Integer disType;

    private List<ItemTag> itemTags;
    /**
     * 接口缓存时间
     */
    @Setter
    @Getter
    private Integer expireSeconds;

    public SkuInfo getData() {
        return data;
    }

    public void setData(SkuInfo data) {
        this.data = data;
    }

    public Boolean getIsPreSale() {
        return isPreSale;
    }

    public void setIsPreSale(Boolean isPreSale) {
        this.isPreSale = isPreSale;
    }

    public List<ItemTag> getItemTags() {
        return itemTags;
    }

    public void setItemTags(List<ItemTag> itemTags) {
        this.itemTags = itemTags;
    }

    public Integer getDisType() {
        return disType;
    }

    public void setDisType(Integer disType) {
        this.disType = disType;
    }
}
