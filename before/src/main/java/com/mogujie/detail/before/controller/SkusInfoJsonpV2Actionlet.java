package com.mogujie.detail.before.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.meili.service.shopcenter.api.ShopReadService;
import com.meili.service.shopcenter.domain.entity.ShopInfo;
import com.meili.service.shopcenter.result.Result;
import com.mogujie.actionlet.ActionResult;
import com.mogujie.actionlet.annotation.ActionletName;
import com.mogujie.actionlet.core.impl.DefaultActionResult;
import com.mogujie.actionlet.mwp.MWPContext;
import com.mogujie.actionlet.mwp.MWPRequestFrom;
import com.mogujie.actionlet.mwp.annotation.MWPApi;
import com.mogujie.actionlet.session.annotation.NeedUserInfo;
import com.mogujie.actionlet.sync.SyncActionlet;
import com.mogujie.commons.utils.EmojiUtil;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.costa.core.domain.RequestType;
import com.mogujie.costa.server.annotations.CostaWeb;
import com.mogujie.costa.server.annotations.RefererCheck;
import com.mogujie.detail.before.common.adt.CommonItem;
import com.mogujie.detail.before.common.adt.DecorateResult;
import com.mogujie.detail.before.common.adt.DetailContextHolder;
import com.mogujie.detail.before.common.adt.InstallmentData;
import com.mogujie.detail.before.common.adt.LimitInfo;
import com.mogujie.detail.before.common.adt.PinTuanInfo;
import com.mogujie.detail.before.common.adt.PropInfo;
import com.mogujie.detail.before.common.adt.SizePropData;
import com.mogujie.detail.before.common.adt.SkuData;
import com.mogujie.detail.before.common.adt.SkuInfo;
import com.mogujie.detail.before.common.adt.SkuInfoDataV2;
import com.mogujie.detail.before.common.adt.SkuInfoForApi;
import com.mogujie.detail.before.common.adt.SkusQueryParam;
import com.mogujie.detail.before.common.adt.StoreType;
import com.mogujie.detail.before.common.adt.StylePropData;
import com.mogujie.detail.before.common.adt.TopoRoute;
import com.mogujie.detail.before.common.constant.App;
import com.mogujie.detail.before.common.constant.BizTag;
import com.mogujie.detail.before.common.constant.DiscountType;
import com.mogujie.detail.before.common.constant.ItemTag;
import com.mogujie.detail.before.common.constant.Platform;
import com.mogujie.detail.before.core.util.ContextUtil;
import com.mogujie.detail.before.core.util.GroupBuyingUtil;
import com.mogujie.detail.before.core.util.ImageUtil;
import com.mogujie.detail.before.core.util.ItemFetcher;
import com.mogujie.detail.before.core.util.NumUtil;
import com.mogujie.detail.before.service.PriceDecorator;
import com.mogujie.detail.before.service.SkuGetter;
import com.mogujie.detail.before.service.SkuReorder;
import com.mogujie.marketing.ferrari.api.RushInfoForDetailService;
import com.mogujie.marketing.ferrari.api.dto.RushInfoDTO;
import com.mogujie.marketing.ferrari.api.dto.RushInfoResultDTO;
import com.mogujie.metabase.spring.client.MetabaseClient;
import com.mogujie.pay.common.dto.Response;
import com.mogujie.pay.mailo.api.MaiLoInstallmentApi;
import com.mogujie.pay.mailo.api.MaiLoUserBasicInfoApi;
import com.mogujie.pay.mailo.dto.InstallmentInfoDTO;
import com.mogujie.pay.mailo.dto.MaiLoReqDTO;
import com.mogujie.pay.mailo.dto.UserBasicInfoDTO;
import com.mogujie.pay.mailo.dto.request.parameters.ComputeInstallmentRequestDTO;
import com.mogujie.service.hummer.constains.RequestConstants;
import com.mogujie.service.inventory.api.InventoryReadService;
import com.mogujie.service.inventory.domain.ActivityInventory;
import com.mogujie.service.inventory.domain.param.BatchActivityInventoryQueryParam;
import com.mogujie.service.inventory.domain.result.MapResult;
import com.mogujie.service.item.api.basic.ItemPreSaleService;
import com.mogujie.service.item.domain.basic.ItemExtraDO;
import com.mogujie.service.item.domain.basic.ItemPreSaleDO;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.service.item.domain.basic.SkuAttributionDO;
import com.mogujie.service.item.utils.SkuUtils;
import com.mogujie.service.shopcenter.util.AuthorizedShopUtil;
import com.mogujie.service.tagcenter.api.read.ItemTagReadService;
import com.mogujie.service.tagcenter.domain.entity.query.ItemTagQueryOption;
import com.mogujie.service.tagcenter.domain.entity.result.BaseResultDO;
import com.mogujie.service.tagcenter.domain.entity.result.ItemTagDO;
import com.mogujie.session.SessionContextHolder;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by xiaoyao on 16/8/24.
 * <p>
 * 备注：2020年万象项目，详情页合并，将appDetail和detailSkip两个老的详情页应用中部分接口进行暴力平行迁移，统一放在before module中。
 */
@Component
@MWPApi(value = "skus.info", version = "v2")
@ActionletName(value = "skus.info", version = "v2")
@CostaWeb(type = {RequestType.JSONP, RequestType.WITHOUT_TOKEN})
@RefererCheck({"mogujie.com", "meilishuo.com", "mogu.com"})
@NeedUserInfo
public class SkusInfoJsonpV2Actionlet implements SyncActionlet<SkusQueryParam, Object>, InitializingBean {
    private static final Logger LOGGER = LoggerFactory.getLogger(SkusInfoJsonpV2Actionlet.class);

    private static final String MAIN_PRICE_DESC = "定金:";

    private static final String SUB_PRICE_DESC = "总价:";

    @Autowired
    private PriceDecorator priceDecorator;

    private Gson gson;

    @Autowired
    private ItemFetcher itemFetcher;

    private InventoryReadService inventoryReadService;

    @Autowired
    private ItemPreSaleService preSaleService;

    private ShopReadService shopReadService;

    @Autowired
    private SkuReorder skuReorder;

    @Resource(name = "confMetabaseClient")
    protected MetabaseClient metabaseClient;

    private ItemTagReadService itemTagService;

    @Autowired
    private SkuGetter skuGetter;

    @Autowired
    private MaiLoUserBasicInfoApi maiLoUserBasicInfoApi;

    @Autowired
    private MaiLoInstallmentApi maiLoInstallmentApi;

    @Autowired
    private ContextUtil contextUtil;


    private RushInfoForDetailService rushInfoForDetailService;

    private static final String PINTUAN_ICON = "/mlcdn/c45406/170830_8f71d3cgg0f2992fdfccjd44i137b_78x42.png";

    @Override
    public void afterPropertiesSet() throws Exception {
        inventoryReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(InventoryReadService.class);
        shopReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ShopReadService.class);
        rushInfoForDetailService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(RushInfoForDetailService.class);
        itemTagService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ItemTagReadService.class);
        gson = new Gson();
    }

    @Override
    public ActionResult<Object> execute(@Nullable SkusQueryParam parameter) {
        String strItemId = parameter.getIid();
        if (StringUtils.isEmpty(strItemId)) {
            return new DefaultActionResult(false, "4004", "未设置商品ID");
        }
        Long itemId = IdConvertor.urlToId(strItemId);
        if (StringUtils.isNotEmpty(parameter.getAppPlat())) {
            DetailContextHolder.set("appPlat", parameter.getAppPlat());
        }

        if (null != parameter.getAddressId()) {
            DetailContextHolder.set("addressId", parameter.getAddressId().toString());
        }

        if (SessionContextHolder.getUserId() != -1L) {
            DetailContextHolder.set(DetailContextHolder.USER_ID, String.valueOf(SessionContextHolder.getUserId()));
        }

        if (null == itemId) {
            return new DefaultActionResult(false, "4004", "无效的商品ID");
        }

        CommonItem commonItem = itemFetcher.getCommonItem(itemId);
        if (null == commonItem) {
            return new DefaultActionResult(false, "4004", "该商品暂时无法购买");
        }
        //医美商品在低版本app上不准购买
        try {
            MWPRequestFrom mwpRequestFrom = MWPContext.getMWPRequestFrom();
            if (contextUtil.isMedicalItem(commonItem)
                    && mwpRequestFrom != null
                    && (mwpRequestFrom.getPlatformType() == MWPRequestFrom.PlatformType.IPHONE
                    || mwpRequestFrom.getPlatformType() == MWPRequestFrom.PlatformType.ANDROID)
                    && Integer.parseInt(mwpRequestFrom.getClientShortVersion()) < 1030) {
                return new DefaultActionResult(false, "4004", "该商品暂时无法购买");
            }
        } catch (Throwable e) {
        }
        int channelId = null == parameter.getChannelId() ? RequestConstants.Channel.UNKNOW : parameter.getChannelId();

        Long activityId = null;
        if (!StringUtils.isEmpty(parameter.getActId())) {
            activityId = IdConvertor.urlToId(parameter.getActId());
        }
        RushInfoDTO rushInfo = null;
        if ("kq".equals(parameter.getChannel()) && null != activityId) {
            rushInfo = getRushInfo(activityId);
        }

        if (!StringUtils.isBlank(parameter.getSource()) && "wx".equals(parameter.getSource())) {
            TopoRoute route = new TopoRoute(App.MLS, Platform.WX, BizTag.NORMAL);
            DetailContextHolder.setRouteTag(route, new ArrayList<ItemTag>());
            parameter.setMarketId((int) RequestConstants.Market.MEILISHUO);
        } else {
            parameter.setMarketId((int) RequestConstants.Market.MOGUJIE);
        }
        StoreType storeType = DetailApiController.getStoreType(commonItem);
        if (null != parameter.getChannelId() && parameter.getChannelId() > 0 && !StringUtils.isBlank(parameter.getActId())) {
            commonItem.setSkus(skuGetter.getChannelSkus(commonItem, parameter.getChannelId(), IdConvertor.urlToId(parameter.getActId()).intValue()));
        } else if (StoreType.INSTORE == storeType) {
            commonItem.setSkus(skuGetter.getInStoreSkus(commonItem));
        } else {
            commonItem.setSkus(skuGetter.getNormalSkus(commonItem));
        }
        // sku属性重排列
        commonItem.setSkus(skuReorder.reorderSkus(commonItem));
        commonItem.setTotalStock(getTotalStock(commonItem));
        if (commonItem.getProcessType() == 1) {
            commonItem.setItemPreSale(preSaleService.queryByItemId(commonItem.getTradeItemId()));
        }
        if (!canBuy(commonItem)) {
            return new DefaultActionResult(false, "4004", "该商品暂时无法购买");
        }

        List<ItemSkuDO> skus = commonItem.getSkus();
        if (CollectionUtils.isEmpty(skus)) {
            LOGGER.error("Item : {} has no skus", commonItem.getTradeItemId());
            return new DefaultActionResult(false, "4004", "无sku");
        }

        if (null != rushInfo) {
            decorateFastbuyInventory(skus, IdConvertor.urlToId(parameter.getActId()));
        }

        DecorateResult decorateResult = null;
        parameter.setChannelId(channelId);
        parameter.setWithPrice(true);
        if (commonItem.getType() == 12) {
            parameter.setMarketId(12);
        }

        SkuInfoForApi skuInfo = new SkuInfoForApi();
        skuInfo.setTitle(EmojiUtil.decode(commonItem.getTitle()));
        skuInfo.setImg(ImageUtil.img(commonItem.getImage()));

        if (null != parameter.getChannelId() && parameter.getChannelId().equals(2012) && !StringUtils.isBlank(parameter.getActId())) {
            commonItem.setItemPreSale(null);
            parameter.setOutType((short) 4);
            decorateResult = priceDecorator.decorateSkuAndPriceNew(commonItem, parameter, rushInfo, false);
        } else {
            ItemTagQueryOption option = new ItemTagQueryOption();
            option.setItemId(commonItem.getTradeItemId().longValue());
            option.setDirectQueryDB(false);
            BaseResultDO<List<ItemTagDO>> ret = itemTagService.queryItemTag(option);
            Boolean isPinTuan = false;
            if (null != ret && ret.isSuccess() && !com.mogujie.metabase.utils.CollectionUtils.isEmpty(ret.getResult())) {
                if (!isPreStart(commonItem) && (StringUtils.isEmpty(parameter.getCaller()) || "pintuan".equals(parameter.getCaller()))) {
                    for (ItemTagDO tag : ret.getResult()) {
                        if (tag.getTagValue().equals("351")) {
                            isPinTuan = true;
                            break;
                        }
                    }
                }
            }
            decorateResult = priceDecorator.decorateSkuAndPriceNew(commonItem, parameter, rushInfo, isPinTuan);
            if (null != decorateResult && (decorateResult.getDisType() == DiscountType.PINTUAN)) {
                PinTuanInfo pinTuanInfo = new PinTuanInfo("7", ImageUtil.img(PINTUAN_ICON));
                skuInfo.setPinTuanInfo(pinTuanInfo);
            }
        }

        this.parseExtra(skuInfo, commonItem.getExtra());

        this.parseSku(skuInfo, commonItem);

        this.updateSkuData(skuInfo, commonItem);

        Integer maxFreePhases = null;
        ShopInfo shopInfo = null;
        try {
            Result<ShopInfo> resultSupport = shopReadService.queryShopByShopId(commonItem.getShopId().longValue());
            shopInfo = resultSupport.getData();
        } catch (Throwable e) {
        }
        if (canInstallment(commonItem, shopInfo)) {
            String tags = null;
            Map<String, String> tagMap = GroupBuyingUtil.getExtraInfo(commonItem.getExtra());
            if (tagMap != null && StringUtils.isNotBlank(tagMap.get("tags"))) {
                tags = tagMap.get("tags");
            }
            String shopTags = shopInfo == null ? null : shopInfo.getTags();
            maxFreePhases = decorateInstallment(skuInfo.getSkus(), tags, shopTags);
        }

        LimitInfo limitInfo = getLimitInfo(commonItem.getExtra());
        if (null != limitInfo && -1L != SessionContextHolder.getUserId()) {
            skuInfo.setLimitTotalStock(limitInfo.getLimitTotalStock());
        }

        if (null != decorateResult.getLimitNum()) {
            skuInfo.setLimitNum(decorateResult.getLimitNum());
            if (null != limitInfo) {
                updateLimitStock(skuInfo, limitInfo.getActivityId());
                skuInfo.setLimitDesc("单次购买" + decorateResult.getLimitNum() + "件以内享优惠，超过则恢复原价");
            }
        }

        //直播商品进图强商品标题逻辑
        fillItemIntoWallTitle(skuInfo, commonItem, parameter);

        skuInfo.setItemId(parameter.getIid());
        skuInfo.setActivityId(parameter.getActId());
        SkuInfoDataV2 skuInfoData = new SkuInfoDataV2();
        skuInfoData.setData(skuInfo);
        skuInfoData.setIsPreSale(isPreSale(commonItem));
        skuInfoData.setItemTags(null);
        skuInfoData.setDisType(decorateResult.getDisType());
        skuInfoData.getData().setOutType(null != parameter.getOutType() ? parameter.getOutType().intValue() : null);
        skuInfoData.setMaxFreePhases(maxFreePhases == null ? 0 : maxFreePhases);

        if (DetailApiController.decorateFastbuy(skuInfoData, rushInfo, commonItem)
                || DetailApiController.decoratePresale(skuInfoData, commonItem)) {
        }
        if (channelId == 2023) {
            skuInfoData.setActivityType(6);
        }

        return new DefaultActionResult(true, "1001", "", skuInfoData);
    }

    private void decorateFastbuyInventory(List<ItemSkuDO> skus, Long fastbuyId) {
        List<Long> skuIdList = new ArrayList<>(skus.size());
        for (ItemSkuDO sku : skus) {
            skuIdList.add(sku.getSkuId());
        }
        BatchActivityInventoryQueryParam param = new BatchActivityInventoryQueryParam();
        param.setSkuIds(skuIdList);
        param.setChannelId(1);
        param.setActivityId(fastbuyId.intValue());
        MapResult<Long, ActivityInventory> inventoryMapResult = inventoryReadService.batchQueryActivityInventory(param);
        if (null != inventoryMapResult && inventoryMapResult.isSuccess()) {
            Map<Long, ActivityInventory> inventoryMap = inventoryMapResult.getData();
            if (null != inventoryMap) {
                for (ItemSkuDO sku : skus) {
                    ActivityInventory inventory = inventoryMap.get(sku.getSkuId());
                    sku.setQuantity(null == inventory ? 0 : inventory.getStock());
                }
            }
        }
    }

    private void updateLimitStock(SkuInfoForApi skuInfo, Long activityId) {
        List<Long> skuIdList = new ArrayList<>();
        for (SkuData skuData : skuInfo.getSkus()) {
            skuIdList.add(IdConvertor.urlToId(skuData.getStockId()));
        }
        BatchActivityInventoryQueryParam param = new BatchActivityInventoryQueryParam();
        param.setSkuIds(skuIdList);
        param.setChannelId(2014);
        param.setActivityId(activityId.intValue());
        MapResult<Long, ActivityInventory> inventoryMapResult = inventoryReadService.batchQueryActivityInventory(param);
        if (null != inventoryMapResult && inventoryMapResult.isSuccess()) {
            Map<Long, ActivityInventory> inventoryMap = inventoryMapResult.getData();
            if (null != inventoryMap) {
                for (SkuData sku : skuInfo.getSkus()) {
                    ActivityInventory inventory = inventoryMap.get(IdConvertor.urlToId(sku.getStockId()));
                    sku.setStock(null == inventory ? 0 : inventory.getStock());
                }
            }
        }
    }

    private RushInfoDTO getRushInfo(Long fastbuyId) {
        if (null != fastbuyId) {
            try {
                RushInfoResultDTO rushRet = rushInfoForDetailService.getRushInfo(fastbuyId);
                if (null != rushRet && rushRet.isSucc()) {
                    RushInfoDTO rushInfo = rushRet.getRushInfoDTO();
                    int nowTime = (int) (System.currentTimeMillis() / 1000);
                    if (rushInfo.getStartTime() < nowTime && rushInfo.getEndTime() > nowTime) {
                        return rushInfo;
                    }
                }
            } catch (Throwable e) {
                LOGGER.error("call rush info failed", e);
            }
        }
        return null;
    }

    private LimitInfo getLimitInfo(String extra) {
        if (StringUtils.isEmpty(extra)) {
            return null;
        }
        try {
            Gson gson = new Gson();
            Map<String, String> extraInfo = gson.fromJson(extra, HashMap.class);
            if (null == extraInfo || extraInfo.isEmpty()) {
                return null;
            }
            String fl = extraInfo.get("fl");
            if (com.mogujie.metabase.utils.StringUtils.isEmpty(fl)) {
                return null;
            }
            String[] flPairs = fl.split("\\|");
            LimitInfo limitInfo = new LimitInfo();
            for (String flPair : flPairs) {
                String[] pair = flPair.split(":");
                if (pair.length != 2) {
                    continue;
                }
                if ("ai".equals(pair[0])) {
                    limitInfo.setActivityId(Long.parseLong(pair[1]));
                } else if ("xg".equals(pair[0])) {
                    limitInfo.setLimitTotalStock(Integer.parseInt(pair[1]));
                } else if ("st".equals(pair[0])) {
                    if ((System.currentTimeMillis() / 1000) < Integer.parseInt(pair[1])) {
                        return null;
                    }
                } else if ("et".equals(pair[0])) {
                    if ((System.currentTimeMillis() / 1000) > Integer.parseInt(pair[1])) {
                        return null;
                    }
                }
            }

            return limitInfo;
        } catch (Throwable e) {
            LOGGER.error("parse extra.tags failed : {}. {}", extra, e);
        }
        return null;
    }

    private int getTotalStock(CommonItem commonItem) {
        int totalStock = 0;
        for (ItemSkuDO sku : commonItem.getSkus()) {
            totalStock += sku.getQuantity();
        }
        return totalStock;
    }

    private boolean isPreSale(CommonItem commonItem) {
        if (null == commonItem.getItemPreSale()) {
            return false;
        }
        int currentTime = (int) (System.currentTimeMillis() / 1000);
        ItemPreSaleDO preSaleInfo = commonItem.getItemPreSale();
        if (preSaleInfo.getStart() < currentTime && currentTime < preSaleInfo.getEnd()) {
            return true;
        }
        return false;
    }

    protected boolean canBuy(CommonItem commonItem) {

        if (commonItem.getItemPreSale() != null) {
            int currentTime = (int) (System.currentTimeMillis() / 1000);
            if (currentTime < commonItem.getItemPreSale().getStart()) {
                return false;
            }
        }

        if (commonItem.getIsDeleted() == 1) {
            return false;
        }

        if (commonItem.getIsShelf() == 1) {
            return false;
        }

        if (commonItem.getStatus() == 1 || commonItem.getStatus() == 3 || commonItem.getStatus() < 0) {
            return false;
        }

        if (commonItem.getTotalStock() <= 0) {
            return false;
        }

        return true;
    }

    private void parseExtra(SkuInfo skuInfo, String extra) {
        if (org.apache.commons.lang.StringUtils.isEmpty(extra)) {
            skuInfo.setAbroad(false);
            skuInfo.setCounterPrice(null);
            return;
        }
        Map<String, Object> extraData = gson.fromJson(extra, Map.class);
        Object counterPrice = extraData.get("counterPrice");
        skuInfo.setAbroad(null != counterPrice);
        skuInfo.setCounterPrice(null != counterPrice ? String.valueOf(counterPrice) : null);
    }

    /**
     * 预售商品，需要重新刷一遍sku信息
     *
     * @param commonItem
     * @param skuInfo
     */
    private void updateSkuData(SkuInfo skuInfo, CommonItem commonItem) {
        ItemPreSaleDO itemPreSale = commonItem.getItemPreSale();
        int time = (int) (System.currentTimeMillis() / 1000);
        // 若预售时间已过，走普通商品逻辑
        if (itemPreSale == null || time > itemPreSale.getEnd()) {
            return;
        }

        // 刷价格
        int nowPrice = itemPreSale.getPrice();
        skuInfo.setPriceRange(formatPrice(nowPrice));
        skuInfo.setDefaultPrice(formatPrice(nowPrice));

        for (SkuData sku : skuInfo.getSkus()) {
            sku.setNowprice(nowPrice);
            sku.setMainPriceStr(MAIN_PRICE_DESC + formatPrice(itemPreSale.getDeposit()));
            sku.setSubPriceStr(SUB_PRICE_DESC + formatPrice(itemPreSale.getPrice()));
        }

        skuInfo.setMainPriceStr(MAIN_PRICE_DESC + formatPrice(itemPreSale.getDeposit()));
        skuInfo.setSubPriceStr(SUB_PRICE_DESC + formatPrice(itemPreSale.getPrice()));
        skuInfo.setMainDesc(MAIN_PRICE_DESC);
        skuInfo.setSubDesc(SUB_PRICE_DESC);
    }

    private boolean isPreStart(CommonItem item) {
        Integer now = (int) (System.currentTimeMillis() / 1000);
        if (null != item.getStart() && now < item.getStart()) {
            return true;
        }
        return false;
    }

    /**
     * 是否为可分期商品
     */
    private boolean canInstallment(CommonItem commonItem, ShopInfo shopInfo) {
        //实时售价大于70元（任一SKU大于70元即可）；
        boolean priceMoreThan70 = false;
        for (ItemSkuDO sku : commonItem.getSkus()) {
            if (sku.getNowPrice() > 7000) {
                priceMoreThan70 = true;
                break;
            }
        }
        if (!priceMoreThan70) {
            return false;
        }

        String virtualCateRoot = "#" + metabaseClient.get("virtual_category_root") + "#";
        //充值中心类目，需要不能分期
        if (commonItem.getCids().contains(virtualCateRoot)) {
            return false;
        }

        //认证店铺
        if (shopInfo == null || !AuthorizedShopUtil.isAuthorized(shopInfo)) {
            return false;
        }

        //非预售商品
        if (commonItem.getItemPreSale() != null) {
            int now = (int) (System.currentTimeMillis() / 1000);
            if (now > commonItem.getItemPreSale().getStart() && now < commonItem.getItemPreSale().getEnd()) {
                return false;
            }
        }

        //未登录则不显示分期数据
        Long userId = SessionContextHolder.getUserId();
        if (userId == null || userId == -1) {
            return false;
        }

        MaiLoReqDTO maiLoReqDTO = new MaiLoReqDTO();
        maiLoReqDTO.setUserId(userId);
        maiLoReqDTO.setIsAdmin(false);
        try {
            Response<UserBasicInfoDTO> response = maiLoUserBasicInfoApi.getUserStatusDto(maiLoReqDTO);
            if (response == null || response.getData() == null) {
                return false;
            } else if (response.getData().getStatus() == 1) {
                return true;
            }
        } catch (Throwable e) {
            LOGGER.error("calling user status failed.", e);
        }
        return false;
    }

    /**
     * sku刷入分期信息
     */
    private Integer decorateInstallment(List<SkuData> skuList, String tags, String shopTags) {
        List<Long> priceList = new ArrayList<>();
        List<SkuData> toBeDecorateList = new ArrayList<>();
        for (int i = 0; i < skuList.size(); i++) {
            if (skuList.get(i).getNowprice() > 7000) {
                priceList.add(skuList.get(i).getNowprice().longValue());
                toBeDecorateList.add(skuList.get(i));
            }
        }
        Integer maxFreePhases = null;
        try {
            List<String> itemNumTags = null;
            if (StringUtils.isNotBlank(tags)) {
                String[] tgs = tags.split(",");
                if (tgs != null && tgs.length > 0) {
                    itemNumTags = Arrays.asList(tgs);
                }
            }
            List<String> shopTagsList = null;
            if (StringUtils.isNotBlank(shopTags)) {
                shopTagsList = Arrays.asList(shopTags.split(","));
            }
            ComputeInstallmentRequestDTO computeInstallmentRequestDTO = new ComputeInstallmentRequestDTO();
            computeInstallmentRequestDTO.setAmounts(priceList);
            computeInstallmentRequestDTO.setUserId(SessionContextHolder.getUserId());
            computeInstallmentRequestDTO.setTags(itemNumTags);
            computeInstallmentRequestDTO.setMerchantTags(shopTagsList);
            Response<List<List<InstallmentInfoDTO>>> ret = maiLoInstallmentApi.computeInstallmentPlan(computeInstallmentRequestDTO);
            if (ret != null && ret.getData() != null) {
                List<List<InstallmentInfoDTO>> resultList = ret.getData();
                for (int i = 0; i < resultList.size(); i++) {
                    SkuData skuData = toBeDecorateList.get(i);
                    List<InstallmentData> installment = new ArrayList<>();
                    for (InstallmentInfoDTO installmentInfoDTO : resultList.get(i)) {
                        InstallmentData installmentItem = new InstallmentData();
                        installmentItem.setFee(installmentInfoDTO.getFee().intValue());
                        installmentItem.setNum(installmentInfoDTO.getTotalCount());
                        installmentItem.setPerPrice(installmentInfoDTO.getSubAmount().intValue());
                        installment.add(installmentItem);
                        maxFreePhases = installmentInfoDTO.getMaxFreeFeeTerms();
                    }
                    skuData.setInstallment(installment);
                }
            }
        } catch (Throwable e) {
            LOGGER.error("get installment price error.", e);
        }
        return maxFreePhases;
    }

    private String formatPrice(double price) {
        return "¥" + NumUtil.formatNum(price / 100D);
    }

    public void parseSku(SkuInfoForApi skuInfo, CommonItem commonItem) {
        List<ItemSkuDO> skus = commonItem.getSkus();
        List<SkuData> skuDatas = new ArrayList<>();
        if (CollectionUtils.isEmpty(skus)) {
            return;
        }
        String styleKey = "";
        String sizeKey = "";
        boolean isSizeDefault = false;
        boolean isStyleDefault = false;
        List<String> tmpStyle = new ArrayList<>();
        List<String> tmpSize = new ArrayList<>();
        List<StylePropData> styles = new ArrayList<>();
        List<SizePropData> sizes = new ArrayList<>();
        int styleId = 1;
        int sizeId = 100;
        Map<String, Integer> styleMap = new HashMap<>();
        Map<String, Integer> sizeMap = new HashMap<>();

        int index = 0;
        int lowestPrice = 0;
        int highestPrice = 0;
        int lowestNowPrice = 0;
        int highestNowPrice = 0;
        Map<String, Map<String, String>> attrsMap = new HashMap<>();
        int totalStock = 0;
        for (ItemSkuDO sku : skus) {
            SkuData skuData = new SkuData();
            skuData.setCurrency("¥");
            skuData.setImg(StringUtils.isEmpty(sku.getImage()) ? ImageUtil.img(commonItem.getImage()) : ImageUtil.img(sku.getImage()));
            skuData.setNowprice(sku.getNowPrice());
            skuData.setPrice(sku.getPrice().intValue());
            skuData.setStock(sku.getQuantity());
            skuData.setXdSkuId(IdConvertor.idToUrl(sku.getXdSkuId()));
            skuData.setStockId(IdConvertor.idToUrl(sku.getSkuId()));
            int delayedDeliveryTime = SkuUtils.getDelayedDeliveryTime(sku);
            long now = System.currentTimeMillis() / 1000;
            skuData.setDelayTime(now > delayedDeliveryTime ? null : delayedDeliveryTime);
            attrsMap.put(skuData.getStockId(), parseProps(skuData, sku.getAttributions()));
            DetailApiController.parseSizeType(skuData, sku.getAttributions());
            totalStock += skuData.getStock();
            skuDatas.add(skuData);
        }
        skuInfo.setTotalStock(totalStock);

        for (SkuData skuData : skuDatas) {

            Map<String, String> attrMap = attrsMap.get(skuData.getStockId());
            styleKey = attrMap.get("style");
            sizeKey = attrMap.get("size");

            String realStyle = attrMap.get("style") == null ? "" : (attrMap.get("style").equals(styleKey)) ? skuData.getStyle() : skuData.getSize();
            String realSize = attrMap.get("size") == null ? "" : (attrMap.get("size").equals(sizeKey)) ? skuData.getSize() : skuData.getStyle();

            skuData.setStyle(StringUtils.isEmpty(realStyle) ? null : realStyle);
            skuData.setSize(StringUtils.isEmpty(realSize) ? null : realSize);

            if (!StringUtils.isEmpty(skuData.getStyle()) && !tmpStyle.contains(skuData.getStyle())) {
                tmpStyle.add(skuData.getStyle());
                if (StringUtils.isEmpty(realStyle) && false == isStyleDefault) {
                    isStyleDefault = true;
                }
                StylePropData styleProp = new StylePropData();
                styleProp.setDefault(false);//StringUtils.isEmpty(realStyle));
                styleProp.setType("style");
                styleProp.setName(skuData.getStyle());
                styleProp.setIndex(styleId);
                styleProp.setStyleId(styleId);
                styles.add(styleProp);
                styleMap.put(skuData.getStyle(), styleId);
                styleId++;
            }

            if (!StringUtils.isEmpty(skuData.getSize()) && !tmpSize.contains(skuData.getSize())) {
                tmpSize.add(skuData.getSize());
                if (StringUtils.isEmpty(realSize) && false == isSizeDefault) {
                    isSizeDefault = true;
                }

                SizePropData sizeProp = new SizePropData();
                sizeProp.setDefault(false);//StringUtils.isEmpty(realSize));
                sizeProp.setType("size");
                sizeProp.setName(skuData.getSize());
                sizeProp.setIndex(sizeId);
                sizeProp.setSizeId(sizeId);
                sizes.add(sizeProp);
                sizeMap.put(skuData.getSize(), sizeId);
                sizeId++;
            }

            // 给sku添加index
            if (!StringUtils.isEmpty(skuData.getStyle()) && null != styleMap.get(skuData.getStyle())) {
                skuData.setStyleId(styleMap.get(skuData.getStyle()));
            }

            if (!StringUtils.isEmpty(skuData.getSize()) && null != sizeMap.get(skuData.getSize())) {
                skuData.setSizeId(sizeMap.get(skuData.getSize()));
            }

            if (index == 0) {
                highestPrice = lowestPrice = skuData.getPrice();
                highestNowPrice = lowestNowPrice = skuData.getNowprice();
            }

            if (skuData.getPrice() > highestPrice) {
                highestPrice = skuData.getPrice();
                highestNowPrice = skuData.getNowprice();
            }
            if (skuData.getPrice() < lowestPrice) {
                lowestPrice = skuData.getPrice();
                lowestNowPrice = skuData.getNowprice();
            }
            index++;
        }

        List<PropInfo> props = new ArrayList<>();
        if (!StringUtils.isEmpty(styleKey)) {
            PropInfo<StylePropData> stylePropInfo = new PropInfo();
            stylePropInfo.setLabel(styleKey);
            stylePropInfo.setList(styles);
            //默认sku能加库存的需求
            stylePropInfo.setDefault(false); //isStyleDefault);
            props.add(stylePropInfo);
            if (StringUtils.isEmpty(sizeKey)) {
                skuInfo.setProps(props);
            }
        }
        if (!StringUtils.isEmpty(sizeKey)) {
            PropInfo<SizePropData> sizePropInfo = new PropInfo();
            sizePropInfo.setLabel(sizeKey);
            sizePropInfo.setList(sizes);
            sizePropInfo.setDefault(false); //isSizeDefault);
            props.add(sizePropInfo);
            skuInfo.setProps(props);
        }

        skuInfo.setStyleKey(StringUtils.isEmpty(styleKey) ? null : styleKey);
        skuInfo.setSizeKey(StringUtils.isEmpty(sizeKey) ? null : sizeKey);
        Collections.sort(skuDatas, new Comparator<SkuData>() {
            @Override
            public int compare(SkuData o1, SkuData o2) {
                if (o1.getStyleId() == o2.getStyleId()) {
                    return o1.getSizeId() == o2.getSizeId() ? 0 : (o1.getSizeId() > o2.getSizeId() ? 1 : -1);
                } else {
                    return o1.getStyleId() > o2.getStyleId() ? 1 : -1;
                }
            }
        });

        skuInfo.setSkus(skuDatas);
        //没有价格区间
        if (highestNowPrice == lowestNowPrice) {
            skuInfo.setPriceRange(formatPrice(lowestNowPrice));
            skuInfo.setOldPrice(formatPrice(lowestPrice));
        } else {
            skuInfo.setPriceRange(formatPrice(lowestNowPrice) + '~' + formatPrice(highestNowPrice));
            skuInfo.setOldPrice(formatPrice(lowestPrice) + '~' + formatPrice(highestPrice));
        }
        skuInfo.setDefaultPrice(skuInfo.getPriceRange());
    }

    protected Map<String, String> parseProps(SkuData skuData, List<SkuAttributionDO> attrs) {
        int index = 0;
        Map<String, String> mapping = new HashMap<>();
        if (CollectionUtils.isEmpty(attrs)) {
            return mapping;
        }

        Collections.sort(attrs, new Comparator<SkuAttributionDO>() {
            @Override
            public int compare(SkuAttributionDO o1, SkuAttributionDO o2) {
                int o1Pos = o1.getShowPosition() == null ? 0 : o1.getShowPosition();
                int o2Pos = o2.getShowPosition() == null ? 0 : o2.getShowPosition();
                return o1Pos < o2Pos ? -1 : (o1Pos > o2Pos ? 1 : 0);
            }
        });

        for (SkuAttributionDO attr : attrs) {
            String attrName = attr.getName();
            if (attr.getShowPosition() == null || attr.getShowPosition() == 0) {
                skuData.setStyle(attr.getValue());
                mapping.put("style", attrName);
            } else {
                skuData.setSize(attr.getValue());
                mapping.put("size", attrName);
            }
            index++;
        }
        return mapping;
    }

    private void fillItemIntoWallTitle(SkuInfoForApi skuInfo, CommonItem commonItem, SkusQueryParam param) {
        if (StringUtils.isBlank(param.getSourceParams()) || commonItem == null) {
            return;
        }

        boolean includeTag = false;
        if (commonItem.getItemTagDOS() != null) {
            for (com.mogujie.service.item.domain.basic.ItemTagDO itemTagDO : commonItem.getItemTagDOS()) {
                if ("2002".equals(itemTagDO.getTagValue()) && "tags".equals(itemTagDO.getTagKey())) {
                    includeTag = true;
                }
            }
        }

        if (!includeTag) {
            return;
        }

        JSONObject sourceObject = JSON.parseObject(param.getSourceParams());
        if (sourceObject == null || !Objects.equals(sourceObject.getInteger("type"), 1)) {
            return;
        }

        ItemExtraDO itemExtraDO = commonItem.getItemExtraDO();
        if (itemExtraDO == null || StringUtils.isBlank(itemExtraDO.getFeatures().get("anchorItemActivityInfo"))) {
            return;
        }

        String anchorItemActivityInfo = itemExtraDO.getFeatures().get("anchorItemActivityInfo");
        JSONObject anchorItemInfoJson = JSON.parseObject(anchorItemActivityInfo);
        String activityTitle = anchorItemInfoJson.getString("activityTitle");
        if (!StringUtils.isBlank(activityTitle)) {
            skuInfo.setTitle(activityTitle);
        }
    }
}