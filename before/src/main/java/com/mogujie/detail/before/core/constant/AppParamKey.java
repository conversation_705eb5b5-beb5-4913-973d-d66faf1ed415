package com.mogujie.detail.before.core.constant;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 15/11/25.
 */
public interface AppParamKey {

    String APP = "app"; //app名称
    String ATYPE = "atype"; //app类型
    String MODULE_VERSION = "moduleversion"; //模块版本
    String CUR_VERSION = "version"; //当前版本
    String LATEST_VERSION = "latest_version"; //最新版本
    String BUILD = "build"; //最新版本
    String VERSION_BUILD = "version_build"; //最新版本


    String DEVICE = "device";//设备机型
    String OS_VERSION = "os_version";//设备机型
    String DID = "did"; //设备ID
    String NETWORK = "network"; //网络类型

    String CHANNEL = "channel"; //来源
    String CPSSOURCE = "cpsSource";//cps
    String SWIDTH = "screenwidth"; //屏幕宽度

    //登录用户
    String SIGN = "sign";
    String COOKIE__MOGUJIE = "__mogujie";

    String UID = "_uid";

    String AID = "aid"; //appID

    //cms前缀从dispatch迁移到base来做。

    String ACCESS_TOKEN = "_at";
    String TIMESTAMP = "_t";

    String DEBUG = "_debug";
    String AEGIS = "_aegis";
    String IID = "iid";

}
