package com.mogujie.detail.before.controller;

import com.mogujie.actionlet.ActionResult;
import com.mogujie.actionlet.annotation.ActionletName;
import com.mogujie.actionlet.core.ActionletExecutor;
import com.mogujie.actionlet.core.impl.DefaultActionResult;
import com.mogujie.actionlet.mwp.annotation.MWPApi;
import com.mogujie.actionlet.session.annotation.NeedUserInfo;
import com.mogujie.actionlet.sync.SyncActionlet;
import com.mogujie.costa.core.domain.RequestType;
import com.mogujie.costa.server.annotations.CostaWeb;
import com.mogujie.costa.server.annotations.RefererCheck;
import com.mogujie.detail.before.common.adt.BaseDomainData;
import com.mogujie.detail.before.common.adt.DetailContextHolder;
import com.mogujie.detail.before.common.adt.DetailParam;
import com.mogujie.detail.before.common.adt.TopoRoute;
import com.mogujie.detail.before.common.constant.ItemTag;
import com.mogujie.detail.before.common.exception.DetailException;
import com.mogujie.detail.before.core.adt.AppDetailResponse;
import com.mogujie.detail.before.core.adt.DetailRequest;
import com.mogujie.detail.before.core.constant.DetailStatusCode;
import com.mogujie.sentry.SentryClient;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Arrays;

/**
 * Created by xiaoyao on 16/11/23.
 *
 * 备注：2020年万象项目，详情页合并，将appDetail和detailSkip两个老的详情页应用中部分接口进行暴力平行迁移，统一放在before module中。
 */
@Component
@MWPApi(value = "detail.info", version = "v1")
@ActionletName(value = "detail.info", version = "v1")
@CostaWeb(type = {RequestType.JSONP, RequestType.WITHOUT_TOKEN})
@RefererCheck({"mogujie.com", "meilishuo.com", "mogu.com"})
@NeedUserInfo
public class DetailJsonpHandler implements SyncActionlet<DetailParam, Object> {

    private static final Logger LOGGER = LoggerFactory.getLogger(DetailJsonpHandler.class);

    @Autowired
    private ActionletExecutor detailExecutor;

    private SentryClient sentryClient;

    @PostConstruct
    public void init() {
        sentryClient = SentryClient.factory("detail");
        sentryClient.openAutoCollector();
    }

    @Override
    public ActionResult<Object> execute(@Nullable DetailParam param) {
        String topoKey = param.getTopoKey();
        if (StringUtils.isEmpty(topoKey)) {
            return new DefaultActionResult(false, "4004", "topoKey is empty");
        }
        String[] topoKeyArr = topoKey.split("_");
        if (topoKeyArr.length < 3) {
            return new DefaultActionResult(false, "4004", "invalid topoKey");
        }
        String app = topoKeyArr[0];
        String v = topoKeyArr[1];
        String biz = topoKeyArr.length > 3 ? topoKeyArr[2] + "_" + topoKeyArr[3] : topoKeyArr[2];
        DetailRequest req = new DetailRequest();
        req.setActionName(app + "." + biz);
        req.setActionVersion(v);
        TopoRoute topoRoute = null;
        try {
            if (StringUtils.isEmpty(param.getItemId())) {
                return new DefaultActionResult(false, "4004", "iid not set");
            }
            req.setParameter(param);
            AppDetailResponse resp = new AppDetailResponse();
            topoRoute = DetailDispatcherMWP.getTopoRoute(app, StringUtils.removeEnd(biz, "_dyn"));
            DetailContextHolder.setRouteTag(topoRoute, Arrays.asList(ItemTag.DEFAULT));
            if (null == topoRoute) {
                return new DefaultActionResult(false, "4004", "app not found");
            }
            if (StringUtils.isNotBlank(param.getAppPlat())) {
                DetailContextHolder.set(DetailContextHolder.APP_PLAT, param.getAppPlat());
            }
            detailExecutor.execute(req, resp);
            ActionResult<BaseDomainData> ret = resp.getResult();
            if (null == ret || !ret.isSuccess()) {
                throw new DetailException("internal error");
            }
            if (null != ret && ret.isSuccess()) {
                if (!DetailStatusCode.SUCCESS.equals(ret.getReturnCode())) {
                    return new DefaultActionResult(false, "4004", ret.getReturnMessage());
                } else if (null != ret.getData()) {
                    return new DefaultActionResult(true, "1001", "", ret.getData());
                } else {
                    return new DefaultActionResult(true, "1001", ret.getReturnMessage(), ret.getData());
                }
            }

        } catch (Exception e) {
            LOGGER.error("DetailJsonpHandler execute exception!param:" + param, e);
        }
        return new DefaultActionResult(false, "4004", "detail internal error");
    }
}
