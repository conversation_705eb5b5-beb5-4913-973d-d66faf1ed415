package com.mogujie.detail.before.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Charsets;
import com.mogujie.actionlet.ActionResult;
import com.mogujie.actionlet.annotation.ActionletName;
import com.mogujie.actionlet.core.impl.DefaultActionResult;
import com.mogujie.actionlet.mwp.annotation.MWPApi;
import com.mogujie.actionlet.session.annotation.NeedUserInfo;
import com.mogujie.actionlet.sync.SyncActionlet;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.costa.server.annotations.RefererCheck;
import com.mogujie.mht.slardar.SmallFile;
import com.mogujie.service.item.api.basic.ItemEditHistoryService;
import com.mogujie.service.item.domain.CompleteItem;
import com.mogujie.service.item.domain.basic.ItemEditHistoryDO;
import com.mogujie.service.muser.Result;
import com.mogujie.service.muser.api.UserService;
import com.mogujie.session.SessionContextHolder;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * Created by xiaoyao on 17/3/29.
 */
@Component
@MWPApi(value = "item.snap.detail", version = "2")
@ActionletName(value = "item.snap.detail", version = "2")
@RefererCheck({"mogujie.com", "mogu.com"})
@NeedUserInfo
public class ItemSnapDetailActionlet implements SyncActionlet<String, Object> {


    private static final Logger LOGGER = LoggerFactory.getLogger(ItemSnapDetailActionlet.class);

    private UserService userService;

    private ItemEditHistoryService itemEditHistoryService;

    private SmallFile smallFile;

    @PostConstruct
    public void init() {
        try {
            itemEditHistoryService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ItemEditHistoryService.class);
            userService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(UserService.class);
            smallFile = new SmallFile("27670d1305e16e00483fc6a3f5ae7401", "eac060028a4327fc3f988c7b8a152fed");
        } catch (Exception e) {
            LOGGER.error("init service failed : ", e);
        }
    }

    @Override
    public ActionResult<Object> execute(@Nullable String snapId) {
        if (!isAdmin()) {
            return new DefaultActionResult(false, "4004", "非小仙小侠不能查看", null);
        }
        try {
            Long snapshotId = IdConvertor.urlToId(snapId);

            ItemEditHistoryDO itemEditHistoryDO = itemEditHistoryService.queryById(snapshotId);
            if (null == itemEditHistoryDO || StringUtils.isBlank(itemEditHistoryDO.getFpath())) {
                return new DefaultActionResult(false, "4004", "无对应快照信息", null);
            }
            byte[] content = smallFile.get(itemEditHistoryDO.getFpath());
            if (content.length <= 0) {
                return new DefaultActionResult(false, "4004", "快照信息文件读取错误", null);
            }
            CompleteItem completeItem = JSONObject.parseObject(new String(content, Charsets.UTF_8), CompleteItem.class);
            return new DefaultActionResult(true, "1001", "", completeItem);
        } catch (Exception e) {
            LOGGER.error("get snap error : ", e);
        }
        return new DefaultActionResult(false, "4004", "内部错误", null);
    }

    /**
     * 是否是小侠账号
     *
     * @return
     */
    private Boolean isAdmin() {
        long loginUserId = SessionContextHolder.getUserId();
        if (0L == loginUserId) {
            return false;
        }
        try {
            Result<Boolean> ret = userService.isAdmin(loginUserId);
            return ret.getValue();
        } catch (Throwable e) {
            LOGGER.error("check is admin failed : {}", e);
        }
        return false;
    }
}
