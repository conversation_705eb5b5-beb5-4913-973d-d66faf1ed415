package com.mogujie.detail.before.common.constant;

/**
 * Created by anshi on 16/10/10.
 */
public interface SwitchKey {

    // 店铺包邮
    String WAITRESS_SHIP_SERVICE = "swt_waitress_shipservice";

    // 蘑豆gift
    String MODOUBUSINESS_MODOU_RULE = "swt_modoubusiness_modouRule";

    // 喜欢数
    String FEEDSTREAM_LIKES_COUNT = "swt_feedstream_likesCounter";

    // 红人信息
    String SOCIALCHANNEL_USER_BY_ITEM = "swt_socialchannel_userByItem";

    // 是否喜欢
    String FEEDSTREAM_IS_USER_LIKE = "swt_feedstream_isUserLike";

    // 店铺标(品质商家等)
    String SHOPCENTER_GET_IMG = "swt_shopcenter_getImg";

    // 蘑菇街店铺DSR
    String OCEAN_GET_KPI_BY_SHOP_ID = "swt_ocean_getKpiByShopId";

    // 判断店铺是否收藏
    String SHOPFAVORITE_CHECK_RELATION = "swt_shopfavorite_checkRelation";

    // 根据类目id获取类目
    String CATEGORYV2_CACHED_CATEGORY = "swt_categoryV2_cachedCategory";

    // 查店铺是否有某标
    String SHOPCENTER_CHECK_SHOP_TAG = "swt_shopcenter_checkShopTag";

    // 查询快抢信息
    String FERRARI_GET_RUSH_INFO = "swt_ferrari_getRushInfo";

    // 检查是否为管理员
    String MUSER_IS_ADMIN = "swt_muser_isAdmin";

    // 查询美丽说twitter id
    String ITEMCENTER_TWITTER_IDS = "swt_itemcenter_twitterIds";

    // 查询商品活动报名信息(蘑豆报名库存)
    String ACTCENTER_GET_BY_ID = "swt_comb_act_center_getById";

    // 查询部分优惠券
    String HUMMER_PART_COUPON_LIST = "swt_hummer_partCouponList";

    // 店铺当前上架商品数
    String ITEMCENTER_COUNT_ONLINE_ITEMS = "swt_itemcenter_countOnlineItems";

    // 店铺粉丝数
    String SHOPFAVORITE_SHOP_FAN_COUNT = "swt_shopfavorite_shopFanCount";

    // 查询店铺类目
    String SHOPCENTER_SHOP_CATEGORY= "swt_shopcenter_shopCategory";

    // 购物车数量
    String CARTSERVICE_SKU_COUNT = "swt_cartMicroService_skuCount";

    // 美丽说店铺DSR
    String MLSOCEAN_GET_KPI_BY_SHOP_ID = "swt_mlsocean_getKpiByShopId";

    // 蘑豆夺宝信息
    String MBSTORE_BARGAIN_DETAIL = "swt_mbstore_bargainDetail";

    // pallas客户端
    String PALLAS_CLIENT = "swt_pallas_client";

    // 评价用户信息
    String USERSERVICE_GET_USER_BY_IDS = "swt_muser_getUserByIds";

    // 用户是否可用分期
    String PAYMAILO_GET_USER_STATUS =  "swt_paymailo_getUserStatus";

    // 用户是否喜欢该商品
    String RELATIONSERVICE_READ_QUERY_RELATION = "swt_relationservice_queryRelation";

    // 商品喜欢数
    String RELATIONSERVICE_READ_LIKES_COUNT = "swt_relationservice_getCounterMuliter";

    // 是否使用手机专享价
    String USE_MOBILE_PRICE = "swt_use_mobile_price";

    // 315不支持7天无理由退货开关
    String NO_REASON_REFOUND = "swt_noReasonRefound";

    // 315标展示在最前面
    String NO_REASON_REFOUND_FIRST = "swt_noReasonRefoundFirst";

    // 315弹窗开关
    String SHOW_315_ALERT = "swt_show315Alert";

    // 是否新人
    String IS_NEW_COMER = "swt_is_new_comer";

    // 使用DTS获取DSR数据
    String USE_DTS_DSR = "swt_useDtsDsr";

    // 根据tangram来为sku属性值排序
    String ORDERED_SKU_ATTRS = "swt_ordered_sku_attrs";

    // ratio：pallas切tag-center-api的比例
    String RTO_SWITCH_TO_TAG_CENTER = "rto_tagcenter_pallas";

    String USE_OLD_TAG_API = "use_old_tag_api";

    //客服是否可查看买家的订单快照
    String CUSTOMER_SERVICE_SNAPSHOT = "swt_customer_service_snapshot";
}
