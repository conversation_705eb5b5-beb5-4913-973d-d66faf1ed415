package com.mogujie.detail.before.core.util;


import java.math.BigDecimal;

/**
 * Created by xiaoyao on 15/11/26.
 */
public class NumUtil {

    public static String formatNum(double num, int precision) {
        BigDecimal b = new BigDecimal(num);
        return b.setScale(precision, BigDecimal.ROUND_HALF_UP).toString();
    }

    public static String formatNum(double num) {
        return String.format("%.2f", num);
    }
}
