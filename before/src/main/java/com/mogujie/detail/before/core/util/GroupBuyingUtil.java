package com.mogujie.detail.before.core.util;

import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by anshi on 16/12/20.
 */
public class GroupBuyingUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(GroupBuyingUtil.class);

    private static final String TUAN_FEATURE = "tg";

    private static final String PINPAI_FEATURE = "ptg";

    private static final String UZHI_FEATURE = "utg";

    /**
     * 解析extra字段,获取(有效的)团购类型
     *
     * @param extra
     * @return
     */
    public static boolean isGroupbuyItem(String extra) {
        Map<String, String> extraInfo = getExtraInfo(extra);
        if (null == extraInfo) {
            return false;
        }

        // 普通团购
        // TODO: 17/5/9 将于团购业务下线后删去
        String tgStr = extraInfo.get(TUAN_FEATURE);
        if (!StringUtils.isBlank(tgStr)) {
            String[] tgInfoPairs = tgStr.split("\\|");
            if (tgInfoPairs.length > 0) {
                Long startTime = null;
                Long endTime = null;
                Long nowTime = System.currentTimeMillis() / 1000;
                for (String tgInfoPair : tgInfoPairs) {
                    String[] tg = tgInfoPair.split(":");
                    if (tg.length != 2) {
                        continue;
                    }
                    if ("st".equals(tg[0])) {
                        startTime = Long.parseLong(tg[1]);
                    } else if ("et".equals(tg[0])) {
                        endTime = Long.parseLong(tg[1]);
                    }
                }

                if ((nowTime + 86400 > startTime) && nowTime < endTime) {
                    return true;
                }
            }
        }

        // 品牌团
        tgStr = extraInfo.get(PINPAI_FEATURE);
        if (!StringUtils.isBlank(tgStr)) {
            String[] tgInfoPairs = tgStr.split("\\|");
            if (tgInfoPairs.length > 0) {
                Long startTime = null;
                Long endTime = null;
                Long nowTime = System.currentTimeMillis() / 1000;
                for (String tgInfoPair : tgInfoPairs) {
                    String[] tg = tgInfoPair.split(":");
                    if (tg.length != 2) {
                        continue;
                    }
                    if ("st".equals(tg[0])) {
                        startTime = Long.parseLong(tg[1]);
                    } else if ("et".equals(tg[0])) {
                        endTime = Long.parseLong(tg[1]);
                    }
                }

                if ((nowTime + 86400 > startTime) && nowTime < endTime) {
                    return true;
                }
            }
        }

        // U质团
        tgStr = extraInfo.get(UZHI_FEATURE);
        if (!StringUtils.isBlank(tgStr)) {
            String[] tgInfoPairs = tgStr.split("\\|");
            if (tgInfoPairs.length > 0) {
                Long startTime = null;
                Long endTime = null;
                Long nowTime = System.currentTimeMillis() / 1000;
                for (String tgInfoPair : tgInfoPairs) {
                    String[] tg = tgInfoPair.split(":");
                    if (tg.length != 2) {
                        continue;
                    }
                    if ("st".equals(tg[0])) {
                        startTime = Long.parseLong(tg[1]);
                    } else if ("et".equals(tg[0])) {
                        endTime = Long.parseLong(tg[1]);
                    }
                }

                if ((nowTime + 86400 > startTime) && nowTime < endTime) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * extra解析成map
     * @param extra
     * @return
     */
    public static Map<String, String> getExtraInfo(String extra) {
        if (StringUtils.isEmpty(extra)) {
            return null;
        }
        try {
            Gson gson = new Gson();
            return gson.fromJson(extra, HashMap.class);
        } catch (Exception e) {
            LOGGER.debug("decode extrainfo failed, {}", e);
        }
        return null;
    }
}
