package com.mogujie.detail.before.common.adt;


import com.mogujie.detail.before.common.constant.StatusCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * Created by xiaoyao on 15/11/24.
 */
public class Status implements Serializable {

    /**
     * 状态码
     */
    @Getter
    @Setter
    private Integer code;

    /**
     * 具体信息
     */
    @Getter
    @Setter
    private String msg;


    public Status() {
        this(StatusCode.SUCCESS, "");
    }

    public Status(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
