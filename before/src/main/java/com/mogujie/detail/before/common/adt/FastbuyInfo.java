package com.mogujie.detail.before.common.adt;

import lombok.Getter;
import lombok.Setter;

/**
 * Created by anshi on 17/2/8.
 */
public class FastbuyInfo {

    /**
     * 快抢倒计时文案
     */
    @Getter
    @Setter
    private String countdownDesc;

    /**
     * 倒计时时间(s)
     */
    @Getter
    @Setter
    private int countdown;

    /**
     * 快抢状态
     * 0. 未开始
     * 1. 活动中
     * 2. 活动中,但是库存为0,并且没有未付款人数
     * 3. 活动结束（这个状态不会使用）
     * 4. 活动中,但是库存为0,并且有未付款人数
     */
    @Getter
    @Setter
    private int state;

    /**
     * 开始时间
     */
    @Getter
    @Setter
    private int startTime;

    /**
     * 结束时间
     */
    @Getter
    @Setter
    private int endTime;
}
