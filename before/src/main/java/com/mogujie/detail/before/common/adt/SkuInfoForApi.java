package com.mogujie.detail.before.common.adt;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/5/26.
 */
public class SkuInfoForApi extends SkuInfo {

    private String img;

    private String oldPrice;

    private Integer limitTotalStock;

    private Integer limitNum;

    private String limitDesc;

    private PinTuanInfo pinTuanInfo;

    private int maxFreePhases;

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getOldPrice() {
        return oldPrice;
    }

    public void setOldPrice(String oldPrice) {
        this.oldPrice = oldPrice;
    }

    public Integer getLimitTotalStock() {
        return limitTotalStock;
    }

    public void setLimitTotalStock(Integer limitTotalStock) {
        this.limitTotalStock = limitTotalStock;
    }

    public Integer getLimitNum() {
        return limitNum;
    }

    public void setLimitNum(Integer limitNum) {
        this.limitNum = limitNum;
    }

    public String getLimitDesc() {
        return limitDesc;
    }

    public void setLimitDesc(String limitDesc) {
        this.limitDesc = limitDesc;
    }

    public PinTuanInfo getPinTuanInfo() {
        return pinTuanInfo;
    }

    public void setPinTuanInfo(PinTuanInfo pinTuanInfo) {
        this.pinTuanInfo = pinTuanInfo;
    }

    public int getMaxFreePhases() {
        return maxFreePhases;
    }

    public void setMaxFreePhases(int maxFreePhases) {
        this.maxFreePhases = maxFreePhases;
    }
}
