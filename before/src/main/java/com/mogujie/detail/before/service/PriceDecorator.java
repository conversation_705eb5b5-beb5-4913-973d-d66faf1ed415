package com.mogujie.detail.before.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.before.common.adt.CommonItem;
import com.mogujie.detail.before.common.adt.DecorateResult;
import com.mogujie.detail.before.common.adt.SkusQueryParam;
import com.mogujie.detail.before.common.constant.DiscountType;
import com.mogujie.detail.before.controller.DetailApiController;
import com.mogujie.detail.before.core.util.NumUtil;
import com.mogujie.detail.before.core.util.TagConvertUtil;
import com.mogujie.marketing.ferrari.api.dto.RushInfoDTO;
import com.mogujie.metabase.spring.client.MetabaseClient;
import com.mogujie.metabase.utils.StringUtils;
import com.mogujie.service.hummer.api.PromotionReadService;
import com.mogujie.service.hummer.constains.PromotionConstants;
import com.mogujie.service.hummer.constains.RequestConstants;
import com.mogujie.service.hummer.domain.dto.CampaignInfo;
import com.mogujie.service.hummer.domain.dto.InvokeInfo;
import com.mogujie.service.hummer.domain.dto.ItemDetailPromotion;
import com.mogujie.service.hummer.domain.dto.ItemDetailRequestV2;
import com.mogujie.service.hummer.domain.dto.Pbuyer;
import com.mogujie.service.hummer.domain.dto.PitemDetail;
import com.mogujie.service.hummer.domain.dto.Pseller;
import com.mogujie.service.hummer.domain.dto.result.Result;
import com.mogujie.service.hummer.utils.PromotionConvertUtils;
import com.mogujie.service.hummer.utils.SystemDiscountChecker;
import com.mogujie.service.hummer.utils.Utils;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.service.tagcenter.api.read.ItemTagReadService;
import com.mogujie.service.tagcenter.domain.entity.query.ItemTagQueryOption;
import com.mogujie.service.tagcenter.domain.entity.result.BaseResultDO;
import com.mogujie.service.tagcenter.domain.entity.result.ItemTagDO;
import com.mogujie.session.SessionContextHolder;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by anshi on 16/12/27.
 */
@Component
public class PriceDecorator {
    private static final Logger LOGGER = LoggerFactory.getLogger(PriceDecorator.class);

    @Autowired
    private PromotionReadService promotionReadService;

    private ItemTagReadService itemTagService;

    @Resource(name = "confMetabaseClient")
    protected MetabaseClient metabaseClient;

    private Gson gson = new Gson();

    @PostConstruct
    public void init() {
        try {
            gson = new Gson();
            itemTagService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ItemTagReadService.class);
        } catch (Throwable e) {
            LOGGER.error("init PriceDecorator bean failed!", e);
        }
    }

    /**
     * 返回boolean,表示是否为直播价
     *
     * @param item
     * @param param
     * @return
     */
    public DecorateResult decorateSkuAndPriceNew(CommonItem item, SkusQueryParam param, RushInfoDTO rushInfo, Boolean isNormalPintuan) {

        DecorateResult decorateResult = new DecorateResult();
        try {
            Map<Long, Long> originSkuMap = getSkuPriceMap(item);
            Map<Long, Long> realSkuMap = null;
            Long realPrice = null;
            String decorate = null;
            if (null != rushInfo) {
                realSkuMap = new HashMap<>(item.getSkus().size());
                for (ItemSkuDO sku : item.getSkus()) {
                    Long rp = (rushInfo.getActivityDiscount() != 1000 && 0 != rushInfo.getActivityDiscount()) ?
                            Long.valueOf((new BigDecimal(sku.getPrice()).multiply(new BigDecimal(rushInfo.getActivityDiscount()))
                                    .divide(new BigDecimal(1000), RoundingMode.DOWN).longValue())) : rushInfo.getSalePrice();
                    realSkuMap.put(sku.getSkuId(), rp);
                }
                realPrice = (long) rushInfo.getSalePrice();
            } else if (!DetailApiController.isFlItem(item.getExtra()) && !isNormalPintuan && param.getChannelId() == null && useLocalDiscount()) {
                SystemDiscountChecker.SystemDiscountRes ret = SystemDiscountChecker.calcSystemDiscount(item.getExtra(), item.getPrice().longValue(), false, (int) RequestConstants.Market.MOGUJIE, originSkuMap);
                if (null != ret) {
                    realSkuMap = ret.getSkuRalPrice();
                    realPrice = ret.getRealPrice();
                    decorate = ret.getName();
                }
            } else if (isNormalPintuan && useLocalDiscount()) {
                SystemDiscountChecker.SystemDiscountReq req = new SystemDiscountChecker.SystemDiscountReq();
                req.setExtra(item.getExtra());
                req.setOrgiPrice(item.getPrice().longValue());
                req.setIsDisplay(false);
                req.setMarket(RequestConstants.Market.MOGUJIE);
                req.setSkuPriceMap(originSkuMap);
                SystemDiscountChecker.SystemDiscountRes ret = SystemDiscountChecker.calcSystemDiscount(req);
                if (null != ret) {
                    realSkuMap = ret.getSkuRalPrice();
                    realPrice = ret.getRealPrice();
                    decorate = ret.getName();
                    if (ret.getIsGroupShopping() != null && ret.getIsGroupShopping()) {
                        decorateResult.setDisType(DiscountType.PINTUAN);
                    }
                }
            }

            if (null == realSkuMap || null == realPrice) {
                ItemDetailRequestV2 request = new ItemDetailRequestV2();
                Pbuyer pbuyer = new Pbuyer();
                pbuyer.setBuyerId(SessionContextHolder.getUserId());
                Pseller pSeller = new Pseller();
                pSeller.setSellerId(item.getUserId().longValue());
                PitemDetail pitemDetail = new PitemDetail();
                pitemDetail.setExtra(item.getExtra());
                pitemDetail.setItemId(item.getTradeItemId().longValue());
                pitemDetail.setSkuPriceMap(originSkuMap);
                pitemDetail.setItemPrice(getHighestPrice(item.getSkus()));
                Long skuNum = param.getSkuNum();
                Integer market = param.getMarketId();
                if (null != skuNum && skuNum > 0L) {
                    pitemDetail.setNumber(skuNum);
                }
                if (isNormalPintuan) {
                    pitemDetail.setBuyType(RequestConstants.BuyType.GROUP_SHOPPING);
                }

                //如果是直播商品进商城商品，则带上BuySource字段
                if (!StringUtils.isBlank(param.getSourceParams())) {
                    JSONObject sourceObject = JSON.parseObject(param.getSourceParams());
                    if (sourceObject != null && Objects.equals(sourceObject.getInteger("type"), 1)) {
                        if (item.getItemTagDOS() != null) {
                            for (com.mogujie.service.item.domain.basic.ItemTagDO itemTagDO : item.getItemTagDOS()) {
                                if ("2002".equals(itemTagDO.getTagValue()) && "tags".equals(itemTagDO.getTagKey())) {
                                    pitemDetail.setBuySource(RequestConstants.BuySource.LIVE_HUAYRA);
                                }
                            }
                        }
                    }
                }

                ItemTagQueryOption options = new ItemTagQueryOption();
                options.setItemId(item.getTradeItemId().longValue());
                options.setDirectQueryDB(false);
                BaseResultDO<List<ItemTagDO>> tRet = itemTagService.queryItemTag(options);
                if (null != tRet && tRet.isSuccess() && !CollectionUtils.isEmpty(tRet.getResult())) {
                    pitemDetail.setItemTagList(PromotionConvertUtils.convertItemTagToItemProTag(TagConvertUtil.convertTODO(tRet.getResult())));
                }
                InvokeInfo invokeInfo = new InvokeInfo();
                invokeInfo.setChannel(null == param.getChannelId() ? (int) RequestConstants.Channel.UNKNOW : param.getChannelId());
                invokeInfo.setMarket(market);
                invokeInfo.setSource(RequestConstants.Source.DETAIL);
                invokeInfo.setTerminal(RequestConstants.Terminal.APP);
                if (null != param.getOutType() && !StringUtils.isBlank(param.getActId())) {
                    invokeInfo.setOutId(IdConvertor.urlToId(param.getActId()).toString());
                    invokeInfo.setOutType(param.getOutType());
                }
                request.setPitemDetail(pitemDetail);
                request.setSeller(pSeller);
                request.setPbuyer(pbuyer);
                request.setInvokeInfo(invokeInfo);

                Result<ItemDetailPromotion> ret = promotionReadService.calcForItemDetailPromotion(request);
                if (null != ret && ret.isSuccess() && null != ret.getData()) {
                    CampaignInfo campaignInfo = ret.getData().getCampaigninfo();
                    if (null != campaignInfo) {
                        if (null != campaignInfo.getPromotionMark() && PromotionConstants.PromotionCode.GROUP_SHOPPING.equals(campaignInfo.getPromotionMark().getPromotionCode())) {
                            decorate = "拼团价";
                            decorateResult.setDisType(DiscountType.PINTUAN);
                        } else {
                            if (Utils.channelCheck(campaignInfo.getParameter(), RequestConstants.Channel.ZHIBO)) {
                                decorateResult.setDisType(DiscountType.LIVEPRICE);
                            } else if (null != campaignInfo.getPromotionMark()
                                    && PromotionConstants.PromotionCode.CHANNEL_PRICE.equals(campaignInfo.getPromotionMark().getPromotionCode())
                                    && param.getChannelId() != null
                                    && param.getChannelId() == 2023) {
                                decorateResult.setDisType(DiscountType.LIVE_CHANNEL);
                            }
                            Map<String, String> parameter = campaignInfo.getParameter();
                            if (null != parameter && !parameter.isEmpty()) {
                                String limitNum = parameter.get("itemCountLimit");
                                if (!StringUtils.isBlank(limitNum)) {
                                    decorateResult.setLimitNum(Integer.parseInt(limitNum));
                                }
                            }
                            decorate = ret.getData().getDecorate();
                        }
                    }
                    realSkuMap = ret.getData().getSkuRealPriceMap();
                    realPrice = ret.getData().getItemRealPrice();
                }
            }
            if (null != realSkuMap && null != realPrice) {
                item.setDiscountDesc(decorate);
                filterSku(item, realSkuMap, realPrice);
            } else {
                item.setDiscountDesc("");
                filterSku(item, originSkuMap, item.getPrice().longValue());
            }
        } catch (Throwable e) {
            LOGGER.error("get discount info failed : {}", e);
            filterSku(item, new HashMap<Long, Long>(), null);
        }
        return decorateResult;
    }


    /**
     * 返回boolean,表示是否为直播价
     *
     * @param commonItem
     * @param param
     * @return
     */
    public DecorateResult decorateSkuAndPrice(CommonItem commonItem, SkusQueryParam param, Boolean isNormalPintuan) {

        DecorateResult decorateResult = new DecorateResult();
        decorateResult.setDisType(DiscountType.NORMAL);
        // 不带价格,刷价格为0
        if (!param.getWithPrice()) {
            commonItem.setDiscountDesc("");
            int totalStock = 0;
            for (ItemSkuDO sku : commonItem.getSkus()) {
                sku.setPrice(0L);
                sku.setNowPrice(0);
                totalStock += sku.getQuantity();
            }
            commonItem.setLowPrice("0");
            commonItem.setLowNowPrice("0");
            commonItem.setHighPrice("0");
            commonItem.setHighNowPrice("0");
            commonItem.setTotalStock(totalStock);
            commonItem.setPrice(0L);
            return decorateResult;
        }

        try {
            ItemDetailRequestV2 request = new ItemDetailRequestV2();
            Pbuyer pbuyer = new Pbuyer();
            pbuyer.setBuyerId(SessionContextHolder.getUserId());
            Pseller pSeller = new Pseller();
            pSeller.setSellerId(commonItem.getUserId().longValue());
            PitemDetail pitemDetail = new PitemDetail();
            pitemDetail.setExtra(commonItem.getExtra());
            pitemDetail.setItemId(commonItem.getTradeItemId().longValue());
            Map<Long, Long> originSkuMap = getSkuPriceMap(commonItem);
            pitemDetail.setSkuPriceMap(originSkuMap);
            pitemDetail.setItemPrice(getHighestPrice(commonItem.getSkus()));

            Long skuNum = param.getSkuNum();
            if (null != skuNum && skuNum > 0L) {
                pitemDetail.setNumber(param.getSkuNum());
            }
            if (isNormalPintuan) {
                pitemDetail.setBuyType(RequestConstants.BuyType.GROUP_SHOPPING);
            }
            ItemTagQueryOption options = new ItemTagQueryOption();
            options.setItemId(commonItem.getTradeItemId().longValue());
            options.setDirectQueryDB(false);
            BaseResultDO<List<ItemTagDO>> tRet = itemTagService.queryItemTag(options);
            if (null != tRet && tRet.isSuccess() && !CollectionUtils.isEmpty(tRet.getResult())) {
                pitemDetail.setItemTagList(PromotionConvertUtils.convertItemTagToItemProTag(TagConvertUtil.convertTODO(tRet.getResult())));
            }
            InvokeInfo invokeInfo = new InvokeInfo();
            invokeInfo.setChannel(param.getChannelId());
            invokeInfo.setMarket(param.getMarketId());
            invokeInfo.setSource(RequestConstants.Source.DETAIL);
            invokeInfo.setTerminal("pc".equals(param.getPlatform()) ? RequestConstants.Terminal.PC : RequestConstants.Terminal.APP);
            Long outIdVal = IdConvertor.urlToId(param.getActId());
            invokeInfo.setOutId(outIdVal == null ? null : outIdVal.toString());
            invokeInfo.setOutType(param.getOutType());
            request.setPitemDetail(pitemDetail);
            request.setSeller(pSeller);
            request.setPbuyer(pbuyer);
            request.setInvokeInfo(invokeInfo);

            Result<ItemDetailPromotion> ret = promotionReadService.calcForItemDetailPromotion(request);

            if (null != ret && ret.isSuccess() && null != ret.getData()) {
                String decorate = null;
                CampaignInfo campaignInfo = ret.getData().getCampaigninfo();
                if (null != campaignInfo) {
                    if (null != campaignInfo.getPromotionMark() && PromotionConstants.PromotionCode.GROUP_SHOPPING.equals(campaignInfo.getPromotionMark().getPromotionCode())) {
                        decorate = "拼团价";
                        decorateResult.setDisType(DiscountType.PINTUAN);
                    } else {
                        if (Utils.channelCheck(campaignInfo.getParameter(), RequestConstants.Channel.ZHIBO)) {
                            decorateResult.setDisType(DiscountType.LIVEPRICE);
                        }
                        Map<String, String> parameter = campaignInfo.getParameter();
                        if (null != parameter && !parameter.isEmpty()) {
                            String limitNum = parameter.get("itemCountLimit");
                            if (!StringUtils.isBlank(limitNum)) {
                                decorateResult.setLimitNum(Integer.parseInt(limitNum));
                            }
                        }
                        decorate = ret.getData().getDecorate();
                    }
                }
                commonItem.setDiscountDesc(decorate);
                filterSku(commonItem, ret.getData().getSkuRealPriceMap(), ret.getData().getItemRealPrice());
            } else {
                commonItem.setDiscountDesc("");
                filterSku(commonItem, originSkuMap, commonItem.getPrice().longValue());
            }
            return decorateResult;
        } catch (Throwable e) {
            LOGGER.error("get discount info failed : {}", e);
            filterSku(commonItem, new HashMap<Long, Long>(), null);
            return decorateResult;
        }
    }

    private static void filterSku(CommonItem item, Map<Long, Long> realPriceMap, Long realPrice) {
        long lowPrice = item.getPrice();
        long highPrice = item.getPrice();
        long lowNowPrice = null == realPrice ? item.getPrice() : realPrice;
        long highNowPrice = null == realPrice ? item.getPrice() : realPrice;
        int totalStock = 0;
        for (ItemSkuDO sku : item.getSkus()) {

            if (sku.getPrice() < lowPrice) {
                lowPrice = sku.getPrice();
            }

            if (sku.getPrice() > highPrice) {
                highPrice = sku.getPrice();
            }
            Long skuRealPrice = realPriceMap.get(sku.getSkuId());
            if (null == skuRealPrice) {
                skuRealPrice = sku.getPrice().longValue();
            }
            sku.setNowPrice(skuRealPrice.intValue());
            if (skuRealPrice < lowNowPrice) {
                lowNowPrice = skuRealPrice;
            }

            if (skuRealPrice > highNowPrice) {
                highNowPrice = skuRealPrice;
            }

            totalStock += sku.getQuantity();
        }
        item.setLowPrice(NumUtil.formatNum(lowPrice / 100D));
        item.setLowNowPrice(NumUtil.formatNum(lowNowPrice / 100D));
        item.setHighPrice(NumUtil.formatNum(highPrice / 100D));
        item.setHighNowPrice(NumUtil.formatNum(highNowPrice / 100D));
        item.setTotalStock(totalStock);
        item.setPrice(lowPrice);
    }

    private Map<Long, Long> getSkuPriceMap(CommonItem commonItem) {
        Map<Long, Long> skuPriceMap = new HashMap<>();
        for (ItemSkuDO sku : commonItem.getSkus()) {
            skuPriceMap.put(sku.getSkuId(), sku.getPrice());
        }
        return skuPriceMap;
    }

    private Long getHighestPrice(final List<ItemSkuDO> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return 0L;
        }
        Long highestPrice = skuList.get(0).getPrice();
        for (ItemSkuDO sku : skuList) {
            if (sku.getPrice() > highestPrice) {
                highestPrice = sku.getPrice();
            }
        }
        return highestPrice;
    }

    private boolean useLocalDiscount() {
        try {
            Boolean useLocal = metabaseClient.getBoolean("discount_useLocal");
            if (null != useLocal) {
                return useLocal;
            }
        } catch (Exception e) {
            ;
        }
        return true;
    }


}
