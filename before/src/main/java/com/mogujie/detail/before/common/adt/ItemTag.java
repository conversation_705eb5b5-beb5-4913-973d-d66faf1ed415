package com.mogujie.detail.before.common.adt;

import com.mogujie.dragon.bigdata.CDNBalance.CDNBalance;
import com.mogujie.dragon.bigdata.CDNBalance.CDNException;
import com.mogujie.tagcenter.client.item.domain.ItemServiceTag;

import java.io.Serializable;

/**
 * Created by anshi on 16/8/8.
 */
public class ItemTag implements Comparable<ItemTag>, Serializable {
    private String icon;
    private String name;
    private String content;
    private int order;

    public ItemTag() {

    }

    public ItemTag(ItemServiceTag itemServiceTag) {
        name = itemServiceTag.getDesc();
        content = itemServiceTag.getText();
        switch (itemServiceTag) {
            case BRAND_CA:
                icon = this.img("/p1/160518/upload_ifrgcnldmyzwkyjzhazdambqmeyde_100x64.png");
                order = 1;
                break;
            case WELL_CA:
                icon = this.img("/p1/160518/upload_ifqweyrtmyzwkyjzhazdambqgyyde_100x64.png");
                order = 2;
                break;
            case NEW_CA:
                icon = this.img("/p2/160808/upload_6i8g8afeb0hf7el2gkb22k686f1fc_100x64.png");
                order = 3;
                break;
            case PHOTO_CA:
                icon = this.img("/p1/160518/upload_ifrgknbvge2gkyjzhazdambqmeyde_100x64.png");
                order = 4;
                break;
            default:
                break;
        }
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    @Override
    public int compareTo(ItemTag o) {
        return this.getOrder() - o.getOrder();
    }

    public static String img(String srcFile) {
        if (org.apache.commons.lang.StringUtils.isBlank(srcFile)) {
            return "";
        }
        try {
            return  CDNBalance.smartGetImg(srcFile, Boolean.TRUE);
        } catch (CDNException e) {
        }
        return "";
    }
}
