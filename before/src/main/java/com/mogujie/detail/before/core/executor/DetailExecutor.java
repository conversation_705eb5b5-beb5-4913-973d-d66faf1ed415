package com.mogujie.detail.before.core.executor;

import com.mogujie.actionlet.ActionRequest;
import com.mogujie.actionlet.ActionResponse;
import com.mogujie.actionlet.core.ActionletExecutor;
import com.mogujie.actionlet.core.pipeline.PipelineActionletExecutor;
import com.mogujie.actionlet.exception.ActionletException;
import com.mogujie.actionlet.pipeline.InvokeValve;
import com.mogujie.actionlet.pipeline.RouteValve;
import com.mogujie.wtpipeline.Valve;
import com.mogujie.wtpipeline.exception.DuplicateLabelException;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by xiaoyao on 16/1/18.
 */

@Component
public class DetailExecutor implements ActionletExecutor {

    private final PipelineActionletExecutor executor = new PipelineActionletExecutor();

    @Override
    public Object execute(ActionRequest actionRequest, ActionResponse actionResponse) throws ActionletException {
        return executor.execute(actionRequest, actionResponse);
    }

    @PostConstruct
    public void init() throws DuplicateLabelException {
        List<Valve> valves = new ArrayList<Valve>(2);
        valves.add(new RouteValve());
        valves.add(new InvokeValve());
        executor.setValves(valves);
    }
}
