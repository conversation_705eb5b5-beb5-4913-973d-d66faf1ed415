package com.mogujie.detail.before.common.adt;

import com.mogujie.detail.before.common.constant.ItemTag;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.basic.ItemDetailDO;
import com.mogujie.service.item.domain.basic.ItemExtraDO;
import com.mogujie.service.item.domain.basic.ItemImageDO;
import com.mogujie.service.item.domain.basic.ItemPreSaleDO;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.service.item.domain.basic.ItemTagDO;
import com.mogujie.service.item.domain.entity.Item;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by xiaoyao on 15/11/25.
 */
public class CommonItem {

    /**
     * 折扣
     */
    @Getter
    @Setter
    private Float discount;

    /**
     * 折扣描述
     */
    @Getter
    @Setter
    private String discountDesc;

    @Getter
    @Setter
    private String lowPrice;

    @Getter
    @Setter
    private String highPrice;

    @Getter
    @Setter
    private String lowNowPrice;

    @Getter
    @Setter
    private String highNowPrice;

    @Getter
    @Setter
    private Integer totalStock;

    // 预售商品
    @Getter
    @Setter
    private ItemPreSaleDO itemPreSale;

    // 判断是否测试流量
    @Getter
    @Setter
    private boolean isTest;

    // 判断是否测试流量
    @Getter
    @Setter
    private List<ItemTag> itemTags;

    // 扩展参数
    @Getter
    @Setter
    private Map<String, Object> params;

    @Getter
    @Setter
    private Integer priceChannel;

    //////////// CompleteItem

//    @Getter
//    @Setter
//    private List<Sku> skus;

    @Getter
    @Setter
    private List<ItemSkuDO> skus;

    @Getter
    @Setter
    private List<ItemImageDO> topImages;

    @Getter
    @Setter
    private ItemDetailDO detail;

    @Getter
    @Setter
    private Long postageId;

    //////////// Item

    /*
     * 商品ID
     */
    @Getter
    @Setter
    protected Long tradeItemId;

    /**
     * 小店itemId
     * FIXME:暂时保留，很快去掉
     */
    @Deprecated
    @Getter
    @Setter
    protected Long xdItemId;

    /*
     * 数据类型：用于区别不同来源的数据
     */
    @Getter
    @Setter
    protected Long type;

    /*
     * 用户Id
     */
    @Getter
    @Setter
    protected Long userId;

    /*
     * 店铺Id
     */
    @Getter
    @Setter
    protected Long shopId;

    /*
     * 商品开始售卖时间
     */
    @Getter
    @Setter
    protected Integer start;

    /*
     * 减库存方式：0付款，1下单减库存
     */
    @Getter
    @Setter
    protected Integer decStockType;

    /*
     * 商品标题
     */
    @Getter
    @Setter
    protected String title;

    /*
     * 一句话描述
     */
    @Getter
    @Setter
    protected String description;

    /*
     * 标签集合，以空格分割
     */
    @Getter
    @Setter
    protected String tags;

    /*
     * 价格,以分为单位
     */
    @Getter
    @Setter
    protected Long price;

    /*
     * 商品编码
     */
    @Getter
    @Setter
    protected String code;

    /*
     * 交易类型，0为全款交易，1为2阶段预售
     */
    @Getter
    @Setter
    protected Integer processType;

    /*
     * 商品状态
     */
    @Getter
    @Setter
    protected Integer status;

    /*
     * 扩展信息,json格式
     */
    @Getter
    @Setter
    protected String extra;

    /**
     * 位置名称
     */
    @Getter
    @Setter
    protected String address;

    /*
     * 商品所属最小类目id
     */
    @Getter
    @Setter
    protected Integer cid;

    /**
     * 商品类型
     */
    @Getter
    @Setter
    private Integer goodsType = 0;

    /**
     * 类目全路径
     */
    @Getter
    @Setter
    private String cids;

    /*
     * 是否下架 1为下架
     */
    @Getter
    @Setter
    protected Integer isShelf;

    /*
     * 扩展属性集合
     */
    @Getter
    @Setter
    protected String properties;

    /*
     * 商品主图
     */
    @Getter
    @Setter
    protected String image;

    /*
     * 店铺内分类ids,逗号分割
     */
    @Getter
    @Setter
    protected String classIds;

    /*
     *
     * 货币单位
     */
    @Getter
    @Setter
    protected String currency;

    /**
     * 品牌编号
     */
    @Getter
    @Setter
    protected Long brandId;

    /**
     * 以分为单位的原价
     * <p>
     * 该字段任何时候都是原价,提供给商品solr增量使用,其他业务方请勿使用.若错误使用,后果自负
     */
    @Getter
    @Setter
    protected Integer originalPrice;

    //乐观锁字段
    @Getter
    @Setter
    protected Long modify;

    //////////// ItemBaseEntity

    @Getter
    @Setter
    private Map<String, String> features;

    @Getter
    @Setter
    protected Integer created;

    @Getter
    @Setter
    protected Integer updated;

    @Getter
    @Setter
    protected Integer isDeleted;

    @Getter
    @Setter
    protected ItemExtraDO itemExtraDO;


    @Getter
    @Setter
    protected List<ItemTagDO> itemTagDOS;

    ////////////

    public CommonItem() {

    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;

        CommonItem that = (CommonItem) o;

        if (isTest != that.isTest) return false;
        if (discount != null ? !discount.equals(that.discount) : that.discount != null) return false;
        if (discountDesc != null ? !discountDesc.equals(that.discountDesc) : that.discountDesc != null) return false;
        if (lowPrice != null ? !lowPrice.equals(that.lowPrice) : that.lowPrice != null) return false;
        if (highPrice != null ? !highPrice.equals(that.highPrice) : that.highPrice != null) return false;
        if (lowNowPrice != null ? !lowNowPrice.equals(that.lowNowPrice) : that.lowNowPrice != null) return false;
        if (highNowPrice != null ? !highNowPrice.equals(that.highNowPrice) : that.highNowPrice != null) return false;
        if (totalStock != null ? !totalStock.equals(that.totalStock) : that.totalStock != null) return false;
        if (itemPreSale != null ? !itemPreSale.equals(that.itemPreSale) : that.itemPreSale != null) return false;
        if (itemTags != null ? !itemTags.equals(that.itemTags) : that.itemTags != null) return false;
        if (params != null ? !params.equals(that.params) : that.params != null) return false;
        return priceChannel != null ? priceChannel.equals(that.priceChannel) : that.priceChannel == null;

    }

    @Override
    public String toString() {
        return "CommonItem{" +
                "discount=" + discount +
                ", discountDesc='" + discountDesc + '\'' +
                ", lowPrice='" + lowPrice + '\'' +
                ", highPrice='" + highPrice + '\'' +
                ", lowNowPrice='" + lowNowPrice + '\'' +
                ", highNowPrice='" + highNowPrice + '\'' +
                ", totalStock=" + totalStock +
                ", itemPreSale=" + itemPreSale +
                ", isTest=" + isTest +
                ", itemTags=" + itemTags +
                ", params=" + params +
                ", priceChannel=" + priceChannel +
                '}';
    }

    @Override
    public int hashCode() {
        int result = super.hashCode();
        result = 31 * result + (discount != null ? discount.hashCode() : 0);
        result = 31 * result + (discountDesc != null ? discountDesc.hashCode() : 0);
        result = 31 * result + (lowPrice != null ? lowPrice.hashCode() : 0);
        result = 31 * result + (highPrice != null ? highPrice.hashCode() : 0);
        result = 31 * result + (lowNowPrice != null ? lowNowPrice.hashCode() : 0);
        result = 31 * result + (highNowPrice != null ? highNowPrice.hashCode() : 0);
        result = 31 * result + (totalStock != null ? totalStock.hashCode() : 0);
        result = 31 * result + (itemPreSale != null ? itemPreSale.hashCode() : 0);
        result = 31 * result + (isTest ? 1 : 0);
        result = 31 * result + (itemTags != null ? itemTags.hashCode() : 0);
        result = 31 * result + (params != null ? params.hashCode() : 0);
        result = 31 * result + (priceChannel != null ? priceChannel.hashCode() : 0);
        return result;
    }

    public CommonItem(Item item) {
        this.tradeItemId = item.getTradeItemId().longValue();
        this.type = item.getType().longValue();
        this.userId = item.getUserId().longValue();
        this.shopId = item.getShopId().longValue();
        this.decStockType = item.getDecStockType();
        this.title = item.getTitle();
        this.description = item.getDescription();
        this.tags = item.getTags();
        this.price = item.getPrice().longValue();
        this.code = item.getCode();
        this.processType = item.getProcessType();
        this.status = item.getStatus();
        this.extra = item.getExtra();
        this.address = item.getAddress();
        this.cid = item.getCid();
        this.setGoodsType(item.getGoodsType());
        this.setCids(item.getCids());
        this.isShelf = item.getIsShelf();
        this.properties = item.getProperties();
        this.image = item.getImage();
        this.classIds = item.getClassIds();
        this.xdItemId = item.getXdItemId().longValue();
        this.isDeleted = item.getIsDeleted();
        this.start = item.getStart();
        this.params = new HashMap<>(2);
        this.brandId = item.getBrandId();
    }

    public CommonItem(ItemDO item) {
        this.tradeItemId = item.getItemId();
        this.type = item.getVerticalMarket();
        this.userId = item.getUserId();
        this.shopId = item.getShopId();
        this.decStockType = item.getInventoryType();
        this.title = item.getTitle();
        this.description = item.getDescription();
        this.tags = item.getTags();
        this.price = item.getReservePrice();
        this.code = item.getCode();
        this.processType = item.getProcessType();
        this.status = item.getStatus();
        this.extra = item.getJsonExtra();
        this.address = item.getAddress();
        this.cid = item.getCategoryId();
        this.setGoodsType(0);
        this.setCids(item.getCids());
        this.isShelf = item.getIsShelf();
        this.properties = item.getPackedProperties();
        this.image = item.getMainImage();
        this.classIds = null;
        this.xdItemId = item.getXdItemId();
        this.isDeleted = item.getIsDeleted();
        this.start = (int) (item.getStarts().getTime() / 1000);
        this.params = new HashMap<>(2);
        this.brandId = item.getBrandId();
        this.detail = item.getItemDetailDO();
        this.topImages = item.getImages();
        this.itemExtraDO = item.getItemExtraDO();
        this.itemTagDOS = item.getItemTags();
    }

    public Object getParam(String key) {
        return this.params.get(key);
    }

}
