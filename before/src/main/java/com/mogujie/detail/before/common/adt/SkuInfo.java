package com.mogujie.detail.before.common.adt;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON>iaoyao on 15/12/7.
 */
public class SkuInfo implements ModuleData {

    @Setter
    @Getter
    private String title;

    @Setter
    @Getter
    private List<SkuData> skus;

    @Setter
    @Getter
    private List<PropInfo> props;

    @Setter
    @Getter
    private String styleKey;

    @Setter
    @Getter
    private String sizeKey;

    @Setter
    @Getter
    private String priceRange;

    @Getter
    @Setter
    private String defaultPrice;

    @Setter
    @Getter
    private boolean isAbroad;

    @Setter
    @Getter
    private String counterPrice;

    @Setter
    @Getter
    private int totalStock;

    /**
     * 预售定金
     */
    @Setter
    @Getter
    private String mainPriceStr;

    /**
     * 预售总价
     */
    @Setter
    @Getter
    private String subPriceStr;

    /**
     * "定金"前缀文案
     */
    @Getter
    @Setter
    private String mainDesc;

    /**
     * "总价"前缀文案
     */
    @Getter
    @Setter
    private String subDesc;

    /**
     * 价格描述: 团购价/快抢价(getSkuV2接口用)
     */
    @Getter
    @Setter
    private String priceDesc;

    /**
     * 商品id
     */
    @Getter
    @Setter
    private String itemId;

    /**
     * 活动id
     */
    @Getter
    @Setter
    private String activityId;

    /**
     * 促销渠道类型
     */
    @Getter
    @Setter
    private Integer outType;

    /**
     * 是否为京东商品
     */
    private boolean isJdItem;

    /**
     * 用户默认收货地 / 用户传进来的address
     */
    @Getter
    @Setter
    private AddressInfo addressInfo;

    /**
     * 京东sku库存剩余
     */
    @Getter
    @Setter
    private Map<String, Integer> jdSkuStock;

    public boolean getIsJdItem() {
        return isJdItem;
    }

    public void setIsJdItem(boolean isJdItem) {
        this.isJdItem = isJdItem;
    }
}
