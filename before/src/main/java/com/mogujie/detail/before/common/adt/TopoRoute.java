package com.mogujie.detail.before.common.adt;

import com.mogujie.detail.before.common.constant.App;
import com.mogujie.detail.before.common.constant.BizTag;
import com.mogujie.detail.before.common.constant.Platform;

/**
 * Created by xiaoyao on 16/3/25.
 */
public class TopoRoute {

    private final App app;

    private final BizTag bizTag;

    private final Platform platform;

    public TopoRoute(App app, Platform platform, BizTag bizTag) {
        this.app = app;
        this.platform = platform;
        this.bizTag = bizTag;
    }

    public App getApp() {
        return app;
    }

    public BizTag getBizTag() {
        return bizTag;
    }

    public Platform getPlatform() {
        return platform;
    }

    public TopoRoute getDefaultBizRoute() {
        return new TopoRoute(this.app, this.platform, BizTag.NORMAL);
    }

    public TopoRoute getDefaultPlatformAndBizRoute() {
        return new TopoRoute(this.app, Platform.ALL, BizTag.NORMAL);
    }

    public TopoRoute getAllDefaultRoute() {
        return new TopoRoute(App.MGJ, Platform.ALL, BizTag.NORMAL);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof TopoRoute)) return false;

        TopoRoute topoRoute = (TopoRoute) o;

        if (getApp() != topoRoute.getApp()) return false;
        if (getBizTag() != topoRoute.getBizTag()) return false;
        return getPlatform() == topoRoute.getPlatform();

    }

    @Override
    public int hashCode() {
        int result = getApp() != null ? getApp().hashCode() : 0;
        result = 31 * result + (getBizTag() != null ? getBizTag().hashCode() : 0);
        result = 31 * result + (getPlatform() != null ? getPlatform().hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "TopoRoute{" +
                "app=" + app +
                ", bizTag=" + bizTag +
                ", platform=" + platform +
                '}';
    }
}
