package com.mogujie.detail.before.common.adt;

import lombok.Getter;
import lombok.Setter;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/8/24.
 */
public class SkusQueryParam {


    @Getter
    @Setter
    private String iid;

    @Getter
    @Setter
    private String outId;

    @Getter
    @Setter
    private Short outType;

    @Getter
    @Setter
    private Integer marketId = 8;

    @Getter
    @Setter
    private Integer channelId = 0;

    @Getter
    @Setter
    private Integer activityId;

    /**
     * string类型的活动id
     */
    @Getter
    @Setter
    private String actId;

    @Getter
    @Setter
    private Boolean withPrice = false;

    /**
     * 库存id
     */
    @Getter
    @Setter
    private String skuId;

    /**
     * 库存数量
     */
    @Getter
    @Setter
    private Long skuNum;

    /**
     * 来源 wx 微信
     */
    @Getter
    @Setter
    private String source;

    /**
     * 平台， pc or app
     */
    @Getter
    @Setter
    private String platform;

    /**
     * 渠道
     */
    @Getter
    @Setter
    private String channel;

    /**
     * 平台号，用于是否走https
     */
    @Getter
    @Setter
    private String appPlat;

    /**
     * 唤起的sku选择器类型。pintuan or normal， 默认是pintuan
     */
    @Getter
    @Setter
    private String caller;

    /**
     * 收货地址id
     */
    @Getter
    @Setter
    private Long addressId;

    @Getter
    @Setter
    private String sourceParams;
}
