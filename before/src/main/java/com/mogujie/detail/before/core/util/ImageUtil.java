package com.mogujie.detail.before.core.util;

import com.mogujie.dragon.bigdata.CDNBalance.CDNBalance;
import com.mogujie.dragon.bigdata.CDNBalance.CDNException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by eryi
 * Date: 2020/4/30
 * Time: 10:10 AM
 * Introduction:
 * Document:
 * Actions:
 */
public class ImageUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(ImageUtil.class);


    public static String img(String srcFile) {
        if (StringUtils.isBlank(srcFile)) {
            return "";
        }
        try {
            return CDNBalance.smartGetImg(srcFile, Boolean.TRUE);
        } catch (CDNException e) {
            LOGGER.error("get realPath failed : {} , ", srcFile, e);
        }
        return "";
    }

}
