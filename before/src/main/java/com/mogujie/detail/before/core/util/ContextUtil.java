package com.mogujie.detail.before.core.util;

import com.mogujie.detail.before.common.adt.CommonItem;
import com.mogujie.metabase.spring.client.MetabaseClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Created by anshi on 18/4/18.
 */
@Component
public class ContextUtil {

    @Resource(name = "confMetabaseClient")
    protected MetabaseClient metabaseClient;

    public boolean isMedicalItem(CommonItem item){
        String[] categories = metabaseClient.get("medical_beauty_categories").split(",");
        for (String category : categories) {
            if (item.getCids().contains("#" + category + "#")) {
                return true;
            }
        }
        return false;
    }
}
