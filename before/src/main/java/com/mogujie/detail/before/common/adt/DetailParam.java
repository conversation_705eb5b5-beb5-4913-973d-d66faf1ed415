package com.mogujie.detail.before.common.adt;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 15/12/14.
 */
public class DetailParam {

    private String topoKey;

    private String itemId;

    private String test;

    private String version;

    /**
     * 传了domain就认为是走了mwp
     */
    private String domain;

    private String appPlat;

    private String activityId;

    public String getTopoKey() {
        return topoKey;
    }

    public void setTopoKey(String topoKey) {
        this.topoKey = topoKey;
    }

    public String getItemId() {
        return itemId;
    }

    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    public String getTest() {
        return test;
    }

    public void setTest(String test) {
        this.test = test;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getAppPlat() {
        return appPlat;
    }

    public void setAppPlat(String appPlat) {
        this.appPlat = appPlat;
    }


    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    @Override
    public String toString() {
        return "DetailParam{" +
                "topoKey='" + topoKey + '\'' +
                ", itemId='" + itemId + '\'' +
                ", test='" + test + '\'' +
                ", version='" + version + '\'' +
                ", domain='" + domain + '\'' +
                ", appPlat='" + appPlat + '\'' +
                ", activityId='" + activityId + '\'' +
                '}';
    }
}
