package com.mogujie.detail.before.core.util;

import com.mogujie.metabase.utils.CollectionUtils;
import com.mogujie.service.item.domain.entity.ItemTag;
import com.mogujie.service.tagcenter.domain.entity.result.ItemTagDO;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by xy on 2018/1/24.
 */
public class TagConvertUtil {

    public static List<ItemTag> convert(List<ItemTagDO> newTags) {
        if (CollectionUtils.isEmpty(newTags)) {
            return null;
        }
        List<ItemTag> oldTagList = new ArrayList<>();
        for (ItemTagDO itemTagDO : newTags) {
            ItemTag tag = new ItemTag();
            tag.setBizId(itemTagDO.getBizId());
            tag.setBizType(itemTagDO.getBizType());
            tag.setEndTime(itemTagDO.getEndTime());
            tag.setId(itemTagDO.getTagId().intValue());
            tag.setItemId(itemTagDO.getItemId().intValue());
            tag.setMarket(itemTagDO.getMarket());
            tag.setStartTime(itemTagDO.getStartTime());
            tag.setSource(itemTagDO.getSource());
            tag.setTagKey(itemTagDO.getTagKey());
            tag.setTagValue(itemTagDO.getTagValue());
            tag.setCreated(itemTagDO.getCreated());
            tag.setUpdated(itemTagDO.getUpdated());
            tag.setIsDeleted(itemTagDO.getIsDeleted());
            oldTagList.add(tag);
        }
        return oldTagList;
    }

    public static List<com.mogujie.service.item.domain.basic.ItemTagDO> convertTODO(List<ItemTagDO> newTags) {
        if (CollectionUtils.isEmpty(newTags)) {
            return null;
        }
        List<com.mogujie.service.item.domain.basic.ItemTagDO> oldTagList = new ArrayList<>();
        for (ItemTagDO itemTagDO : newTags) {
            com.mogujie.service.item.domain.basic.ItemTagDO tag = new com.mogujie.service.item.domain.basic.ItemTagDO();
            tag.setBizId(itemTagDO.getBizId());
            tag.setBizType(itemTagDO.getBizType());
            tag.setEndTime(itemTagDO.getEndTime());
            tag.setId(itemTagDO.getTagId());
            tag.setItemId(itemTagDO.getItemId());
            tag.setMarket(itemTagDO.getMarket());
            tag.setStartTime(itemTagDO.getStartTime());
            tag.setSource(itemTagDO.getSource());
            tag.setTagKey(itemTagDO.getTagKey());
            tag.setTagValue(itemTagDO.getTagValue());
            tag.setCreated(itemTagDO.getCreated());
            tag.setUpdated(itemTagDO.getUpdated());
            tag.setIsDeleted(itemTagDO.getIsDeleted());
            oldTagList.add(tag);
        }
        return oldTagList;
    }
}
