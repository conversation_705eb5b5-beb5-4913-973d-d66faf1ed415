package com.mogujie.detail.before.core.util;

import com.mogujie.detail.before.common.adt.CommonItem;
import com.mogujie.service.item.api.basic.ItemReadService;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.external.IcException;
import com.mogujie.service.item.domain.query.QueryItemOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by anshi on 18/3/30.
 */
@Component
public class ItemFetcher {

    private static final Logger LOGGER = LoggerFactory.getLogger(ItemFetcher.class);

    @Autowired
    private ItemReadService itemReadService;

    public ItemDO getItemDO(Long itemId) {
        QueryItemOptions queryItemOptions = new QueryItemOptions();
        queryItemOptions.setIncludeItemExtraInfo(true);
        queryItemOptions.setQueryBasicItem(true);
        queryItemOptions.setQueryInventory(true);
        queryItemOptions.setQuerySkuInfo(true);
        queryItemOptions.setIncludeDeleted(false);
        queryItemOptions.setQueryItemDetail(true);
        queryItemOptions.setQueryPresaleDO(false);
        queryItemOptions.setQuerySkuAttribute(true);
        queryItemOptions.setQueryTgrcStock(true);
        queryItemOptions.setQueryExpressTmpl(true);
        queryItemOptions.setQueryItemIdPropertyMap(true);
        queryItemOptions.setQueryItemTag(true);
        try {
            com.mogujie.service.item.domain.result.BaseResultDO<ItemDO> baseResultDO = itemReadService.queryItemById(itemId, queryItemOptions);
            if (baseResultDO != null && baseResultDO.isSuccess() && baseResultDO.getResult() != null) {
                return baseResultDO.getResult();
            } else {
                LOGGER.debug("item not found : {}", itemId);
                return null;
            }
        } catch (IcException e) {
            LOGGER.debug("ic error!", e);
            return null;
        }
    }

    public CommonItem getCommonItem(Long itemId) {
        ItemDO itemDO = getItemDO(itemId);
        if (itemDO == null) {
            return null;
        }
        return new CommonItem(itemDO);
    }
}
