package com.mogujie.detail.before.service;

import com.mogujie.detail.before.common.adt.CommonItem;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.service.item.domain.basic.SkuAttributionDO;
import com.mogujie.service.tangram.domain.entity.BaseValue;
import com.mogujie.service.tangram.property.domain.CategorySkuDTO;
import com.mogujie.service.tangram.property.service.CategoryPropertyReadClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by anshi on 17/5/2.
 */
@Component
public class SkuReorder {

    private static final Logger logger = LoggerFactory.getLogger(SkuReorder.class);

    @Autowired
    private CategoryPropertyReadClient categoryPropertyReadClient;

    /**
     * 重新排序sku,保证显示顺序与小店后台相同
     *
     * @param itemDO
     * @return
     */
    public List<ItemSkuDO> reorderSkus(CommonItem itemDO) {
        List<ItemSkuDO> skuList = itemDO.getSkus();
        if (skuList == null) {
            return null;
        }
        if (skuList.isEmpty()) {
            return skuList;
        }
        try {
            if (itemDO.getCid() == null) {
                return skuList;
            }
            CategorySkuDTO categorySkuDTO = categoryPropertyReadClient.getCategorySku(itemDO.getCid().longValue(), true);
            if (categorySkuDTO == null) {
                return skuList;
            }

            List<ItemSkuDO> orderedSkus = new ArrayList<>();
            if (skuList.get(0).getAttributions() == null || skuList.get(0).getAttributions().size() == 0) {
                return skuList;
            }
            //商品是一维sku
            if (skuList.get(0).getAttributions().size() == 1) {
                // 该维sku的属性名("款式"、"尺码"、"颜色"等)
                String attrKey = skuList.get(0).getAttributions().get(0).getName();
                // 将商品sku以属性值为key,存入map中
                Map<String, ItemSkuDO> skuMap = new LinkedHashMap<>();
                for (ItemSkuDO skuDO : skuList) {
                    SkuAttributionDO attr = skuDO.getAttributions().get(0);
                    skuMap.put(attr.getValue(), skuDO);
                }
                // 获取该维sku所对应的标准属性表
                List<BaseValue> standardValues = null;
                if (categorySkuDTO.getSize() != null && categorySkuDTO.getSize().equals(attrKey)) {
                    standardValues = categorySkuDTO.getSizeValueList();
                } else {
                    standardValues = categorySkuDTO.getStyleValueList();
                }
                // 按照标准属性表的顺序,重新将sku存入list中
                for (BaseValue baseValue : standardValues) {
                    if (skuMap.containsKey(baseValue.getValue())) {
                        orderedSkus.add(skuMap.get(baseValue.getValue()));
                        skuMap.remove(baseValue.getValue());
                    }
                }
                // 剩下的是自定义属性值的sku,一起存入list尾部
                if (!org.apache.commons.collections4.CollectionUtils.isEmpty(skuMap.values())) {
                    orderedSkus.addAll(skuMap.values());
                }
                return orderedSkus;
            } else if (skuList.get(0).getAttributions().size() == 2) {
                //商品是二维sku
                //将sku存入三元组map,key1为style(款式)、key2为size(尺码)
                Map<String, Map<String, ItemSkuDO>> skuMap = new LinkedHashMap<>();
                for (ItemSkuDO skuDO : skuList) {
                    String styleKey = null, sizeKey = null;
                    for (SkuAttributionDO attr : skuDO.getAttributions()) {
                        if (attr.getShowPosition() == 0) {
                            styleKey = attr.getValue();
                        } else {
                            sizeKey = attr.getValue();
                        }
                    }
                    Map<String, ItemSkuDO> sizeSkuMap = skuMap.get(styleKey);
                    if (sizeSkuMap == null) {
                        sizeSkuMap = new LinkedHashMap<>();
                        skuMap.put(styleKey, sizeSkuMap);
                    }
                    sizeSkuMap.put(sizeKey, skuDO);
                }

                //对照标准属性表,将sku重新塞入list
                for (BaseValue styleValue : categorySkuDTO.getStyleValueList()) {
                    if (!skuMap.containsKey(styleValue.getValue())) {
                        continue;
                    }
                    for (BaseValue sizeValue : categorySkuDTO.getSizeValueList()) {
                        if (skuMap.get(styleValue.getValue()) != null
                                && skuMap.get(styleValue.getValue()).get(sizeValue.getValue()) != null) {
                            Map<String, ItemSkuDO> sizeSkuMap = skuMap.get(styleValue.getValue());
                            orderedSkus.add(sizeSkuMap.get(sizeValue.getValue()));
                            sizeSkuMap.remove(sizeValue.getValue());
                            if (sizeSkuMap.isEmpty()) {
                                skuMap.remove(styleValue.getValue());
                            }
                        }
                    }
                }
                // 自定义style属性值的sku,也存入list
                for (Map<String, ItemSkuDO> map : skuMap.values()) {
                    for (BaseValue sizeValue : categorySkuDTO.getSizeValueList()) {
                        if (map.get(sizeValue.getValue()) != null) {
                            orderedSkus.add(map.get(sizeValue.getValue()));
                            map.remove(sizeValue.getValue());
                        }
                    }
                }
                // 剩下的是自定义size属性值的sku,一起存入list尾部
                for (Map<String, ItemSkuDO> map : skuMap.values()) {
                    if (!org.apache.commons.collections4.CollectionUtils.isEmpty(map.values())) {
                        orderedSkus.addAll(map.values());
                    }
                }
                return orderedSkus;
            }

        } catch (Throwable e) {
            logger.error("order sku attrs error", e);
        }
        return skuList;
    }

}
