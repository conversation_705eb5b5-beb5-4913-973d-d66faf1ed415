package com.mogujie.detail.before.common.adt;

import com.mogujie.detail.before.common.constant.ItemTag;
import com.mogujie.tesla.common.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by xiaoyao on 15/12/29.
 */
public class DetailContextHolder {

    private static final ThreadLocal<Map<String, String>> PARAMS = new ThreadLocal<>();

    private static final ThreadLocal<Map<String, AtomicInteger>> SPI_RECORDS = new ThreadLocal<>();

    private static final ThreadLocal<RouteTagWrapper> ROUTE_TAG = new ThreadLocal<>();

    private static final ThreadLocal<Map<Class, Object>> SPI_CACHE = new ThreadLocal<>();

    public static final String ESI = "esi";

    public static final String USER_ID = "userId";

    public static final String PRE_HOS_IP = "preHostIp";

    public static final String VERSION = "version";

    public static final String APP_PLAT = "appPlat";

    public static void setAllRequestParams(HttpServletRequest request) {
        Map<String, String> params = PARAMS.get();
        if (null == params) {
            params = new HashMap<String, String>();
            PARAMS.set(params);
        }
        Enumeration<String> nameEnum = request.getParameterNames();
        while (nameEnum.hasMoreElements()) {
            String paramName = nameEnum.nextElement();
            params.put(paramName, request.getParameter(paramName));
        }
    }

    public static String get(String key) {
        if (CollectionUtils.isEmpty(PARAMS.get())) {
            return null;
        }
        return PARAMS.get().get(key);
    }

    public static void set(String key, String val) {
        Map<String, String> params = PARAMS.get();
        if (null == params) {
            params = new HashMap<String, String>();
            PARAMS.set(params);
        }
        params.put(key, val);
    }

    public static RouteTagWrapper getRouteTag() {
        return ROUTE_TAG.get();
    }

    public static void setRouteTag(TopoRoute route, List<ItemTag> itemTags) {
        ROUTE_TAG.set(new RouteTagWrapper(route, itemTags));
    }

    public static void addTag(ItemTag itemTag) {
        ROUTE_TAG.get().addTag(itemTag);
    }

    public static void setAll(Map<String, String> params) {
        PARAMS.set(params);
    }

    public static boolean isESI() {
        Map<String, String> params = PARAMS.get();
        if (null == params) {
            return false;
        }
        String esiStr = params.get(ESI);
        return esiStr == null ? false : "true".equals(esiStr);
    }

    public static Long getUserId() {
        Map<String, String> params = PARAMS.get();
        if (null == params) {
            return null;
        }
        String userIdStr = params.get(USER_ID);
        return userIdStr == null ? null : Long.valueOf(userIdStr);
    }

    public static void clear() {
        PARAMS.remove();
        SPI_RECORDS.remove();
        ROUTE_TAG.remove();
        SPI_CACHE.remove();
    }

    public static Map<String, String> getContext() {
        return PARAMS.get();
    }

    public static Boolean isSpiProxied(String spiMethod) {
        Map<String, AtomicInteger> spiRecords = SPI_RECORDS.get();
        if (null == spiRecords) {
            spiRecords = new HashMap<>(10);
            SPI_RECORDS.set(spiRecords);
            return false;
        }
        if (null == spiRecords.get(spiMethod)) {
            spiRecords.put(spiMethod, new AtomicInteger(0));
            return false;
        } else {
            boolean ret =  spiRecords.get(spiMethod).get()%2 == 1;
            spiRecords.get(spiMethod).incrementAndGet();
            return ret;
        }
    }

    public static void setSpiProxied(String spiMethod) {
        Map<String, AtomicInteger> spiRecords = SPI_RECORDS.get();
        if (null == spiRecords) {
            spiRecords = new HashMap<>(10);
            SPI_RECORDS.set(spiRecords);
        }
        AtomicInteger counter = spiRecords.get(spiMethod);
        if (null == counter) {
            spiRecords.put(spiMethod, new AtomicInteger(1));
        }
    }

    public static void setSpiCache(Class spiInterf, Object impl) {
        Map<Class, Object> spiCache = SPI_CACHE.get();
        if (null == spiCache) {
            spiCache = new HashMap<>();
            SPI_CACHE.set(spiCache);
        }
        spiCache.put(spiInterf, impl);
    }

    public static Object getSpiCache(Class spiInterf) {
        if (null == SPI_CACHE.get()) {
            return null;
        } else {
            return SPI_CACHE.get().get(spiInterf);
        }
    }


    public static class RouteTagWrapper {

        private List<ItemTag> itemTags;

        private TopoRoute route;

        public RouteTagWrapper(TopoRoute route, List<ItemTag> itemTags) {
            this.route = route;
            this.itemTags = new ArrayList<>(itemTags);
        }

        public List<ItemTag> getItemTags() {
            return itemTags;
        }

        public void addTag(ItemTag itemTag) {
            this.itemTags.add(itemTag);
        }

        public void setItemTags(List<ItemTag> itemTags) {
            this.itemTags = itemTags;
        }

        public TopoRoute getRoute() {
            return route;
        }

        public void setRoute(TopoRoute route) {
            this.route = route;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (!(o instanceof RouteTagWrapper)) return false;

            RouteTagWrapper that = (RouteTagWrapper) o;

            if (getItemTags() != null ? !getItemTags().equals(that.getItemTags()) : that.getItemTags() != null)
                return false;
            return getRoute() != null ? getRoute().equals(that.getRoute()) : that.getRoute() == null;

        }

        @Override
        public int hashCode() {
            int result = getItemTags() != null ? getItemTags().hashCode() : 0;
            result = 31 * result + (getRoute() != null ? getRoute().hashCode() : 0);
            return result;
        }

        @Override
        public String toString() {
            return "RouteTagWrapper{" +
                    "itemTags=" + itemTags +
                    ", route=" + route +
                    '}';
        }
    }

}
