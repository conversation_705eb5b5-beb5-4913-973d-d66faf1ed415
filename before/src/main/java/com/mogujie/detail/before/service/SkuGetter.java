package com.mogujie.detail.before.service;

import com.mogujie.detail.before.common.adt.CommonItem;
import com.mogujie.service.item.api.basic.SkuReadService;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.service.item.domain.external.IcException;
import com.mogujie.service.item.domain.query.QuerySkuOptions;
import com.mogujie.service.item.domain.result.BaseResultDO;
import com.mogujie.stable.spirit.EntryUtil;
import com.mogujie.stable.spirit.exception.BlockException;
import com.mogujie.stable.spirit.limit.Entry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Created by anshi on 17/8/22.
 */
@Component
public class SkuGetter {
    private static final Logger LOGGER = LoggerFactory.getLogger(SkuGetter.class);

    @Autowired
    private SkuReadService skuReadService;

    public List<ItemSkuDO> getChannelSkus(CommonItem commonItem, Integer channelId, Integer actId) {
        QuerySkuOptions options = new QuerySkuOptions();
        options.setMarketplaceId(8);
        options.setQuerySkuAttribute(true);
        options.setQueryInventory(true);
        options.setChannelId(channelId);
        options.setActivityId(Long.valueOf(actId));
        Entry entry = null;
        try {
            entry = EntryUtil.entry("com.mogujie.service.item.api.basic.SkuReadService:queryItemSkuDOByItemId");
            BaseResultDO<List<ItemSkuDO>> result = skuReadService.queryItemSkuDOByItemId(commonItem.getTradeItemId(), options);
            if (result != null && result.isSuccess() && result.getResult() != null) {
                return result.getResult();
            }
        } catch (BlockException e) {
        } catch (IcException e) {
            return Collections.emptyList();
        } catch (Throwable e) {
            LOGGER.error("tesla calling failed. ", e);
        } finally {
            if (entry != null) {
                entry.exit();
            }
        }
        return new ArrayList<>();
    }

    public List<ItemSkuDO> getInStoreSkus(CommonItem commonItem) {
        return getChannelSkus(commonItem, 2016, 0);
    }

    public List<ItemSkuDO> getNormalSkus(CommonItem commonItem) {
        Entry entry = null;
        try {
            entry = EntryUtil.entry("com.mogujie.service.item.api.SkuService:getItemSkus");
            BaseResultDO<List<ItemSkuDO>> result = skuReadService.getItemSkus(commonItem.getTradeItemId());
            if (result!=null && result.isSuccess() && result.getResult()!=null){
                return result.getResult();
            }
        } catch (BlockException e) {
        } catch (Throwable e) {
            LOGGER.error("tesla calling failed. ", e);
        } finally {
            if (entry != null) {
                entry.exit();
            }
        }
        return new ArrayList<>();
    }
}
