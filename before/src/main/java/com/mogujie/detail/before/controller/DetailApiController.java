package com.mogujie.detail.before.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.meili.service.shopcenter.api.ShopReadService;
import com.meili.service.shopcenter.domain.entity.ShopInfo;
import com.mogujie.commons.utils.EmojiUtil;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.before.common.adt.CommonItem;
import com.mogujie.detail.before.common.adt.DecorateResult;
import com.mogujie.detail.before.common.adt.FastbuyInfo;
import com.mogujie.detail.before.common.adt.InstallmentData;
import com.mogujie.detail.before.common.adt.ItemTag;
import com.mogujie.detail.before.common.adt.LimitInfo;
import com.mogujie.detail.before.common.adt.PresaleInfo;
import com.mogujie.detail.before.common.adt.PropInfo;
import com.mogujie.detail.before.common.adt.RetData;
import com.mogujie.detail.before.common.adt.SizePropData;
import com.mogujie.detail.before.common.adt.SkuData;
import com.mogujie.detail.before.common.adt.SkuInfo;
import com.mogujie.detail.before.common.adt.SkuInfoDataV2;
import com.mogujie.detail.before.common.adt.SkuInfoForApi;
import com.mogujie.detail.before.common.adt.SkuQueryOption;
import com.mogujie.detail.before.common.adt.Status;
import com.mogujie.detail.before.common.adt.StoreType;
import com.mogujie.detail.before.common.adt.StylePropData;
import com.mogujie.detail.before.common.constant.DiscountType;
import com.mogujie.detail.before.core.util.ImageUtil;
import com.mogujie.detail.before.core.util.ItemFetcher;
import com.mogujie.detail.before.core.util.NumUtil;
import com.mogujie.detail.before.core.util.TagConvertUtil;
import com.mogujie.detail.before.service.SkuGetter;
import com.mogujie.detail.before.service.SkuReorder;
import com.mogujie.marketing.ferrari.api.RushInfoForDetailService;
import com.mogujie.marketing.ferrari.api.dto.RushInfoDTO;
import com.mogujie.marketing.ferrari.api.dto.RushInfoResultDTO;
import com.mogujie.metabase.spring.client.MetabaseClient;
import com.mogujie.pay.common.dto.Response;
import com.mogujie.pay.mailo.api.MaiLoInstallmentApi;
import com.mogujie.pay.mailo.api.MaiLoUserBasicInfoApi;
import com.mogujie.pay.mailo.dto.InstallmentInfoDTO;
import com.mogujie.pay.mailo.dto.MaiLoReqDTO;
import com.mogujie.pay.mailo.dto.UserBasicInfoDTO;
import com.mogujie.pay.mailo.dto.request.parameters.ComputeInstallmentRequestDTO;
import com.mogujie.service.hummer.api.PromotionReadService;
import com.mogujie.service.hummer.constains.PromotionConstants;
import com.mogujie.service.hummer.constains.RequestConstants;
import com.mogujie.service.hummer.domain.dto.CampaignInfo;
import com.mogujie.service.hummer.domain.dto.InvokeInfo;
import com.mogujie.service.hummer.domain.dto.ItemDetailPromotion;
import com.mogujie.service.hummer.domain.dto.ItemDetailRequestV2;
import com.mogujie.service.hummer.domain.dto.Pbuyer;
import com.mogujie.service.hummer.domain.dto.PitemDetail;
import com.mogujie.service.hummer.domain.dto.Pseller;
import com.mogujie.service.hummer.domain.dto.result.Result;
import com.mogujie.service.hummer.utils.PromotionConvertUtils;
import com.mogujie.service.hummer.utils.SystemDiscountChecker;
import com.mogujie.service.hummer.utils.Utils;
import com.mogujie.service.inventory.api.InventoryReadService;
import com.mogujie.service.inventory.domain.ActivityInventory;
import com.mogujie.service.inventory.domain.param.BatchActivityInventoryQueryParam;
import com.mogujie.service.inventory.domain.result.MapResult;
import com.mogujie.service.item.api.basic.ItemPreSaleService;
import com.mogujie.service.item.domain.basic.ItemPreSaleDO;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.service.item.domain.basic.SkuAttributionDO;
import com.mogujie.service.shopcenter.util.AuthorizedShopUtil;
import com.mogujie.service.tagcenter.api.read.ItemTagReadService;
import com.mogujie.service.tagcenter.domain.entity.query.ItemTagQueryOption;
import com.mogujie.service.tagcenter.domain.entity.result.BaseResultDO;
import com.mogujie.service.tagcenter.domain.entity.result.ItemTagDO;
import com.mogujie.session.SessionContextHolder;
import com.mogujie.tagcenter.client.item.ItemDetailTagUtils;
import com.mogujie.tagcenter.client.item.domain.ItemServiceTag;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by xiaoyao on 16/5/26.
 *
 * 备注：2020年万象项目，详情页合并，将appDetail和detailSkip两个老的详情页应用中部分接口进行暴力平行迁移，统一放在before module中。
 */
@Slf4j
@Controller
public class DetailApiController {

    private static final String MAIN_PRICE_DESC = "定金:";

    private static final String SUB_PRICE_DESC = "总价:";

    private static final Logger LOGGER = LoggerFactory.getLogger(DetailApiController.class);

    @Autowired
    private ItemFetcher itemFetcher;


    @Autowired
    private PromotionReadService promotionReadService;

    @Autowired
    private MaiLoInstallmentApi maiLoInstallmentApi;

    @Autowired
    private MaiLoUserBasicInfoApi maiLoUserBasicInfoApi;

    @Autowired
    private ItemPreSaleService preSaleService;

    private ShopReadService shopReadService;

    private InventoryReadService inventoryReadService;

    private RushInfoForDetailService rushInfoForDetailService;

    private ItemTagReadService itemTagService;

    private Gson gson;

    @Resource(name = "detailSwitchConf")
    private MetabaseClient metabaseSwitch;

    @Resource(name = "confMetabaseClient")
    protected MetabaseClient metabaseClient;

    @Autowired
    private SkuReorder skuReorder;

    @Autowired
    private SkuGetter skuGetter;

    @PostConstruct
    public void init() {
        gson = new Gson();
        try {
            shopReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ShopReadService.class);
            inventoryReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(InventoryReadService.class);
            rushInfoForDetailService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(RushInfoForDetailService.class);
            itemTagService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ItemTagReadService.class);
        } catch (Exception e) {
            LOGGER.error("init service failed : {}", e);
        }
    }


    @RequestMapping(value = "/trade/item/detail/api/getSkusV3", produces = {"application/json;charset=UTF-8"})
    @ResponseBody
    public String getSkusV3(HttpServletRequest request) {
        SkuQueryOption option = convertOption(request);
        return getSkuData(option, 3);
    }


    private String getSkuData(SkuQueryOption option, Integer version) {
        String strItemId = option.getItemId();
        String strChannel = option.getChannel();
        String strActivityId = option.getActivityId();
        String strSkuNum = option.getSkuNum();
        String strCaller = option.getCaller();
        String marketStr = option.getMarket();

        String strAV = option.getAv();
        String strAType = option.getAtype();
        Long skuNum = null;
        if (!StringUtils.isBlank(strSkuNum)) {
            skuNum = Long.parseLong(strSkuNum);
        }
        String caller = StringUtils.isEmpty(strCaller) ? "pintuan" : strCaller;
        RetData<Object> ret = new RetData();
        if (StringUtils.isEmpty(strItemId)) {
            ret.setStatus(new Status(4004, "itemId not set"));
            ret.setResult(null);
            return gson.toJson(ret);
        }
        Long itemId = IdConvertor.urlToId(strItemId);
        Long activityId = null;
        if (!StringUtils.isEmpty(strActivityId)) {
            activityId = IdConvertor.urlToId(strActivityId);
        }

        if (null == itemId) {
            ret.setStatus(new Status(4004, "invalid itemId"));
            ret.setResult(null);
            return gson.toJson(ret);
        }

        CommonItem commonItem = itemFetcher.getCommonItem(itemId);
        if (null == commonItem) {
            ret.setStatus(new Status(4004, "该商品暂时无法购买"));
            ret.setResult(null);
            return gson.toJson(ret);
        }
        try {
            if (this.isMedicalItem(commonItem)
                    && StringUtils.isNotBlank(option.getAv())
                    && Integer.parseInt(option.getAv()) < 1030) {
                ret.setStatus(new Status(4004, "该商品暂时无法购买"));
                ret.setResult(null);
                return gson.toJson(ret);
            }
        } catch (Throwable e) {
        }

        int market = 8;
        if (!StringUtils.isEmpty(marketStr) && StringUtils.isNumeric(marketStr)) {
            try {
                market = Integer.parseInt(marketStr);
            } catch (Throwable e) {
                LOGGER.error("parse market failed");
            }
        }

        RushInfoDTO rushInfo = null;
        if ("kq".equals(strChannel) && null != activityId) {
            rushInfo = getRushInfo(activityId);
        }

        StoreType storeType = getStoreType(commonItem);
        List<ItemSkuDO> skus = null;
        if (StringUtils.isEmpty(strChannel) && StoreType.INSTORE == storeType) {
            skus = skuGetter.getInStoreSkus(commonItem);
        } else {
            skus = skuGetter.getNormalSkus(commonItem);
        }
        if (null != rushInfo) {
            decorateFastbuyInventory(skus, IdConvertor.urlToId(strActivityId));
        }
        commonItem.setSkus(skus);
        // sku属性重排列
        commonItem.setSkus(skuReorder.reorderSkus(commonItem));
        commonItem.setTotalStock(getTotalStock(commonItem));
        if (commonItem.getProcessType() == 1 && !"kq".equals(strChannel)) {
            commonItem.setItemPreSale(preSaleService.queryByItemId(itemId));
        }
        if (!canBuy(commonItem)) {
            ret.setStatus(new Status(4004, "该商品暂时无法购买"));
            ret.setResult(null);
            return gson.toJson(ret);
        }

        if (CollectionUtils.isEmpty(skus)) {
            LOGGER.error("Item : {} has no skus", commonItem.getTradeItemId());
            ret.setStatus(new Status(4004, "no sku found"));
            ret.setResult(null);
            return gson.toJson(ret);
        }
        Integer limitNum = null;
        SkuInfoForApi skuInfo = new SkuInfoForApi();
        skuInfo.setTitle(EmojiUtil.decode(commonItem.getTitle()));
        skuInfo.setImg(ImageUtil.img(commonItem.getImage()));
        Integer disType = 0;
        if (3 == version || 4 == version) {
            DecorateResult decorateResult = this.decorateSkuAndPriceV3(commonItem, rushInfo, market, skuNum, version, caller);
            limitNum = decorateResult.getLimitNum();
            disType = decorateResult.getDisType();
        } else {
            this.decorateSkuAndPriceV2(commonItem, rushInfo, market);
        }

        this.parseExtra(skuInfo, commonItem.getExtra());

        this.parseSku(skuInfo, commonItem, version);

        this.updateSkuData(skuInfo, commonItem);

        //获取商品标
        String key = "itemTags_new:" + commonItem.getTradeItemId();
        List<ItemTag> itemTags = null;
        boolean itemTagsSwitch = true;
        if ((null == itemTags || itemTags.isEmpty())) {
            List<ItemServiceTag> serviceTags = ItemDetailTagUtils.queryItemServiceTag(commonItem.getTradeItemId(), commonItem.getExtra());
            itemTags = this.convert(serviceTags);
        }

        ShopInfo shopInfo = null;
        try {
            com.meili.service.shopcenter.result.Result<ShopInfo> resultSupport = shopReadService.queryShopByShopId(commonItem.getShopId());
            shopInfo = resultSupport.getData();
        } catch (Throwable e) {
        }
        if (canInstallment(commonItem, shopInfo)) {
            String tags = null;
            Map<String, String> tagMap = this.getExtraInfo(commonItem.getExtra());
            if (tagMap != null && StringUtils.isNotBlank(tagMap.get("tags"))) {
                tags = tagMap.get("tags");
            }
            String shopTags = shopInfo == null ? null : shopInfo.getTags();
            decorateInstallment(skuInfo.getSkus(), tags, shopTags);
        }
        SkuInfoDataV2 skuInfoData = new SkuInfoDataV2();
        skuInfoData.setExpireSeconds(0);
        skuInfoData.setData(skuInfo);
        skuInfoData.setIsPreSale(isPreSale(commonItem));
        skuInfoData.setItemTags(itemTags);
        skuInfoData.setDisType(disType);
        if (decorateFastbuy(skuInfoData, rushInfo, commonItem)
                || decoratePresale(skuInfoData, commonItem)) {
        }

        LimitInfo limitInfo = getLimitInfo(commonItem.getExtra());
        if (null != limitInfo && -1L != SessionContextHolder.getUserId()) {
            skuInfo.setLimitTotalStock(limitInfo.getLimitTotalStock());
        }
        if (null != limitNum) {
            skuInfo.setLimitNum(limitNum);
            if (null != limitInfo) {
                skuInfo.setLimitDesc("单次购买" + limitNum + "件以内享优惠，超过则恢复原价");
                updateLimitStock(skuInfo, limitInfo.getActivityId());
            }
        }

        if (!StringUtils.isEmpty(strAV) && strAV.equals("940") && !StringUtils.isEmpty(strAType) && strAType.equals("android")) {
            if (skuInfo.getStyleKey() == null && !StringUtils.isEmpty(skuInfo.getSizeKey())) {
                skuInfo.getProps().add(0, new PropInfo());
            }
        }

        //此处设置presale、activityType、tuanInfo、fastbuyInfo
        ret.setResult(skuInfoData);
        ret.setStatus(new Status(1001, ""));
        return gson.toJson(ret);
    }

    private void updateLimitStock(SkuInfoForApi skuInfo, Long activityId) {
        List<Long> skuIdList = new ArrayList<>();
        for (SkuData skuData : skuInfo.getSkus()) {
            skuIdList.add(IdConvertor.urlToId(skuData.getStockId()));
        }
        BatchActivityInventoryQueryParam param = new BatchActivityInventoryQueryParam();
        param.setSkuIds(skuIdList);
        param.setChannelId(2014);
        param.setActivityId(activityId.intValue());
        MapResult<Long, ActivityInventory> inventoryMapResult = inventoryReadService.batchQueryActivityInventory(param);
        if (null != inventoryMapResult && inventoryMapResult.isSuccess()) {
            Map<Long, ActivityInventory> inventoryMap = inventoryMapResult.getData();
            if (null != inventoryMap) {
                for (SkuData sku : skuInfo.getSkus()) {
                    ActivityInventory inventory = inventoryMap.get(IdConvertor.urlToId(sku.getStockId()));
                    sku.setStock(null == inventory ? 0 : inventory.getStock());
                }
            }
        }
    }

    private LimitInfo getLimitInfo(String extra) {
        if (StringUtils.isEmpty(extra)) {
            return null;
        }
        try {
            Gson gson = new Gson();
            Map<String, String> extraInfo = gson.fromJson(extra, HashMap.class);
            if (null == extraInfo || extraInfo.isEmpty()) {
                return null;
            }
            String fl = extraInfo.get("fl");
            if (com.mogujie.metabase.utils.StringUtils.isEmpty(fl)) {
                return null;
            }
            String[] flPairs = fl.split("\\|");
            LimitInfo limitInfo = new LimitInfo();
            for (String flPair : flPairs) {
                String[] pair = flPair.split(":");
                if (pair.length != 2) {
                    continue;
                }
                if ("ai".equals(pair[0])) {
                    limitInfo.setActivityId(Long.parseLong(pair[1]));
                } else if ("xg".equals(pair[0])) {
                    limitInfo.setLimitTotalStock(Integer.parseInt(pair[1]));
                } else if ("st".equals(pair[0])) {
                    if ((System.currentTimeMillis() / 1000) < Integer.parseInt(pair[1])) {
                        return null;
                    }
                } else if ("et".equals(pair[0])) {
                    if ((System.currentTimeMillis() / 1000) > Integer.parseInt(pair[1])) {
                        return null;
                    }
                }
            }

            return limitInfo;
        } catch (Throwable e) {
            LOGGER.error("parse extra.tags failed : {}. {}", extra, e);
        }
        return null;
    }

    public static Boolean decorateFastbuy(SkuInfoDataV2 skuInfoData, RushInfoDTO rushInfo, CommonItem commonItem) {
        if (null != rushInfo) {
            int nowTime = (int) (System.currentTimeMillis() / 1000);
            FastbuyInfo fastbuyInfo = new FastbuyInfo();
            fastbuyInfo.setCountdown(rushInfo.getEndTime() - nowTime);
            fastbuyInfo.setStartTime(rushInfo.getStartTime());
            fastbuyInfo.setEndTime(rushInfo.getEndTime());
            fastbuyInfo.setCountdownDesc("距快抢结束还剩 ：");
            fastbuyInfo.setState(getFastbuyState(rushInfo, commonItem.getTotalStock()));
            skuInfoData.setFastbuyInfo(fastbuyInfo);
            skuInfoData.setActivityType(3);
            skuInfoData.getData().setPriceDesc("快抢价");
            return true;
        }
        return false;
    }

    public static int getFastbuyState(RushInfoDTO rushInfo, Integer totalStock) {
        if (totalStock > 0) {
            return 1;
        }
        return rushInfo.getUnPay() > 0 ? 4 : 2;
    }

    public static Boolean decoratePresale(SkuInfoDataV2 skuInfoData, CommonItem commonItem) {
        if (skuInfoData.getIsPreSale()) {
            ItemPreSaleDO preSale = commonItem.getItemPreSale();
            int nowTime = (int) (System.currentTimeMillis() / 1000);
            PresaleInfo presaleInfo = new PresaleInfo();
            presaleInfo.setDepositCountDownTime(preSale.getEnd() - nowTime);
            presaleInfo.setStartTime(preSale.getStart());
            presaleInfo.setEndTime(preSale.getEnd());
            presaleInfo.setPayStartTime(preSale.getPriceStart());
            presaleInfo.setComment("注: 定金一旦支付,非卖家原因不予退回。");
            skuInfoData.setPresale(presaleInfo);
            skuInfoData.setActivityType(1);
            return true;
        }
        return false;
    }

    public static StoreType getStoreType(CommonItem commonItem) {
        JSONObject feature;
        String extra = commonItem.getExtra();
        try {
            feature = extra != null && !"".equals(extra) ? JSON.parseObject(extra) : new JSONObject();
        } catch (ClassCastException var3) {
            feature = new JSONObject();
        }
        JSONObject whTag = feature.getJSONObject("wh");
        if (whTag != null) {
            if ("INBOUND".equals(whTag.getString("turnoverType"))) {
                return StoreType.INSTORE;
            } else if ("JIT".equals(whTag.getString("turnoverType"))) {
                return StoreType.TRANSFER;
            }
        }
        return StoreType.NORMAL;
    }

    private RushInfoDTO getRushInfo(Long fastbuyId) {
        if (null != fastbuyId) {
            try {
                RushInfoResultDTO rushRet = rushInfoForDetailService.getRushInfo(fastbuyId);
                if (null != rushRet && rushRet.isSucc()) {
                    RushInfoDTO rushInfo = rushRet.getRushInfoDTO();
                    int nowTime = (int) (System.currentTimeMillis() / 1000);
                    if (rushInfo.getStartTime() < nowTime && rushInfo.getEndTime() > nowTime) {
                        return rushInfo;
                    }
                }
            } catch (Throwable e) {
                LOGGER.error("call rush info failed", e);
            }
        }
        return null;
    }

    private void decorateFastbuyInventory(List<ItemSkuDO> skus, Long fastbuyId) {
        List<Long> skuIdList = new ArrayList<>(skus.size());
        for (ItemSkuDO sku : skus) {
            skuIdList.add(sku.getSkuId());
        }
        BatchActivityInventoryQueryParam param = new BatchActivityInventoryQueryParam();
        param.setSkuIds(skuIdList);
        param.setChannelId(1);
        param.setActivityId(fastbuyId.intValue());
        MapResult<Long, ActivityInventory> inventoryMapResult = inventoryReadService.batchQueryActivityInventory(param);
        if (null != inventoryMapResult && inventoryMapResult.isSuccess()) {
            Map<Long, ActivityInventory> inventoryMap = inventoryMapResult.getData();
            if (null != inventoryMap) {
                for (ItemSkuDO sku : skus) {
                    ActivityInventory inventory = inventoryMap.get(sku.getSkuId());
                    sku.setQuantity(null == inventory ? 0 : inventory.getStock());
                }
            }
        }
    }

    private int getTotalStock(CommonItem commonItem) {
        int totalStock = 0;
        for (ItemSkuDO sku : commonItem.getSkus()) {
            totalStock += sku.getQuantity();
        }
        return totalStock;
    }

    private int decorateSkuAndPriceV2(CommonItem item, RushInfoDTO rushInfo, int market) {
        int retVal = 0; //0 表示普通， 1 表示拼团
        try {
            Map<Long, Long> originSkuMap = getSkuPriceMap(item);
            Map<Long, Long> realSkuMap = null;
            Long realPrice = null;
            String decorate = null;
            if (null != rushInfo) {
                realSkuMap = new HashMap<>(item.getSkus().size());
                for (ItemSkuDO sku : item.getSkus()) {
                    Long rp = (rushInfo.getActivityDiscount() != 1000 && 0 != rushInfo.getActivityDiscount()) ?
                            Long.valueOf((new BigDecimal(sku.getPrice()).multiply(new BigDecimal(rushInfo.getActivityDiscount()))
                                    .divide(new BigDecimal(1000), RoundingMode.DOWN).longValue())) : rushInfo.getSalePrice();
                    realSkuMap.put(sku.getSkuId(), rp);
                }
                realPrice = (long) rushInfo.getSalePrice();
            } else if (useLocalDiscount()) {
                SystemDiscountChecker.SystemDiscountRes ret = SystemDiscountChecker.calcSystemDiscount(item.getExtra(), item.getPrice().longValue(), false, (int) RequestConstants.Market.MOGUJIE, originSkuMap);
                if (null != ret) {
                    realSkuMap = ret.getSkuRalPrice();
                    realPrice = ret.getRealPrice();
                    decorate = ret.getName();
                }
            }


            if (null == realSkuMap || null == realPrice) {
                ItemDetailRequestV2 request = new ItemDetailRequestV2();
                Pbuyer pbuyer = new Pbuyer();
                pbuyer.setBuyerId(SessionContextHolder.getUserId());
                Pseller pSeller = new Pseller();
                pSeller.setSellerId(item.getUserId().longValue());
                PitemDetail pitemDetail = new PitemDetail();
                pitemDetail.setExtra(item.getExtra());
                pitemDetail.setItemId(item.getTradeItemId().longValue());
                pitemDetail.setSkuPriceMap(originSkuMap);
                pitemDetail.setItemPrice(getHighestPrice(item.getSkus()));
                InvokeInfo invokeInfo = new InvokeInfo();
                invokeInfo.setChannel((int) RequestConstants.Channel.UNKNOW);
                invokeInfo.setMarket(market);
                invokeInfo.setSource(RequestConstants.Source.DETAIL);
                invokeInfo.setTerminal(RequestConstants.Terminal.APP);
                request.setPitemDetail(pitemDetail);
                request.setSeller(pSeller);
                request.setPbuyer(pbuyer);
                request.setInvokeInfo(invokeInfo);

                Result<ItemDetailPromotion> ret = promotionReadService.calcForItemDetailPromotion(request);
                if (null != ret && ret.isSuccess() && null != ret.getData()) {
                    decorate = ret.getData().getDecorate();
                    realSkuMap = ret.getData().getSkuRealPriceMap();
                    realPrice = ret.getData().getItemRealPrice();
                }
            }
            if (null != realSkuMap && null != realPrice) {
                item.setDiscountDesc(decorate);
                filterSku(item, realSkuMap, realPrice);
            } else {
                item.setDiscountDesc("");
                filterSku(item, originSkuMap, item.getPrice().longValue());
            }
        } catch (Throwable e) {
            LOGGER.error("get discount info failed : {}", e);
            filterSku(item, new HashMap<Long, Long>(), null);
        }
        return retVal;
    }


    private DecorateResult decorateSkuAndPriceV3(CommonItem item, RushInfoDTO rushInfo, int market, Long skuNum, int version, String caller) {
        DecorateResult decorateResult = new DecorateResult();
        try {
            Map<Long, Long> originSkuMap = getSkuPriceMap(item);
            Map<Long, Long> realSkuMap = null;
            Long realPrice = null;
            String decorate = null;
            ItemTagQueryOption option = new ItemTagQueryOption();
            option.setItemId(item.getTradeItemId().longValue());
            option.setDirectQueryDB(false);
            BaseResultDO<List<ItemTagDO>> tagRet = itemTagService.queryItemTag(option);
            boolean isPinTuan = false;
            if (null != tagRet && tagRet.isSuccess() && !com.mogujie.metabase.utils.CollectionUtils.isEmpty(tagRet.getResult())) {
                List<ItemTagDO> tags = tagRet.getResult();
                if (4 == version && !CollectionUtils.isEmpty(tags) && "pintuan".equals(caller) && !isPreStart(item)) {
                    for (ItemTagDO tag : tags) {
                        if (tag.getTagValue().equals("351")) {
                            isPinTuan = true;
                            break;
                        }
                    }
                }
            }

            if (null != rushInfo) {
                realSkuMap = new HashMap<>(item.getSkus().size());
                for (ItemSkuDO sku : item.getSkus()) {
                    Long rp = (rushInfo.getActivityDiscount() != 1000 && 0 != rushInfo.getActivityDiscount()) ?
                            Long.valueOf((new BigDecimal(sku.getPrice()).multiply(new BigDecimal(rushInfo.getActivityDiscount()))
                                    .divide(new BigDecimal(1000), RoundingMode.DOWN).longValue())) : rushInfo.getSalePrice();
                    realSkuMap.put(sku.getSkuId(), rp);
                }
                realPrice = (long) rushInfo.getSalePrice();
            } else if (!isFlItem(item.getExtra()) && isPinTuan && useLocalDiscount()) {
                SystemDiscountChecker.SystemDiscountRes ret = SystemDiscountChecker.calcSystemDiscount(item.getExtra(), item.getPrice().longValue(), false, (int) RequestConstants.Market.MOGUJIE, originSkuMap);
                if (null != ret) {
                    realSkuMap = ret.getSkuRalPrice();
                    realPrice = ret.getRealPrice();
                    decorate = ret.getName();
                }
            } else if (isPinTuan && useLocalDiscount()) {
                SystemDiscountChecker.SystemDiscountReq req = new SystemDiscountChecker.SystemDiscountReq();
                req.setExtra(item.getExtra());
                req.setOrgiPrice(item.getPrice().longValue());
                req.setIsDisplay(false);
                req.setMarket(RequestConstants.Market.MOGUJIE);
                req.setSkuPriceMap(originSkuMap);
                SystemDiscountChecker.SystemDiscountRes ret = SystemDiscountChecker.calcSystemDiscount(req);
                if (null != ret) {
                    realSkuMap = ret.getSkuRalPrice();
                    realPrice = ret.getRealPrice();
                    decorate = ret.getName();
                }
            }

            if (null == realSkuMap || null == realPrice) {
                ItemDetailRequestV2 request = new ItemDetailRequestV2();
                Pbuyer pbuyer = new Pbuyer();
                pbuyer.setBuyerId(SessionContextHolder.getUserId());
                Pseller pSeller = new Pseller();
                pSeller.setSellerId(item.getUserId().longValue());
                PitemDetail pitemDetail = new PitemDetail();
                pitemDetail.setExtra(item.getExtra());
                pitemDetail.setItemId(item.getTradeItemId().longValue());
                pitemDetail.setSkuPriceMap(originSkuMap);
                pitemDetail.setItemPrice(getHighestPrice(item.getSkus()));

                if (null != skuNum && skuNum > 0L) {
                    pitemDetail.setNumber(skuNum);
                }
                ItemTagQueryOption options = new ItemTagQueryOption();
                option.setItemId(item.getTradeItemId().longValue());
                option.setDirectQueryDB(false);
                BaseResultDO<List<ItemTagDO>> tRet = itemTagService.queryItemTag(options);
                if (null != tRet && tRet.isSuccess() && !CollectionUtils.isEmpty(tRet.getResult())) {
                    pitemDetail.setItemTagList(PromotionConvertUtils.convertItemTagToItemProTag(TagConvertUtil.convertTODO(tRet.getResult())));
                }
                if (isPinTuan) {
                    pitemDetail.setBuyType(RequestConstants.BuyType.GROUP_SHOPPING);
                }
                InvokeInfo invokeInfo = new InvokeInfo();
                invokeInfo.setChannel((int) RequestConstants.Channel.UNKNOW);
                invokeInfo.setMarket(market);
                invokeInfo.setSource(RequestConstants.Source.DETAIL);
                invokeInfo.setTerminal(RequestConstants.Terminal.APP);
                request.setPitemDetail(pitemDetail);
                request.setSeller(pSeller);
                request.setPbuyer(pbuyer);
                request.setInvokeInfo(invokeInfo);

                Result<ItemDetailPromotion> ret = promotionReadService.calcForItemDetailPromotion(request);
                if (null != ret && ret.isSuccess() && null != ret.getData()) {
                    CampaignInfo campaignInfo = ret.getData().getCampaigninfo();
                    if (null != campaignInfo) {
                        if (null != campaignInfo.getPromotionMark() && PromotionConstants.PromotionCode.GROUP_SHOPPING.equals(campaignInfo.getPromotionMark().getPromotionCode())) {
                            decorate = "拼团价";
                            decorateResult.setDisType(DiscountType.PINTUAN);
                        } else {
                            if (Utils.channelCheck(campaignInfo.getParameter(), RequestConstants.Channel.ZHIBO)) {
                                decorateResult.setDisType(DiscountType.LIVEPRICE);
                            }
                            Map<String, String> parameter = campaignInfo.getParameter();
                            if (null != parameter && !parameter.isEmpty()) {
                                String limitNum = parameter.get("itemCountLimit");
                                if (!com.mogujie.metabase.utils.StringUtils.isBlank(limitNum)) {
                                    decorateResult.setLimitNum(Integer.parseInt(limitNum));
                                }
                            }
                            decorate = ret.getData().getDecorate();
                        }
                    }
                    realSkuMap = ret.getData().getSkuRealPriceMap();
                    realPrice = ret.getData().getItemRealPrice();
                }
            }
            if (null != realSkuMap && null != realPrice) {
                item.setDiscountDesc(decorate);
                filterSku(item, realSkuMap, realPrice);
            } else {
                item.setDiscountDesc("");
                filterSku(item, originSkuMap, item.getPrice().longValue());
            }
        } catch (Throwable e) {
            LOGGER.error("get discount info failed : {}", e);
            filterSku(item, new HashMap<Long, Long>(), null);
        }
        return decorateResult;
    }

    private boolean isPreStart(CommonItem item) {
        Integer now = (int) (System.currentTimeMillis() / 1000);
        if (null != item.getStart() && now < item.getStart()) {
            return true;
        }
        return false;
    }

    private boolean useLocalDiscount() {
        try {
            Boolean useLocal = metabaseClient.getBoolean("discount_useLocal");
            if (null != useLocal) {
                return useLocal;
            }
        } catch (Exception e) {
            ;
        }
        return true;
    }

    private static void filterSku(CommonItem item, Map<Long, Long> realPriceMap, Long realPrice) {
        long lowPrice = item.getPrice();
        long highPrice = item.getPrice();
        long lowNowPrice = null == realPrice ? item.getPrice() : realPrice;
        long highNowPrice = null == realPrice ? item.getPrice() : realPrice;
        int totalStock = 0;
        for (ItemSkuDO sku : item.getSkus()) {

            if (sku.getPrice() < lowPrice) {
                lowPrice = sku.getPrice();
            }

            if (sku.getPrice() > highPrice) {
                highPrice = sku.getPrice();
            }
            Long skuRealPrice = realPriceMap.get(sku.getSkuId());
            if (null == skuRealPrice) {
                skuRealPrice = sku.getPrice();
            }
            sku.setNowPrice(skuRealPrice.intValue());
            if (skuRealPrice < lowNowPrice) {
                lowNowPrice = skuRealPrice;
            }

            if (skuRealPrice > highNowPrice) {
                highNowPrice = skuRealPrice;
            }

            totalStock += sku.getQuantity();
        }
        item.setLowPrice(NumUtil.formatNum(lowPrice / 100D));
        item.setLowNowPrice(NumUtil.formatNum(lowNowPrice / 100D));
        item.setHighPrice(NumUtil.formatNum(highPrice / 100D));
        item.setHighNowPrice(NumUtil.formatNum(highNowPrice / 100D));
        item.setTotalStock(totalStock);
        item.setPrice(lowPrice);
    }

    private boolean isPreSale(CommonItem commonItem) {
        if (null == commonItem.getItemPreSale()) {
            return false;
        }
        int currentTime = (int) (System.currentTimeMillis() / 1000);
        ItemPreSaleDO preSaleInfo = commonItem.getItemPreSale();
        if (preSaleInfo.getStart() < currentTime && currentTime < preSaleInfo.getEnd()) {
            return true;
        }
        return false;
    }

    protected boolean canBuy(CommonItem commonItem) {

        if (commonItem.getIsDeleted() == 1) {
            return false;
        }

        if (commonItem.getIsShelf() == 1) {
            return false;
        }

        if (commonItem.getStatus() == 1 || commonItem.getStatus() == 3 || commonItem.getStatus() < 0) {
            return false;
        }

        return true;
    }

    private void parseExtra(SkuInfo skuInfo, String extra) {
        if (org.apache.commons.lang.StringUtils.isEmpty(extra)) {
            skuInfo.setAbroad(false);
            skuInfo.setCounterPrice(null);
            return;
        }
        Map<String, Object> extraData = gson.fromJson(extra, Map.class);
        Object counterPrice = extraData.get("counterPrice");
        skuInfo.setAbroad(null != counterPrice);
        skuInfo.setCounterPrice(null != counterPrice ? String.valueOf(counterPrice) : null);
    }

    /**
     * 预售商品，需要重新刷一遍sku信息
     *
     * @param commonItem
     * @param skuInfo
     */
    private void updateSkuData(SkuInfo skuInfo, CommonItem commonItem) {
        ItemPreSaleDO itemPreSale = commonItem.getItemPreSale();
        int time = (int) (System.currentTimeMillis() / 1000);
        // 若预售时间已过，走普通商品逻辑
        if (itemPreSale == null || time > itemPreSale.getEnd()) {
            return;
        }

        // 刷价格
        int nowPrice = itemPreSale.getPrice();
        skuInfo.setPriceRange(formatPrice(nowPrice));
        skuInfo.setDefaultPrice(formatPrice(nowPrice));

        for (SkuData sku : skuInfo.getSkus()) {
            sku.setNowprice(nowPrice);
            sku.setMainPriceStr(MAIN_PRICE_DESC + formatPrice(itemPreSale.getDeposit()));
            sku.setSubPriceStr(SUB_PRICE_DESC + formatPrice(itemPreSale.getPrice()));
        }
        skuInfo.setMainPriceStr(MAIN_PRICE_DESC + formatPrice(itemPreSale.getDeposit()));
        skuInfo.setSubPriceStr(SUB_PRICE_DESC + formatPrice(itemPreSale.getPrice()));
        skuInfo.setMainDesc(MAIN_PRICE_DESC);
        skuInfo.setSubDesc(SUB_PRICE_DESC);
    }

    private String formatPrice(double price) {
        return "¥" + NumUtil.formatNum(price / 100D);
    }

    public void parseSku(SkuInfoForApi skuInfo, CommonItem commonItem, Integer version) {
        List<ItemSkuDO> skus = commonItem.getSkus();
        List<SkuData> skuDatas = new ArrayList<>();
        if (CollectionUtils.isEmpty(skus)) {
            return;
        }
        String styleKey = "";
        String sizeKey = "";
        boolean isSizeDefault = false;
        boolean isStyleDefault = false;
        List<String> tmpStyle = new ArrayList<>();
        List<String> tmpSize = new ArrayList<>();
        List<StylePropData> styles = new ArrayList<>();
        List<SizePropData> sizes = new ArrayList<>();
        int styleId = 1;
        int sizeId = 100;
        Map<String, Integer> styleMap = new HashMap<>();
        Map<String, Integer> sizeMap = new HashMap<>();

        int index = 0;

        int lowestPrice = 0;
        int highestPrice = 0;
        int lowestNowPrice = 0;
        int highestNowPrice = 0;
        Map<String, Map<String, String>> attrsMap = new HashMap<>();
        int totalStock = 0;
        for (ItemSkuDO sku : skus) {
            SkuData skuData = new SkuData();
            skuData.setCurrency("¥");
            skuData.setImg(StringUtils.isEmpty(sku.getImage()) ? ImageUtil.img(commonItem.getImage()) : ImageUtil.img(sku.getImage()));
            skuData.setNowprice(sku.getNowPrice());
            skuData.setPrice(sku.getPrice().intValue());
            skuData.setStock(sku.getQuantity());
            skuData.setXdSkuId(IdConvertor.idToUrl(sku.getXdSkuId()));
            skuData.setStockId(IdConvertor.idToUrl(sku.getSkuId()));
            attrsMap.put(skuData.getStockId(), parseProps(skuData, sku.getAttributions(), version));
            totalStock += skuData.getStock();
            if (version >= 4) {
                parseSizeType(skuData, sku.getAttributions());
            }
            skuDatas.add(skuData);
        }
        skuInfo.setTotalStock(totalStock);

        for (SkuData skuData : skuDatas) {
            Map<String, String> attrMap = attrsMap.get(skuData.getStockId());
            styleKey = attrMap.get("style");
            sizeKey = attrMap.get("size");
            String realStyle = attrMap.get("style") == null ? "" : (attrMap.get("style").equals(styleKey)) ? skuData.getStyle() : skuData.getSize();
            String realSize = attrMap.get("size") == null ? "" : (attrMap.get("size").equals(sizeKey)) ? skuData.getSize() : skuData.getStyle();
            skuData.setStyle(org.apache.commons.lang.StringUtils.isEmpty(realStyle) ? (version < 3 ? "默认" : null) : realStyle);
            skuData.setSize(org.apache.commons.lang.StringUtils.isEmpty(realSize) ? (version < 3 ? "均码" : null) : realSize);

            if (!org.apache.commons.lang.StringUtils.isEmpty(skuData.getStyle()) && !tmpStyle.contains(skuData.getStyle())) {
                tmpStyle.add(skuData.getStyle());
                if (org.apache.commons.lang.StringUtils.isEmpty(realStyle) && false == isStyleDefault) {
                    isStyleDefault = true;
                }
                StylePropData styleProp = new StylePropData();
                styleProp.setDefault(false);//org.apache.commons.lang.StringUtils.isEmpty(realStyle));
                styleProp.setType("style");
                styleProp.setName(skuData.getStyle());
                styleProp.setIndex(styleId);
                styleProp.setStyleId(styleId);
                styles.add(styleProp);
                styleMap.put(skuData.getStyle(), styleId);
                styleId++;
            }

            if (!org.apache.commons.lang.StringUtils.isEmpty(skuData.getSize()) && !tmpSize.contains(skuData.getSize())) {
                tmpSize.add(skuData.getSize());
                if (org.apache.commons.lang.StringUtils.isEmpty(realSize) && false == isSizeDefault) {
                    isSizeDefault = true;
                }

                SizePropData sizeProp = new SizePropData();
                sizeProp.setDefault(false);//org.apache.commons.lang.StringUtils.isEmpty(realSize));
                sizeProp.setType("size");
                sizeProp.setName(skuData.getSize());
                sizeProp.setIndex(sizeId);
                sizeProp.setSizeId(sizeId);
                sizes.add(sizeProp);
                sizeMap.put(skuData.getSize(), sizeId);
                sizeId++;
            }

            // 给sku添加index
            if (!org.apache.commons.lang.StringUtils.isEmpty(skuData.getStyle()) && null != styleMap.get(skuData.getStyle())) {
                skuData.setStyleId(styleMap.get(skuData.getStyle()));
            }

            if (!org.apache.commons.lang.StringUtils.isEmpty(skuData.getSize()) && null != sizeMap.get(skuData.getSize())) {
                skuData.setSizeId(sizeMap.get(skuData.getSize()));
            }

            if (index == 0) {
                highestPrice = lowestPrice = skuData.getPrice();
                highestNowPrice = lowestNowPrice = skuData.getNowprice();
            }

            if (skuData.getPrice() > highestPrice) {
                highestPrice = skuData.getPrice();
                highestNowPrice = skuData.getNowprice();
            }
            if (skuData.getPrice() < lowestPrice) {
                lowestPrice = skuData.getPrice();
                lowestNowPrice = skuData.getNowprice();
            }
            index++;
        }

        List<PropInfo> props = new ArrayList<>();
        if (version < 3) {
            PropInfo<StylePropData> stylePropInfo = new PropInfo();
            stylePropInfo.setLabel(StringUtils.isEmpty(styleKey) ? "款式" : styleKey);
            stylePropInfo.setList(styles);
            stylePropInfo.setDefault(false);//isStyleDefault);
            props.add(stylePropInfo);
            skuInfo.setStyleKey(StringUtils.isEmpty(styleKey) ? "款式" : styleKey);
        } else {
            if (!StringUtils.isEmpty(styleKey)) {
                PropInfo<StylePropData> stylePropInfo = new PropInfo();
                stylePropInfo.setLabel(StringUtils.isEmpty(styleKey) ? "款式" : styleKey);
                stylePropInfo.setList(styles);
                stylePropInfo.setDefault(false);//isStyleDefault);
                props.add(stylePropInfo);
                skuInfo.setStyleKey(org.apache.commons.lang.StringUtils.isEmpty(styleKey) ? "款式" : styleKey);
                if (StringUtils.isEmpty(sizeKey)) {
                    skuInfo.setProps(props);
                }
            }
        }
        if (version < 3) {
            PropInfo<SizePropData> sizePropInfo = new PropInfo();
            sizePropInfo.setLabel(StringUtils.isEmpty(sizeKey) ? "尺码" : sizeKey);
            sizePropInfo.setList(sizes);
            sizePropInfo.setDefault(false);//isSizeDefault);
            props.add(sizePropInfo);
            skuInfo.setProps(props);
            skuInfo.setSizeKey(org.apache.commons.lang.StringUtils.isEmpty(sizeKey) ? "尺码" : sizeKey);
        } else {
            if (!StringUtils.isEmpty(sizeKey)) {
                PropInfo<SizePropData> sizePropInfo = new PropInfo();
                sizePropInfo.setLabel(StringUtils.isEmpty(sizeKey) ? "尺码" : sizeKey);
                sizePropInfo.setList(sizes);
                sizePropInfo.setDefault(false);//isSizeDefault);
                props.add(sizePropInfo);
                skuInfo.setProps(props);
                skuInfo.setSizeKey(org.apache.commons.lang.StringUtils.isEmpty(sizeKey) ? "尺码" : sizeKey);
            }
        }

        Collections.sort(skuDatas, new Comparator<SkuData>() {
            @Override
            public int compare(SkuData o1, SkuData o2) {
                if (o1.getStyleId() == o2.getStyleId()) {
                    return o1.getSizeId() == o2.getSizeId() ? 0 : (o1.getSizeId() > o2.getSizeId() ? 1 : -1);
                } else {
                    return o1.getStyleId() > o2.getStyleId() ? 1 : -1;
                }
            }
        });

        skuInfo.setSkus(skuDatas);
        //没有价格区间
        if (highestNowPrice == lowestNowPrice) {
            skuInfo.setPriceRange(formatPrice(lowestNowPrice));
            skuInfo.setOldPrice(formatPrice(lowestPrice));
        } else {
            skuInfo.setPriceRange(formatPrice(lowestNowPrice) + '~' + formatPrice(highestNowPrice));
            skuInfo.setOldPrice(formatPrice(lowestPrice) + '~' + formatPrice(highestPrice));
        }
        skuInfo.setDefaultPrice(skuInfo.getPriceRange());
    }

    protected Map<String, String> parseProps(SkuData skuData, List<SkuAttributionDO> attrs, Integer version) {
        int index = 0;
        Map<String, String> mapping = new HashMap<>();
        if (CollectionUtils.isEmpty(attrs)) {
            if (version < 3) {
                mapping.put("style", null);
                mapping.put("size", null);
            }
            return mapping;
        }

        Collections.sort(attrs, new Comparator<SkuAttributionDO>() {
            @Override
            public int compare(SkuAttributionDO o1, SkuAttributionDO o2) {
                int o1Pos = o1.getShowPosition() == null ? 0 : o1.getShowPosition();
                int o2Pos = o2.getShowPosition() == null ? 0 : o2.getShowPosition();
                return o1Pos < o2Pos ? -1 : (o1Pos > o2Pos ? 1 : 0);
            }
        });

        for (SkuAttributionDO attr : attrs) {
            String attrName = attr.getName();
            if (version < 3) {
                if ("style".equals(attr.getName())) {
                    attrName = "颜色";
                } else if ("size".equals(attr.getName())) {
                    attrName = "规格";
                }
            }
            if (attr.getShowPosition() == null || attr.getShowPosition() == 0) {
                skuData.setStyle(attr.getValue());
                mapping.put("style", attrName);
            } else {
                skuData.setSize(attr.getValue());
                mapping.put("size", attrName);
            }
            index++;
        }
        return mapping;
    }

    private Map<Long, Long> getSkuPriceMap(CommonItem commonItem) {
        Map<Long, Long> skuPriceMap = new HashMap<>();
        for (ItemSkuDO sku : commonItem.getSkus()) {
            skuPriceMap.put(sku.getSkuId(), sku.getPrice());
        }
        return skuPriceMap;
    }

    private Long getHighestPrice(final List<ItemSkuDO> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return 0L;
        }
        Long highestPrice = skuList.get(0).getPrice();
        for (ItemSkuDO sku : skuList) {
            if (sku.getPrice() > highestPrice) {
                highestPrice = sku.getPrice();
            }
        }
        return highestPrice;
    }

    public static List<ItemTag> convert(List<ItemServiceTag> serviceTags) {
        ArrayList<ItemTag> itemTags = new ArrayList<>();
        for (ItemServiceTag tag : serviceTags) {
            ItemTag itemTag = new ItemTag(tag);
            if (itemTag.getOrder() > 0) {
                itemTags.add(new ItemTag(tag));
            }
        }
        Collections.sort(itemTags);
        // 当 品牌标 和 良品标 同时出现时,只显示品牌标
        // 因为已经按order拍过序,因此如果两个标同时出现,良品标必然在第二个
        if (itemTags.size() > 1) {
            ItemTag itemTag = itemTags.get(1);
            if (null != itemTag && 2 == itemTag.getOrder() && "蘑菇良品".equals(itemTag.getName())) {
                itemTags.remove(itemTag);
            }
        }
        return itemTags;
    }

    /**
     * 是否为可分期商品
     */
    private boolean canInstallment(CommonItem commonItem, ShopInfo shopInfo) {
        //实时售价大于70元（任一SKU大于70元即可）；
        boolean priceMoreThan70 = false;
        for (ItemSkuDO sku : commonItem.getSkus()) {
            if (sku.getNowPrice() > 7000) {
                priceMoreThan70 = true;
                break;
            }
        }
        if (!priceMoreThan70) {
            return false;
        }

        String virtualCateRoot = "#" + metabaseClient.get("virtual_category_root") + "#";
        //充值中心类目，需要不能分期
        if (commonItem.getCids().contains(virtualCateRoot)) {
            return false;
        }

        //认证店铺
        if (shopInfo == null || !AuthorizedShopUtil.isAuthorized(shopInfo)) {
            return false;
        }

        //非预售商品
        if (commonItem.getItemPreSale() != null) {
            int now = (int) (System.currentTimeMillis() / 1000);
            if (now > commonItem.getItemPreSale().getStart() && now < commonItem.getItemPreSale().getEnd()) {
                return false;
            }
        }

        //未登录则不显示分期数据
        Long userId = SessionContextHolder.getUserId();
        if (userId == null || userId == -1) {
            return false;
        }

        MaiLoReqDTO maiLoReqDTO = new MaiLoReqDTO();
        maiLoReqDTO.setUserId(userId);
        maiLoReqDTO.setIsAdmin(false);
        try {
            Response<UserBasicInfoDTO> response = maiLoUserBasicInfoApi.getUserStatusDto(maiLoReqDTO);
            if (response == null || response.getData() == null) {
                return false;
            } else if (response.getData().getStatus() == 1) {
                return true;
            }
        } catch (Throwable e) {
            LOGGER.error("calling user status failed.", e);
        }
        return false;
    }

    /**
     * sku刷入分期信息
     */
    private void decorateInstallment(List<SkuData> skuList, String tags, String shopTags) {
        List<Long> priceList = new ArrayList<>();
        List<SkuData> toBeDecorateList = new ArrayList<>();
        for (int i = 0; i < skuList.size(); i++) {
            if (skuList.get(i).getNowprice() > 7000) {
                priceList.add(skuList.get(i).getNowprice().longValue());
                toBeDecorateList.add(skuList.get(i));
            }
        }
        try {
            List<String> itemNumTags = null;
            if (StringUtils.isNotBlank(tags)) {
                String[] tgs = tags.split(",");
                if (tgs != null && tgs.length > 0) {
                    itemNumTags = Arrays.asList(tgs);
                }
            }
            List<String> shopTagsList = null;
            if (StringUtils.isNotBlank(shopTags)) {
                shopTagsList = Arrays.asList(shopTags.split(","));
            }
            ComputeInstallmentRequestDTO computeInstallmentRequestDTO = new ComputeInstallmentRequestDTO();
            computeInstallmentRequestDTO.setAmounts(priceList);
            computeInstallmentRequestDTO.setUserId(SessionContextHolder.getUserId());
            computeInstallmentRequestDTO.setTags(itemNumTags);
            computeInstallmentRequestDTO.setMerchantTags(shopTagsList);

            Response<List<List<InstallmentInfoDTO>>> ret = maiLoInstallmentApi.computeInstallmentPlan(computeInstallmentRequestDTO);
            if (ret != null && ret.getData() != null) {
                List<List<InstallmentInfoDTO>> resultList = ret.getData();
                for (int i = 0; i < resultList.size(); i++) {
                    SkuData skuData = toBeDecorateList.get(i);
                    List<InstallmentData> installment = new ArrayList<>();
                    for (InstallmentInfoDTO installmentInfoDTO : resultList.get(i)) {
                        InstallmentData installmentItem = new InstallmentData();
                        installmentItem.setFee(installmentInfoDTO.getFee().intValue());
                        installmentItem.setNum(installmentInfoDTO.getTotalCount());
                        installmentItem.setPerPrice(installmentInfoDTO.getSubAmount().intValue());
                        installment.add(installmentItem);
                    }
                    skuData.setInstallment(installment);
                }
            }
        } catch (Throwable e) {
            LOGGER.error("get installment price error.", e);
        }
    }

    protected Map<String, String> getExtraInfo(String extra) {
        if (com.mogujie.metabase.utils.StringUtils.isEmpty(extra)) {
            return null;
        }
        try {
            Gson gson = new Gson();
            return gson.fromJson(extra, HashMap.class);
        } catch (Exception e) {
            LOGGER.debug("decode extrainfo failed, {}", e);
        }
        return null;
    }

    public static boolean isFlItem(String extra) {
        if (org.apache.commons.lang.StringUtils.isEmpty(extra)) {
            return false;
        }
        try {
            Gson gson = new Gson();
            Map<String, String> extraInfo = gson.fromJson(extra, HashMap.class);
            if (null == extraInfo || extraInfo.isEmpty()) {
                return false;
            }
            String fl = extraInfo.get("fl");
            if (org.apache.commons.lang.StringUtils.isBlank(fl)) {
                return false;
            }

            String[] flPairs = fl.split("\\|");
            for (String flPair : flPairs) {
                String[] pair = flPair.split(":");
                if (pair.length != 2) {
                    continue;
                }
                if ("st".equals(pair[0])) {
                    if ((System.currentTimeMillis() / 1000) < Integer.parseInt(pair[1])) {
                        return false;
                    }
                } else if ("et".equals(pair[0])) {
                    if ((System.currentTimeMillis() / 1000) > Integer.parseInt(pair[1])) {
                        return false;
                    }
                }
            }
            return true;
        } catch (Throwable e) {
            LOGGER.error("parse fl failed : {}", extra);
        }
        return false;
    }


    private SkuQueryOption convertOption(HttpServletRequest request) {
        SkuQueryOption option = new SkuQueryOption();
        if (request == null)
            return option;
        option.setActivityId(request.getParameter("activityId"));
        option.setAtype(request.getParameter("_atype"));
        option.setAv(request.getParameter("_av"));
        option.setCaller(request.getParameter("caller"));
        option.setChannel(request.getParameter("channel"));
        option.setItemId(request.getParameter("itemId"));
        option.setMarket(request.getParameter("market"));
        option.setSkuNum(request.getParameter("skuNum"));
        return option;
    }

    public static void parseSizeType(SkuData skuData, List<SkuAttributionDO> attrs) {
        if (attrs == null) {
            return;
        }
        for (SkuAttributionDO stockAttribution : attrs) {
            String props = stockAttribution.getProperties();
            if (StringUtils.isBlank(props)) {
                continue;
            }
            String[] propsPair = StringUtils.split(props, ";");
            for (String property : propsPair) {
                String[] kvs = StringUtils.split(property, ":");
                if (kvs.length != 2) {
                    continue;
                }
                if ("号型".equals(kvs[0])) {
                    skuData.setSize(skuData.getSize() + " (" + kvs[1] + ")");
                    return;
                }
            }
        }
    }

    public boolean isMedicalItem(CommonItem item) {
        String[] categories = metabaseClient.get("medical_beauty_categories").split(",");
        for (String category : categories) {
            if (item.getCids().contains("#" + category + "#")) {
                return true;
            }
        }
        return false;
    }

}
