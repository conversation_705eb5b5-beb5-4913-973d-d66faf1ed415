package com.mogujie.detail.before.common.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Created by xiaoyao on 15/12/16.
 */
@ResponseStatus(value= HttpStatus.BAD_GATEWAY, reason="detail interal error")
public class DetailException extends RuntimeException {

    public DetailException(String msg) {
        super(msg);
    }

    public DetailException(Exception e) {
        super(e);
    }

}
