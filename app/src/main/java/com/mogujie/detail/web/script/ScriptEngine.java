//package com.mogujie.detail.web.script;
//
//import groovy.lang.GroovyClassLoader;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.BeansException;
//import org.springframework.beans.factory.xml.XmlBeanDefinitionReader;
//import org.springframework.context.ApplicationContext;
//import org.springframework.context.ApplicationContextAware;
//import org.springframework.context.support.GenericApplicationContext;
//import org.springframework.core.io.ClassPathResource;
//import org.springframework.core.io.Resource;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.PostConstruct;
//import java.io.IOException;
//
///**
// * Created by xiaoyao on 16/10/18.
// */
//@Component
//public class ScriptEngine implements ApplicationContextAware {
//
//    private static final Logger LOGGER = LoggerFactory.getLogger(ScriptEngine.class);
//
//    private ApplicationContext parentContext;
//
//    private GenericApplicationContext appContext;
//
//    private long lastModified;
//
//    private static final String TRANSLATOR_FILE = "translator.xml";
//
//    @Override
//    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
//        this.parentContext = applicationContext;
//    }
//
//    @PostConstruct
//    public void init() {
//        try {
//            this.lastModified = new ClassPathResource(TRANSLATOR_FILE).lastModified();
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
//        reload();
//        LOGGER.info("script engine initialized");
//    }
//
//    @Scheduled(fixedDelay = 30000L)
//    public void checkUpdate() {
//        try {
//            long currentLastModified = new ClassPathResource(TRANSLATOR_FILE).lastModified();
//            if (this.lastModified < currentLastModified) {
//                reload();
//                this.lastModified = currentLastModified;
//                LOGGER.info("update script engine context");
//            }
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
//    }
//
//    private synchronized void reload() {
//        Resource scriptConfig = new ClassPathResource(TRANSLATOR_FILE);
//        if (!scriptConfig.exists()) {
//            LOGGER.error("script config file not exist");
//            throw new RuntimeException("script config file not exist");
//        }
//
//        GenericApplicationContext oldContext = this.appContext;
//        String[] config = new String[0];
//        try {
//            config = new String[]{scriptConfig.getURI().toString()};
//        } catch (IOException e) {
//            LOGGER.error("task file not found, {}", e);
//        }
//        GenericApplicationContext newContext = new GenericApplicationContext(this.parentContext);
//        XmlBeanDefinitionReader reader = new XmlBeanDefinitionReader(newContext);
//        ClassLoader groovyClassLoader = new GroovyClassLoader(getClass().getClassLoader());
//        reader.setBeanClassLoader(groovyClassLoader);
//        reader.loadBeanDefinitions(config);
//        newContext.refresh();
//
//        this.appContext = newContext;
//        if (null != oldContext && oldContext.isActive()) {
//            oldContext.close();
//        }
//    }
//}
