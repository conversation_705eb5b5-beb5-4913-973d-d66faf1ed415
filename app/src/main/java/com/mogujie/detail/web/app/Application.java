//package com.mogujie.detail.web.app;
//
///**
// * Created by <PERSON><PERSON><PERSON><PERSON> on 16/9/26.
// */
//
//import org.apache.commons.lang.math.NumberUtils;
//import org.springframework.boot.SpringApplication;
//import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
//import org.springframework.boot.autoconfigure.SpringBootApplication;
//import org.springframework.context.annotation.ComponentScan;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.ImportResource;
//
//@Configuration
//@ComponentScan
//@EnableAutoConfiguration
//@SpringBootApplication
//@ImportResource("classpath:app.xml")
//public class Application {
//
//    private static int port = 8080;
//
//    public static void main(String[] args) {
//        if (args != null && args.length == 2) {
//            String p = args[1];
//            port = NumberUtils.toInt(p, 8080);
//        }
//        SpringApplication.run(Application.class, args);
//    }
//}
