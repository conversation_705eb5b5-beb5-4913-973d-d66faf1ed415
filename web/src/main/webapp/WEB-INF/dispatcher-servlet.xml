<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:beans="http://www.springframework.org/schema/mvc" xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">



    <context:component-scan base-package="com.mogujie.detail"/>
    <context:annotation-config/>
    <beans:annotation-driven/>

    <bean id="topologyManager" class="com.mogujie.detail.core.manager.TopologyManager"/>

    <bean id="costaConfig"
          class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="order" value="2"/>
        <property name="ignoreUnresolvablePlaceholders" value="true"/>
        <property name="locations">
            <list>
                <value>/WEB-INF/conf/costa.properties</value>
            </list>
        </property>
    </bean>

    <!--<bean id="detailConfigurer"-->
          <!--class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">-->
        <!--<property name="order" value="2"/>-->
        <!--<property name="ignoreUnresolvablePlaceholders" value="true"/>-->
        <!--<property name="locations">-->
            <!--<list>-->
                <!--<value>/WEB-INF/conf/detail.properties</value>-->
            <!--</list>-->
        <!--</property>-->
    <!--</bean>-->

    <import resource="classpath*:costa/costa-core.xml"/>

    <context:component-scan base-package="com.mogujie.costa.server.controller"/>

    <import resource="classpath*:spring-salesquery.xml" />

    <context:property-placeholder location="/WEB-INF/conf/traffic.properties"

    order="2" ignore-unresolvable="true"/>

    <import resource="classpath*:conf/traffic.xml"/>

    <!--<bean id="detailSpout" class="com.mogujie.detail.component.spout.DefaultDetailSpout"/>-->

    <!--<bean id="detailSeoListener" class="com.mogujie.detail.component.seo.listener.DefaultDetailSeoListener"/>-->

    <import resource="classpath*:spring/*.xml"/>

    <bean id="MWPAppName" class="com.mogujie.actionlet.mwp.spring.MWPAppNameFactory">
        <property name="appName" value="detailwebmwp"/>
        <!--如果不设置 此属性 则使用Tesla中的appName-->
    </bean>

    <import resource="classpath*:META-INF/tesla/core/*.xml"/>
    <bean class="com.mogujie.actionlet.mwp.spring.MWPServiceConfig">
        <!--配置MWP暴露的Tesla服务是否预热-->
        <property name="lazyExport" value="true"/>
        <!--如果上面的配置是true,那么有配置延迟时间的化,那么系统自动在延迟配置的秒数后自动暴露-->
        <!--这个选项可以不用配置,那么这个时候需要主动调用MWPServiceUtil.serviceReadyForLazyExport进行最后的暴露-->
    </bean>
    <import resource="classpath*:META-INF/actionlet/spring/Actionlet-MWP-Proxy.xml"/>

    <import resource="classpath*:contentcenter-client/contentcenter-client.xml"/>

    <bean id="contentCenterPropertyConfigurer"
          class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="locations">
            <list>
                <value>classpath*:contentcenter.client.properties</value>
            </list>
        </property>
    </bean>
</beans>
