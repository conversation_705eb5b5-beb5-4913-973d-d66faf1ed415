<configuration debug="false" scan="true" scanPeriod="30 seconds">
    <property name="FILE_PATTERN" value='[%d] [%ip] [%t] %p %c [%X{topo}] [%X{position}] [%X{iid}] [%exName] - %m%replaceAndParse(%swtXException){"(\r?\n)", "$1[%ip]"}%nopex%n' />

    <property name="LOG_FILE_NAME" value="mogu-detail" />

    <appender name="FILE"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${CATALINA_APPLOG}/${LOG_FILE_NAME}.log</File>
        <Filter class="com.mogujie.detail.common.log.LogFilter" />
        <Encoder class="com.mogujie.detail.common.log.DetailPatternLayoutEncoder">
            <pattern>${FILE_PATTERN}</pattern>
        </Encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--<timeBasedFileNamingAndTriggeringPolicy-->
            <!--class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">-->
            <!--<maxFileSize>32MB</maxFileSize>-->
            <!--</timeBasedFileNamingAndTriggeringPolicy>-->
            <fileNamePattern>${CATALINA_APPLOG}/${LOG_FILE_NAME}-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
    </appender>

    <appender name="DATASOURCE_FILE" class="ch.qos.logback.core.FileAppender">
        <file>${CATALINA_APPLOG}/datasource.log</file>
        <encoder>
            <pattern>${FILE_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- LURKER -->
    <appender name="LURKERFILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>/tmp/trace/tesla_trace.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>/tmp/trace/tesla_trace.%d{yyyy-MM-dd}.gz</FileNamePattern>
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%msg%n</pattern>
        </encoder>
    </appender>

    <appender name ="ASYNCLURKERFILE" class= "ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold >80</discardingThreshold>
        <queueSize>8192</queueSize>
        <appender-ref ref ="LURKERFILE"/>
    </appender>

    <logger name="com.mogujie.trace.lurker.LurkerAgent" level="DEBUG" additivity="FALSE">
        <appender-ref ref="ASYNCLURKERFILE" />
    </logger>
    <!-- LURKER -->

    <logger name="druid.sql" level="WARN" addtivity="false">
        <appender-ref ref="DATASOURCE_FILE" />
    </logger>

    <logger name="com.alibaba.druid" level="WARN" addtivity="false">
        <appender-ref ref="DATASOURCE_FILE" />
    </logger>

    <logger name="com.mogujie.raptor" level="INFO" addtivity="false">
        <appender-ref ref="FILE" />
    </logger>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d [%t] %5p \(%F:%L\) %M\(\) - %m%n</pattern>
        </encoder>
    </appender>

    <logger name="com.mogujie.tesla" level="INFO" />

    <logger name="com.mogujie.detail" level="INFO" />

    <logger name="com.mogujie.metabase.client" level="INFO" />

    <logger name="com.mogujie.service.waitress.client" level="ERROR"/>

    <logger name="com.mogujie.kit.workflow" level="ERROR"/>

    <root level="WARN">
        <appender-ref ref="FILE" />
    </root>

    <appender name="SESSION"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${CATALINA_APPLOG}/sessoin.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>session.%i.log.zip</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>3</maxIndex>
        </rollingPolicy>

        <triggeringPolicy
                class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>10MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%-4relative [%thread] %-5level %logger{35} - %msg%n
            </pattern>
        </encoder>
    </appender>
    <!-- SESSION日志异步输出 -->
    <appender name="SESSION_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>256</queueSize>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="SESSION" />
    </appender>

    <logger name="com.mogujie.session" additivity="false" level="INFO">
        <appender-ref ref="SESSION_ASYNC" />
    </logger>
</configuration>