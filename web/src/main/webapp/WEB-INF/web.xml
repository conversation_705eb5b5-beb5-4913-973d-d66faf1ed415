<web-app version="2.4"
         xmlns="http://java.sun.com/xml/ns/j2ee" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee
	http://java.sun.com/xml/ns/j2ee/web-app_2_4.xsd">

    <display-name>Spring MVC Application</display-name>

    <servlet>
        <servlet-name>dispatcher</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>

    <servlet-mapping>
        <servlet-name>dispatcher</servlet-name>
        <url-pattern>/</url-pattern>
    </servlet-mapping>

    <!-- logback -->
    <context-param>
        <param-name>logbackConfigLocation</param-name>
        <param-value>WEB-INF/conf/logback.xml</param-value>
    </context-param>

    <context-param>
        <param-name>detailProperties</param-name>
        <param-value>WEB-INF/conf/detail.properties</param-value>
    </context-param>

    <listener>
        <listener-class>ch.qos.logback.ext.spring.web.LogbackConfigListener</listener-class>
    </listener>

    <listener>
        <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
    </listener>



    <servlet-mapping>
        <servlet-name>default</servlet-name>
        <url-pattern>/resources/*</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>default</servlet-name>
        <url-pattern>/status_mgj</url-pattern>
    </servlet-mapping>

    <filter>
        <filter-name>sessionFilter</filter-name>
        <filter-class>com.mogujie.session.filter.HttpSessionFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>sessionFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <filter>
        <filter-name>LurkerFilter</filter-name>
        <filter-class>com.mogujie.trace.lurker.LurkerFilter</filter-class>
    </filter>

    <filter-mapping>
        <filter-name>LurkerFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
</web-app>