<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

    <bean id="detailSwitchConf" class="com.mogujie.metabase.spring.client.MetabaseClient">
         <property name="appName" value="detail-all"/>
    </bean>
    <bean id="confMetabaseClient" class="com.mogujie.metabase.spring.client.MetabaseClient">
        <property name="appName" value="detailConf"/>
        <property name="singleListeners">
            <bean class="com.mogujie.detail.core.listener.ThreadPoolSizeListener"/>
        </property>
    </bean>

    <bean id="detailTemplate" class="com.mogujie.metabase.spring.client.MetabaseClient">
        <property name="appName" value="detail_template"/>
    </bean>

    <bean id="darwinConf"
          class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="order" value="2"/>
        <property name="ignoreUnresolvablePlaceholders" value="true"/>
        <property name="locations">
            <list>
                <value>/WEB-INF/conf/darwin.properties</value>
            </list>
        </property>
    </bean>

    <bean id="darwinClient" class="com.mogujie.darwin.application.client.DarwinClient">
        <property name="runEnv" value="${project.run.env}" />
    </bean>

    <bean id="dailyConfigClient" class="com.mogujie.themis.daily.config.DailyConfigClient">
        <constructor-arg value="detailweb"/>
    </bean>

    <!--<import resource="classpath*:/itemcenter/client/bean.xml"/>-->

    <!--<bean id="clientSetting" class="com.mogujie.itemcenter.client.spring.ItemCenterClientSetting">-->
        <!--<constructor-arg name="env" value="${ic_env}"/>-->
        <!--<property name="serviceGroup" value="${itemcenter.detail.group}" />-->
        <!--<property name="parallelismOfForkJoinPool" value="64"/>-->
        <!--<property name="timeout" value="1000"/>-->
    <!--</bean>-->
</beans>