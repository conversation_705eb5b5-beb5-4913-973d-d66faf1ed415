package com.mogujie.detail.web.mwp;

import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: wuzhao
 * @create: 2020-02-20 14:35
 **/
@Data
public class CalcPromotionReq implements Serializable {

    /**
     * skuId
     */
    private String itemId;

    /**
     * 来源
     */
    private String from = "normal";

    /**
     * 商品价格
     */
    private Long itemPrice;
    /**
     * sku数量
     */
    private Long skuNum;

    /**
     * 终端
     *
     * @see com.mogujie.service.hummer.constains.RequestConstants.Terminal
     * 必填
     */
    private Integer terminal;

    /**
     * 市场
     *
     * @see com.mogujie.service.hummer.constains.RequestConstants.Channel
     * 必填
     */
    private Integer channel;

    /**
     * 卖家id
     * 必填
     */
    private String sellerId;

    /**
     * 商品标签信息
     */

    private String itemTags;

    /**
     * //扩展信息的字符串表示
     */

    private String jsonExtra;


    /**
     * 类目id #16# #5512# #44568#
     */
    private String cids;

    /**
     * 店铺标，等同于shopInfo节点中的tags
     */
    private String shopTags;

    /**
     * 快抢活动id   url形式
     */
    private String fastbuyId;


}
