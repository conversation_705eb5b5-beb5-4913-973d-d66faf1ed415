package com.mogujie.detail.web.mwp;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.mogujie.actionlet.ActionResult;
import com.mogujie.actionlet.annotation.ActionletName;
import com.mogujie.actionlet.mwp.MWPContext;
import com.mogujie.actionlet.mwp.annotation.MWPApi;
import com.mogujie.actionlet.session.annotation.NeedUserInfo;
import com.mogujie.actionlet.sync.SyncActionlet;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.darwin.application.client.DarwinClient;
import com.mogujie.darwin.application.query.ApplicationLaunchQuery;
import com.mogujie.darwin.application.service.client.ApplicationLaunchClient;
import com.mogujie.detail.core.constant.PromotionTypeEnum;
import com.mogujie.detail.module.detail.util.CategoryTypeUtil;
import com.mogujie.metabase.spring.client.MetabaseClient;
import com.mogujie.service.hummer.api.PromotionReadService;
import com.mogujie.service.hummer.constains.RequestConstants;
import com.mogujie.service.hummer.domain.dto.*;
import com.mogujie.service.hummer.domain.dto.result.Result;
import com.mogujie.service.hummer.utils.InvokeExtraUtil;
import com.mogujie.service.hummer.utils.PromotionConvertUtils;
import com.mogujie.service.item.api.basic.ItemReadService;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.basic.ItemTagDO;
import com.mogujie.service.item.domain.query.QueryItemOptions;
import com.mogujie.service.item.domain.result.BaseResultDO;
import com.mogujie.service.tagcenter.api.read.ShopTagReadService;
import com.mogujie.service.tagcenter.common.TagValueEnum;
import com.mogujie.service.tagcenter.domain.entity.query.ShopTagQueryOption;
import com.mogujie.service.tagcenter.domain.entity.result.ShopTagDO;
import com.mogujie.session.SessionContextHolder;
import com.mogujie.stable.spirit.point.annotation.ClassSpirit;
import com.mogujie.stable.spirit.point.annotation.MethodSpirit;
import com.mogujie.stable.spirit.util.MethodUtil;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * @description: 根据sku和数量计算优惠价格
 * @author: wuzhao
 * @create: 2020-02-20 14:32
 **/
@Component
@MWPApi(value = "detail.calcPromotion", version = "1")
@ActionletName(value = "detail.calcPromotion", version = "1")
@NeedUserInfo
@ClassSpirit
public class CalcPromotionController extends BaseController<Object> implements SyncActionlet<CalcPromotionReq, Object> {

    private static final Logger LOGGER = LoggerFactory.getLogger(CalcPromotionController.class);

    @Resource
    private PromotionReadService promotionReadService;

    @Resource
    private ItemReadService itemReadService;

    @Resource
    private ShopTagReadService shopTagReadService;

    @Resource(name = "confMetabaseClient")
    private MetabaseClient metabaseClient;

    @PostConstruct
    public void init() throws Exception {
        promotionReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(PromotionReadService.class);

    }

    @Override
    @MethodSpirit
    public ActionResult<Object> execute(@Nullable CalcPromotionReq calcPromotionReq) {

        try {

            //参数校验
            if (calcPromotionReq == null || calcPromotionReq.getItemId() == null
                    || StringUtils.isEmpty(calcPromotionReq.getItemId())) {
                return getParamFailRet();
            }
            if (Stream.of(calcPromotionReq.getSkuNum(), calcPromotionReq.getTerminal(), calcPromotionReq.getChannel(), calcPromotionReq.getItemPrice())
                    .anyMatch(Objects::isNull)) {

                return getParamFailRet();
            }
            ItemDetailRequestV2 request = new ItemDetailRequestV2();
            Long userId = SessionContextHolder.getUserId();
            Pbuyer pbuyer = new Pbuyer();
            pbuyer.setBuyerId(userId);
            Pseller pSeller = new Pseller();

            PitemDetail pitemDetail = new PitemDetail();
            pitemDetail.setItemPrice(calcPromotionReq.getItemPrice());
            pitemDetail.setItemId(IdConvertor.urlToId(calcPromotionReq.getItemId()));
            pitemDetail.setNumber(calcPromotionReq.getSkuNum());
            pitemDetail.setItemTagList(PromotionConvertUtils.convertItemTagToItemProTag(buildItemTags(calcPromotionReq.getItemTags())));
            pitemDetail.setExtra(calcPromotionReq.getJsonExtra());
            //支持品类券
            CategoryTypeUtil util = new CategoryTypeUtil(calcPromotionReq.getCids());
            List<String> cids = util.cids().stream().map(String::valueOf).collect(Collectors.toList());
            pitemDetail.setCids(cids);

            InvokeInfo invokeInfo = new InvokeInfo();
            invokeInfo.setChannel((int) RequestConstants.Channel.UNKNOW);
            if (metabaseClient.getBoolean("fastbuy_show_discountprice")
                    && Objects.equals((int)RequestConstants.Channel.FASTBUY, calcPromotionReq.getChannel())
                    && StringUtils.isNotEmpty(calcPromotionReq.getFastbuyId())
            ) {
                invokeInfo.setChannel(calcPromotionReq.getChannel());
                invokeInfo.setOutType(RequestConstants.OutType.FASTBUY);
                invokeInfo.setOutId(IdConvertor.urlToId(calcPromotionReq.getFastbuyId()).toString());
            }
            invokeInfo.setTerminal(calcPromotionReq.getTerminal());
            request.setPitemDetail(pitemDetail);
            request.setSeller(pSeller);
            request.setPbuyer(pbuyer);
            request.setInvokeInfo(invokeInfo);

            //来源是直播间
            if (calcPromotionReq.getFrom().equals("live")) {
                invokeInfo.setSource(RequestConstants.Source.LIVE_DETAIL);
                InvokeExtraUtil.setDetailPromotionPrice(invokeInfo);
            } else {
                //如果是直播切片来的，因为没有标签信息，需要从商品上补全这些信息并返回回去。
                invokeInfo.setSource(RequestConstants.Source.DETAIL_PARALLEL);
                if (calcPromotionReq.getFrom().equals("liveSlice")) {
                    fillPitemDetail(pitemDetail, calcPromotionReq);
                    pitemDetail.setBuySource(RequestConstants.BuySource.LIVE);
                    InvokeExtraUtil.setCurrentSource(invokeInfo, "liveSlice");
                }
                this.appendShopTags(calcPromotionReq, request);
            }
            pSeller.setSellerId(IdConvertor.urlToId(calcPromotionReq.getSellerId()));

            //分开调用，针对直播切片的，促销底层有单独的限流任务
            Result<ItemDetailPromotion> ret = null;
            if ("liveSlice".equals(calcPromotionReq.getFrom())) {
                ret = promotionReadService.calcForItemDetailWithMockCoupon(request);
            } else {
                ret = promotionReadService.calcForItemDetailPromotion(request);
            }

            Map<String, Object> map = new HashMap<>();
            if (null != ret && ret.isSuccess() && null != ret.getData()) {
                ItemDetailPromotion itemDetailPromotion = ret.getData();
                //可用店铺券id
                List<String> shopCampaignIds = CollectionUtils.isEmpty(itemDetailPromotion.getShopCampaignIds()) ? Collections.EMPTY_LIST :
                        itemDetailPromotion.getShopCampaignIds().stream().map(IdConvertor::idToUrl).collect(toList());

                //优惠力度
                List<ItemDetailPromotion.SkuPromotionDecorate> skuPromotionDecorates = itemDetailPromotion.getSkuPromotionDecorates();
                map.put("decorate", itemDetailPromotion.getDecorate());
                map.put("shopCoupons", shopCampaignIds);
                map.put("discountPrice", itemDetailPromotion.getItemRealPrice());

                if (CollectionUtils.isNotEmpty(skuPromotionDecorates)) {
                    skuPromotionDecorates = skuPromotionDecorates.stream().filter(decorate -> decorate.getCutPrice() != null)
                            .sorted(Comparator.comparing(ItemDetailPromotion.SkuPromotionDecorate::getCutPrice).reversed()).collect(toList());
                    //第一个是最大的优惠力度
                    Pair<ItemDetailPromotion.SkuPromotionDecorate, List<ItemDetailPromotion.SkuPromotionDecorate>> pair = getMaxPromotion(skuPromotionDecorates);

                    map.put("expectPayPrice", itemDetailPromotion.getExpectPayPrice());
                    map.put("skuDecorate", pair.getLeft().getSkuDecorate());
                    map.put("promotionType", pair.getLeft().getPromotionType());
                    map.put("skuCutPrice", skuPromotionDecorates.stream().mapToLong(ItemDetailPromotion.SkuPromotionDecorate::getCutPrice).sum());
                    //是否还有其他优惠
                    map.put("hasOtherPromo", skuPromotionDecorates.size() > 1 ? Boolean.TRUE : Boolean.FALSE);

                }
            }

            //https://mogu.feishu.cn/docs/doccnPXemVXfQsUMr7lizqpwUXd  1580 0元购
            if ("liveSlice".equals(calcPromotionReq.getFrom()) && metabaseClient.getBoolean("liveSliceZeroYuanBuy")) {
                String av = null, platform = null;
                if (MWPContext.getMWPRequestFrom() != null) {
                    if (org.apache.commons.lang.StringUtils.isNotBlank(MWPContext.getMWPRequestFrom().getClientShortVersion())) {
                        av = MWPContext.getMWPRequestFrom().getClientShortVersion();
                    }
                    if (org.apache.commons.lang.StringUtils.isNotBlank(MWPContext.getMWPRequestFrom().getPlatform())) {
                        platform = MWPContext.getMWPRequestFrom().getPlatform();
                    }
                }
                List<Map<String, Object>> maitData = getTargetedMaitData(155923L,
                        SessionContextHolder.getUserId() > 0 ? SessionContextHolder.getUserId() : null, null, av, platform, calcPromotionReq.getItemId());
                if (maitData != null && !maitData.isEmpty()) {
                    Map<String, Object> obj = maitData.get(0);
                    Object itemMark = obj.get("itemMark");
                    if (calcPromotionReq.getItemTags() != null && itemMark != null && calcPromotionReq.getItemTags().contains(itemMark.toString())) {
                        Map<String, Object> zeroYuanBuy = new HashMap<>();
                        zeroYuanBuy.put("tagImg", obj.get("tagImg"));
                        zeroYuanBuy.put("buyButtonFirstLine", obj.get("buyButtonFirstLine"));
                        zeroYuanBuy.put("buyButtonSecondLine", obj.get("buyButtonSecondLine"));
                        map.put("zeroYuanBuy", zeroYuanBuy);
                    }
                }
            }

                //直播切片加的，现在只有直播切片在用。
            map.put("req", calcPromotionReq);
            return getSuccessRet(map);
        } catch (Exception e) {
            LOGGER.error("mwp.detailwebmwp.detail.calcPromotion error  itemId :{}", calcPromotionReq.getItemId(), e);
            return getParamFailRet("mwp.detailwebmwp.detail.calcPromotion error " + e.getMessage());
        }
    }

    // MaitUtil.getTargetedMaitData
    private static List<Map<String, Object>> getTargetedMaitData(Long defId, Long loginUserId, String did, String av, String platform, String iid) {
        ApplicationLaunchClient launchClient = DarwinClient.getLaunchClient();
        ApplicationLaunchQuery query = new ApplicationLaunchQuery();
        query.setDefinitionId(defId);
        query.setDid(did);
        query.setUid(loginUserId);
        Map<String, Object> extra = new HashMap();
        extra.put("login", null == loginUserId ? "0" : "1");
        extra.put("v", StringUtils.isEmpty(av) ? "0" : av);
        extra.put("channel", "0");
        query.setExtra(extra);
        query.setPlatform(platform);


        Map<String, Object> params = new HashMap();
        params.put("iid", iid);
        query.setParams(params);
        com.mogujie.market.base.model.ResultBase<List<Map<String, Object>>> rstBase = launchClient.applicationLaunch(query);
        if (null != rstBase && rstBase.hasSuccessValue() && !com.mogujie.tesla.common.CollectionUtils.isEmpty(rstBase.getValue())) {
            return rstBase.getValue();
        }
        return null;
    }

    /**
     * sku面板请求促销获取优惠文案时请求参数追加店铺标签
     *
     * @param calcPromotionReq 原始请求对象
     * @param request          促销请求对象
     */
    private void appendShopTags(CalcPromotionReq calcPromotionReq, ItemDetailRequestV2 request) {
        if (null == calcPromotionReq || StringUtils.isBlank(calcPromotionReq.getShopTags())) {
            return;
        }
        List<Integer> tagList = Arrays
                .stream(calcPromotionReq.getShopTags().split(","))
                .filter(idStr -> StringUtils.isNotBlank(idStr) && StringUtils.isNumeric(idStr))
                .map(Integer::new)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tagList)) {
            return;
        }
        ShopInfo shopInfo = new ShopInfo();
        shopInfo.setShopTagIdList(tagList);
        request.setShopInfo(shopInfo);
    }

    /**
     * 如果是直播切片来的，需要给他补充信息，第一次请求的时候，缓存一下，
     * @param pitemDetail
     * @param calcPromotionReq
     */
    private void fillPitemDetail(PitemDetail pitemDetail,CalcPromotionReq calcPromotionReq) {
        if (calcPromotionReq == null || pitemDetail == null ||
                CollectionUtils.isNotEmpty(pitemDetail.getCids()) || CollectionUtils.isNotEmpty(pitemDetail.getItemTagList())
                || StringUtils.isNotBlank(calcPromotionReq.getShopTags())) {
            return;
        }

        try {
            QueryItemOptions queryItemOptions = new QueryItemOptions();
            queryItemOptions.setQueryItemIdPropertyMap(false);
            BaseResultDO<ItemDO> baseResultDO = itemReadService.queryItemById(pitemDetail.getItemId(), queryItemOptions);
            if (baseResultDO.getResult() != null) {
                ItemDO itemDO = baseResultDO.getResult();
                List<String> itemDOCids = Arrays.stream(itemDO.getCids().split(" "))
                        .map(cid -> StringUtils.replace(cid, "#", "")).collect(Collectors.toList());
                pitemDetail.setCids(itemDOCids);
                pitemDetail.setItemTagList(PromotionConvertUtils.convertItemTagToItemProTag(itemDO.getItemTags()));
                pitemDetail.setExtra(itemDO.getJsonExtra());

                calcPromotionReq.setCids(JSON.toJSONString(itemDOCids));
                calcPromotionReq.setSellerId(IdConvertor.idToUrl(itemDO.getUserId()));
                calcPromotionReq.setJsonExtra(itemDO.getJsonExtra());
                if (itemDO.getItemTags() != null) {
                    calcPromotionReq.setItemTags(JSON.toJSONString(itemDO.getItemTags()));
                }

                ShopTagQueryOption shopTagQueryOption = new ShopTagQueryOption();
                shopTagQueryOption.setShopId(itemDO.getShopId());
                shopTagQueryOption.setTagKey("tags");
                shopTagQueryOption.setTagValueEnum(TagValueEnum.NUM);
                com.mogujie.service.tagcenter.domain.entity.result.BaseResultDO<List<ShopTagDO>> shopTag = shopTagReadService.queryShopTag(shopTagQueryOption);
                if (shopTag.getResult() != null) {
                    String shopTags = shopTag.getResult().stream().map(ShopTagDO::getTagValue).collect(Collectors.joining(","));
                    calcPromotionReq.setShopTags(shopTags);
                }
            }
        } catch (Exception e) {
            LOGGER.error("领券购买，拼装商品信息失败");
        }
    }


    /**
     * 获取最大优惠文案
     * 促销那边不会拼接优惠信息，要按照 平台券>购物金>店铺券>店铺活动
     *
     * @param skuPromotionDecorates
     * @return
     */
    private static Pair<ItemDetailPromotion.SkuPromotionDecorate, List<ItemDetailPromotion.SkuPromotionDecorate>> getMaxPromotion(List<ItemDetailPromotion.SkuPromotionDecorate> skuPromotionDecorates) {

        //根据最大优惠价格分组
        Map<Long, List<ItemDetailPromotion.SkuPromotionDecorate>> groupByPriceMap =
                skuPromotionDecorates.stream().collect(Collectors.groupingBy(ItemDetailPromotion.SkuPromotionDecorate::getCutPrice));

        //按照最大优惠价倒序放到map中，并获取第一个数据
        Map<Long, List<ItemDetailPromotion.SkuPromotionDecorate>> finalMap = new LinkedHashMap<>();

        groupByPriceMap.entrySet().stream()
                .sorted(Map.Entry.<Long, List<ItemDetailPromotion.SkuPromotionDecorate>>comparingByKey().reversed()).forEachOrdered(e -> finalMap.put(e.getKey(), e.getValue()));

        List<ItemDetailPromotion.SkuPromotionDecorate> maxPromotion = Lists.newArrayList();
        for (Map.Entry<Long, List<ItemDetailPromotion.SkuPromotionDecorate>> entry : finalMap.entrySet()) {
            maxPromotion = entry.getValue();
            break;

        }
        //根据平台券>购物金>店铺券>店铺活动规则返回优惠信息
        Map<Integer, ItemDetailPromotion.SkuPromotionDecorate> map = maxPromotion.stream().
                collect(Collectors.toMap(ItemDetailPromotion.SkuPromotionDecorate::getPromotionType, Function.identity(), (o, n) -> n, LinkedHashMap::new));

        if (map.containsKey(PromotionTypeEnum.PLATFORM.getIndex())) {
            return Pair.of(map.get(PromotionTypeEnum.PLATFORM.getIndex()), maxPromotion);

        } else if (map.containsKey(PromotionTypeEnum.SHOPPRICE.getIndex())) {
            return Pair.of(map.get(PromotionTypeEnum.SHOPPRICE.getIndex()), maxPromotion);

        } else if (map.containsKey(PromotionTypeEnum.SHOPCOUPON.getIndex())) {
            return Pair.of(map.get(PromotionTypeEnum.SHOPCOUPON.getIndex()), maxPromotion);

        } else if (map.containsKey(PromotionTypeEnum.SHOPACTIVITY.getIndex())) {
            return Pair.of(map.get(PromotionTypeEnum.SHOPACTIVITY.getIndex()), maxPromotion);

        } else {
            return Pair.of(new ItemDetailPromotion.SkuPromotionDecorate(), maxPromotion);
        }
    }

    private List<ItemTagDO> buildItemTags(String itemTags) {
        if (StringUtils.isNotEmpty(itemTags)) {
            return JSON.parseArray(itemTags, ItemTagDO.class);

        }
        return Collections.EMPTY_LIST;
    }

    public static void main(String[] args) {
        for(Method method:CalcPromotionController.class.getMethods()){
            System.out.println(MethodUtil.getMethodName(method));
        }
    }
}



