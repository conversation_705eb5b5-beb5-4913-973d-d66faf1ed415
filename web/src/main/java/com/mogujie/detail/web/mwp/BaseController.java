package com.mogujie.detail.web.mwp;

import com.mogujie.actionlet.ActionResult;
import com.mogujie.actionlet.core.ResultState;
import com.mogujie.actionlet.core.impl.DefaultActionResult;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * @auther huasheng
 * @time 19/3/27 16:11
 */
public class BaseController<T> {

    protected ActionResult<T> getSuccessRet( Map<String, Object> data) {
        if (null == data) {
            data = new HashedMap();
        }

        return new DefaultActionResult(data);
    }

    protected ActionResult<T> getParamFailRet( ) {
        return new DefaultActionResult(Boolean.FALSE, ResultState.PARAMETER_VERIFY_FAILED);
    }

    protected ActionResult<T> getParamFailRet( String msg) {
        return new DefaultActionResult(Boolean.FALSE, ResultState.PARAMETER_VERIFY_FAILED.name(),msg);
    }
}
