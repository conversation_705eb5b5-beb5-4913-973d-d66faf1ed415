package com.mogujie.detail.web.mwp;


import com.mogujie.actionlet.ActionResult;
import com.mogujie.actionlet.annotation.ActionletName;
import com.mogujie.actionlet.core.impl.DefaultActionResult;
import com.mogujie.actionlet.mwp.annotation.MWPApi;
import com.mogujie.actionlet.session.annotation.NeedUserInfo;
import com.mogujie.actionlet.sync.SyncActionlet;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.RetData;
import com.mogujie.detail.core.util.ContextUtil;
import com.mogujie.detail.module.busi.admin.AdminUserService;
import com.mogujie.service.muser.Result;
import com.mogujie.service.muser.api.UserService;
import com.mogujie.session.SessionContextHolder;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;


/**
 * @auther huasheng
 * @time 19/3/26 11:46
 */
@Component
@MWPApi(value = "detail.admininfo.api", version = "1")
@ActionletName(value = "detail.admininfo.api", version = "1")
@NeedUserInfo
public class AdminInfoController extends BaseController<Object> implements SyncActionlet<AdminInfoReq, Object> {

    private static final Logger LOGGER = LoggerFactory.getLogger(AdminInfoController.class);

    @Resource
    private AdminUserService adminUserService;


    private UserService userService;

    @PostConstruct
    public void init() throws Exception {
        userService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(UserService.class);
    }


    @Override
    public ActionResult<Object> execute(AdminInfoReq adminInfoReq) {
        Long userId = SessionContextHolder.getUserId();

        if (userId == null) {
            return getParamFailRet();
        }
        Result<Boolean> adminRet = userService.isAdmin(userId);
        if (adminRet == null || !adminRet.getValue()) {
            return getParamFailRet();
        }
        if (StringUtils.isBlank(adminInfoReq.getIid())) {
            return getParamFailRet();
        }
        RetData<Map<String, Object>> retData = adminUserService.getAdminItemInfo(adminInfoReq.getIid());

        return getSuccessRet(retData.getResult());
    }


}
