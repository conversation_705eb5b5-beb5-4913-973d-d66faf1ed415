<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <import resource="classpath*:spring/module.xml"/>
    <import resource="classpath*:spring/before.xml"/>
    <!--<bean id="itemReadServiceClient" class=" com.mogujie.itemcenter.client.service.impl.ItemReadServiceClientImpl"/>-->

    <bean class="com.mogujie.service.shopcenter.module.spring.ClientSpringInitializer">
        <property name="env" value="${shop_env}"/>
    </bean>

    <import resource="classpath*:spring-salesquery.xml"/>

    <bean id="kvstoreCacheFactory" class="com.mogujie.trade.sales.query.util.KvStoreCacheFactory" init-method="init">
        <constructor-arg index="0" value="${salesclient.kvstore.namespace}"/>
        <constructor-arg index="1" value="50"/> <!--缓存接口的超时时间，单位ms，建议50-->
    </bean>
    <!-- 以店铺销量查询为例-->
    <bean id="shopSalesQueryClient" class="com.mogujie.trade.sales.query.client.ShopSalesQueryClient">
        <property name="cacheFactory" ref="kvstoreCacheFactory"/>
        <property name="teslaGroup" value="${trade.service.salesquery.group}"/>
    </bean>

    <bean id="itemSalesQueryClient" class="com.mogujie.trade.sales.query.client.ItemSalesQueryClient">
        <property name="cacheFactory" ref="kvstoreCacheFactory"/>
        <property name="teslaGroup" value="${trade.service.salesquery.group}"/>
    </bean>

    <bean id="preItemSalesQueryClient" class="com.mogujie.trade.sales.query.client.PreItemSalesQueryClient">
        <property name="cacheFactory" ref="kvstoreCacheFactory"/>
        <property name="teslaGroup" value="${trade.service.salesquery.group}"/>
    </bean>

    <bean id="catCompRelationServiceClient"
          class="com.mogujie.service.tangram.component.client.CatCompRelationServiceClient">
        <constructor-arg index="0" ref="catCompRelationService"/>
    </bean>

<!--    <bean id="convertMapper" class="com.mogujie.item.mapper.ObjectConverter" init-method="init">-->
<!--        <property name="configFiles">-->
<!--            <list>-->
<!--                <value>ic-mapper/client-bean-mapping.xml</value>-->
<!--            </list>-->
<!--        </property>-->
<!--    </bean>-->
</beans>