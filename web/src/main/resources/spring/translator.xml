<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:lang="http://www.springframework.org/schema/lang"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/lang http://www.springframework.org/schema/lang/spring-lang.xsd">

    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="topBarNormal" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/TopBarNormal.groovy" />-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="galleryNormal" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/GalleryNormal.groovy" />-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="qualityCheck" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/QualityCheck.groovy" />-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="shopFullInfo" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/ShopFullInfo.groovy" />-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="rateNormal" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/RateNormal.groovy" />-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="skuData" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/SKUData.groovy" />-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="skuDataV2" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/SKUDataV2.groovy" />-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="skuSelect" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/SKUSelect.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="skuSelect_930" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/SKUSelect930.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="itemService" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/ItemService.groovy" />-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="itemServiceExcellent" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/ItemServiceExcellent.groovy" />-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="itemServiceNormalV2" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/ItemServiceNormalV2.groovy" />-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="collocation" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/Collocation.groovy" />-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="collocationV2" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/CollocationV2.groovy" />-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="preSale" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/PreSale.groovy" />-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="platformCoupon" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/DetailPlatformCoupon.groovy" />-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="detailIconTextInfo" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/DetailIconTextInfo.groovy" />-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="promotion" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/normal/DetailPromotion.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="itemDetailNormal" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/ItemDetailNormal.groovy" />-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="groupBuy" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/GroupBuyInfo.groovy" />-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="bottomBarNormal" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/BottomBarNormal.groovy" />-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="summary" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/Summary.groovy" />-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="countdown" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/normal/CountDownNormal.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="waitForSaleInfo" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/WaitForSaleInfo.groovy" />-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="giftList" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/GiftList.groovy" />-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="buyerShow" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/BuyerShow.groovy" />-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="floatLayer" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/FloatLayer.groovy" />-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="salePromotionBanner" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/SalePromotionBanner.groovy" />-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="addCartRedPackets" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/AddCartRedPackets.groovy" />-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="share" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/Share.groovy" />-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="iOSSearchData" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/normal/DetailiOSSearchData.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="detailExpress" refresh-check-delay="2000" script-source="classpath:mgj/app/normal/DetailExpress.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="topBarNormalV2" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/normal/TopBarNormalV2.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="anchorDetail" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/normal/anchor/AnchorDetail.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="anchorRate" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/normal/anchor/AnchorRate.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="anchorRecommend" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/normal/anchor/AnchorRecommend.groovy"/>-->

    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="fastbuyAddress" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/fastbuy/RushAddressLink.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="bottomBarFastbuy"-->
                 <!--refresh-check-delay="2000" script-source="classpath:mgj/app/fastbuy/BottomBarFastbuy.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="fastbuyInfo" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/fastbuy/RushInfo.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="skuSelectFastbuy"-->
                 <!--refresh-check-delay="2000" script-source="classpath:mgj/app/fastbuy/SKULinkFastbuy.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="countdownFastbuy" refresh-check-delay="2000" script-source="classpath:mgj/app/fastbuy/CountDownFastbuy.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="shareFastbuy" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/fastbuy/ShareFastbuy.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="fastbuyProgress" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/fastbuy/RushProgress.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="summaryFastbuy" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/fastbuy/SummaryFastbuy.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="skuDataFastbuy" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/fastbuy/SKUDataFastbuy.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="skuDataFastbuyV2" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/fastbuy/SKUDataFastbuyV2.groovy"/>-->

    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="skuDataV3" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/normal/SKUDataV3.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="sizeHelper" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/normal/SizeHelper.groovy"/>-->

    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="countDownNormalV2Group" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/normal/CountDownNormalV2Group.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="countDownFastbuyV2Group" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/fastbuy/CountDownFastbuyV2Group.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="themeName" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/normal/ThemeName.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="rateNormalV2" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/normal/RateNormalV2.groovy"/>-->


    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="lazyRecommend" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/normal/LazyRecommend.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="lazyDetail" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/normal/LazyDetail.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="lazyDetailV2" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/normal/LazyDetailV2.groovy"/>-->

    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="sizeTable" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/normal/SizeTable.groovy"/>-->

    <!--&lt;!&ndash;图文详情的组件&ndash;&gt;-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="imageList" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/detailinfo/ImageList.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="itemParamsApp" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/detailinfo/ItemParams.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="shopDecoration" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/detailinfo/ShopDecoration.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="detailInfoTitle" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/detailinfo/Title.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="video" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/detailinfo/TopVideo.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="shopRecommend" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/detailinfo/ShopRecommend.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="itemParamsAppV2" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/app/detailinfo/ItemParamsAppV2.groovy"/>-->


    <!--&lt;!&ndash; H5公共模块 &ndash;&gt;-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="buyerShowInfo" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgjvommon/BuyerShowInfo.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="celebrityInfo" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgjvommon/CelebrityInfo.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="detailInfo" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgjvommon/DetailInfo.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="itemInfo" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgjvommon/ItemInfo.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="itemParams" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgjvommon/ItemParams.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="itemServices" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgjvommon/ItemServices.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="qualityCheckInfo" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgjvommon/QualityCheckInfo.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="rateInfo" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgjvommon/RateInfo.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="shopInfo" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgjvommon/ShopInfo.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="skuInfo" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgjvommon/SkuInfo.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="topImages" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgjvommon/TopImages.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="userInfo" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgjvommon/UserInfo.groovy"/>-->

    <!--&lt;!&ndash; H5私有模块：普通详情页 &ndash;&gt;-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="normalPrice" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/h5/normal/NormalPrice.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="collcationSet" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/h5/normal/CollcationSet.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="groupBuying" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/h5/normal/GroupBuying.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="listBanner" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/h5/normal/ListBanner.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="normalCountdown" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/h5/normal/NormalCountdown.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="preSaleInfo" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/h5/normal/PreSaleInfo.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="platformCoupons" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/h5/normal/PlatformCoupons.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="normalGiftList" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/h5/normal/GiftList.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="goodsBanners" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/h5/normal/GoodsBanners.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="normalShareInfo" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/h5/normal/NormalShareInfo.groovy"/>-->

    <!--&lt;!&ndash; H5私有模块：秒杀详情页 &ndash;&gt;-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="seckillInfo" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/h5/seckill/SeckillInfo.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="seckillPrice" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/h5/seckill/SeckillPrice.groovy"/>-->

    <!--&lt;!&ndash; H5私有模块：魔豆详情页（超值购） &ndash;&gt;-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="mdOverValueInfo" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/h5/mdovervalue/MdOverValueInfo.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="mdOverValuePrice" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/h5/mdovervalue/MdOverValuePrice.groovy"/>-->

    <!--&lt;!&ndash; H5私有模块：拼团详情页 &ndash;&gt;-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="pintuanInfo" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/h5/pintuan/PintuanInfo.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="pintuanPrice" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/h5/pintuan/PintuanPrice.groovy"/>-->
    <!-- H5私有模块：快抢详情页 -->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="rushInfo" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/h5/fastbuy/RushInfo.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="rushPrice" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/h5/fastbuy/RushPrice.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="rushCountdown" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/h5/fastbuy/RushCountdown.groovy"/>-->
    <!--<lang:groovy xmlns:lang="http://www.springframework.org/schema/lang" id="rushProgress" refresh-check-delay="2000"-->
                 <!--script-source="classpath:mgj/h5/fastbuy/RushProgress.groovy"/>-->



</beans>
