<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tesla="http://www.mogujie.com/schema/tesla"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.mogujie.com/schema/tesla http://www.mogujie.com/schema/tesla/tesla.xsd">

       <tesla:consumer id="itemExtraService" interface="com.mogujie.service.item.api.basic.ItemExtraService" group="${itemcenter.detail.group}" timeout="500"/>

       <tesla:consumer id="itemReadService" interface="com.mogujie.service.item.api.basic.ItemReadService" group="${itemcenter.detail.group}" timeout="500"/>

       <tesla:consumer id="commonMappingService" interface="com.mogujie.service.item.api.CommonMappingService" group="${itemcenter.detail.group}" timeout="500"/>

       <tesla:consumer id="shopFacade" interface="com.mogujie.service.item.api.basic.facade.ShopFacade" group="${itemcenter.detail.group}" timeout="500"/>

       <tesla:consumer id="itemSearchService" interface="com.mogujie.service.item.search.api.ItemSearchService" group="${itemcenter.detail.group}" timeout="500"/>

       <tesla:consumer id="itemRelevanceService" interface="com.mogujie.service.item.api.basic.ItemRelevanceService" group="${itemcenter.detail.group}" timeout="500"/>

       <tesla:consumer id="itemInitialSaleService" interface="com.mogujie.service.item.api.ItemInitialSaleService" group="${itemcenter.detail.group}" timeout="500"/>

       <tesla:consumer id="catCompRelationService" interface="com.mogujie.service.tangram.component.api.CatCompRelationService" />

       <tesla:consumer id="promotionReadService" interface="com.mogujie.service.hummer.api.PromotionReadService" group="${hummer.group}" timeout="200"/>

       <tesla:consumer id="collocationSetReadService" interface="com.mogujie.service.hummer.api.CollocationSetReadService" group="${hummer.group}" timeout="200"/>

       <tesla:consumer id="couponReadService" interface="com.mogujie.service.hummer.api.CouponReadService" group="${hummer.group}" timeout="200"/>

       <tesla:consumer id="maiLoInstallmentApi" interface="com.mogujie.pay.mailo.api.MaiLoInstallmentApi" group="${mailo.group}" timeout="300"/>

       <tesla:consumer id="maiLoUserBasicInfoApi" interface="com.mogujie.pay.mailo.api.MaiLoUserBasicInfoApi" group="${mailo.group}" timeout="300"/>

       <tesla:consumer id="maiLoUserCreditApi" interface="com.mogujie.pay.mailo.api.MaiLoUserCreditApi" group="${mailo.group}" timeout="300"/>

       <tesla:consumer id="maiLoUserInfoApi" interface="com.mogujie.pay.mailo.api.v2.MaiLoUserInfoApi" group="${mailo.group}" timeout="300"/>

       <tesla:consumer id="cartQueryService" interface="com.mogujie.cart.api.CartQueryService" timeout="300"/>

       <tesla:consumer id="shopOrderQueryService" interface="com.mogujie.service.trade.microservice.order.api.query.shoporder.ShopOrderQueryService" timeout="300"/>

       <tesla:consumer id="skuReadService" interface="com.mogujie.service.item.api.basic.SkuReadService" group="${itemcenter.detail.group}" timeout="300"/>

       <tesla:consumer id="inventoryReadService" interface="com.mogujie.service.inventory.api.InventoryReadService" timeout="300"/>

       <tesla:consumer id="preSaleService" interface="com.mogujie.service.item.api.basic.ItemPreSaleService" group="${itemcenter.detail.group}" timeout="300"/>

       <tesla:consumer id="searchUserSizeApi" interface="com.mogujie.darling.daren.service.SearchUserSizeApi" check="false"/>

       <tesla:consumer id="moguLiveItemExplainService" interface="com.mogujie.live.mogulive.api.service.MoguLiveItemExplainService"/>

       <tesla:consumer id="liveReadService" interface="com.mogujie.mogulive.service.LiveReadService"/>

       <tesla:consumer id="shopTagReadService" interface="com.mogujie.service.tagcenter.api.read.ShopTagReadService"/>
       
       <tesla:consumer id="paganiApiService" interface="com.mogujie.pagani.api.service.PaganiApiService" timeout="300"  version="1.0.0"/>

       <tesla:consumer id="buyerQueryService" interface="com.mogujie.service.trade.service.logistics.address.v1.api.BuyerQueryService"/>

       <tesla:consumer id="rushNocticeServiceApi" interface="com.mogujie.marketing.remind.RushNocticeServiceApi"/>
</beans>