<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>detail-web</artifactId>
        <groupId>com.mogujie.detail</groupId>
        <version>1.1.0.dsl</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <packaging>war</packaging>

    <artifactId>web</artifactId>

    <properties>
        <velocity.version>1.7</velocity.version>
        <velocity-tools.version>2.0</velocity-tools.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <version>2.4.8</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.mst</groupId>
            <artifactId>kvstore-client</artifactId>
            <version>1.2.2</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie</groupId>
            <artifactId>slardar-client</artifactId>
            <version>0.1.8</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.detail</groupId>
            <artifactId>spi</artifactId>
            <version>${detail.version}</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.detail</groupId>
            <artifactId>before</artifactId>
            <version>${detail.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity</artifactId>
            <version>${velocity.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-tools</artifactId>
            <version>${velocity-tools.version}</version>
        </dependency>
        <dependency>
            <groupId>com.mogujie.detail</groupId>
            <artifactId>detail-urlhub</artifactId>
            <version>1.2.2</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.android</groupId>
            <artifactId>mwpsdk-support</artifactId>
            <version>2.4.5</version>
            <exclusions>
                <exclusion>
                    <groupId>de.psdev.slf4j-android-logger</groupId>
                    <artifactId>slf4j-android-logger</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-android</artifactId>
                </exclusion>
            </exclusions>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>5.1.35</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>detailweb</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>2.1-alpha-1</version>
                <configuration>
                    <webResources>
                        <resource>
                            <!-- 元配置文件的目录，相对于pom.xml文件的路径 -->
                            <directory>src/main/webapp/WEB-INF</directory>
                            <!-- 目标路径 -->
                            <targetPath>WEB-INF</targetPath>
                            <filtering>true</filtering>
                            <excludes>
                                <exclude>**/*.groovy</exclude>
                            </excludes>
                        </resource>
                    </webResources>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.mortbay.jetty</groupId>
                <artifactId>maven-jetty-plugin</artifactId>
                <version>6.1.9</version>
                <configuration>
                    <contextPath>/</contextPath>
                    <connectors>
                        <connector implementation="org.mortbay.jetty.nio.SelectChannelConnector">
                            <port>8088</port>
                            <maxIdleTime>60000</maxIdleTime>
                        </connector>
                    </connectors>
                    <systemProperties>
                        <systemProperty>
                            <name>productionMode</name>
                            <value>false</value>
                        </systemProperty>
                    </systemProperties>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.groovy.maven</groupId>
                <artifactId>gmaven-plugin</artifactId>
                <version>1.0</version>
            </plugin>
        </plugins>

        <resources>
            <resource>
                <directory>src/main/resources/</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/translator/</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/webapp/WEB-INF</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

    <profiles>
        <profile>
            <id>deploy</id>
            <build>
                <filters>
                    <filter>/tmp/eless/conf/config.properties</filter>
                </filters>
            </build>
        </profile>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <filters>
                    <filter>../dev_config.properties</filter>
                </filters>
            </build>
        </profile>
    </profiles>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.codehaus.groovy.maven</groupId>
                <artifactId>gmaven-mojo</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy.maven.runtime</groupId>
                <artifactId>gmaven-runtime-1.6</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>1.4.17</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


</project>