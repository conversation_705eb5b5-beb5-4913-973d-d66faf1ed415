<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>detail-web</artifactId>
        <groupId>com.mogujie.detail</groupId>
        <version>1.1.0.dsl</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>module</artifactId>

    <properties>
        <waitress.version>1.5.6</waitress.version>
        <tag.version>1.2.2</tag.version>
        <rate.version>2.0.4.3</rate.version>
        <trade.version>1.0.24</trade.version>
        <muser.version>1.1.3.0</muser.version>
        <orderidquery.version>1.0.2</orderidquery.version>
        <sales.version>1.1.0.7</sales.version>
        <designcenter.version>1.1.2</designcenter.version>
        <jmockit.version>1.18</jmockit.version>
        <junit.version>4.12</junit.version>
        <planet.version>1.0.4</planet.version>
        <socialchannel.version>1.0.5</socialchannel.version>
        <modoubusiness.version>1.1.11</modoubusiness.version>
        <act-tag.version>2.0.0</act-tag.version>
        <themis.version>1.2.33</themis.version>
        <socialcontent.verison>1.0.17</socialcontent.verison>
        <uni.version>1.1.3</uni.version>
        <pay-mailo-api.version>1.5.6</pay-mailo-api.version>
        <relation-api.version>1.1.9</relation-api.version>
        <warehouse.version>1.0.4</warehouse.version>
        <trade.order.constants.version>1.0.13.22</trade.order.constants.version>
        <trade.microserviceorderapi.version>1.0.9.15</trade.microserviceorderapi.version>
        <openapi-utils.version>1.0.0</openapi-utils.version>
        <trade.order-process.version>1.0.4</trade.order-process.version>
        <trade-logistics.version>1.0.0.10</trade-logistics.version>
        <tangram-client.version>1.0.9</tangram-client.version>
        <enzo.version>3.3.8</enzo.version>
        <tangram-property.version>1.1.9</tangram-property.version>
        <traffic.version>0.0.4</traffic.version>
        <veyron-api.version>1.2.4</veyron-api.version>
        <mogulive.version>1.0.35</mogulive.version>
        <waitress-api.version>2.0.0.4</waitress-api.version>
        <prism-client.version>1.1.8</prism-client.version>
        <item-business.version>1.0.25</item-business.version>
        <msd-api.version>1.0.20</msd-api.version>
        <diana-api.version>1.1.6</diana-api.version>
        <liveitem.api.version>1.1.6</liveitem.api.version>
        <darling.api.version>1.5.0</darling.api.version>
        <union.api.version>1.0.7.2.8</union.api.version>
        <cart.api.version>2.0.5.5</cart.api.version>
        <aston.api.version>1.2.0</aston.api.version>
        <member.api.version>1.5</member.api.version>
        <subuser.api.version>1.1.7.3</subuser.api.version>
        <muser.version>1.1.3.0</muser.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.mogujie.mst</groupId>
            <artifactId>kvstore-client</artifactId>
            <version>1.2.2</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.contentcenter</groupId>
            <artifactId>contentcenter-client</artifactId>
            <version>${socialcontent.verison}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mogujie.tesla</groupId>
                    <artifactId>tesla-spring-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>kvstore-client</artifactId>
                    <groupId>com.mogujie.mst</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>org.jmockit</groupId>
            <artifactId>jmockit</artifactId>
            <version>${jmockit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.item</groupId>
            <artifactId>IcConverter</artifactId>
            <version>1.0.5</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.service.subuser</groupId>
            <artifactId>subuser-api</artifactId>
            <version>${subuser.api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>muser-api</artifactId>
            <version>${muser.version}</version>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.mogujie.detail</groupId>
            <artifactId>core</artifactId>
            <version>${detail.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mogujie.mst</groupId>
                    <artifactId>kvstore-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>item-center-api</artifactId>
            <version>2.1.0.20</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>itemaudit-api</artifactId>
            <version>1.0.18</version>
        </dependency>
        <dependency>
            <groupId>com.mogujie.union</groupId>
            <artifactId>cpsstandardjudge-api</artifactId>
            <version>1.3.0.2</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-test</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-test</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.service</groupId>
                    <artifactId>shopcenter-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.tesla</groupId>
                    <artifactId>tesla-support-dependency</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.raptor</groupId>
                    <artifactId>raptor-support-spring-orm</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.raptor</groupId>
                    <artifactId>raptor-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.raptor</groupId>
                    <artifactId>raptor-config-metabase</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.raptor</groupId>
                    <artifactId>raptor-monitor-sentry</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.jappcollector</groupId>
                    <artifactId>jappcollector</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.logback-extensions</groupId>
                    <artifactId>logback-ext-spring</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis-spring</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>rate-api</artifactId>
            <version>${rate.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mogujie.item.doraemon</groupId>
                    <artifactId>canon</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>socialchannel-api</artifactId>
            <version>${socialchannel.version}</version>
        </dependency>
        <dependency>
            <groupId>com.mogujie.trade</groupId>
            <artifactId>warehouse-api</artifactId>
            <version>${warehouse.version}</version>
        </dependency>
        <!-- relation service -->
        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>relation-api</artifactId>
            <version>${relation-api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mogujie.moguboot</groupId>
                    <artifactId>mogu-boot-autoconfigure</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>trade.service.orderidquery-api</artifactId>
            <version>${orderidquery.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpclient</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.tesla</groupId>
                    <artifactId>tesla-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.service</groupId>
                    <artifactId>trade.service.order-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.metabase</groupId>
                    <artifactId>metabase-spring-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.metabase</groupId>
                    <artifactId>metabase-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.metabase</groupId>
                    <artifactId>metabase-admin</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.service</groupId>
                    <artifactId>trade.service.base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mogujie.trade</groupId>
            <artifactId>trade.base.order.constants</artifactId>
            <version>${trade.order.constants.version}</version>
        </dependency>
        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>trade.service.order-microserviceorderapi</artifactId>
            <version>${trade.microserviceorderapi.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mogujie.ecommerce</groupId>
                    <artifactId>metadata</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mogujie.trade</groupId>
            <artifactId>trade-common-interceptors</artifactId>
            <version>1.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>trade.service.order-api</artifactId>
            <version>${trade.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mogujie.mst</groupId>
                    <artifactId>kvstore-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.corgi</groupId>
                    <artifactId>corgi-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mogujie.algo</groupId>
            <artifactId>topn-client</artifactId>
            <version>3.1.6</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mogujie.tesla</groupId>
                    <artifactId>tesla-spring-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.metabase</groupId>
                    <artifactId>metabase-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.metabase</groupId>
                    <artifactId>metabase-spring-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.metabase</groupId>
                    <artifactId>metabase-spring-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.metabase</groupId>
                    <artifactId>metabase-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.flatbuffers</groupId>
                    <artifactId>flatbuffers-java</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.discover</groupId>
                    <artifactId>discover-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.discover</groupId>
                    <artifactId>discover-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mogujie.corgi</groupId>
            <artifactId>corgi-client</artifactId>
            <version>1.0.9.5.4</version>
        </dependency>
        <dependency>
            <groupId>com.mogujie.openapi</groupId>
            <artifactId>openapi-utils</artifactId>
            <version>${openapi-utils.version}</version>
        </dependency>
        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>trade.service.order-process</artifactId>
            <version>${trade.order-process.version}</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>muser-api</artifactId>
            <version>${muser.version}</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.actcenter</groupId>
            <artifactId>act-tag-client</artifactId>
            <version>${act-tag.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mogujie.themis</groupId>
            <artifactId>themis-client</artifactId>
            <version>${themis.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mogujie.corgi</groupId>
                    <artifactId>corgi-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.service</groupId>
                    <artifactId>category-service-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.mogujie.trade</groupId>
            <artifactId>trade-sales-query</artifactId>
            <version>${sales.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mogujie.tesla</groupId>
                    <artifactId>tesla-spring-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.tesla</groupId>
                    <artifactId>tesla-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.trade</groupId>
                    <artifactId>trade.switch</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.mst</groupId>
                    <artifactId>kvstore-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>waitress-api</artifactId>
            <version>${waitress-api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>designcenter-api</artifactId>
            <version>${designcenter.version}</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.modou.modoubusiness</groupId>
            <artifactId>api</artifactId>
            <version>${modoubusiness.version}</version>
        </dependency>

        <!-- 白付美白名单接口 -->
        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>pay-mailo-api</artifactId>
            <version>${pay-mailo-api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mogujie.corgi</groupId>
                    <artifactId>corgi-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.mst</groupId>
                    <artifactId>kvstore-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>trade.service.logistics-api</artifactId>
            <version>${trade-logistics.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mogujie.tesla</groupId>
                    <artifactId>tesla-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>tangram-component-client</artifactId>
            <version>${tangram-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>tangram-property-client</artifactId>
            <version>${tangram-property.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mogujie.mst</groupId>
                    <artifactId>kvstore-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.service</groupId>
                    <artifactId>category-service-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mogujie.traffic</groupId>
            <artifactId>traffic-core</artifactId>
            <version>${traffic.version}</version>
        </dependency>
        <!-- ip2addr-->
        <dependency>
            <groupId>com.mogujie.utils</groupId>
            <artifactId>iplib</artifactId>
            <version>1.0.5</version>
        </dependency>
        <dependency>
            <groupId>com.mogujie.planet</groupId>
            <artifactId>planet-api</artifactId>
            <version>${planet.version}</version>
        </dependency>
        <dependency>
            <groupId>com.mogujie.marketing</groupId>
            <artifactId>veyron-api</artifactId>
            <version>${veyron-api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mogujie.service</groupId>
                    <artifactId>category-service-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--直播二方包-->
        <dependency>
            <groupId>com.mogujie.live</groupId>
            <artifactId>mogulive-api</artifactId>
            <version>1.1.25</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.live</groupId>
            <artifactId>fendi-api</artifactId>
            <version>1.0.5</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.social</groupId>
            <artifactId>mogulive-api</artifactId>
            <version>${mogulive.version}</version>
        </dependency>
        <dependency>
            <groupId>com.mogujie.algo.prism</groupId>
            <artifactId>prism-client</artifactId>
            <version>${prism-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mogujie.mst</groupId>
                    <artifactId>kvstore-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>item-business-api</artifactId>
            <version>${item-business.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mogujie.service</groupId>
                    <artifactId>category-service-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--美丽买手店佣金、店主数据接口-->
        <dependency>
            <groupId>com.mogujie</groupId>
            <artifactId>msd-api</artifactId>
            <version>${msd-api.version}</version>
        </dependency>
        <!-- 快抢夺宝 -->
        <dependency>
            <groupId>com.mogujie.marketing</groupId>
            <artifactId>aston-api</artifactId>
            <version>${aston.api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.mogujie.service.diana</groupId>
            <artifactId>diana-api</artifactId>
            <version>${diana-api.version}</version>
        </dependency>

        <!--查询店铺当日加购-->
        <dependency>
            <groupId>com.mogujie.trade</groupId>
            <artifactId>cart-api</artifactId>
            <version>${cart.api.version}</version>
        </dependency>

        <!--直播供应链商品-->
        <dependency>
            <groupId>com.mogujie.liveitem</groupId>
            <artifactId>item-api</artifactId>
            <version>${liveitem.api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.mogujie.mars</groupId>
            <artifactId>mars-api</artifactId>
            <version>1.0.24</version>
        </dependency>

        <dependency>
            <groupId>com.mogujie.livelist</groupId>
            <artifactId>livelist-api</artifactId>
            <version>1.1.11.4</version>
        </dependency>
        <dependency>
            <groupId>com.mogujie.live</groupId>
            <artifactId>mogulive-console-api</artifactId>
            <version>1.1.1.1</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mogujie.live</groupId>
                    <artifactId>mogulive-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mogujie.live</groupId>
                    <artifactId>live-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mogujie.darling</groupId>
            <artifactId>client</artifactId>
            <version>${darling.api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.mogujie.union</groupId>
            <artifactId>molitongkit-api</artifactId>
            <version>1.0.7.2.8</version>
        </dependency>
        <dependency>
            <groupId>com.mogujie.fashion</groupId>
            <artifactId>fashion-api</artifactId>
            <version>1.0.14</version>
        </dependency>
        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>pay-insurance-api</artifactId>
            <version>1.2.45</version>
        </dependency>
        <dependency>
            <groupId>com.mogujie.member</groupId>
            <artifactId>member-kir-api</artifactId>
            <version>${member.api.version}</version>
        </dependency>

        <!--图墙二方包-->
        <dependency>
            <groupId>com.mogujie.pagani</groupId>
            <artifactId>pagani-api</artifactId>
            <version>1.1.7</version>
        </dependency>

        <!--直播信息接口-->
        <dependency>
            <groupId>com.mogujie.live</groupId>
            <artifactId>prada-api</artifactId>
        </dependency>
    </dependencies>

</project>
