package com.mogujie.detail.module.detail.provider;

import com.mogujie.detail.module.ProviderBaseTest;
import junit.framework.Assert;
import org.junit.Before;
import org.junit.Test;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * Created by xiaoyao on 16/8/18.
 */
public class DetailDOProviderTest extends ProviderBaseTest<DetailDOProvider> {


    @Before
    public void init() throws Exception {
        super.init(DetailDOProvider.class);
    }

    @Test
    public void testGetType() {

        DetailDOProvider provider = new DetailDOProvider();

        Type type = provider.getClass().getGenericInterfaces()[0];

        Type trueType = ((ParameterizedType) type).getActualTypeArguments()[0];

        System.out.println(trueType);
    }

    @Test
    public void testEmit() {
        Assert.assertNotNull(provider.emit(getDetailContext()));
    }



}