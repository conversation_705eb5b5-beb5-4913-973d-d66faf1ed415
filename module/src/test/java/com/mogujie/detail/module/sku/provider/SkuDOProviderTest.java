package com.mogujie.detail.module.sku.provider;

import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.util.ImageUtil;
import com.mogujie.detail.core.util.NumUtil;
import com.mogujie.detail.module.ProviderBaseTest;
import com.mogujie.detail.module.sku.domain.*;
import com.mogujie.detail.module.sku.spi.ISkuParser;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.service.item.domain.basic.SkuAttributionDO;
import junit.framework.Assert;
import org.apache.commons.lang.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/9/8.
 */
public class SkuDOProviderTest extends ProviderBaseTest<SkuDOProvider> {

    @Before
    public void setUp() throws Exception {
        super.init(SkuDOProvider.class);
    }

    @Test
    public void testEmit() throws Exception {
        addAutowiredSpi(ISkuParser.class, new ISkuParser() {
            @Override
            public void parseSku(DetailContext context, SkuDO skuDO) {
                DetailItemDO item = context.getItemDO();
                List<ItemSkuDO> skus = item.getItemSkuDOList();
                List<SkuData> skuDatas = new ArrayList<>();
                if (CollectionUtils.isEmpty(skus)) {
                    return;
                }
                boolean canBuy = this.canBuy(item);
                String styleKey = "";
                String sizeKey = "";
                boolean isSizeDefault = false;
                boolean isStyleDefault = false;
                List<String> tmpStyle = new ArrayList<>();
                List<String> tmpSize = new ArrayList<>();
                List<StylePropData> styles = new ArrayList<>();
                List<SizePropData> sizes = new ArrayList<>();
                int styleId = 1;
                int sizeId = 100;
                Map<String, Integer> styleMap = new HashMap<>();
                Map<String, Integer> sizeMap = new HashMap<>();

                int index = 0;

                int lowestPrice = 0;
                int highestPrice = 0;
                int lowestNowPrice = 0;
                int highestNowPrice = 0;
                Map<String, Map<String, String>> attrsMap = new HashMap<>();
                int totalStock = 0;
                for (ItemSkuDO sku : skus) {
                    SkuData skuData = new SkuData();
                    skuData.setCurrency("¥");
                    skuData.setImg(null == sku.getImage() ? ImageUtil.img(item.getMainImage()) : ImageUtil.img(sku.getImage()));
                    skuData.setNowprice(sku.getNowPrice());
                    skuData.setPrice(sku.getPrice().intValue());
                    skuData.setStock(canBuy ? sku.getQuantity() : 0);
                    skuData.setXdSkuId(IdConvertor.idToUrl(sku.getXdSkuId()));
                    skuData.setStockId(IdConvertor.idToUrl(sku.getSkuId()));
                    attrsMap.put(skuData.getStockId(), parseProps(skuData, sku.getAttributions()));
                    totalStock += skuData.getStock();
                    skuDatas.add(skuData);
                }
                skuDO.setTotalStock(totalStock);

                for (SkuData skuData : skuDatas) {
                    Map<String, String> attrMap = attrsMap.get(skuData.getStockId());
                    if (StringUtils.isEmpty(styleKey)) {
                        styleKey = attrMap.get("style");
                        sizeKey = attrMap.get("size");
                    }

                    String realStyle = attrMap.get("style") == null ? "" : (attrMap.get("style").equals(styleKey)) ? skuData.getStyle() : skuData.getSize();
                    String realSize = attrMap.get("size") == null ? "" : (attrMap.get("size").equals(sizeKey)) ? skuData.getSize() : skuData.getStyle();

                    skuData.setStyle(StringUtils.isEmpty(realStyle) ? "默认" : realStyle);
                    skuData.setSize(StringUtils.isEmpty(realSize) ? "均码" : realSize);

                    if (!StringUtils.isEmpty(skuData.getStyle()) && !tmpStyle.contains(skuData.getStyle())) {
                        tmpStyle.add(skuData.getStyle());
                        if (StringUtils.isEmpty(realStyle) && false == isStyleDefault) {
                            isStyleDefault = true;
                        }
                        StylePropData styleProp = new StylePropData();
                        styleProp.setDefault(false);//StringUtils.isEmpty(realStyle));
                        styleProp.setType("style");
                        styleProp.setName(skuData.getStyle());
                        styleProp.setIndex(styleId);
                        styleProp.setStyleId(styleId);
                        styles.add(styleProp);
                        styleMap.put(skuData.getStyle(), styleId);
                        styleId++;
                    }

                    if (!StringUtils.isEmpty(skuData.getSize()) && !tmpSize.contains(skuData.getSize())) {
                        tmpSize.add(skuData.getSize());
                        if (StringUtils.isEmpty(realSize) && false == isSizeDefault) {
                            isSizeDefault = true;
                        }

                        SizePropData sizeProp = new SizePropData();
                        sizeProp.setDefault(false);//StringUtils.isEmpty(realSize));
                        sizeProp.setType("size");
                        sizeProp.setName(skuData.getSize());
                        sizeProp.setIndex(sizeId);
                        sizeProp.setSizeId(sizeId);
                        sizes.add(sizeProp);
                        sizeMap.put(skuData.getSize(), sizeId);
                        sizeId++;
                    }

                    // 给sku添加index
                    if (!StringUtils.isEmpty(skuData.getStyle()) && null != styleMap.get(skuData.getStyle())) {
                        skuData.setStyleId(styleMap.get(skuData.getStyle()));
                    }

                    if (!StringUtils.isEmpty(skuData.getSize()) && null != sizeMap.get(skuData.getSize())) {
                        skuData.setSizeId(sizeMap.get(skuData.getSize()));
                    }

                    if (index == 0) {
                        highestPrice = lowestPrice = skuData.getPrice();
                        highestNowPrice = lowestNowPrice = skuData.getNowprice();
                    }

                    if (skuData.getPrice() > highestPrice) {
                        highestPrice = skuData.getPrice();
                        highestNowPrice = skuData.getNowprice();
                    }
                    if (skuData.getPrice() < lowestPrice) {
                        lowestPrice = skuData.getPrice();
                        lowestNowPrice = skuData.getNowprice();
                    }
                    index++;
                }

                List<PropInfo> props = new ArrayList<>();
                PropInfo<StylePropData> stylePropInfo = new PropInfo();
                stylePropInfo.setLabel(StringUtils.isEmpty(styleKey) ? "款式" : styleKey + ":");
                stylePropInfo.setList(styles);
                //默认sku能加库存的需求
                stylePropInfo.setDefault(false); //isStyleDefault);
                props.add(stylePropInfo);
                PropInfo<SizePropData> sizePropInfo = new PropInfo();
                sizePropInfo.setLabel(StringUtils.isEmpty(sizeKey) ? "尺码" : sizeKey + ":");
                sizePropInfo.setList(sizes);
                sizePropInfo.setDefault(false); //isSizeDefault);
                props.add(sizePropInfo);
                skuDO.setProps(props);

                skuDO.setStyleKey(StringUtils.isEmpty(styleKey) ? "款式" : styleKey);
                skuDO.setSizeKey(StringUtils.isEmpty(sizeKey) ? "尺码" : sizeKey);
                Collections.sort(skuDatas, new Comparator<SkuData>() {
                    @Override
                    public int compare(SkuData o1, SkuData o2) {
                        if (o1.getStyleId() == o2.getStyleId()) {
                            return o1.getSizeId() == o2.getSizeId() ? 0 : (o1.getSizeId() > o2.getSizeId() ? 1 : -1);
                        } else {
                            return o1.getStyleId() > o2.getStyleId() ? 1 : -1;
                        }
                    }
                });

                skuDO.setSkus(skuDatas);
                //没有价格区间
                if (highestNowPrice == lowestNowPrice) {
                    skuDO.setPriceRange(formatPrice(lowestNowPrice));
                } else {
                    skuDO.setPriceRange(formatPrice(lowestNowPrice) + '~' + formatPrice(highestNowPrice));
                }
                skuDO.setDefaultPrice(skuDO.getPriceRange());
            }

            protected boolean canBuy(DetailItemDO item) {
                String whiteList = "";
                if (item.getIsDeleted() == 1) {
                    return false;
                }

                if (item.getIsShelf() == 1) {
                    return false;
                }

                int time = (int) (System.currentTimeMillis() / 1000);

                if (item.getStatus() == 1 || (item.getStatus() == 3 && (StringUtils.isEmpty(whiteList) || !whiteList.contains(String.valueOf(item.getShopId())))) || item.getStatus() < 0) {
                    return false;
                }

                if (item.getTotalStock() <= 0) {
                    return false;
                }

                return true;
            }

            protected String formatPrice(double price) {
                return "¥" + NumUtil.formatNum(price / 100D);
            }

            protected Map<String, String> parseProps(SkuData skuData, List<SkuAttributionDO> attrs) {
                int index = 0;
                Map<String, String> mapping = new HashMap<>();
                if (CollectionUtils.isEmpty(attrs)) {
                    mapping.put("style", null);
                    mapping.put("size", null);
                    return mapping;
                }

                for (SkuAttributionDO attr : attrs) {
                    String attrName = "";

                    if ("style".equals(attr.getName())) {
                        attrName = "颜色";
                    } else if ("size".equals(attr.getName())) {
                        attrName = "规格";
                    }
                    if (index == 0) {
                        skuData.setStyle(attr.getValue());
                        mapping.put("style", attrName);
                    } else {
                        skuData.setSize(attr.getValue());
                        mapping.put("size", attrName);
                    }
                    index++;
                }
                return mapping;
            }
        });

        Assert.assertNotNull(provider.emit(getDetailContext()));
    }
}