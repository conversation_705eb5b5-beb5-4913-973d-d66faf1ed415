package com.mogujie.detail.module.oneYuanTreasure.provider;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.module.ProviderBaseTest;
import com.mogujie.detail.module.extra.provider.ExtraDOProvider;
import com.mogujie.detail.module.oneYuanTreasure.OneYuanTreasureDOProvider;
import com.mogujie.detail.module.oneYuanTreasure.domain.OneYuanTreasureDO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * Created by houan on 18/7/10.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:oneyuantreasure-test-context.xml"})
public class OneYuanTreasureDOProviderTest extends ProviderBaseTest<ExtraDOProvider> {

    @Autowired
    private OneYuanTreasureDOProvider oneYuanTreasureDOProvider;

    @Before
    public void setUp() throws Exception {
        super.init(OneYuanTreasureDOProvider.class);
    }

    @Test
    public void testEmit() throws Exception {
        oneYuanTreasureDOProvider.init();
        DetailContext context = getDetailContext();
        context.getItemDO().setItemId(761307621);
        context.setLoginUserId(93105274L);
        OneYuanTreasureDO result =  oneYuanTreasureDOProvider.emit(context);
        Assert.assertNotNull(result);

        context.setLoginUserId(93105275L);
        result =  oneYuanTreasureDOProvider.emit(context);
        Assert.assertNotNull(result);
    }


}
