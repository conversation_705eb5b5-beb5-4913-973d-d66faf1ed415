package com.mogujie.detail.module.itemTags.provider;

import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.module.ProviderBaseTest;
import mockit.integration.junit4.JMockit;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

/**
 * Created by x<PERSON><PERSON><PERSON> on 16/9/8.
 */
@RunWith(JMockit.class)
public class ItemTagsDOProviderTest extends ProviderBaseTest<ItemTagsDOProvider> {

    @Autowired
    private ItemTagsDOProvider itemTagsDOProvider;

    @Before
    public void setUp() throws Exception {
        ApplicationContext context = new ClassPathXmlApplicationContext("classpath*:itemtags-test-context.xml");
        itemTagsDOProvider = context.getBean(ItemTagsDOProvider.class);
        CommonSwitchUtil commonSwitchUtil = context.getBean(CommonSwitchUtil.class);
        addAutowiredSpi(itemTagsDOProvider, CommonSwitchUtil.class, commonSwitchUtil);
        itemTagsDOProvider.init();
        super.init(ItemTagsDOProvider.class);
    }

    @Test
    public void testEmit() throws Exception {
//        final List<ItemServiceTag> serviceTags = new ArrayList<>();
//        final DetailContext detailContext = getDetailContext();
//        new Expectations(ItemDetailTagUtils.class) {{
//            Deencapsulation.invoke(ItemDetailTagUtils.class, "queryItemServiceTag", detailContext.getItemId(), detailContext.getItemDO().getJsonExtra());
//            result = serviceTags;
//        }};
//        Assert.assertNotNull(itemTagsDOProvider.emit(detailContext));
    }
}