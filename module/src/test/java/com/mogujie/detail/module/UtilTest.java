package com.mogujie.detail.module;

import com.alibaba.fastjson.JSON;
import com.mogujie.detail.core.util.NumUtil;
import com.mogujie.detail.module.coupon.domain.CouponDO;
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO;
import com.mogujie.service.item.api.basic.ItemReadService;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.query.QueryItemOptions;
import com.mogujie.service.item.domain.result.BaseResultDO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import com.mogujie.tesla.core.ReferConfig;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;

import javax.swing.*;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectOutputStream;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * Created by anshi on 18/3/6.
 */
public class UtilTest {

    @Test
    public void testSerialization(){
        List<CouponDO> list = Collections.emptyList();
//        list.add(new CouponDO());
        byte[] bytes = toByteForObject("key", list);
        System.out.println(new String(bytes, Charset.forName("utf-8")));
    }

    public byte[] toByteForObject(String key, Object obj) {
        if (null == obj) {
            return new byte[0];
        }
        byte[] bytes = null;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            ObjectOutputStream oos = new ObjectOutputStream(bos);
            oos.writeObject(obj);
            oos.flush();
            bytes = bos.toByteArray();
            oos.close();
            bos.close();
        } catch (IOException ex) {
            ex.printStackTrace();
        }
        return bytes;
    }

    @Test
    public void test() throws Exception {
        ReferConfig<ItemReadService> referConfig = new ReferConfig<>(ItemReadService.class);
        referConfig.setTargetAddress("***********:20031");
        ItemReadService itemReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(referConfig);
        QueryItemOptions options = new QueryItemOptions();
//        options.setQueryTopImage(true);
        options.setQuerySkuInfo(true);
        BaseResultDO<ItemDO> ret = itemReadService.queryItemById(672003996L, options);
        if (ret!=null && ret.isSuccess()){
            System.out.println(JSON.toJSONString(ret.getResult()));
        }
    }

    @Test
    public void testPrice() {
        System.out.println("¥" + NumUtil.formatPriceDrawer(10000));
        System.out.println("¥" + NumUtil.formatPriceDrawer(10010));
        System.out.println("¥" + NumUtil.formatPriceDrawer(10001));
        System.out.println("¥" + NumUtil.formatPriceDrawer(9900));
        System.out.println("¥" + NumUtil.formatPriceDrawer(9990));
        System.out.println("¥" + NumUtil.formatPriceDrawer(9999));
        System.out.println("¥" + NumUtil.formatPriceDrawer(99900));
        System.out.println("¥" + NumUtil.formatPriceDrawer(99990));
        System.out.println("¥" + NumUtil.formatPriceDrawer(99999));
        System.out.println("¥" + NumUtil.formatPriceDrawer(100000));
        System.out.println("¥" + NumUtil.formatPriceDrawer(100010));
        System.out.println("¥" + NumUtil.formatPriceDrawer(100001));
        System.out.println("¥" + NumUtil.formatPriceDrawer(999900));
        System.out.println("¥" + NumUtil.formatPriceDrawer(999990));
        System.out.println("¥" + NumUtil.formatPriceDrawer(999999));
        System.out.println("¥" + NumUtil.formatPriceDrawer(1000000));
        System.out.println("¥" + NumUtil.formatPriceDrawer(1000010));
        System.out.println("¥" + NumUtil.formatPriceDrawer(1000001));
        System.out.println("¥" + NumUtil.formatPriceDrawer(9999900));
        System.out.println("¥" + NumUtil.formatPriceDrawer(1010000));
        System.out.println("¥" + NumUtil.formatPriceDrawer(1100000));
        System.out.println("¥" + NumUtil.formatPriceDrawer(1990001));
        System.out.println("¥" + NumUtil.formatPriceDrawer(299901));
    }
}
