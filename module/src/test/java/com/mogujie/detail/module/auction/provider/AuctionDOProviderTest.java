package com.mogujie.detail.module.auction.provider;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.module.ProviderBaseTest;
import com.mogujie.detail.module.rate.spi.IUserProfileProvider;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @AUTUOR: lengshan
 * @DESCRIPTION: TODO
 * @DATE: 2019/8/14 上午10:14
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:auction-test-context.xml"})
public class AuctionDOProviderTest extends ProviderBaseTest<AuctionDOProvider> {

    @Autowired
    private AuctionDOProvider auctionDOProvider;

    @Before
    public void setUp() throws Exception {
        super.init(AuctionDOProvider.class);
        auctionDOProvider.init();
    }

    @Test
    public void testEmit() throws Exception {
        addAutowiredSpi(auctionDOProvider, IUserProfileProvider.class, new IUserProfileProvider() {
            @Override
            public String getProfilePrefix(DetailContext context) {
                return "";
            }

            @Override
            public String getAnonymousImg(DetailContext context, long userId) {
                return "";
            }
        });
        Assert.assertNotNull(auctionDOProvider.emit(getDetailContext()));
    }

    @Test
    public void testEmit2() throws Exception {
        addAutowiredSpi(auctionDOProvider, IUserProfileProvider.class, new IUserProfileProvider() {
            @Override
            public String getProfilePrefix(DetailContext context) {
                return "";
            }

            @Override
            public String getAnonymousImg(DetailContext context, long userId) {
                return "";
            }
        });
        Assert.assertNotNull(auctionDOProvider.emit(getDetailContext(201293545L)));
    }

}