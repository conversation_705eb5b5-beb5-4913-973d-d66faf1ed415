package com.mogujie.detail.module.activity.provider;

import org.junit.Test;

import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by <PERSON><PERSON><PERSON> on 17/6/13.
 */
public class TestDOA {
    class InnerDO {
        String name;
        int age;
    }

    public InnerDO getDO(){
        InnerDO innerDO = new InnerDO();
        innerDO.age = 10;
        innerDO.name = "name";
        return innerDO;
    }

    @Test
    public void testNullValue(){
        ConcurrentHashMap<String, Object> map= new ConcurrentHashMap<>();
        map.put("1", "1");
        map.put("2",null);
    }
}
