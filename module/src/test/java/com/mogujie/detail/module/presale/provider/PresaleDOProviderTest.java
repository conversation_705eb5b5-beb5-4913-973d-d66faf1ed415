package com.mogujie.detail.module.presale.provider;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.util.MaitUtil;
import com.mogujie.detail.module.ProviderBaseTest;
import com.mogujie.service.item.domain.basic.ItemPreSaleDO;
import junit.framework.Assert;
import mockit.Deencapsulation;
import mockit.Expectations;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by xiaoyao on 16/11/2.
 */
public class PresaleDOProviderTest extends ProviderBaseTest<PresaleDOProvider> {

    @Before
    public void setUp() throws Exception {
        super.init(PresaleDOProvider.class);
    }

    @Test
    public void testEmit() throws Exception {
        Assert.assertNull(provider.emit(getDetailContext(720016386L)));
    }

    @Test
    public void testEmit2() throws Exception {
        DetailContext context = getDetailContext();
        ItemPreSaleDO preSale = new ItemPreSaleDO();
        int nowTime = (int)(System.currentTimeMillis()/1000);
        preSale.setStart(nowTime-1000);
        preSale.setEnd(nowTime+2000);
        preSale.setDeposit(100);
        preSale.setPrice(1000);
        context.getItemDO().setItemPreSaleDO(preSale);
        provider.emit(context);
    }

    @Test
    public void testEmit3() throws Exception {
        final List<Map<String, Object>> data = new ArrayList<>(2);
        data.add(new HashMap<String, Object>(){{
            put("ruleIcon", "http://test");
        }});
        new Expectations(MaitUtil.class) {{
            Deencapsulation.invoke(MaitUtil.class, "getMaitData", 5191L);
            result = data;
        }};
        DetailContext context = getDetailContext();
        ItemPreSaleDO preSale = new ItemPreSaleDO();
        int nowTime = (int)(System.currentTimeMillis()/1000);
        preSale.setStart(nowTime-1000);
        preSale.setEnd(nowTime+2000);
        preSale.setDeposit(100);
        preSale.setPrice(1000);
        context.getItemDO().setItemPreSaleDO(preSale);
        provider.emit(context);
    }

    @Test
    public void testEmit4() throws Exception {
        DetailContext context = getDetailContext();
        ItemPreSaleDO preSale = new ItemPreSaleDO();
        int nowTime = (int)(System.currentTimeMillis()/1000);
        preSale.setStart(nowTime-1000);
        preSale.setEnd(nowTime-500);
        preSale.setDeposit(100);
        preSale.setPrice(1000);
        context.getItemDO().setItemPreSaleDO(preSale);
        provider.emit(context);
    }

}