package com.mogujie.detail.module.shop.provider;

import com.meili.service.shopcenter.api.ShopReadService;
import com.meili.service.shopcenter.domain.entity.ShopInfo;
import com.meili.service.shopcenter.result.Result;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.module.ProviderBaseTest;
import com.mogujie.detail.module.shop.domain.ShopDO;
import com.mogujie.detail.module.shop.domain.ShopDsr;
import com.mogujie.detail.module.shop.domain.ShopServicesDO;
import com.mogujie.detail.module.shop.spi.*;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import junit.framework.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Created by xiaoyao on 16/9/8.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:shop-test-context.xml"})
public class ShopDOProviderTest extends ProviderBaseTest<ShopDOProvider> {


    @Autowired
    private ShopDOProvider shopDOProvider;

    private ShopReadService shopReadService;
    @Before
    public void setUp() throws Exception {
        super.init(ShopDOProvider.class);
        shopDOProvider.init();
        shopReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ShopReadService.class);
    }

    @Test
    public void testEmit() throws Exception {
        addAutowiredSpi(shopDOProvider, IServiceConfProvider.class, new IServiceConfProvider() {
            @Override
            public String getServiceName(DetailContext context, String key) {
                return "abc";
            }
        });
        addAutowiredSpi(shopDOProvider, IShopCategoryUrlProvider.class, new IShopCategoryUrlProvider() {
            @Override
            public String getShopCategoryUrl(DetailContext context, Integer cid) {
                return "hahah";
            }
        });
        addAutowiredSpi(shopDOProvider, IShopCollectInfoProvider.class, new IShopCollectInfoProvider() {
            @Override
            public Boolean isCollected(DetailContext context) {
                return false;
            }
        });
        addAutowiredSpi(shopDOProvider, IShopDsrProvider.class, new IShopDsrProvider() {
            @Override
            public List<ShopDsr> listShopDsr(DetailContext context, ShopDO shopDO) {
                ShopDsr shopDsr = new ShopDsr();
                shopDsr.setIsBetter(false);
                shopDsr.setName("haha");
                shopDsr.setScore(11.1f);
                List<ShopDsr> dsrs = new ArrayList<ShopDsr>(1);
                dsrs.add(shopDsr);
                return dsrs;
            }
        });

        addAutowiredSpi(shopDOProvider, IShopHeaderProvider.class, new IShopHeaderProvider() {
            @Override
            public Map<String, Object> getShopHeader(DetailContext context) {
                return Collections.emptyMap();
            }
        });

        addAutowiredSpi(shopDOProvider, IShopSalesProvider.class, new IShopCategoryUrlProvider() {
            @Override
            public String getShopCategoryUrl(DetailContext context, Integer cid) {
                return "";
            }
        });

        addAutowiredSpi(shopDOProvider, IShopTagProvider.class, new IShopTagProvider() {
            @Override
            public String getShopTag(DetailContext context, ShopInfo shopInfo) {
                return "";
            }
        });

        addAutowiredSpi(shopDOProvider, IShopUrlProvider.class, new IShopUrlProvider() {
            @Override
            public String getShopUrl(DetailContext context) {
                return "";
            }

            @Override
            public String getShopAllGoodsUrl(DetailContext context) {
                return "";
            }
        });

        addAutowiredSpi(shopDOProvider, IWaitressProvider.class, new IWaitressProvider() {
            @Override
            public ShopServicesDO listService(DetailContext context) {
                return null;
            }
        });
        DetailContext context = getDetailContext();
        Result<ShopInfo> ret = shopReadService.queryShopByShopId(context.getItemDO().getShopId());
        context.getItemDO().setShopInfo(ret.getData());
        Assert.assertNotNull(shopDOProvider.emit(getDetailContext()));
    }

    @Test
    public void testEmit2() throws Exception {
        addAutowiredSpi(shopDOProvider, IServiceConfProvider.class, new IServiceConfProvider() {
            @Override
            public String getServiceName(DetailContext context, String key) {
                return "abc";
            }
        });
        addAutowiredSpi(shopDOProvider, IShopCategoryUrlProvider.class, new IShopCategoryUrlProvider() {
            @Override
            public String getShopCategoryUrl(DetailContext context, Integer cid) {
                return "hahah";
            }
        });
        addAutowiredSpi(shopDOProvider, IShopCollectInfoProvider.class, new IShopCollectInfoProvider() {
            @Override
            public Boolean isCollected(DetailContext context) {
                return false;
            }
        });
        addAutowiredSpi(shopDOProvider, IShopDsrProvider.class, new IShopDsrProvider() {
            @Override
            public List<ShopDsr> listShopDsr(DetailContext context, ShopDO shopDO) {
                ShopDsr shopDsr = new ShopDsr();
                shopDsr.setIsBetter(false);
                shopDsr.setName("haha");
                shopDsr.setScore(11.1f);
                List<ShopDsr> dsrs = new ArrayList<ShopDsr>(1);
                dsrs.add(shopDsr);
                return dsrs;
            }
        });

        addAutowiredSpi(shopDOProvider, IShopHeaderProvider.class, new IShopHeaderProvider() {
            @Override
            public Map<String, Object> getShopHeader(DetailContext context) {
                return Collections.emptyMap();
            }
        });

        addAutowiredSpi(shopDOProvider, IShopSalesProvider.class, new IShopCategoryUrlProvider() {
            @Override
            public String getShopCategoryUrl(DetailContext context, Integer cid) {
                return "";
            }
        });

        addAutowiredSpi(shopDOProvider, IShopTagProvider.class, new IShopTagProvider() {
            @Override
            public String getShopTag(DetailContext context, ShopInfo shopInfo) {
                return "";
            }
        });

        addAutowiredSpi(shopDOProvider, IShopUrlProvider.class, new IShopUrlProvider() {
            @Override
            public String getShopUrl(DetailContext context) {
                return "";
            }

            @Override
            public String getShopAllGoodsUrl(DetailContext context) {
                return "";
            }
        });

        addAutowiredSpi(shopDOProvider, IWaitressProvider.class, new IWaitressProvider() {
            @Override
            public ShopServicesDO listService(DetailContext context) {
                return null;
            }
        });

        DetailContext context = getDetailContext();
        context.setDyn(true);
        Result<ShopInfo> ret = shopReadService.queryShopByShopId(context.getItemDO().getShopId());
        context.getItemDO().setShopInfo(ret.getData());
        Assert.assertNotNull(shopDOProvider.emit(context));
    }

    @Test
    public void testEmit3() throws Exception {
        addAutowiredSpi(shopDOProvider, IServiceConfProvider.class, new IServiceConfProvider() {
            @Override
            public String getServiceName(DetailContext context, String key) {
                return "abc";
            }
        });
        addAutowiredSpi(shopDOProvider, IShopCategoryUrlProvider.class, new IShopCategoryUrlProvider() {
            @Override
            public String getShopCategoryUrl(DetailContext context, Integer cid) {
                return "hahah";
            }
        });
        addAutowiredSpi(shopDOProvider, IShopCollectInfoProvider.class, new IShopCollectInfoProvider() {
            @Override
            public Boolean isCollected(DetailContext context) {
                return false;
            }
        });
        addAutowiredSpi(shopDOProvider, IShopDsrProvider.class, new IShopDsrProvider() {
            @Override
            public List<ShopDsr> listShopDsr(DetailContext context, ShopDO shopDO) {
                ShopDsr shopDsr = new ShopDsr();
                shopDsr.setIsBetter(false);
                shopDsr.setName("haha");
                shopDsr.setScore(11.1f);
                List<ShopDsr> dsrs = new ArrayList<ShopDsr>(1);
                dsrs.add(shopDsr);
                return dsrs;
            }
        });

        addAutowiredSpi(shopDOProvider, IShopHeaderProvider.class, new IShopHeaderProvider() {
            @Override
            public Map<String, Object> getShopHeader(DetailContext context) {
                return Collections.emptyMap();
            }
        });

        addAutowiredSpi(shopDOProvider, IShopSalesProvider.class, new IShopCategoryUrlProvider() {
            @Override
            public String getShopCategoryUrl(DetailContext context, Integer cid) {
                return "";
            }
        });

        addAutowiredSpi(shopDOProvider, IShopTagProvider.class, new IShopTagProvider() {
            @Override
            public String getShopTag(DetailContext context, ShopInfo shopInfo) {
                return "";
            }
        });

        addAutowiredSpi(shopDOProvider, IShopUrlProvider.class, new IShopUrlProvider() {
            @Override
            public String getShopUrl(DetailContext context) {
                return "";
            }

            @Override
            public String getShopAllGoodsUrl(DetailContext context) {
                return "";
            }
        });

        addAutowiredSpi(shopDOProvider, IWaitressProvider.class, new IWaitressProvider() {
            @Override
            public ShopServicesDO listService(DetailContext context) {
                return null;
            }
        });
        DetailContext context = getDetailContext();
        Result<ShopInfo> ret = shopReadService.queryShopByShopId(context.getItemDO().getShopId());
        context.getItemDO().setShopInfo(ret.getData());
        context.setLoginUserId(111L);
        Assert.assertNotNull(shopDOProvider.emit(getDetailContext()));
    }

    @Test
    public void testEmit4() throws Exception {
        addAutowiredSpi(shopDOProvider, IServiceConfProvider.class, new IServiceConfProvider() {
            @Override
            public String getServiceName(DetailContext context, String key) {
                return "abc";
            }
        });
        addAutowiredSpi(shopDOProvider, IShopCategoryUrlProvider.class, new IShopCategoryUrlProvider() {
            @Override
            public String getShopCategoryUrl(DetailContext context, Integer cid) {
                return "hahah";
            }
        });
        addAutowiredSpi(shopDOProvider, IShopCollectInfoProvider.class, new IShopCollectInfoProvider() {
            @Override
            public Boolean isCollected(DetailContext context) {
                return false;
            }
        });
        addAutowiredSpi(shopDOProvider, IShopDsrProvider.class, new IShopDsrProvider() {
            @Override
            public List<ShopDsr> listShopDsr(DetailContext context, ShopDO shopDO) {
                ShopDsr shopDsr = new ShopDsr();
                shopDsr.setIsBetter(false);
                shopDsr.setName("haha");
                shopDsr.setScore(11.1f);
                List<ShopDsr> dsrs = new ArrayList<ShopDsr>(1);
                dsrs.add(shopDsr);
                return dsrs;
            }
        });

        addAutowiredSpi(shopDOProvider, IShopHeaderProvider.class, new IShopHeaderProvider() {
            @Override
            public Map<String, Object> getShopHeader(DetailContext context) {
                return Collections.emptyMap();
            }
        });

        addAutowiredSpi(shopDOProvider, IShopSalesProvider.class, new IShopCategoryUrlProvider() {
            @Override
            public String getShopCategoryUrl(DetailContext context, Integer cid) {
                return "";
            }
        });

        addAutowiredSpi(shopDOProvider, IShopTagProvider.class, new IShopTagProvider() {
            @Override
            public String getShopTag(DetailContext context, ShopInfo shopInfo) {
                return "";
            }
        });

        addAutowiredSpi(shopDOProvider, IShopUrlProvider.class, new IShopUrlProvider() {
            @Override
            public String getShopUrl(DetailContext context) {
                return "";
            }

            @Override
            public String getShopAllGoodsUrl(DetailContext context) {
                return "";
            }
        });

        addAutowiredSpi(shopDOProvider, IWaitressProvider.class, new IWaitressProvider() {
            @Override
            public ShopServicesDO listService(DetailContext context) {
                return null;
            }
        });

        DetailContext context = getDetailContext();
        context.setDyn(true);
        Result<ShopInfo> ret = shopReadService.queryShopByShopId(context.getItemDO().getShopId());
        context.getItemDO().setShopInfo(ret.getData());
        context.setLoginUserId(9999L);
        Assert.assertNotNull(shopDOProvider.emit(context));
    }
}