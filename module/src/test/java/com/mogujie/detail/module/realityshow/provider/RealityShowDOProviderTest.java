package com.mogujie.detail.module.realityshow.provider;

import com.mogujie.detail.module.realityshow.domain.RealityShow;
import org.junit.Assert;
import org.junit.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * Created by xy on 2017/7/10.
 */
public class RealityShowDOProviderTest {
    @Test
    public void getShowList() throws Exception {
        final String data = "[\n" +
                "  {\n" +
                "    \"videoId\": 136874,\n" +
                "    \"url\": \"http://www.xiaodian.com/goods/publish/video?videoId=136874\",\n" +
                "    \"duration\": 47,\n" +
                "    \"source\": \"custom\",\n" +
                "    \"auditStatus\":2,\n" +
                "    \"coverImage\": \"/mlcdn/c45406/170706_59gea24jefaigejjbbc92l36bj975_640x589.jpg\",\n" +
                "    \"sku\":{\n" +
                "        \"颜色\":\"红色\",\n" +
                "        \"尺码\":\"M\"\n" +
                "    },\n" +
                "    \"hongren\":{\n" +
                "        \"id\": 111,\n" +
                "         \"name\":\"紫晴妹妹\",                      \"avatar\":\"mlcdn/c45406/170623_0g0h4deeac6h4cd2j8jg99j28a7d0_750x860.jpg_468x468.jpg\",\n" +
                "         \"height\":165,\n" +
                "         \"weight\":45,\n" +
                "         \"effect\":\"合身\"\n" +
                "    }\n" +
                "  },\n" +
                "  {\n" +
                "    \"videoId\": 136875,\n" +
                "    \"url\": \"http://www.xiaodian.com/goods/publish/video?videoId=136874\",\n" +
                "    \"duration\": 60,\n" +
                "    \"source\": \"custom\",\n" +
                "    \"auditStatus\":2,\n" +
                "    \"coverImage\": \"/mlcdn/c45406/170706_59gea24jefaigejjbbc92l36bj975_640x589.jpg\",\n" +
                "    \"sku\":{\n" +
                "        \"颜色\":\"红色\",\n" +
                "        \"尺码\":\"L\"\n" +
                "    },\n" +
                "    \"hongren\":{\n" +
                "        \"id\": 112,\n" +
                "         \"name\":\"xxxx\",                      \"avatar\":\"mlcdn/c45406/170623_0g0h4deeac6h4cd2j8jg99j28a7d0_750x860.jpg_468x468.jpg\",\n" +
                "         \"height\":166,\n" +
                "         \"weight\":46,\n" +
                "         \"effect\":\"偏大\"\n" +
                "    }\n" +
                "  }\n" +
                "]";
        Map<String, String> features = new HashMap<String, String>() {{
           put("showVideos", data);
        }};

        List<RealityShow> showList = RealityShowDOProvider.getShowList(features);
        Assert.assertNotNull(showList);
    }

}