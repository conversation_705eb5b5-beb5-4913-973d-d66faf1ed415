package com.mogujie.detail.module.fastbuy.provider;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.module.ProviderBaseTest;
import com.mogujie.marketing.ferrari.api.dto.RushInfoDTO;
import junit.framework.Assert;
import org.junit.Before;
import org.junit.Test;

/**
 * Created by <PERSON>iaoyao on 16/9/8.
 */
public class FastbuyDOProviderTest extends ProviderBaseTest<FastbuyDOProvider> {

    @Before
    public void setUp() throws Exception {
        super.init(FastbuyDOProvider.class);
    }

    @Test
    public void testEmit() {
        Assert.assertNull(provider.emit(getDetailContext()));
    }

    @Test
    public void testEmit2() {
        DetailContext context = getDetailContext();
        RushInfoDTO rushInfo = new RushInfoDTO();
        int nowTime = (int)(System.currentTimeMillis()/1000);
        rushInfo.setEndTime(nowTime+2000);
        rushInfo.setStartTime(nowTime-1000);
        rushInfo.setSalePrice(1000);
        rushInfo.setSales(1000);
        rushInfo.setUnPay(1000);
        context.addContext("fastbuyInfo", rushInfo);
        Assert.assertNotNull(provider.emit(context));
    }
}