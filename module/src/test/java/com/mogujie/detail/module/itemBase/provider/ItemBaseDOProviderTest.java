package com.mogujie.detail.module.itemBase.provider;

import com.google.gson.Gson;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.constant.ItemTag;
import com.mogujie.detail.module.ProviderBaseTest;
import com.mogujie.detail.module.itemBase.domain.GoodItemInfo;
import com.mogujie.detail.module.itemBase.spi.ICFavProvider;
import com.mogujie.detail.module.itemBase.spi.IFavInfoProvider;
import com.mogujie.detail.module.itemBase.spi.IItemStateProvider;
import com.mogujie.detail.module.itemBase.spi.IShareUrlProvider;
import com.mogujie.service.item.api.basic.ItemExtraService;
import com.mogujie.service.item.domain.basic.ItemExtraDO;
import com.mogujie.service.item.domain.result.BaseResultDO;
import junit.framework.Assert;
import mockit.Capturing;
import mockit.integration.junit4.JMockit;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by xiaoyao on 16/9/8.
 */
@RunWith(JMockit.class)
public class ItemBaseDOProviderTest extends ProviderBaseTest<ItemBaseDOProvider>{

    @Before
    public void setUp() throws Exception{
        super.init(ItemBaseDOProvider.class);
    }

    @Test
    public void testEmit() {

        addAutowiredSpi(ICFavProvider.class, new ICFavProvider() {
            @Override
            public Long getCFav(DetailContext context) {
                return 1L;
            }
        });
        addAutowiredSpi(IFavInfoProvider.class, new IFavInfoProvider() {
            @Override
            public Boolean isFaved(DetailContext context) {
                return true;
            }
        });
        addAutowiredSpi(IItemStateProvider.class, new IItemStateProvider() {
            @Override
            public int getItemState(DetailContext context) {
                return 0;
            }
        });
        addAutowiredSpi(IShareUrlProvider.class, new IShareUrlProvider() {
            @Override
            public String getShareUrl(DetailContext context) {
                return "http://share.link";
            }
        });

        DetailContext detailContext = getDetailContext();
        detailContext.setLoginUserId(111332610L);
        Assert.assertNotNull(provider.emit(detailContext));
    }

    @Test
    public void testEmit3() {

        addAutowiredSpi(ICFavProvider.class, new ICFavProvider() {
            @Override
            public Long getCFav(DetailContext context) {
                return 1L;
            }
        });
        addAutowiredSpi(IFavInfoProvider.class, new IFavInfoProvider() {
            @Override
            public Boolean isFaved(DetailContext context) {
                return true;
            }
        });
        addAutowiredSpi(IItemStateProvider.class, new IItemStateProvider() {
            @Override
            public int getItemState(DetailContext context) {
                return 0;
            }
        });
        addAutowiredSpi(IShareUrlProvider.class, new IShareUrlProvider() {
            @Override
            public String getShareUrl(DetailContext context) {
                return "http://share.link";
            }
        });
        DetailContext context = getDetailContext();
        context.setDyn(true);
        Assert.assertNotNull(provider.emit(context));
    }

    @Test
    public void testEmit2(@Capturing final ItemExtraService itemExtraService) {

        ItemExtraDO itemExtraDO = new ItemExtraDO();
        GoodItemInfo goodItemInfo = new GoodItemInfo();
        goodItemInfo.setImage("");
        goodItemInfo.setImageDescs(new ArrayList<GoodItemInfo.ImageDesc>());
        goodItemInfo.setLeVideo(new GoodItemInfo.LeVideo());
        goodItemInfo.setSubtitle("");
        Map<String, String> dataMap = new HashMap<>();
        Gson gson = new Gson();
        dataMap.put("goodItem", gson.toJson(goodItemInfo));
        itemExtraDO.setFeatures(dataMap);
        final BaseResultDO<ItemExtraDO> rett = new BaseResultDO<>();
        rett.setResult(itemExtraDO);
        rett.setSuccess(true);
        addAutowiredSpi(ICFavProvider.class, new ICFavProvider() {
            @Override
            public Long getCFav(DetailContext context) {
                return 1L;
            }
        });
        addAutowiredSpi(IFavInfoProvider.class, new IFavInfoProvider() {
            @Override
            public Boolean isFaved(DetailContext context) {
                return true;
            }
        });
        addAutowiredSpi(IItemStateProvider.class, new IItemStateProvider() {
            @Override
            public int getItemState(DetailContext context) {
                return 0;
            }
        });
        addAutowiredSpi(IShareUrlProvider.class, new IShareUrlProvider() {
            @Override
            public String getShareUrl(DetailContext context) {
                return "http://share.link";
            }
        });

        DetailContext context = getDetailContext();
        context.getItemDO().setItemBizTags(Arrays.asList(ItemTag.MOGU_SELECTION));
        Assert.assertNotNull(provider.emit(getDetailContext()));

    }
}