package com.mogujie.detail.module;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.adt.RouteInfo;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.Platform;
import com.mogujie.detail.core.manager.TranslatorManager;
import com.mogujie.service.item.api.basic.ItemReadService;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.external.IcException;
import com.mogujie.service.item.domain.query.QueryItemOptions;
import com.mogujie.service.item.domain.result.BaseResultDO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by xiaoyao on 16/9/8.
 */
public abstract class ProviderBaseTest<T extends IModuleDOProvider> {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProviderBaseTest.class);

    protected ItemReadService itemReadService;

    protected T provider;

    protected static final Integer ITEM_ID = 761252352;

    private void init() throws Exception {
        itemReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ItemReadService.class);

    }

    protected void init(Class providerClass) throws Exception {
        init();
        provider = (T) providerClass.newInstance();
        provider.init();
    }

    protected DetailContext getDetailContext(Long itemId) {
        QueryItemOptions queryItemOptions = new QueryItemOptions();
        queryItemOptions.setIncludeItemExtraInfo(true);
        queryItemOptions.setQueryBasicItem(true);
        queryItemOptions.setQueryInventory(true);
        queryItemOptions.setQuerySkuInfo(true);
        queryItemOptions.setIncludeDeleted(false);
        queryItemOptions.setQueryItemDetail(true);
        queryItemOptions.setQueryPresaleDO(true);
        queryItemOptions.setQuerySkuAttribute(true);
//        queryItemOptions.setQueryTagCenter(true);
        try {
            List<String> componentIds = new ArrayList<>(3);
            componentIds.add("summary");
            BaseResultDO<ItemDO> ret = itemReadService.queryItemById(itemId, queryItemOptions);
            DetailContext context = new DetailContext(new RouteInfo(App.MGJ, Platform.ALL, BizType.FASTBUY, "", ""), componentIds, itemId.longValue(), TranslatorManager.getTranslatorMappingHolder());
            DetailItemDO itemDO = new DetailItemDO(ret.getResult());
            itemDO.setTotalStock(1000L);
            context.setItemDO(itemDO);
            return context;
        } catch (IcException e) {
            LOGGER.error("get item failed : {}", e);
        }
        return null;
    }

    protected DetailContext getDetailContext() {
        QueryItemOptions queryItemOptions = new QueryItemOptions();
        queryItemOptions.setIncludeItemExtraInfo(true);
        queryItemOptions.setQueryBasicItem(true);
        queryItemOptions.setQueryInventory(true);
        queryItemOptions.setQuerySkuInfo(true);
        queryItemOptions.setIncludeDeleted(false);
        queryItemOptions.setQueryItemDetail(true);
        queryItemOptions.setQueryPresaleDO(true);
        queryItemOptions.setQuerySkuAttribute(true);
//        queryItemOptions.setQueryTagCenter(true);
        try {
            List<String> componentIds = new ArrayList<>(3);
            componentIds.add("summary");
            BaseResultDO<ItemDO> ret = itemReadService.queryItemById(ITEM_ID, queryItemOptions);
            DetailContext context = new DetailContext(new RouteInfo(App.MGJ, Platform.ALL, BizType.FASTBUY, "", ""), componentIds, ITEM_ID.longValue(), TranslatorManager.getTranslatorMappingHolder());
            DetailItemDO itemDO = new DetailItemDO(ret.getResult());
            itemDO.setTotalStock(1000L);
            context.setItemDO(itemDO);
            return context;
        } catch (IcException e) {
            LOGGER.error("get item failed : {}", e);
        }
        return null;
    }

    protected void addAutowiredSpi(Class spiCls, Object defaultImpl) {
            List<Field> fields = FieldUtils.getAllFieldsList(provider.getClass());
            for (Field field : fields) {
                    if (field.getType().equals(spiCls)) {
                        field.setAccessible(true);
                        try {
                            field.set(provider, defaultImpl);
                        } catch (Throwable e) {
                            LOGGER.error("set default for field {} failed {}", field.getName(), e);
                        }
                        field.setAccessible(false);
                        break;
                    }
            }
    }

    protected void addAutowiredSpi(T selfInitProvider, Class spiCls, Object defaultImpl) {
        List<Field> fields = FieldUtils.getAllFieldsList(selfInitProvider.getClass());
        for (Field field : fields) {
            if (field.getType().equals(spiCls)) {
                field.setAccessible(true);
                try {
                    field.set(selfInitProvider, defaultImpl);
                } catch (Throwable e) {
                    LOGGER.error("set default for field {} failed {}", field.getName(), e);
                }
                field.setAccessible(false);
                break;
            }
        }
    }
}
