package com.mogujie.detail.module.buyershow.provider;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.module.ProviderBaseTest;
import org.junit.Before;
import org.junit.Test;

/**
 * Created by xiaoyao on 16/11/3.
 */
public class BuyerShowDOProviderTest extends ProviderBaseTest<BuyerShowDOProvider> {


    @Before
    public void init() throws Exception {
        super.init(BuyerShowDOProvider.class);
    }

    @Test
    public void testEmit() {
        DetailContext context = getDetailContext(720002530L);
        provider.emit(context);
    }
}