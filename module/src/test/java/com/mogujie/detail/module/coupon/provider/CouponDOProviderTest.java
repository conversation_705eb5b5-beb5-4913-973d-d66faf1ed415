package com.mogujie.detail.module.coupon.provider;

import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.module.ProviderBaseTest;
import com.mogujie.detail.module.coupon.domain.PlatformCoupon;
import com.mogujie.service.hummer.api.CouponReadService;
import com.mogujie.service.hummer.domain.dto.ActivityExtraDTO;
import com.mogujie.service.hummer.domain.dto.PlatformCouponDTO;
import com.mogujie.service.hummer.domain.dto.QueryPartPlatformCouponParam;
import com.mogujie.service.hummer.domain.dto.result.Result;
import com.mogujie.trade.sales.api.dto.PreItemSalesQueryDto;
import com.mogujie.trade.sales.query.domain.App;
import junit.framework.Assert;
import mockit.Capturing;
import mockit.Expectations;
import mockit.integration.junit4.JMockit;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by xiaoyao on 16/11/2.
 */
@RunWith(JMockit.class)
public class CouponDOProviderTest extends ProviderBaseTest<CouponDOProvider> {

    private CouponDOProvider couponDOProvider;

    @Before
    public void setUp() throws Exception {
        ApplicationContext context = new ClassPathXmlApplicationContext("classpath*:coupon-test-context.xml");
        couponDOProvider = context.getBean(CouponDOProvider.class);
        CommonSwitchUtil commonSwitchUtil = context.getBean(CommonSwitchUtil.class);
        addAutowiredSpi(couponDOProvider, CommonSwitchUtil.class, commonSwitchUtil);
        couponDOProvider.init();
        super.init(CouponDOProvider.class);
    }

    @Test
    public void testGetPromotions(@Capturing final CouponReadService couponReadService) throws Exception {
        final Result<Map<ActivityExtraDTO, List<PlatformCouponDTO>>> ret = new Result<>();
        Map<ActivityExtraDTO, List<PlatformCouponDTO>> map = new HashMap<>();
        ret.setCode(1001);
        ret.setData(map);
        ActivityExtraDTO activityExtraDTO = new ActivityExtraDTO();
        activityExtraDTO.setActivityName("huodong");
        activityExtraDTO.setActivityUrl("url");
        activityExtraDTO.setStartTime(System.currentTimeMillis() - 1000000L);
        activityExtraDTO.setEndTime(System.currentTimeMillis() + 1000000L);
        List<PlatformCouponDTO> list = new ArrayList<>();
        PlatformCouponDTO dto = new PlatformCouponDTO();
        dto.setDecorate("do");
        list.add(dto);
        map.put(activityExtraDTO, list);

        new Expectations() {{
            couponReadService.getValidPartPlatformCouponList((QueryPartPlatformCouponParam) any);
            result = ret;
        }};
        Assert.assertNotNull(couponDOProvider.emit(getDetailContext(720016386L)));
    }

    @Test
    public void testGetPromotions2(@Capturing final CouponReadService couponReadService) throws Exception {
        final Result<Map<ActivityExtraDTO, List<PlatformCouponDTO>>> ret = new Result<>();
        Map<ActivityExtraDTO, List<PlatformCouponDTO>> map = new HashMap<>();
        ret.setCode(1001);
        ret.setData(map);
        ActivityExtraDTO activityExtraDTO = new ActivityExtraDTO();
        activityExtraDTO.setActivityName("huodong");
        activityExtraDTO.setActivityUrl("url");
        activityExtraDTO.setStartTime(System.currentTimeMillis() - 1000000L);
        activityExtraDTO.setEndTime(System.currentTimeMillis() + 1000000L);
        List<PlatformCouponDTO> list = new ArrayList<>();
        PlatformCouponDTO dto = new PlatformCouponDTO();
        dto.setDecorate("do");
        list.add(dto);
        map.put(activityExtraDTO, list);

        new Expectations() {{
            couponReadService.getValidPartPlatformCouponList((QueryPartPlatformCouponParam) any);
            result = ret;
        }};
        Assert.assertNotNull(couponDOProvider.emit(getDetailContext()));
    }
}