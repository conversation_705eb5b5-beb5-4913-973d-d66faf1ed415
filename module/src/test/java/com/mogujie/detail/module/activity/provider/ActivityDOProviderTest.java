package com.mogujie.detail.module.activity.provider;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.mogujie.actcenter.tagclient.ActTagClient;
import com.mogujie.actcenter.tagclient.bean.ActivityBean;
import com.mogujie.actcenter.tagclient.bean.TagProperty;
import com.mogujie.actcenter.tagclient.common.BaseResult;
import com.mogujie.actcenter.tagclient.enums.PlatformEnum;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.module.ProviderBaseTest;
import com.mogujie.detail.module.activity.util.ActivityUtil;
import mockit.Deencapsulation;
import mockit.Expectations;
import mockit.integration.junit4.JMockit;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by xiaoyao on 16/11/2.
 */
@RunWith(JMockit.class)
public class ActivityDOProviderTest extends ProviderBaseTest<ActivityDOProvider>  {

    private ActivityDOProvider activityDOProvider;

    @Before
    public void setUp() throws Exception {
        ApplicationContext context = new ClassPathXmlApplicationContext("classpath*:activity-test-context.xml");
        activityDOProvider = context.getBean(ActivityDOProvider.class);
        CommonSwitchUtil commonSwitchUtil = context.getBean(CommonSwitchUtil.class);
        addAutowiredSpi(activityDOProvider, CommonSwitchUtil.class, commonSwitchUtil);
        super.init(ActivityDOProvider.class);
    }

    @Test
    public void testGetEventInfo1() throws Exception {
        int time = (int) (System.currentTimeMillis() / 1000);
        DetailContext context = getDetailContext(477432917L);
        context.getItemDO().setJsonExtra("{\"tags\":\"2\",\"rp\":\"700000276295|500|**********|**********|616红包|1\",\"source\":8,\"tg\":\"dc:650|st:" + (time - 20000) + "|et:" + (time + 20000) + "|ap:7000|ti:2|ws:0|we:0|ai:191\",\"back\":\"java\",\"hd\":\"dc:530|tp:1|ci:700002071817|st:**********|mk:8|ws:**********|et:**********|nm:活动价|\",\"au\":\"baoming\"}");
        Assert.assertNull(activityDOProvider.emit(context));
    }

    @Test
    public void testGetEventInfo2() throws Exception {
        int time = (int) (System.currentTimeMillis() / 1000);
        DetailContext context = getDetailContext(477432917L);
        context.getItemDO().setJsonExtra("{\"tags\":\"71\",\"rp\":\"700000276295|500|**********|**********|616红包|1\",\"source\":8,\"tg\":\"dc:650|st:" + (time + 20000) + "|et:" + (time + 40000) + "|ap:7000|ti:2|ws:" + (time - 20000) + "|we:" + (time + 20000) + "|ai:191\",\"back\":\"java\",\"hd\":\"dc:530|tp:1|ci:700002071817|st:**********|mk:8|ws:**********|et:**********|nm:活动价|\",\"au\":\"baoming\"}");
        Assert.assertNull(activityDOProvider.emit(context));
    }

    @Test
    public void testGetEventInfo3() throws Exception {
        int time = (int) (System.currentTimeMillis() / 1000);
        DetailContext context = getDetailContext(477432917L);
        context.getItemDO().setJsonExtra("{\"tags\":\"16\",\"rp\":\"700000276295|500|" + (time - 20000) + "|" + (time + 20000) + "|616红包|0\",\"source\":8,\"tg\":\"dc:650|st:" + (time - 20000) + "|et:" + (time + 20000) + "|ap:7000|ti:2|ws:0|we:0|ai:191\",\"back\":\"java\",\"hd\":\"dc:530|tp:1|ci:700002071817|st:**********|mk:8|ws:**********|et:**********|nm:活动价|\",\"au\":\"baoming\"}");
        Assert.assertNull(activityDOProvider.emit(context));
    }

    @Test
    public void testGetEventInfo4() throws Exception {
        int time = (int) (System.currentTimeMillis() / 1000);
        DetailContext context = getDetailContext(477432917L);
        context.getItemDO().setJsonExtra("{\"tags\":\"16\",\"rp\":\"700000276295|500|" + (time - 20000) + "|" + (time + 20000) + "|616红包|1\",\"source\":8,\"tg\":\"dc:650|st:" + (time - 20000) + "|et:" + (time + 20000) + "|ap:7000|ti:2|ws:0|we:0|ai:191\",\"back\":\"java\",\"hd\":\"dc:530|tp:1|ci:700002071817|st:**********|mk:8|ws:**********|et:**********|nm:活动价|\",\"au\":\"baoming\"}");
        activityDOProvider.emit(context);
    }

    @Test
    public void testGetEventInfo5() throws Exception {
        int time = (int) (System.currentTimeMillis() / 1000);
        DetailContext context = getDetailContext(477432917L);
        context.getItemDO().setJsonExtra("{\"tags\":\"99\",\"rp\":\"700000276295|500|" + (time - 20000) + "|" + (time + 20000) + "|616红包|0\",\"source\":8,\"tg\":\"dc:650|st:" + (time - 20000) + "|et:" + (time + 20000) + "|ap:7000|ti:2|ws:0|we:0|ai:191\",\"back\":\"java\",\"hd\":\"dc:530|tp:1|ci:700002071817|st:**********|mk:8|ws:**********|et:**********|nm:活动价|\",\"au\":\"baoming\"}");
        Assert.assertNull(activityDOProvider.emit(context));
    }

    @Test
    public void testGetInfos3() throws Exception {
        ActivityUtil util = new ActivityUtil();
        List<Map<String, Object>> resources = new ArrayList<>(2);
        Map<String, Object> objectMap = new HashMap<>(10);
        objectMap.put("icon", "1231");
        resources.add(objectMap);
        Assert.assertNotNull(util.getInfos(resources));
    }

    @Test
    public void testGetEventInfo6() throws Exception {
        int time = (int) (System.currentTimeMillis() / 1000);
        DetailContext context = getDetailContext(477432917L);
        context.getItemDO().setJsonExtra("{\"tags\":\"99\",\"rp\":\"700000276295|500|" + (time - 20000) + "|" + (time + 20000) + "|616红包|1\",\"source\":8,\"tg\":\"dc:650|st:" + (time - 20000) + "|et:" + (time + 20000) + "|ap:7000|ti:2|ws:0|we:0|ai:191\",\"back\":\"java\",\"hd\":\"dc:530|tp:1|ci:700002071817|st:**********|mk:8|ws:**********|et:**********|nm:活动价|\",\"au\":\"baoming\"}");
        activityDOProvider.emit(context);
    }

    @Test
    public void testGetEventInfo() throws Exception {
        Assert.assertNull(activityDOProvider.emit(getDetailContext()));
    }


    @Test
    public void testGetTuanPrice() throws Exception {
        DetailContext context = getDetailContext();
        context.getItemDO().setJsonExtra("{\"tags\":\"71\",\"tg\":\"dc:500|ap:200\"}");
        activityDOProvider.emit(context);
    }

    @Test
    public void testCheckTime() throws Exception {
        int time = (int) (System.currentTimeMillis() / 1000);
        DetailContext context = getDetailContext(477432917L);
        context.getItemDO().setJsonExtra("{\"tags\":\"16\",\"rp\":\"700000276295|500|**********|**********|616红包|1\",\"source\":8,\"tg\":\"dc:650|st:" + (time - 20000) + "|et:" + (time + 20000) + "|ap:7000|ti:2|ws:0|we:0|ai:191\",\"back\":\"java\",\"hd\":\"dc:530|tp:1|ci:700002071817|st:**********|mk:8|ws:**********|et:**********|nm:活动价|\",\"au\":\"baoming\"}");
        final BaseResult<ActivityBean> ret = new BaseResult<>();
        ActivityBean activityBean = new ActivityBean();
        activityBean.setPre(false);
        activityBean.setStartTime(**********);
        activityBean.setEndTime(**********);
        ret.setSuccess(true);
        ret.setData(activityBean);

        final BaseResult<TagProperty> baseResult = new BaseResult<>();
        baseResult.setSuccess(true);
        TagProperty tagProperty = new TagProperty();
        tagProperty.put(",", "");
        baseResult.setData(tagProperty);

        new Expectations(ActTagClient.class) {{
            Deencapsulation.invoke(ActTagClient.class, "getEffectiveActivity", PlatformEnum.MGJ);
            result = ret;

            Deencapsulation.invoke(ActTagClient.class, "getTagProperty", PlatformEnum.MGJ, "detail", "16", "mgj_app_809");
            result = baseResult;
        }};
        activityDOProvider.emit(context);
    }

    @Test
    public void gsonTest(){
        TestDOA testDOA = new TestDOA();
        Object o = testDOA.getDO();
        Gson gson = new Gson();
        System.out.println(gson.toJson(o));
        System.out.println(JSON.toJSONString(o));
    }
}