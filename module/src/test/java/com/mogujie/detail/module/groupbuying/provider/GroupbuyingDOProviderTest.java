package com.mogujie.detail.module.groupbuying.provider;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.module.ProviderBaseTest;
import junit.framework.Assert;
import org.junit.Before;
import org.junit.Test;

/**
 * Created by xiaoyao on 16/11/2.
 */
public class GroupbuyingDOProviderTest extends ProviderBaseTest<GroupbuyingDOProvider>{

    @Before
    public void setUp() throws Exception {
        super.init(GroupbuyingDOProvider.class);
    }

    @Test
    public void testEmit() throws Exception {
        Assert.assertNull(provider.emit(getDetailContext(720016386L)));
    }

    @Test
    public void testEmit2() throws Exception {
        DetailContext context = getDetailContext(720016386L);
        context.getItemDO().setJsonExtra("\t{\"tags\":\"16,2,116,82,93,95,160,61,96\",\"rp\":\"700002601256|1500|**********|**********|双11红包|1\",\"source\":8,\"hd_11\":\"dc:13900|tp:2|ci:700003908787|st:**********|mk:11|ws:**********|et:**********|nm:活动价|\",\"hd_9\":\"dc:13900|tp:2|ci:700003908787|st:**********|mk:9|ws:**********|et:**********|nm:活动价|\",\"tg\":\"dc:650|st:**********|et:**********|ap:22714|ti:2|ws:0|we:0|ai:6368506\",\"back\":\"java\",\"hd\":\"dc:13900|tp:2|ci:700003908787|st:**********|mk:8|ws:**********|et:**********|nm:活动价|\",\"goods_on_shelf\":\"2016-10-11 10:18:10.809\",\"au\":\"tagcenter\"} ");
        Assert.assertNotNull(provider.emit(context));
    }
}