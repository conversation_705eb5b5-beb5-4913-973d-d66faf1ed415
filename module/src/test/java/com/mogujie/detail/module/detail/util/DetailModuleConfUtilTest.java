package com.mogujie.detail.module.detail.util;

import junit.framework.Assert;
import org.junit.Test;

/**
 * Created by <PERSON>iaoyao on 16/9/9.
 */
public class DetailModuleConfUtilTest {

    @Test
    public void testModule() {
        Assert.assertNotNull(DetailModuleConfUtil.getCategoryModules("#1160#"));
        Assert.assertNotNull(DetailModuleConfUtil.getCategoryModules("#1264#"));
        Assert.assertNotNull(DetailModuleConfUtil.getCategoryModules("#882#"));
        Assert.assertNotNull(DetailModuleConfUtil.getCategoryModules("#757#"));
        Assert.assertNotNull(DetailModuleConfUtil.getCategoryModules("#777#"));
        Assert.assertNotNull(DetailModuleConfUtil.getCategoryModules("#795#"));
        Assert.assertNotNull(DetailModuleConfUtil.getCategoryModules("#925#"));
        Assert.assertNotNull(DetailModuleConfUtil.getCategoryModules("#1049#"));
        Assert.assertNotNull(DetailModuleConfUtil.getCategoryModules("#1089#"));
        Assert.assertNotNull(DetailModuleConfUtil.getCategoryModules("#1263#"));
        Assert.assertNotNull(DetailModuleConfUtil.getCategoryModules("#1622#"));
        Assert.assertNotNull(DetailModuleConfUtil.getCategoryModules("#1623#"));
        Assert.assertNotNull(DetailModuleConfUtil.getCategoryModules("#1603#"));
    }
}