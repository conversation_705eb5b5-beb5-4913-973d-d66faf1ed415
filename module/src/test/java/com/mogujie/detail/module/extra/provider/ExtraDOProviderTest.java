package com.mogujie.detail.module.extra.provider;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.module.ProviderBaseTest;
import com.mogujie.service.item.domain.basic.ItemPreSaleDO;
import junit.framework.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * Created by x<PERSON>oya<PERSON> on 16/9/8.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:extra-test-context.xml"})
public class ExtraDOProviderTest extends ProviderBaseTest<ExtraDOProvider> {

    @Autowired
    private ExtraDOProvider extraDOProvider;

    @Before
    public void setUp() throws Exception {
        super.init(ExtraDOProvider.class);
//        addAutowiredSpi(extraDOProvider, ITags, new IServiceConfProvider() {
//            @Override
//            public String getServiceName(DetailContext context, String key) {
//                return "abc";
//            }
//        });
    }

    @Test
    public void testEmit() {
        Assert.assertNotNull(extraDOProvider.emit(getDetailContext()));
    }


    @Test
    public void testEmit3() {
        DetailContext context = getDetailContext();
        ItemPreSaleDO preSale = new ItemPreSaleDO();
        int nowTime = (int)(System.currentTimeMillis()/1000);
        preSale.setStart(nowTime-1000);
        preSale.setEnd(nowTime+2000);
        context.getItemDO().setItemPreSaleDO(preSale);
        Assert.assertNotNull(extraDOProvider.emit(getDetailContext()));
    }

}