package com.mogujie.detail.module.itemParams.provider;

import com.mogujie.detail.module.ProviderBaseTest;
import junit.framework.Assert;
import org.junit.Before;
import org.junit.Test;

/**
 * Created by xiaoyao on 16/9/8.
 */
public class ItemParamsDOProviderTest extends ProviderBaseTest<ItemParamsDOProvider> {

    @Before
    public void setUp() throws Exception {
        super.init(ItemParamsDOProvider.class);
    }

    @Test
    public void testEmit() throws Exception {
        Assert.assertNotNull(provider.emit(getDetailContext()));
    }
}