package com.mogujie.detail.module.floatlayer.provider;

import com.mogujie.detail.core.util.MaitUtil;
import com.mogujie.detail.module.ProviderBaseTest;
import junit.framework.Assert;
import mockit.Deencapsulation;
import mockit.Expectations;
import mockit.integration.junit4.JMockit;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by xiaoyao on 16/11/14.
 */
@RunWith(JMockit.class)
public class FloatLayerDOProviderTest extends ProviderBaseTest<FloatLayerDOProvider> {


    @Before
    public void setUp() throws Exception {
        super.init(FloatLayerDOProvider.class);
    }

    @Test
    public void testEmit() throws Exception {

        final List<Map<String, Object>> data = new ArrayList<>(2);
        data.add(new HashMap<String, Object>(){{
            put("location", "2");
            put("title", "heihei");
        }});
        new Expectations(MaitUtil.class) {{
            Deencapsulation.invoke(MaitUtil.class, "getMaitData", 438L);
            result = data;
        }};
        Assert.assertNull(provider.emit(getDetailContext()));
    }
}