package com.mogujie.detail.module.rate.provider;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.module.ProviderBaseTest;
import com.mogujie.detail.module.rate.spi.IUserProfileProvider;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * Created by xia<PERSON><PERSON> on 16/9/8.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:rate-test-context.xml"})
public class RateDOProviderTest extends ProviderBaseTest<RateDOProvider> {

    @Autowired
    private RateDOProvider rateDOProvider;

    @Before
    public void setUp() throws Exception {
        super.init(RateDOProvider.class);
        rateDOProvider.init();
    }

    @Test
    public void testEmit() throws Exception {
        addAutowiredSpi(rateDOProvider, IUserProfileProvider.class, new IUserProfileProvider() {
            @Override
            public String getProfilePrefix(DetailContext context) {
                return "";
            }

            @Override
            public String getAnonymousImg(DetailContext context, long userId) {
                return "";
            }
        });
        Assert.assertNotNull(rateDOProvider.emit(getDetailContext()));
    }
//
//    @Test
//    public void testEmit2() throws Exception {
//        addAutowiredSpi(rateDOProvider, IUserProfileProvider.class, new IUserProfileProvider() {
//            @Override
//            public String getProfilePrefix(DetailContext context) {
//                return "";
//            }
//
//            @Override
//            public String getAnonymousImg(DetailContext context, long userId) {
//                return "";
//            }
//        });
//        Assert.assertNotNull(rateDOProvider.emit(getDetailContext(201293545L)));
//    }
//
//    @Test
//    public void testGetExtraInfo(){
//        Assert.assertNotNull(baseRateModule.getExtraInfo("{\"platform\":\"app\",\"num\":1,\"source\":\"MAMmiui900\",\"ux\":{\"weight\":\"60kg\",\"height\":\"160cm\",\"suit1\":\"合身\"},\"did\":\"868869026991132\",\"l1\":30392666492523,\"mk\":\"mgj\"}"));
//    }
}