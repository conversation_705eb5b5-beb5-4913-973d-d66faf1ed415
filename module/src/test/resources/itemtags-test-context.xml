<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:beans="http://www.springframework.org/schema/mvc"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd">

       <bean id="cacheUtil" class="com.mogujie.detail.core.cache.impl.GuavaCache"/>

       <bean id="itemTagsDOProvider" class="com.mogujie.detail.module.itemTags.provider.ItemTagsDOProvider"/>

       <bean id="commonSwitchUtil" class="com.mogujie.detail.core.util.CommonSwitchUtil"/>


</beans>