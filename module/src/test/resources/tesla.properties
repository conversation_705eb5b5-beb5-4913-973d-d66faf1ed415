#global config
tesla.application.name = mogu-detail
tesla.application.owner = tesla

tesla.server.port = 20066

#threads config
tesla.invokerExecutor.corePoolSize = 24
tesla.invokerExecutor.maxPoolSize = 96
tesla.invokerExecutor.keepAliveSeconds = 60
tesla.invokerExecutor.queueCapacity = 5120

tesla.core.worker.threads=0

tesla.server.registry.ignore = false
tesla.server.registry.ignoreError = false

tesla.monitorTask.timeSpan = 60000

tesla.core.network.readIdleTimeout = 0

#client
tesla.client.provider.invaildThreshold = 10000
tesla.client.defaultFailover = failFast
tesla.client.defautLoadBalance = weightedLeastConcurrency

tesla.server.network.concurrency.allow.num=3000