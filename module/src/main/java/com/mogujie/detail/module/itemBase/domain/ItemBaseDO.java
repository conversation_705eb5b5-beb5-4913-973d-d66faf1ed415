package com.mogujie.detail.module.itemBase.domain;

import com.mogujie.commons.utils.EmojiUtil;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailContextHolder;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.ModuleDO;
import com.mogujie.detail.core.constant.ItemTag;
import com.mogujie.detail.core.constant.VirtualItemType;
import com.mogujie.detail.core.util.ContextUtil;
import com.mogujie.detail.core.util.NumUtil;
import com.mogujie.service.item.domain.basic.ItemPreSaleDO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON>iaoyao on 16/8/8.
 */
public class ItemBaseDO implements ModuleDO {

    public ItemBaseDO(DetailItemDO item) {
        this.setTitle(EmojiUtil.decode(item.getTitle()));
        this.setDesc(EmojiUtil.decode(item.getDescription()));
        this.setIid(IdConvertor.idToUrl(item.getItemId()));
        this.setUserId(IdConvertor.idToUrl(item.getUserId()));
        this.setShopId(IdConvertor.idToUrl(item.getShopId()));
        this.setPriceInfo(item);
        this.setItemTags(item.getItemBizTags());
        this.setFirstOnlineTime(item.getFirstOnline());
        this.setCids(item.getCids());
        this.setType(item.getVerticalMarket());
        this.priceChannel = item.getPriceChannel();
        this.memberPrice = item.getMemberPrice();
    }

    /**
     * 货币单位
     */
    @Getter
    @Setter
    private String currency;

    /**
     * 商品描述
     */
    @Getter
    @Setter
    private String desc;

    /**
     * 商品促销活动副标题（口号）
     */
    @Getter
    @Setter
    private String slogan;

    /**
     * 用户id(id2url转过的)
     */
    @Getter
    @Setter
    private String userId;

    /**
     * 店铺id
     */
    @Getter
    @Setter
    private String shopId;

    /**
     * 标题
     */
    @Getter
    @Setter
    private String title;

    /**
     * 最低原价
     */
    @Getter
    @Setter
    private String lowPrice;

    /**
     * 最高原价
     */
    @Getter
    @Setter
    private String highPrice;

    /**
     * 最低现价
     */
    @Getter
    @Setter
    private String lowNowPrice;

    /**
     * 最高现价
     */
    @Getter
    @Setter
    private String highNowPrice;


    /**
     * 折扣描述
     */
    @Getter
    @Setter
    private String discountDesc;

    /**
     * 商品id
     */
    @Getter
    @Setter
    private String iid;

    /**
     * 是否自己
     */
    @Getter
    @Setter
    private boolean isSelf;

    /**
     * 是否喜欢
     */
    @Getter
    @Setter
    private Boolean isFaved;

    /**
     * 状态
     * 0: 正常售卖
     * 1: 下架
     * 2: 库存不足
     * 3: 待开售
     */
    @Getter
    @Setter
    private int state;

    /**
     * 封面图
     */
    @Getter
    @Setter
    private List<String> topImages;

    /**
     * 商品类型，0为普通商品，1为预售商品
     */
    @Getter
    @Setter
    private int saleType;

    /**
     * fot app search
     */
    @Getter
    @Setter
    private String tags;

    @Getter
    @Setter
    private List<String> bizTags;

    /**
     * 商品标签
     */
    @Getter
    @Setter
    private List<ItemTag> itemTags;

    /**
     * pc详情页需要, cps分享地址
     */
    @Setter
    @Getter
    private String shareCpsUrl;

    /**
     * pc详情页需要, 喜欢数
     */
    @Setter
    @Getter
    private int cFav;

    /**
     * 登陆用户id
     */
    @Setter
    @Getter
    private String loginUserId;

    /**
     * 用户昵称
     */
    @Getter
    @Setter
    private String loginUserNickname;

    /**
     * 用户头像
     */
    @Getter
    @Setter
    private String loginUserAvatar;

    /**
     * 当前用户是否为小仙小侠,废弃,使用buyuserinfodo
     */
    @Setter
    @Getter
    private boolean admin;

    /**
     * 封面视频
     */
    @Setter
    @Getter
    private VideoInfo video;

    /**
     * 业务banner
     */
    @Setter
    @Getter
    private List<GoodsBanner> goodsBanners;

    /**
     * 类目路径
     */
    @Setter
    @Getter
    private String cids;


    /**
     * 红包开关（控制加购后是否进行执行抽奖）
     */
    @Setter
    @Getter
    private Boolean redPacketSwitch;

    /**
     * 添加购物车提示开关（控制购物车按钮上的气泡tips展示）
     */
    @Setter
    @Getter
    private Boolean addCartTips;

    /**
     * 当前用户是否可以申请开通分期
     */
    @Setter
    @Getter
    private Boolean canApplyInstallment;


    /**
     * 当前商品是否可以展示划线价
     */
    @Setter
    @Getter
    private Boolean canShowStrikethroughPrice;

    /**
     * 第一次上架时间
     */
    @Getter
    @Setter
    private int firstOnlineTime;

    /**
     * 试穿报告
     */
    @Getter
    @Setter
    private List<TrialReportInfo> trialReportInfos;

    @Setter
    @Getter
    private Map<String, Object> extra;

    /**
     * 商品数字标
     */
    @Getter
    @Setter
    private List<String> numTags;

    /**
     * 3D 模型，已废弃
     */
    @Getter
    @Setter
    private ThreeDModel threeDModel;

    /**
     * 商品类型，8/11/100/12/13/14等
     */
    @Getter
    @Setter
    private Long type;

    /**
     * 当前价格所属渠道号
     */
    @Getter
    @Setter
    private Integer priceChannel;

    /**
     * 会员价（会员所能享受的价格，当前用户若不是会员，则无法享受该价格）
     */
    @Getter
    @Setter
    private Map<Long, Long> memberPrice;

    /**
     * 虚拟商品类型
     */
    @Getter
    @Setter
    private VirtualItemType virtualItemType;

    /**
     * 先付后买装填信息
     */
    @Getter
    @Setter
    private ForetasteAuth foretasteAuth;

    /**
     * 海外商品信息（>1110版本），@雨果
     */
    @Getter
    @Setter
    private OverseaItemInfo overseaItemInfo;

    /**
     * 详情页中间的活动banner
     */
    @Getter
    @Setter
    private ActivityBanner activityBanner;

    /**
     * 商品官方推荐
     */
    @Getter
    @Setter
    private OfficialRecommend officialRecommend;

    /**
     * 前N件立减需求（2019年3月大促）
     */
    @Getter
    @Setter
    private LimitDiscountInfo limitDiscountInfo;

    /**
     * 商品大促价：取自商品标hd
     */
    @Getter
    @Setter
    private Integer activityPrice;

    /**
     * 商品限购数量
     */
    @Getter
    @Setter
    private Integer purchaseLimit;

    /**
     * 商品尺码图片 商品
     */
    @Getter
    @Setter
    private List<ImageInfo> imageInfo;

    private void setPriceInfo(DetailItemDO item) {
        ItemPreSaleDO itemPreSale = item.getItemPreSaleDO();
        int time = (int) (System.currentTimeMillis() / 1000);
        this.highPrice = item.getHighPrice();
        this.lowPrice = item.getLowPrice();
        DetailContext context = DetailContextHolder.get();
        // 若预售时间已过或未开始，走普通商品逻辑
        if (itemPreSale != null && time > itemPreSale.getStart() && time < itemPreSale.getEnd()
                && ContextUtil.isNormalBizDetail(context)) {
            this.lowNowPrice = NumUtil.formatNum(itemPreSale.getPrice() / 100D);
            this.highNowPrice = this.lowNowPrice;
        } else {
            this.lowNowPrice = item.getLowNowPrice();
            this.highNowPrice = item.getHighNowPrice();
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ItemBaseDO that = (ItemBaseDO) o;

        if (isSelf != that.isSelf) return false;
        if (state != that.state) return false;
        if (saleType != that.saleType) return false;
        if (cFav != that.cFav) return false;
        if (admin != that.admin) return false;
        if (firstOnlineTime != that.firstOnlineTime) return false;
        if (currency != null ? !currency.equals(that.currency) : that.currency != null) return false;
        if (desc != null ? !desc.equals(that.desc) : that.desc != null) return false;
        if (userId != null ? !userId.equals(that.userId) : that.userId != null) return false;
        if (shopId != null ? !shopId.equals(that.shopId) : that.shopId != null) return false;
        if (title != null ? !title.equals(that.title) : that.title != null) return false;
        if (lowPrice != null ? !lowPrice.equals(that.lowPrice) : that.lowPrice != null) return false;
        if (highPrice != null ? !highPrice.equals(that.highPrice) : that.highPrice != null) return false;
        if (lowNowPrice != null ? !lowNowPrice.equals(that.lowNowPrice) : that.lowNowPrice != null) return false;
        if (highNowPrice != null ? !highNowPrice.equals(that.highNowPrice) : that.highNowPrice != null) return false;
        if (discountDesc != null ? !discountDesc.equals(that.discountDesc) : that.discountDesc != null) return false;
        if (iid != null ? !iid.equals(that.iid) : that.iid != null) return false;
        if (isFaved != null ? !isFaved.equals(that.isFaved) : that.isFaved != null) return false;
        if (topImages != null ? !topImages.equals(that.topImages) : that.topImages != null) return false;
        if (tags != null ? !tags.equals(that.tags) : that.tags != null) return false;
        if (bizTags != null ? !bizTags.equals(that.bizTags) : that.bizTags != null) return false;
        if (itemTags != null ? !itemTags.equals(that.itemTags) : that.itemTags != null) return false;
        if (shareCpsUrl != null ? !shareCpsUrl.equals(that.shareCpsUrl) : that.shareCpsUrl != null) return false;
        if (loginUserId != null ? !loginUserId.equals(that.loginUserId) : that.loginUserId != null) return false;
        if (video != null ? !video.equals(that.video) : that.video != null) return false;
        if (goodsBanners != null ? !goodsBanners.equals(that.goodsBanners) : that.goodsBanners != null) return false;
        if (cids != null ? !cids.equals(that.cids) : that.cids != null) return false;
        if (redPacketSwitch != null ? !redPacketSwitch.equals(that.redPacketSwitch) : that.redPacketSwitch != null)
            return false;
        if (addCartTips != null ? !addCartTips.equals(that.addCartTips) : that.addCartTips != null) return false;
        if (canApplyInstallment != null ? !canApplyInstallment.equals(that.canApplyInstallment) : that.canApplyInstallment != null)
            return false;
        if (trialReportInfos != null ? !trialReportInfos.equals(that.trialReportInfos) : that.trialReportInfos != null)
            return false;
        if (extra != null ? !extra.equals(that.extra) : that.extra != null) return false;
        if (numTags != null ? !numTags.equals(that.numTags) : that.numTags != null) return false;
        if (threeDModel != null ? !threeDModel.equals(that.threeDModel) : that.threeDModel != null) return false;
        if (type != null ? !type.equals(that.type) : that.type != null) return false;
        if (priceChannel != null ? !priceChannel.equals(that.priceChannel) : that.priceChannel != null) return false;
        if (foretasteAuth != null ? !foretasteAuth.equals(that.foretasteAuth) : that.foretasteAuth != null)
            return false;
        return virtualItemType == that.virtualItemType;
    }

    @Override
    public int hashCode() {
        int result = currency != null ? currency.hashCode() : 0;
        result = 31 * result + (desc != null ? desc.hashCode() : 0);
        result = 31 * result + (userId != null ? userId.hashCode() : 0);
        result = 31 * result + (shopId != null ? shopId.hashCode() : 0);
        result = 31 * result + (title != null ? title.hashCode() : 0);
        result = 31 * result + (lowPrice != null ? lowPrice.hashCode() : 0);
        result = 31 * result + (highPrice != null ? highPrice.hashCode() : 0);
        result = 31 * result + (lowNowPrice != null ? lowNowPrice.hashCode() : 0);
        result = 31 * result + (highNowPrice != null ? highNowPrice.hashCode() : 0);
        result = 31 * result + (discountDesc != null ? discountDesc.hashCode() : 0);
        result = 31 * result + (iid != null ? iid.hashCode() : 0);
        result = 31 * result + (isSelf ? 1 : 0);
        result = 31 * result + (isFaved != null ? isFaved.hashCode() : 0);
        result = 31 * result + state;
        result = 31 * result + (topImages != null ? topImages.hashCode() : 0);
        result = 31 * result + saleType;
        result = 31 * result + (tags != null ? tags.hashCode() : 0);
        result = 31 * result + (bizTags != null ? bizTags.hashCode() : 0);
        result = 31 * result + (itemTags != null ? itemTags.hashCode() : 0);
        result = 31 * result + (shareCpsUrl != null ? shareCpsUrl.hashCode() : 0);
        result = 31 * result + cFav;
        result = 31 * result + (loginUserId != null ? loginUserId.hashCode() : 0);
        result = 31 * result + (admin ? 1 : 0);
        result = 31 * result + (video != null ? video.hashCode() : 0);
        result = 31 * result + (goodsBanners != null ? goodsBanners.hashCode() : 0);
        result = 31 * result + (cids != null ? cids.hashCode() : 0);
        result = 31 * result + (redPacketSwitch != null ? redPacketSwitch.hashCode() : 0);
        result = 31 * result + (addCartTips != null ? addCartTips.hashCode() : 0);
        result = 31 * result + (canApplyInstallment != null ? canApplyInstallment.hashCode() : 0);
        result = 31 * result + firstOnlineTime;
        result = 31 * result + (trialReportInfos != null ? trialReportInfos.hashCode() : 0);
        result = 31 * result + (extra != null ? extra.hashCode() : 0);
        result = 31 * result + (numTags != null ? numTags.hashCode() : 0);
        result = 31 * result + (threeDModel != null ? threeDModel.hashCode() : 0);
        result = 31 * result + (type != null ? type.hashCode() : 0);
        result = 31 * result + (priceChannel != null ? priceChannel.hashCode() : 0);
        result = 31 * result + (virtualItemType != null ? virtualItemType.hashCode() : 0);
        result = 31 * result + (foretasteAuth != null ? foretasteAuth.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "ItemBaseDO{" +
                "currency='" + currency + '\'' +
                ", desc='" + desc + '\'' +
                ", slogan='" + slogan + '\'' +
                ", userId='" + userId + '\'' +
                ", shopId='" + shopId + '\'' +
                ", title='" + title + '\'' +
                ", lowPrice='" + lowPrice + '\'' +
                ", highPrice='" + highPrice + '\'' +
                ", lowNowPrice='" + lowNowPrice + '\'' +
                ", highNowPrice='" + highNowPrice + '\'' +
                ", discountDesc='" + discountDesc + '\'' +
                ", iid='" + iid + '\'' +
                ", isSelf=" + isSelf +
                ", isFaved=" + isFaved +
                ", state=" + state +
                ", topImages=" + topImages +
                ", saleType=" + saleType +
                ", tags='" + tags + '\'' +
                ", bizTags=" + bizTags +
                ", itemTags=" + itemTags +
                ", shareCpsUrl='" + shareCpsUrl + '\'' +
                ", cFav=" + cFav +
                ", loginUserId='" + loginUserId + '\'' +
                ", admin=" + admin +
                ", video=" + video +
                ", goodsBanners=" + goodsBanners +
                ", cids='" + cids + '\'' +
                ", redPacketSwitch=" + redPacketSwitch +
                ", addCartTips=" + addCartTips +
                ", canApplyInstallment=" + canApplyInstallment +
                ", firstOnlineTime=" + firstOnlineTime +
                ", trialReportInfos=" + trialReportInfos +
                ", extra=" + extra +
                ", numTags=" + numTags +
                ", threeDModel=" + threeDModel +
                ", type=" + type +
                ", priceChannel=" + priceChannel +
                ", virtualItemType=" + virtualItemType +
                ", foretasteAuth=" + foretasteAuth +
                '}';
    }
}
