package com.mogujie.detail.module.mycoupon.domain;

import lombok.Getter;
import lombok.Setter;

/**
 * @description:
 * @author: wuzhao
 * @create: 2020-02-18 17:53
 **/
public class MyMoDouCoupon {

    /**
     * 优惠名称
     */
    @Getter
    @Setter
    private String name;

    /**
     * 优惠券有效期开始时间
     */
    @Getter
    @Setter
    private Integer startTime;

    /**
     * 优惠券有效期结束时间
     */
    @Getter
    @Setter
    private Integer endTime;

    /**
     * 描述领取后多少天有效
     */
    @Getter
    @Setter
    private Integer et;
    /**
     * 优惠券是否已被领取
     */
    @Getter
    @Setter
    private boolean hasReceived;

    /**
     * 优惠券类型 1为平台券、2为店铺券
     */
    @Getter
    @Setter
    private Integer couponType;


    /**
     * 每日领取上限：每日限领1张
     */
    @Getter
    @Setter
    private Integer  dailyDrawLimit;

    /**
     * 活动期间领取限制：限领5张
     */
    @Getter
    @Setter
    private Integer  activityDrawLimit;

    /**
     * 兑换所需要的蘑豆数量
     */
    @Getter
    @Setter
    private Integer costNum;

    /**
     * 券包id
     */
    @Getter
    @Setter
    private String pkgId;

    /**
     * 优惠券金额，单位为分
     */
    @Getter
    @Setter
    private Long cutCent;

    /**
     * 优惠券门槛，单位为分
     */
    @Getter
    @Setter
    private Long limitCent;

    /**
     * 券状态
     */
    @Getter
    @Setter
    Status status;

    public static class Status {
        @Getter
        @Setter
        private String msg;

        /**
         * 1-可兑换
         * 2000-单日活动达到限制
         * 2001-活动期间达到限制
         * 2002-库存不足
         * 2003-等级不足
         */
        @Getter
        @Setter
        private int code;
    }
}
