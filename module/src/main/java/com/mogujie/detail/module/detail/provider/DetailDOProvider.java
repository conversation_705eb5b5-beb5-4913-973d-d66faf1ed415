package com.mogujie.detail.module.detail.provider;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.meili.service.shopcenter.util.ShopInfoTagsUtil;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.constant.SwitchKey;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.spi.SpiAutowired;
import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.core.util.ImageUtil;
import com.mogujie.detail.core.util.MetabaseTool;
import com.mogujie.detail.core.util.TagUtil;
import com.mogujie.detail.module.detail.domain.CertInfo;
import com.mogujie.detail.module.detail.domain.DetailDO;
import com.mogujie.detail.module.detail.domain.DetailVideo;
import com.mogujie.detail.module.detail.domain.ShopDecorate;
import com.mogujie.detail.module.detail.spi.IDetailVideoProvider;
import com.mogujie.detail.module.detail.spi.IShopDecorateProvider;
import com.mogujie.detail.module.detail.util.DetailParseUtil;
import com.mogujie.detail.module.detail.util.ModuleNameUtil;
import com.mogujie.detail.module.itemBase.domain.ImageInfo;
import com.mogujie.detail.module.itemBase.provider.ItemBaseDOProvider;
import com.mogujie.metabase.spring.client.MetabaseClient;
import com.mogujie.metabase.utils.CollectionUtils;
import com.mogujie.metabase.utils.StringUtils;
import com.mogujie.service.item.api.basic.ItemExtraService;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.basic.ItemDetailDO;
import com.mogujie.service.item.domain.basic.ItemExtraDO;
import com.mogujie.service.item.domain.result.BaseResultDO;
import com.mogujie.service.tangram.component.client.CatCompRelationServiceClient;
import com.mogujie.service.tangram.domain.entity.DetailModule;
import com.mogujie.service.tangram.utils.ComponentParseUtil;
import org.apache.commons.collections4.MapUtils;
import org.apache.http.util.TextUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by xiaoyao on 16/8/16.
 */
@Module(name = "detail")
public class DetailDOProvider implements IModuleDOProvider<DetailDO> {

    @Autowired
    private CommonSwitchUtil commonSwitchUtil;

    @Autowired
    private CatCompRelationServiceClient catCompRelationServiceClient;

    @SpiAutowired
    private IShopDecorateProvider shopDecorateProvider;

    @SpiAutowired
    private IDetailVideoProvider detailVideoProvider;

    @Autowired
    ItemExtraService itemExtraService;

    private static final Logger logger = LoggerFactory.getLogger(DetailDOProvider.class);


    @Resource(name = "confMetabaseClient")
    private MetabaseClient metabaseClient;

    private static final String DETAIL_IMAGE_SPLIT_KEY = "detailImageSpaceType";

    private static final Logger LOGGER = LoggerFactory.getLogger(DetailDOProvider.class);

    /**
     * 跨境商品店铺标
     */
    private static final Integer CROSS_BORDER_SHOP_TAG = 1954;

    @Override
    public void init() throws DetailException {

    }

    @Override
    public DetailDO emit(DetailContext context) {
        if (context.isDyn()) {
            return null;
        }
        DetailItemDO item = context.getItemDO();
        String desc = item.getDescription();
        List<DetailModule> detailModules = parseDetailModule(item);
        if (StringUtils.isBlank(desc) && CollectionUtils.isEmpty(detailModules)) {
            return null;
        }

        String advertBanner = getAdvertBanner(context);
        String guideImg = getXcxGuideImg(context);
        String overseaBanner = getOverseaBanner(context);
        CertInfo certInfo = getCertInfo(context);

        if (null != detailModules && detailModules.size() > 0) {
            DetailModule firstModule = detailModules.get(0);
            List<String> imgs = firstModule.getList();
            if (imgs == null) {
                imgs = new ArrayList<>(1);
                firstModule.setList(imgs);
            }
            DetailModule lastModule = detailModules.get(detailModules.size() - 1);
            List<String> lastImgs = lastModule.getList();
            if (null == lastImgs) {
                lastImgs = new ArrayList<>(1);
                lastModule.setList(lastImgs);
            }

            // 药品类目-添加图片
            DetailParseUtil.addDrugImages(item, lastImgs);

            List<ImageInfo> sizeImgs = ItemBaseDOProvider.getSizeImageInfo(item);
            if (sizeImgs != null) {
                for (ImageInfo imageInfo : sizeImgs) {
                    if (!org.apache.commons.lang3.StringUtils.isEmpty(imageInfo.getPath())) {
                        lastImgs.add(ImageUtil.img(imageInfo.getPath()));
                    }
                }
            }
            if (!org.apache.commons.lang3.StringUtils.isEmpty(guideImg)) {
                lastImgs.add(guideImg);
            }
            if (!org.apache.commons.lang3.StringUtils.isEmpty(advertBanner)) {
                lastImgs.add(advertBanner);
            }
            if (!org.apache.commons.lang3.StringUtils.isEmpty(overseaBanner)) {
                lastImgs.add(overseaBanner);
            }
        }

        DetailDO detailDO = new DetailDO();
        // 如果是某些特殊类目的商品，在前面加上一句提示文案
        detailDO.setDesc(decorateDesc(context, desc));
        ShopDecorate shopDecorate = shopDecorateProvider.getDecorate(context);
        detailDO.setShopDecorate(shopDecorate);
        detailDO.setDetailImage(detailModules);
        DetailVideo detailVideo = detailVideoProvider.getDetailVideo(context);
        detailDO.setVideo(detailVideo);
        detailDO.setCertInfo(certInfo);
        if (MapUtils.isNotEmpty(item.getFeatures())
                && item.getFeatures().containsKey(DETAIL_IMAGE_SPLIT_KEY)
                && "1".equals(item.getFeatures().get(DETAIL_IMAGE_SPLIT_KEY))){
            detailDO.setSplitDetailImage(false);
        }
        return detailDO;
    }

    private String decorateDesc(DetailContext context, String desc) {
        ItemDO itemDO = context.getItemDO();
        // cid:text##cid:text
        String originConfigStr = MetabaseTool.getValue("detail_title_decorated");
        String decorateStr = "";
        if (originConfigStr != null) {
            String[] configs = originConfigStr.split("##");
            for (String config : configs) {
                String[] pair = config.split(":");
                if (pair.length != 2) {
                    continue;
                }
                if (itemDO.getCids().contains("#" + pair[0] + "#")) {
                    decorateStr = pair[1];
                    break;
                }
            }
        }
        if (!TextUtils.isEmpty(decorateStr)) {
            return decorateStr + (desc == null ? "" : "\n\n" + desc);
        } else {
            return desc;
        }
    }

    /**
     * 获取商品详情
     *
     * @param item
     * @return
     */
    public List<DetailModule> parseDetailModule(ItemDO item) {
        ItemDetailDO detail = item.getItemDetailDO();

        if (null == detail) {
            return null;
        }

        // 获取图文详情的模块
        List<DetailModule> detailModuleList = ComponentParseUtil.parseItemDetail(detail.getDetail());
        try {
            if (!CollectionUtils.isEmpty(detailModuleList)) {
                for (DetailModule detailModule : detailModuleList) {
                    if (!CollectionUtils.isEmpty(detailModule.getList())) {
                        List<String> images = new ArrayList<>();
                        for (String img : detailModule.getList()) {
                            images.add(ImageUtil.img(img));
                        }
                        detailModule.setList(images);
                    }
                }
            }
        } catch (Throwable e) {
            LOGGER.warn("filter wx image failed : ", e);
        }
        //兼容新版商品发布,新版商品发布把详情图放到ItemExtra中
        List<String> extraImages = getItemExtraImages(item);
        //extraImages不为空，说明是新版商品发布修改过的商品
        if (CollectionUtils.isNotEmpty(extraImages)){

            detailModuleList = this.getDefaultModule(item,extraImages);

        } else  if (CollectionUtils.isEmpty(detailModuleList)) { // 图文详情没有数据,用topImgs填充
            detailModuleList = this.getDefaultModule(item,extraImages);
        } else { // 设置模块的中文名
            setModuleName(detailModuleList, item);
        }
        return detailModuleList;
    }

    public void setModuleName(List<DetailModule> detailModules, ItemDO item) {
        if (commonSwitchUtil.isOn(SwitchKey.USE_DYNAMIC_DETAILINFO)) {
            // 用tangram客户端设置模块名
            Map<String, String> componentCNNameMap = catCompRelationServiceClient.getCategoryComponentNameMappings(item.getCids(), null);
            for (DetailModule detailModule : detailModules) {
                String name = ModuleNameUtil.getModuleName(detailModule.getAnchor(), componentCNNameMap);
//                String moduleName = componentCNNameMap.get(detailModule.getAnchor());
                detailModule.setKey(name);
            }
        } else {
            // 用本地静态数据设置模块名
            Map<String, String> moduleNames = ModuleNameUtil.getModuleNames(item.getCids());
            for (DetailModule detailModule : detailModules) {
                String moduleName = ModuleNameUtil.getModuleName(detailModule.getAnchor(), moduleNames);
                detailModule.setKey(moduleName);
            }
        }
    }


    private List<DetailModule> getDefaultModule(ItemDO item,List<String> extraImages) {
        DetailModule detailModule = null;
        if (CollectionUtils.isNotEmpty(extraImages)){
            detailModule = DetailParseUtil.fillItemExtraImages(extraImages);

        }else {
            detailModule = DetailParseUtil.fillTopImages(item);

        }
        if (detailModule == null) {
            return null;
        }

        detailModule.setKey("图文详情");
        detailModule.setAnchor("detail_info");

        return Collections.singletonList(detailModule);
    }

    private boolean isMeiliSelectShop(Long shopId) {
        try {
            String meiliShopId = metabaseClient.get("meiliSelect_shopId");
            if (!org.apache.commons.lang3.StringUtils.isBlank(meiliShopId)) {
                return meiliShopId.equals(shopId.toString());
            }
        } catch (Throwable e) {
            ;
        }
        return false;
    }

    private String getOverseaBanner(DetailContext context) {
        if (ShopInfoTagsUtil.stringToSet(context.getItemDO().getShopInfo().getTags()).contains(CROSS_BORDER_SHOP_TAG)) {
            String commonKey = "common_oversea_banner";
            try {
                // 不区分不同平台，用统一的图
                String bannerPath = metabaseClient.get(commonKey);
                if (!org.apache.commons.lang3.StringUtils.isEmpty(bannerPath)) {
                    return ImageUtil.img(bannerPath);
                }
            } catch (Exception ignore) {
            }
        }
        return null;
    }

    private String getAdvertBanner(DetailContext context) {
        Long shopId = context.getItemDO().getShopId();
        if (isMeiliSelectShop(shopId)) {
            return null;
        }
        String appkey = context.getParam("appkey");
        if (!StringUtils.isEmpty(appkey)) {
            try {
                String key = appkey + "_advert_banner";
                String bannerPath = metabaseClient.get(key);
                if (!org.apache.commons.lang3.StringUtils.isEmpty(bannerPath)) {
                    return ImageUtil.img(bannerPath);
                }
            } catch (Exception e) {}
        }

        String app = context.getRouteInfo().getApp().name().toLowerCase();
        String platform = context.getRouteInfo().getPlatform().name().toLowerCase();
        platform = "pc".equals(platform) ? platform : "app";
        try {
            String key = app + "_" + platform + "_advert_banner";
            String bannerPath = metabaseClient.get(key);
            if (!org.apache.commons.lang3.StringUtils.isEmpty(bannerPath)) {
                return ImageUtil.img(bannerPath);
            }
        } catch (Exception e) {
        }
        return null;
    }

    //获取资质图片
    private CertInfo getCertInfo(DetailContext context) {
        if (!MetabaseTool.isOn("show_cert_info", false)) {
            return null;
        }
        DetailItemDO itemDO = context.getItemDO();
        try {
            if (itemDO.getFeatures() == null) {
                return null;
            }
            String path = itemDO.getCids();
            List<JSONObject> configs = JSON.parseArray(MetabaseTool.getValue("cert_cid_config"), JSONObject.class);
            JSONObject match = null;
            outer:
            for (JSONObject config : configs) {
                JSONArray cids = config.getJSONArray("cids");
                for (Object cid : cids) {
                    if (path.contains("#" + cid + "#")) {
                        match = config;
                        break outer;
                    }
                }
            }
            if (match == null) {
                return null;
            }


            String certImgField = null;
            //影子商品取主商品的资质字段
            if (TagUtil.isContainsTag(itemDO.getItemTags(), 800)) {
                String templateItemId = itemDO.getFeatures().get("templateItemId");
                if (StringUtils.isNotEmpty(templateItemId)) {
                    BaseResultDO<ItemExtraDO> itemExtraDOBaseResultDO = itemExtraService.queryItemExtraInfo(Long.parseLong(templateItemId));
                    if (itemExtraDOBaseResultDO.isSuccess()) {
                        ItemExtraDO itemExtraDO = itemExtraDOBaseResultDO.getResult();
                        if (itemExtraDO != null) {
                            certImgField = MapUtils.getString(itemExtraDO.getFeatures(),match.getString("certImgField"));
                        }
                    }

                }
            } else {
                certImgField = itemDO.getFeatures().get(match.getString("certImgField"));
            }
            if (certImgField != null) {
                CertInfo certInfo = new CertInfo();
                List<Map> images = JSON.parseArray(certImgField, Map.class);
                List<String> urls = new ArrayList<>();
                for (Map img : images) {
                    Object imgPath = img.get("path");
                    if (imgPath != null) {
                        urls.add(ImageUtil.img(imgPath.toString()) + MetabaseTool.getValue("cert_img_watermark_suffix"));
                    }
                }
                certInfo.setCertImgList(urls);
                certInfo.setTitle(match.getString("title"));
                certInfo.setJumpUrl(String.format(MetabaseTool.getValue("cert_img_jumpurl"), IdConvertor.idToUrl(itemDO.getItemId())));
                return certInfo;
            }
        } catch (Exception ex) {
            logger.error("获取资质图片失败 {}", JSON.toJSONString(itemDO), ex);
        }

        return null;
    }

    private String getXcxGuideImg(DetailContext context) {
        try {
            if ("100063".equals(context.getParam("appkey"))) {
                String imgPath = metabaseClient.get("xcx_h5_guide_img");
                if (org.apache.commons.lang3.StringUtils.isNotBlank(imgPath)) {
                    return ImageUtil.img(imgPath);
                }
            }
        } catch (Throwable e) {
        }
        return null;
    }

    private static List<String> getItemExtraImages(ItemDO item){

        List<String> imageUrl = Lists.newArrayList();
        ItemExtraDO itemExtraDO = item.getItemExtraDO();
        if (itemExtraDO != null &&  itemExtraDO.getFeatures() != null && itemExtraDO.getFeatures().get("detailImages") != null) {
            List<ImageInfo> imageInfoList = JSONObject.parseArray(itemExtraDO.getFeatures().get("detailImages") ,ImageInfo.class);
            imageUrl = imageInfoList.stream().map(ImageInfo ::getPath).map(ImageUtil::img).collect(Collectors.toList());

        }
     return imageUrl;
    }


}
