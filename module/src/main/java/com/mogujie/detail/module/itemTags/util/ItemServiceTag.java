package com.mogujie.detail.module.itemTags.util;

/**
 * Created by anshi on 17/10/12.
 */
public enum ItemServiceTag {

    WELL_CA("WELL_CA", "蘑菇良品", "/p2/161123/upload_655a8fjadhhj1hbd87ae13hd91325_60x60.png", "蘑菇良品商品来自品质商家，购买此商品因质量问题、描述不符等原因发起维权，且维权成立，您将得到3倍赔偿。"),
    BRAND_CA("BRAND_CA", "品牌认证", "/p2/161123/upload_7689bkb031gkfeh3f69598c60fj9l_60x60.png", "严格审核，正规品牌授权链；精挑细选，国内外知名品牌合作。引领潮流趋势，时尚品质保证。"),
    PHOTO_CA("PHOTO_CA", "实拍认证", "/p2/161123/upload_4feh98909481a8805bbdal579kg79_60x60.png", "商家承诺所有商品图片均为实物拍摄，所见即所得，解除您网购货不对板的后顾之虑。"),
    NEW_CA("NEW_CA", "新品认证", "/p2/161123/upload_76c2a0i2h57c64k88029kchk2gkce_60x60.png", "新品认证商品是蘑菇街根据当下时尚流行元素挑选出来的热点新品。关注新品，轻松掌控潮流趋势。"),
    GOOD_GOODS("GOOD_GOODS", "名品制造", "/p2/161123/upload_82e30ebae7g3dl1515gea36d1il8d_60x60.png", "所有名品制造商品均由优质厂商供货，经资深买手选款和专业质检团队质检，从源头把控商品的质量，并使用统一包装。"),

    OUTSTANDING_WITH_BRAND_CA("BRAND_CA", "品牌认证", "/mlcdn/e5265e/170331_5lb4141g4ig023habefc012231a9c_60x60.png", "严格审核，正规品牌授权链；精挑细选，国内外知名品牌合作。引领潮流趋势，时尚品质保证。"),
    OUTSTANDING_WITH_PHOTO_CA("PHOTO_CA", "实拍认证", "/mlcdn/e5265e/170331_1adj10ae6eg4b28g589a4hdk4j9aj_60x63.png", "商家承诺所有商品图片均为实物拍摄，所见即所得，解除您网购货不对板的后顾之虑。"),
    OUTSTANDING_WITH_NEW_CA("NEW_CA", "新品认证", "/mlcdn/e5265e/170331_097j5aa31bhg8f8j9hibc3i86glh3_60x63.png", "新品认证商品是蘑菇街根据当下时尚流行元素挑选出来的热点新品。关注新品，轻松掌控潮流趋势。"),
    OUTSTANDING_WITH_GOOD_GOODS("GOOD_GOODS", "名品制造", "/mlcdn/e5265e/170331_8dlhh1eb66aki5612889kca91fk7d_60x63.png", "所有名品制造商品均由优质厂商供货，经资深买手选款和专业质检团队质检，从源头把控商品的质量，并使用统一包装。");

    private String key;
    private String desc;
    private String icon;
    private String text;

    ItemServiceTag(String key, String desc, String icon, String text) {
        this.key = key;
        this.desc = desc;
        this.icon = icon;
        this.text = text;
    }


    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }


    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getDesc() {
        return desc;
    }
}
