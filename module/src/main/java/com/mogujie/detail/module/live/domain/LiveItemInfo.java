package com.mogujie.detail.module.live.domain;

import com.mogujie.live.fendi.api.response.FendiLiveItemInfoResponse;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 * Date: 2021/3/18
 * Introduction:商品对应直播间信息
 */
@Data
public class LiveItemInfo {
    /**
     * 商品id
     */
    private Long itemId;
    /**
     * 直播间id
     */
    private Long liveId;
    /**
     * @see com.mogujie.live.fendi.api.enums.FendiItemSeckillStatus
     */
    private Integer seckillType;
    /**
     * 0不是主推商品，1是主推商品
     */
    private Integer isMainItem;
    /**
     * acm
     */
    private String acm;
    /**
     * 主播Id
     */
    private Long actUserId;
    /**
     * 主播名
     */
    private String actorName;
    /**
     * 主播头像
     */
    private String actorAvatar;

    public static LiveItemInfo convert(FendiLiveItemInfoResponse fendiLiveItemInfoResponse){
        LiveItemInfo liveItemInfo = new LiveItemInfo();
        BeanUtils.copyProperties(fendiLiveItemInfoResponse, liveItemInfo);
        return  liveItemInfo;
    }
}
