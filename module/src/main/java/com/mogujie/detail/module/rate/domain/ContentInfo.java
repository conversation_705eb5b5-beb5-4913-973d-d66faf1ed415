package com.mogujie.detail.module.rate.domain;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @AUTUOR: lengshan
 * @DESCRIPTION: 精选晒单
 * @DATE: 2019/9/6 下午2:44
 */
public class ContentInfo implements Serializable {

    private static final long serialVersionUID = -8108265471333677117L;

    @Getter
    @Setter
    private String contentId;

    @Getter
    @Setter
    private String cover;

    @Getter
    @Setter
    private String link;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ContentInfo that = (ContentInfo) o;

        if (contentId != null ? !contentId.equals(that.contentId) : that.contentId != null) return false;
        if (cover != null ? !cover.equals(that.cover) : that.cover != null) return false;
        return link != null ? link.equals(that.link) : that.link == null;
    }

    @Override
    public int hashCode() {
        int result = contentId != null ? contentId.hashCode() : 0;
        result = 31 * result + (cover != null ? cover.hashCode() : 0);
        result = 31 * result + (link != null ? link.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "ContentInfo{" +
                "contentId='" + contentId + '\'' +
                ", cover='" + cover + '\'' +
                ", link='" + link + '\'' +
                '}';
    }
}