package com.mogujie.detail.module.itemParams.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by xiaoyao on 16/8/16.
 */
public class ItemParamsDO implements ModuleDO {

    @Getter
    @Setter
    private ProductInfo info;

    @Getter
    @Setter
    private Rule rule;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ItemParamsDO)) return false;

        ItemParamsDO that = (ItemParamsDO) o;

        if (getInfo() != null ? !getInfo().equals(that.getInfo()) : that.getInfo() != null) return false;
        return getRule() != null ? getRule().equals(that.getRule()) : that.getRule() == null;

    }

    @Override
    public int hashCode() {
        int result = getInfo() != null ? getInfo().hashCode() : 0;
        result = 31 * result + (getRule() != null ? getRule().hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "ItemParamsDO{" +
                "info=" + info +
                ", rule=" + rule +
                '}';
    }
}
