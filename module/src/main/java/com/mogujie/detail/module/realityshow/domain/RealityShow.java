package com.mogujie.detail.module.realityshow.domain;

import java.io.Serializable;
import java.util.Map;

/**
 * Created by xy on 2017/7/10.
 */
public class RealityShow implements Serializable {

    /**
     * 视频id
     */
    private Long videoId;

    private Integer width;

    private Integer height;

    /**
     * 封面图
     */
    private String coverImg;

    /**
     * sku数据
     */
    private Map<String, String> skuInfo;

    /**
     * 审核状态，2为通过
     */
    private Integer auditStatus;

    private StarInfo starInfo;

    public Long getVideoId() {
        return videoId;
    }

    public void setVideoId(Long videoId) {
        this.videoId = videoId;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public String getCoverImg() {
        return coverImg;
    }

    public void setCoverImg(String coverImg) {
        this.coverImg = coverImg;
    }

    public Map<String, String> getSkuInfo() {
        return skuInfo;
    }

    public void setSkuInfo(Map<String, String> skuInfo) {
        this.skuInfo = skuInfo;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public StarInfo getStarInfo() {
        return starInfo;
    }

    public void setStarInfo(StarInfo starInfo) {
        this.starInfo = starInfo;
    }
}
