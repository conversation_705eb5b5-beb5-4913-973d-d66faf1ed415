package com.mogujie.detail.module.live;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.module.live.domain.LiveSliceDO;
import com.mogujie.item.api.LiveItemExplainService;
import com.mogujie.item.domain.LiveItemExplainDTO;
import com.mogujie.item.domain.LiveItemResponse;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by anshi on 17/12/6.
 */
@Module(name = "liveSlice")
public class LiveSliceDOProvider implements IModuleDOProvider<LiveSliceDO> {
    private static final Logger logger = LoggerFactory.getLogger(LiveSliceDOProvider.class);

    private LiveItemExplainService liveItemExplainService;

    @Override
    public void init() throws DetailException {
        try {
            liveItemExplainService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(LiveItemExplainService.class);
        } catch (Exception e) {
            logger.error("liveSlice service init error.", e);
        }
    }

    @Override
    public LiveSliceDO emit(DetailContext context) {
        if(context.isDyn()){
            return null;
        }

        try {
            Long itemId = context.getItemId();
            LiveItemResponse<LiveItemExplainDTO> liveSliceRes = liveItemExplainService.queryItemExplainInfo(itemId);
            if (liveSliceRes != null && liveSliceRes.isSuccess() && liveSliceRes.getResult() != null) {
                LiveItemExplainDTO liveItemExplainDTO = liveSliceRes.getResult();
                LiveSliceDO liveSliceDO = new LiveSliceDO();
                liveSliceDO.setUrl(liveItemExplainDTO.getExplainUrl());
                liveSliceDO.setVideoId(liveItemExplainDTO.getVideoId());
                liveSliceDO.setCoverImage(liveItemExplainDTO.getExplainCover());
                return liveSliceDO;
            }
        } catch (Exception e) {
            logger.error("get item LiveSlice do error!", e);
        }
        return null;
    }


}
