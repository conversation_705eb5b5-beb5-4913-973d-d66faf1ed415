package com.mogujie.detail.module.spout.spi;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.spi.Exposed;
import com.mogujie.detail.core.task.AbstractCollectDataTask;

import java.util.List;

/**
 * 公共数据
 * Created by anshi on 17/3/8.
 */
@Exposed
public interface ICommonDataProvider {

    /**
     * 获取静态请求异步数据收集器
     *
     * @param context
     * @return
     */
    List<AbstractCollectDataTask> listStaticCollectDataTask(DetailContext context) throws DetailException;

    /**
     * 获取动态请求异步数据收集器
     *
     * @param context
     * @return
     */
    List<AbstractCollectDataTask> listDynCollectDataTask(DetailContext context) throws DetailException;
}
