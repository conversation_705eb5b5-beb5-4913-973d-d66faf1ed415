package com.mogujie.detail.module.coupon.provider;

import com.google.common.collect.Lists;
import com.meili.service.shopcenter.domain.entity.ShopInfo;
import com.meili.service.shopcenter.util.ShopInfoTagsUtil;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.darwin.util.CollectionUtil;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.constant.SwitchKey;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.core.util.ContextUtil;
import com.mogujie.detail.core.util.ImageUtil;
import com.mogujie.detail.core.util.MaitUtil;
import com.mogujie.detail.module.coupon.domain.CouponConfigType;
import com.mogujie.detail.module.coupon.domain.CouponDO;
import com.mogujie.detail.module.coupon.domain.CrossShopDiscount;
import com.mogujie.detail.module.coupon.domain.CrossShopDiscountBanner;
import com.mogujie.detail.module.coupon.domain.PlatformCouponV2;
import com.mogujie.service.diana.CouponPkgReadService;
import com.mogujie.service.diana.dto.DianaCouponInfoDTO;
import com.mogujie.service.diana.dto.ItemProTagDTO;
import com.mogujie.service.diana.query.CouponInfosQuery;
import com.mogujie.service.diana.result.NatashaResult;
import com.mogujie.service.hummer.api.BonusReadService;
import com.mogujie.service.hummer.domain.dto.BonusDTO;
import com.mogujie.service.hummer.domain.dto.QueryItemBonusParam;
import com.mogujie.service.hummer.domain.dto.result.Result;
import com.mogujie.service.hummer.utils.PromotionConvertUtils;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.basic.ItemPreSaleDO;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.service.item.domain.basic.ItemTagDO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import com.mogujie.themis.camp.config.CampTagConfigClient;
import com.mogujie.themis.config.BaseInfo;
import com.mogujie.themis.config.CampMetabaseConfig;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.mogujie.cayenne.timeclient.TimeClient;
import org.mogujie.cayenne.timeclient.TimeClientFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by xiaoyao on 16/10/24.
 */
@Module(name = "coupon")
public class CouponDOProvider implements IModuleDOProvider<CouponDO> {

    private static final Logger LOGGER = LoggerFactory.getLogger(CouponDOProvider.class);


    private CouponPkgReadService couponPkgReadService;

    private BonusReadService bonusReadService;


    private static final Long CROSS_SHOP_MAIT_ID = 134291L;

    // 新版跨店满减优惠banner的风车请求code
    private static final String CROSS_SHOP_DISCOUTN_BANNER_CODE = "camp_temp_cross_reduce_config";

    private TimeClient timeClient;

    private static final Long NEW_USER_COUPON_MAIT_ID = 149764L;

    @Autowired
    private CommonSwitchUtil commonSwitchUtil;

    @Override
    public void init() throws DetailException {
        try {
            couponPkgReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(CouponPkgReadService.class);
            bonusReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(BonusReadService.class);
            timeClient = TimeClientFactory.client("cayenne");
        } catch (Exception e) {
            LOGGER.error("init CouponDOProvider error!");
            throw new DetailException(e);
        }
    }

    @Override
    public CouponDO emit(DetailContext context) {
        if (!commonSwitchUtil.isOn(SwitchKey.HUMMER_PART_COUPON_LIST)) {
            return null;
        }
        try {
            DetailItemDO item = context.getItemDO();
            List<String> tags = ContextUtil.getNumTags(item);
            String tagString = StringUtils.join(tags, ",");

            Map<String, Object> map = CampTagConfigClient.getCampConfigDetail(8, BaseInfo.ConfigLife.WHOLE,
                    tagString, BaseInfo.Platform.APP.getCode(), CampMetabaseConfig.CampConfigMenu.CAMP_CONFIG_PAGE_DETAIL);
            String relationKey = CampTagConfigClient.objToString(map.get("couponLink"));
            //跨店满减优惠（购物津贴）
            List<Map<String, Object>> crossShopConfigs = MaitUtil.getMaitData(CROSS_SHOP_MAIT_ID);
            Map<String, Object> crossShopConfig = null;
            if (CollectionUtils.isNotEmpty(crossShopConfigs)) {
                crossShopConfig = crossShopConfigs.get(0);
            } else {
                crossShopConfig = new HashMap<>();
            }
            String crossShopDiscountKey = CampTagConfigClient.objToString(crossShopConfig.get("crossShopDiscountKey"));
            CrossShopDiscount crossShopDiscount = StringUtils.isBlank(crossShopDiscountKey) ? null : getCrossShopDiscountById(context, Long.parseLong(crossShopDiscountKey));
            CouponDO couponDO = null;
            if (ContextUtil.isBonusItem(context) && crossShopDiscount != null) {
                couponDO = new CouponDO();
                couponDO.setCrossShopDiscountBgImg(ImageUtil.img(CampTagConfigClient.objToString(crossShopConfig.get("crossShopDiscountBgImg"))));
                couponDO.setCrossShopDiscountBtnText(CampTagConfigClient.objToString(crossShopConfig.get("crossShopDiscountBtnText")));
                couponDO.setCrossShopDiscount(crossShopDiscount);
                couponDO.setCouponPkgTitle(CampTagConfigClient.objToString(crossShopConfig.get("crossShopDiscountTitle")));
            }
            //平台券
            if (StringUtils.isNotBlank(relationKey)) {
                //单张券
                List<PlatformCouponV2> couponV2s = getPlatformCoupon(context, relationKey, 1);
                couponDO = couponDO == null ? new CouponDO() : couponDO;
                couponDO.setCouponConfigType(CouponConfigType.PACKAGE);
                if (StringUtils.isBlank(couponDO.getCouponPkgTitle())) {
                    couponDO.setCouponPkgTitle(CampTagConfigClient.objToString(map.get("couponTitle")));
                }
                couponDO.setCouponPkgImg(ImageUtil.img(CampTagConfigClient.objToString(map.get("couponImg"))));
                couponDO.setRelationKey(relationKey);
                couponDO.setPlatformCouponV2(couponV2s);
            } else {
                //券包
                String multiCouponRelationKey = CampTagConfigClient.objToString(map.get("multiCouponLink"));
                if (StringUtils.isNotBlank(multiCouponRelationKey)) {
                    List<PlatformCouponV2> couponV2s = getPlatformCoupon(context, multiCouponRelationKey, 2);
                    couponDO = couponDO == null ? new CouponDO() : couponDO;
                    couponDO.setCouponConfigType(CouponConfigType.MULTI_COUPONS);
                    if (StringUtils.isBlank(couponDO.getCouponPkgTitle())) {
                        couponDO.setCouponPkgTitle(CampTagConfigClient.objToString(map.get("couponTitle")));
                    }
                    couponDO.setCouponBgImg(ImageUtil.img(CampTagConfigClient.objToString(map.get("couponBgImg"))));
                    couponDO.setCouponTagImg(ImageUtil.img(CampTagConfigClient.objToString(map.get("couponTagImg"))));
                    couponDO.setPlatformCouponV2(couponV2s);
                    couponDO.setCrossShopDiscountBgImg(ImageUtil.img(CampTagConfigClient.objToString(crossShopConfig.get("crossShopDiscountBgImg"))));
                    couponDO.setCrossShopDiscountBtnText(CampTagConfigClient.objToString(crossShopConfig.get("crossShopDiscountBtnText")));
                    couponDO.setCrossShopDiscount(crossShopDiscount);
                }
            }
            if (couponDO == null) {
                couponDO = getNewUserPlatformCoupon();
            }

            CrossShopDiscountBanner crossShopDiscountBanner = this.buildCrossShopDiscountBanner(context);
            if (crossShopDiscountBanner != null) {
                couponDO = couponDO == null ? new CouponDO() : couponDO;
                couponDO.setCrossShopDiscountBanner(crossShopDiscountBanner);
            }

            //对优惠券进行排序。
            if (couponDO != null && couponDO.getPlatformCouponV2() != null) {
                Long promotionPrice = context.getItemDO().getPromotionPrice();
                if (promotionPrice == null) {
                    promotionPrice = getLowPrice(context.getItemDO().getItemSkuDOList());
                }
                if (promotionPrice != null && promotionPrice.intValue() > 0) {
                    couponDO.setPlatformCouponV2(sortCouponByPrice(couponDO.getPlatformCouponV2(), promotionPrice.intValue()));
                }
            }
            return couponDO;
        } catch (Throwable e) {
            LOGGER.error("get platform coupon error!", e);
        }
        return null;
    }

    //具体需求见PRD https://mogu.feishu.cn/docs/doccntF4XwgTwjrFtCyoVNpFsNd
    private List<PlatformCouponV2> sortCouponByPrice(List<PlatformCouponV2> coupons, final Integer price) {
        if (coupons == null) {
            return null;
        }

        //按照门槛进行划分，true为满足门槛，false为不满足门槛的
        Map<Boolean, List<PlatformCouponV2>> result = coupons.stream()
                .collect(Collectors.partitioningBy(x -> x.getLimitPrice() <= price, Collectors.toList()));

        //满足门槛的按照减金从大到小排序，减金额相同的按照门槛从小到大
        result.get(true).sort((x, y) -> {
            if (Objects.equals(x.getCutPrice(), y.getCutPrice())) {
                return x.getLimitPrice() - y.getLimitPrice();
            } else {
                return y.getCutPrice() - x.getCutPrice();
            }
        });

        //不满足门槛的按照门槛从小到达排序，门槛相同的按照减金额从大到校排序
        result.get(false).sort((x, y) -> {
            if (Objects.equals(x.getLimitPrice(), y.getCutPrice())) {
                return y.getCutPrice() - x.getCutPrice();
            } else {
                return x.getLimitPrice() - y.getLimitPrice();
            }
        });
        result.get(true).addAll(result.get(false));
        return result.get(true);
    }


    private CouponDO getNewUserPlatformCoupon() {
        List<Map<String, Object>> newUserCouponConfig = MaitUtil.getTargetedMaitData(NEW_USER_COUPON_MAIT_ID);
        if (CollectionUtils.isEmpty(newUserCouponConfig)) {
            return null;
        }

        CouponDO couponDO = new CouponDO();
        couponDO.setCouponConfigType(CouponConfigType.MULTI_COUPONS);
        couponDO.setCouponPkgTitle("新人专享券");
        couponDO.setCouponBgImg("https://s10.mogucdn.com/mlcdn/c45406/200928_1g7ig8f9jfelh34kh1f8a87fgdiak_1035x225.png");
        List<PlatformCouponV2> platformCouponV2s = Lists.newArrayList();
        newUserCouponConfig.forEach(config -> {
            PlatformCouponV2 platformCouponV2 = new PlatformCouponV2();
            platformCouponV2.setPromotionCode("platformCoupon");
            platformCouponV2.setHasReceived(true);
            platformCouponV2.setForNewUser(true);

            couponDO.setCouponPkgTitle(config.getOrDefault("newPeopleTitle", "新人专享券").toString());
            String limitPrice = config.getOrDefault("limitPrice", "0.0").toString();
            String cutPrice = config.getOrDefault("cutPrice", "0.0").toString();
            platformCouponV2.setLimitPrice((int) (NumberUtils.toDouble(limitPrice) * 100));
            platformCouponV2.setCutPrice((int) (NumberUtils.toDouble(cutPrice) * 100));
            platformCouponV2.setDecorate(config.getOrDefault("promotionDesc", "").toString());
            platformCouponV2.setCouponId(config.get("promotionId") != null ? Long.valueOf(config.get("promotionId").toString()) : null);
            platformCouponV2.setName("新人券");
            platformCouponV2s.add(platformCouponV2);
        });
        couponDO.setPlatformCouponV2(platformCouponV2s);
        return couponDO;
    }


    public CrossShopDiscount getCrossShopDiscountById(DetailContext context, Long campaignId) {
        try {
            if (!ContextUtil.isBonusItem(context)) {
                return null;
            }
            DetailItemDO itemDO = context.getItemDO();
            QueryItemBonusParam param = new QueryItemBonusParam();
            param.setCampaignId(campaignId);
            param.setMarket((short) ContextUtil.getMarketByContext(context));
            param.setItemId(context.getItemId());
            param.setShopNumTags(getShopLongTags(itemDO));
            param.setPreSale(isPresale(itemDO));
            param.setItemProTags(PromotionConvertUtils.convertItemTagToItemProTag(itemDO.getItemTags()));
            Result<BonusDTO> bonusDTOResult = bonusReadService.getItemValidBonus(param);
            if (bonusDTOResult != null && bonusDTOResult.isSuccess() && bonusDTOResult.getData() != null) {
                BonusDTO bonusDTO = bonusDTOResult.getData();
                CrossShopDiscount crossShopDiscount = new CrossShopDiscount();
                //终端
                crossShopDiscount.setTerminal(getTerminalByTerminalType(bonusDTO.getTerminalType()));
                //优惠名称
                crossShopDiscount.setName(bonusDTO.getName());
                //上限
                crossShopDiscount.setUpperLimit(bonusDTO.getPersonLimitNum());
                //每满20元减5元为例
                //每满 20000
                crossShopDiscount.setLimitPrice(bonusDTO.getPerLimitPrice());
                //减 500
                crossShopDiscount.setCutPrice(bonusDTO.getPerCutPrice());
                //活动开始时间 1548850680
                crossShopDiscount.setStartTime(bonusDTO.getStartTime().intValue());
                //活动结束时间 1549850680
                crossShopDiscount.setEndTime(bonusDTO.getEndTime().intValue());
                crossShopDiscount.setDecorate(bonusDTO.getDecorate());
                crossShopDiscount.setPromotionId(bonusDTO.getId());
                crossShopDiscount.setPromotionCode(bonusDTO.getPromotionCode());
                return crossShopDiscount;
            }
        } catch (Throwable e) {
            LOGGER.error("get cross shop discount error!", e);
        }
        return null;
    }

    private List<PlatformCouponV2> getPlatformCoupon(DetailContext context, String relationKey, Integer strategy) {
        CouponInfosQuery query = buildCouponInfoQuery(context, relationKey, strategy);
        NatashaResult<List<DianaCouponInfoDTO>> result = couponPkgReadService.getItemValidPlatformCouponList(query);
        if (result != null && result.isSuccess() && CollectionUtils.isNotEmpty(result.getData())) {
            List<DianaCouponInfoDTO> coupons = result.getData();
            return coupons.stream()
                    .map(coupon -> {
                        PlatformCouponV2 platformCouponV2 = new PlatformCouponV2();
                        platformCouponV2.setCouponId(coupon.getCouponId());
                        platformCouponV2.setCutPrice(coupon.getCutPrice());
                        platformCouponV2.setLimitPrice(coupon.getLimitPrice());
                        platformCouponV2.setName(coupon.getName());
                        platformCouponV2.setDecorate(coupon.getDecorate());
                        platformCouponV2.setDiscount(coupon.getDiscount() != null ? coupon.getDiscount().intValue() : null);
                        platformCouponV2.setMaxDecrease(coupon.getMaxDecrease());
                        platformCouponV2.setTerminal(getTerminalByTerminalType(coupon.getTerminalType()));
                        platformCouponV2.setStartTime(coupon.getStartTime() != null ? coupon.getStartTime().intValue() : null);
                        platformCouponV2.setEndTime(coupon.getEndTime() != null ? coupon.getEndTime().intValue() : null);
                        platformCouponV2.setPromotionId(coupon.getCouponId());
                        platformCouponV2.setPromotionCode(coupon.getCouponType());
                        return platformCouponV2;
                    })
                    .collect(Collectors.toList());

        }
        return Collections.EMPTY_LIST;
    }


    private List<Long> getShopLongTags(DetailItemDO item) {
        List<Integer> tags = getShopTags(item);
        if (tags == null) {
            return null;
        } else {
            return tags.stream().map(Integer::longValue).collect(Collectors.toList());
        }
    }

    private List<Integer> getShopTags(DetailItemDO item) {
        Object shopInfoObj = item.getShopInfo();
        if (null != shopInfoObj) {
            ShopInfo shopInfo = (ShopInfo) shopInfoObj;
            Set<Integer> tags = ShopInfoTagsUtil.stringToSet(shopInfo.getTags());
            if (!com.mogujie.tesla.common.CollectionUtils.isEmpty(tags)) {
                return new ArrayList<>(tags);
            }
        }
        return null;
    }


    /**
     * 构造券包请求对象
     *
     * @param context
     * @param relationKey
     * @return
     */
    private static CouponInfosQuery buildCouponInfoQuery(DetailContext context, String relationKey, Integer strategy) {
        try {
            CouponInfosQuery query = new CouponInfosQuery();
            query.setItemId(context.getItemId());
            query.setMarket((short) ContextUtil.getMarketByContext(context));
            query.setRelationKey(relationKey);
            DetailItemDO itemDO = context.getItemDO();
            //设置商品feature标（KV标）
            List<ItemTagDO> itemTagDOS = itemDO.getItemTags();
            if (itemTagDOS != null) {
                query.setItemProTags(itemTagDOS.stream()
                        .map(tag -> {
                            ItemProTagDTO proTag = new ItemProTagDTO();
                            proTag.setBizId(tag.getBizId());
                            proTag.setTagKey(tag.getTagKey());
                            proTag.setTagValue(tag.getTagValue());
                            proTag.setStartTime(tag.getStartTime());
                            proTag.setEndTime(tag.getEndTime());
                            return proTag;
                        })
                        .collect(Collectors.toList()));
            }
            //设置店铺数字标
            Set<Integer> shopTags = ShopInfoTagsUtil.stringToSet(itemDO.getShopInfo().getTags());
            if (shopTags != null) {
                query.setShopNumTags(shopTags.stream()
                        .map(tag -> tag.longValue())
                        .collect(Collectors.toList()));
            }
            //设置是否为预售
            query.setPreSale(isPresale(itemDO));
            query.setStrategy(strategy);
            return query;
        } catch (Throwable e) {
            LOGGER.error("build coupon info query error.", e);
            return null;
        }
    }

    /**
     * 根据终端type获取具体的终端文案
     *
     * @param type
     * @return
     */
    private static String getTerminalByTerminalType(Integer type) {
        String terminal = "";
        if (type != null) {
            switch (type) {
                case 1:
                    terminal = "App专享";
                    break;
                case 2:
                    terminal = "PC专享";
                    break;
                case 3:
                    terminal = "女装小程序专享";
                    break;
                default:
                    break;
            }
        }
        return terminal;
    }

    private static Boolean isPresale(ItemDO item) {
        int nowTime = (int) (System.currentTimeMillis() / 1000);
        if (null != item.getItemPreSaleDO()) {
            ItemPreSaleDO preSale = item.getItemPreSaleDO();
            if (preSale.getStart() < nowTime && nowTime < preSale.getEnd()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 构造跨店满减banner信息
     *
     * @param context 详情页请求上下文
     */
    private CrossShopDiscountBanner buildCrossShopDiscountBanner(DetailContext context) {
        // 1.通用开关。在非大促期间可以关闭
        if (!commonSwitchUtil.isOn(SwitchKey.SWITCH_CROSS_SHOP_DISCOUNT_BANNER)) {
            return null;
        }
        // 2.请求风车获取跨店满减配置
        Map<String, Object> campConfig = CampTagConfigClient.getCampConfigForCrossReduce(ContextUtil.getNumTagString(context.getItemDO()));
        if (MapUtils.isEmpty(campConfig)) {
            return null;
        }
        // 3.assemble and set
        Boolean needCrossReduce = MapUtils.getBoolean(campConfig, "needCrossReduce");
        if (!needCrossReduce) {
            return null;
        }
        Long startTime = MapUtils.getLong(campConfig, "startTime");//预热开始时间
        Long officialStartTime = MapUtils.getLong(campConfig, "officialStartTime");//预热结束时间=正式开始时间
        Long endTime = MapUtils.getLong(campConfig, "endTime");//正式结束时间
        Integer nowTime = (int) (timeClient.fake() / 1000);
        if (nowTime < startTime || nowTime > endTime) {
            return null;
        }
        CrossShopDiscountBanner crossShopDiscountBanner;
        if (nowTime < officialStartTime) {
            crossShopDiscountBanner = this.buildWarmBanner(campConfig);
        } else {
            crossShopDiscountBanner = this.buildOfficialBanner(campConfig, officialStartTime, endTime, context.getItemDO().getUserId());
        }
        return crossShopDiscountBanner;
    }

    /**
     * 构造正式期的跨店满减banner
     *
     * @param campConfig        风车配置信息
     * @param officialStartTime 正式开始时间
     * @param officialEndTime   正式结束时间
     * @return
     */
    private CrossShopDiscountBanner buildOfficialBanner(Map<String, Object> campConfig, Long officialStartTime, Long officialEndTime, long userId) {
        // 正式期
        String bannerUrl = MapUtils.getString(campConfig, "detailBanner");
        Long promotionId = MapUtils.getLong(campConfig, "promotionId");
        String title = MapUtils.getString(campConfig, "coudanCrossReduceDesc");
        if (StringUtils.isBlank(bannerUrl) || StringUtils.isBlank(title) || promotionId == null || officialStartTime == null || officialEndTime == null) {
            return null;
        }
        CrossShopDiscountBanner crossShopDiscountBanner = new CrossShopDiscountBanner();
        crossShopDiscountBanner.setImageUrl(bannerUrl);
        String startTime = new SimpleDateFormat("yyyy-MM-dd HH:mm").format(new Date(officialStartTime * 1000));
        String endTime = new SimpleDateFormat("yyyy-MM-dd HH:mm").format(new Date(officialEndTime * 1000));
        String desc = "使用时间：" + startTime + "-" + endTime;
        crossShopDiscountBanner.setAppUrl(
                String.format("mgj://coudanwaterfall?promotionCode=crossReachReduce&coudanStatus=2&unionId=%s&promotionId=%s&tip1=%s&tip2=%s&wallTopDecorateTitle=%s&wallTopDecorateSubTitle=%s"
                        , IdConvertor.idToUrl(userId)
                        , promotionId
                        , title
                        , title
                        , title
                        , desc));
        crossShopDiscountBanner.setXcxUrl(
                String.format("/pages/wallPackage/index?&cKey=xcx-coupon-mass&promotionCode=crossReachReduce&promotionId=%s&title=%s\n%s"
                        , IdConvertor.idToUrl(promotionId)
                        , title
                        , desc));
        return crossShopDiscountBanner;
    }

    /**
     * 构造预热期的跨店满减banner
     *
     * @param campConfig 风车配置信息
     * @return
     */
    private CrossShopDiscountBanner buildWarmBanner(Map<String, Object> campConfig) {
        String bannerUrl = MapUtils.getString(campConfig, "detailWarmUpBanner");
        if (StringUtils.isBlank(bannerUrl)) {
            return null;
        }
        CrossShopDiscountBanner crossShopDiscountBanner = new CrossShopDiscountBanner();
        crossShopDiscountBanner.setImageUrl(bannerUrl);
        return crossShopDiscountBanner;
    }

    private Long getLowPrice(final List<ItemSkuDO> skuList) {
        if (CollectionUtil.isEmpty(skuList)) {
            return 0L;
        }
        Long lowPrice = skuList.get(0).getPrice().longValue();
        for (ItemSkuDO sku : skuList) {
            if (sku.getPrice().longValue() < lowPrice) {
                lowPrice = sku.getPrice().longValue();
            }
        }
        return lowPrice;
    }
}
