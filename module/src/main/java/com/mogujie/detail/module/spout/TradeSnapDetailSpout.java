package com.mogujie.detail.module.spout;

import com.google.gson.Gson;
import com.meili.service.shopcenter.domain.entity.ShopInfo;
import com.meili.service.shopcenter.result.Result;
import com.mogujie.commons.utils.Base62;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.SnapDetailSpout;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.NumUtil;
import com.mogujie.item.mapper.ModelMapperHelper;
import com.mogujie.item.mapper.ObjectConverter;
import com.mogujie.metabase.utils.CollectionUtils;
import com.mogujie.service.imagev2.domain.complex.TaggedImage;
import com.mogujie.service.item.domain.CompleteItem;
import com.mogujie.service.item.domain.Sku;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.service.item.domain.entity.StockAttribution;
import com.mogujie.service.muser.api.UserService;
import com.mogujie.service.shopcenter.client.ShopReadServiceClient;
import com.mogujie.service.subuser.api.SellerSubUserService;
import com.mogujie.service.subuser.domain.entity.SellerSubUser;
import com.mogujie.service.subuser.domain.result.DataResult;
import com.mogujie.service.trade.microservice.order.api.query.itemorder.ItemOrderQueryService;
import com.mogujie.service.trade.microservice.order.api.query.shoporder.ShopOrderQueryService;
import com.mogujie.service.trade.microservice.order.domain.dto.query.req.SingleOrderReqDTO;
import com.mogujie.service.trade.microservice.order.domain.dto.query.res.itemorder.ItemOrderAndItemExResDTO;
import com.mogujie.service.trade.microservice.order.domain.dto.query.res.itemorder.ItemOrderResDTO;
import com.mogujie.service.trade.microservice.order.domain.dto.query.res.shoporder.ShopOrderCoreResDTO;
import com.mogujie.service.xiaodian.trade.service.order.v1.api.snapshot.QueryForSnapshotService;
import com.mogujie.service.xiaodian.trade.service.order.v1.domain.dto.Response;
import com.mogujie.service.xiaodian.trade.service.order.v1.domain.dto.snapshot.request.SnapshotRequestDto;
import com.mogujie.service.xiaodian.trade.service.order.v1.domain.dto.snapshot.response.SnapshotDetailDto;
import com.mogujie.session.SessionContextHolder;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import com.mogujie.trade.warehouse.api.qcorder.QcConfigQueryUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by anshi on 17/3/8.
 */
@Component
public class TradeSnapDetailSpout implements SnapDetailSpout {

    private static final Logger LOGGER = LoggerFactory.getLogger(TradeSnapDetailSpout.class);

    private Gson gson;

    @Autowired
    private ObjectConverter convertMapper;

    private QueryForSnapshotService queryForSnapshotService;

    private ItemOrderQueryService itemOrderQueryService;

    private ShopOrderQueryService shopOrderQueryService;

    private SellerSubUserService sellerSubUserService;

    private UserService userService;

    @PostConstruct
    public void init() throws Exception {
        gson = new Gson();
        try {
            queryForSnapshotService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(QueryForSnapshotService.class);
            userService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(UserService.class);
            shopOrderQueryService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ShopOrderQueryService.class);
            itemOrderQueryService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ItemOrderQueryService.class);
            sellerSubUserService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(SellerSubUserService.class);
        } catch (Exception e) {
            LOGGER.error("init service failed : {}", e);
            throw new DetailException(e);
        }
    }

    /**
     * 将ItemDO塞入context
     *
     * @param context    详情上下文
     * @param orderIdUrl 订单idUrl形式
     */
    @Override
    public void decorateSnapItemDO(DetailContext context, String orderIdUrl) {
        if (StringUtils.isEmpty(orderIdUrl)) {
            LOGGER.warn("invalid order id : {}", orderIdUrl);
            return;
        }
        Long orderId = IdConvertor.urlToId(orderIdUrl);
        DetailItemDO detailItemDO = this.getTradeSnapItemInfo(context, orderId);
        if (null == detailItemDO) {
            return;
        } else {
            context.setItemDO(detailItemDO);
            context.setItemId(detailItemDO.getItemId());
            context.setOrderSnap(Boolean.TRUE);
        }
        this.appendShopInfo(detailItemDO);
        this.decorateSkuAndPrice(detailItemDO, orderId);
    }

    /**
     * 根据订单信息获取快照中的商品信息
     *
     * @param orderId 订单id的url形式
     * @return
     */
    private DetailItemDO getTradeSnapItemInfo(DetailContext context, Long orderId) {
        SnapshotRequestDto requestDto = new SnapshotRequestDto();
        requestDto.setItemOrderId(orderId);
        Response<SnapshotDetailDto> req = queryForSnapshotService.getSnapshotDetailByItemOrderId(requestDto);
        if (null == req || !req.isSuccess() || null == req.getData()) {
            return null;
        }
        String data = Base62.decodeWithCompress(req.getData().getSnapshotDetail());
        Map<String, Object> mapData = gson.fromJson(data, HashMap.class);
        Object itemDetail = mapData.get("itemDetail");
        SingleOrderReqDTO singleOrderReqDTO = new SingleOrderReqDTO();
        singleOrderReqDTO.setOrderId(((Double) mapData.get("orderId")).longValue());
        com.mogujie.trade.response.Response<ItemOrderResDTO> parentOrderData = itemOrderQueryService.getOrderByItemOrderId(singleOrderReqDTO);
        Integer orderType = null;
        if (null != parentOrderData && parentOrderData.isSuccess()) {
            singleOrderReqDTO.setOrderId(parentOrderData.getData().getParentOrderId());
            com.mogujie.trade.response.Response<ShopOrderCoreResDTO> orderData = shopOrderQueryService.getCoreShopOrderByShopOrderId(singleOrderReqDTO);
            if (null != orderData && orderData.isSuccess()) {
                if ("GO_MARKET".equals(orderData.getData().getShopOrderType())) {
                    orderType = 9;
                }
            }

        }
        CompleteItem item;
        if (null != itemDetail) {
            item = gson.fromJson(itemDetail.toString(), CompleteItem.class);
            String skuImage = this.getSkuSnapImage(item.getSkus(), parentOrderData.getData().getStockId());
            item.setImage(com.mogujie.metabase.utils.StringUtils.isEmpty(skuImage) ? item.getImage() : skuImage);
            List<TaggedImage> taggedImages = new ArrayList<>();
            TaggedImage skuTaggedImage = new TaggedImage();
            skuTaggedImage.setImageId(0);
            skuTaggedImage.setRelateId(0);
            skuTaggedImage.setCreated((int) (System.currentTimeMillis() / 1000));
            skuTaggedImage.setUpdated((int) (System.currentTimeMillis() / 1000));
            skuTaggedImage.setPath(com.mogujie.metabase.utils.StringUtils.isEmpty(skuImage) ? item.getImage() : skuImage);
            taggedImages.add(skuTaggedImage);
            item.setTopImages(taggedImages);
        } else {
            item = new CompleteItem();
            item.setTitle(mapData.get("title").toString());
            item.setShopId(((Double) mapData.get("shopId")).intValue());
            item.setDescription(mapData.get("description").toString());
            item.setType(((Double) mapData.get("type")).intValue());
            item.setUserId(((Double) mapData.get("userId")).intValue());
            item.setImage(mapData.get("image").toString());
            item.setIsDeleted(((Double) mapData.get("isDeleted")).intValue());
            item.setPrice(((Double) mapData.get("price")).intValue());
            item.setTradeItemId(((Double) mapData.get("tradeItemId")).intValue());
            item.setXdItemId(((Double) mapData.get("xdItemId")).intValue());
            item.setProcessType(((Double) mapData.get("processType")).intValue());
            item.setIsShelf(0);
            item.setStatus(2);
            item.setGoodsType(0);
            List<Sku> skus = parseOldSkus((Map<String, Object>) mapData.get("skus"));
            String skuSnapImage = getSkuSnapImage(item.getSkus(), parentOrderData.getData().getStockId());
            item.setSkus(skus);
            item.setImage(com.mogujie.metabase.utils.StringUtils.isEmpty(skuSnapImage) ? item.getImage() : skuSnapImage);
            List<TaggedImage> taggedImages = new ArrayList<>();
            TaggedImage skuImage = new TaggedImage();
            skuImage.setImageId(0);
            skuImage.setRelateId(0);
            skuImage.setCreated((int) (System.currentTimeMillis() / 1000));
            skuImage.setUpdated((int) (System.currentTimeMillis() / 1000));
            skuImage.setPath(item.getImage());
            taggedImages.add(skuImage);
            item.setTopImages(taggedImages);
        }
        if (null != orderType) {
            item.setType(orderType);
        }
        ItemDO itemDO = ModelMapperHelper.getItemDO(item, convertMapper);
        if (null == itemDO) {
            return null;
        }
        //只有该订单的买家、对应的店铺子账号、小仙小侠、质检人员能查看订单快照
        Long buyerUserId = req.getData().getXdItemOrderDto().getBuyerUserId();
        Long loginUserId = SessionContextHolder.getUserId();
        if (!((null != buyerUserId && buyerUserId.equals(loginUserId)) || isAdmin(loginUserId) || isQualityCheck(loginUserId) || isSubUserOfShop(loginUserId, itemDO.getShopId()))) {
            LOGGER.warn("invalid userId for order id : {}, {}", buyerUserId, SessionContextHolder.getUserId());
            return null;
        }
        Map<String, Object> sellerServiceListMap = (Map) mapData.get("sellerServiceList");
        String snapExtra = req.getData().getXdItemOrderDto().getExtra();
        Map<String, Object> extraData = gson.fromJson(snapExtra, HashMap.class);
        Long created = req.getData().getXdItemOrderDto().getCreated();
        context.addContext("service", sellerServiceListMap.get("data"));
        context.addContext("orderId", orderId);
        context.addContext("isHide610", this.isHide610(extraData, created));
        return new DetailItemDO(itemDO);
    }

    /**
     * 追加店铺信息
     *
     * @param detailItemDO 商品信息
     */
    private void appendShopInfo(DetailItemDO detailItemDO) {
        long shopId = detailItemDO.getShopId();
        if (shopId <= 0) {
            return;
        }
        try {
            Result<ShopInfo> ret = ShopReadServiceClient.getShopByShopId(shopId);
            if (null != ret && ret.isSuccess()) {
                detailItemDO.setShopInfo(ret.getData());
            }
        } catch (Exception e) {
            LOGGER.error("get shopInfo Exception!shopId:" + shopId);
        }
    }

    /**
     * 装修sku及价格
     *
     * @param detailItemDO 原始商品对象
     */
    public void decorateSkuAndPrice(DetailItemDO detailItemDO, Long orderId) {
        try {
            if (null == orderId) {
                LOGGER.error("orderId not set : {}", orderId);
                return;
            }
            SingleOrderReqDTO reqDTO = new SingleOrderReqDTO();
            reqDTO.setOrderId(orderId);
            com.mogujie.trade.response.Response<ItemOrderAndItemExResDTO> ret = itemOrderQueryService.getOrderAndItemExByItemOrderId(reqDTO);
            if (null != ret && ret.isSuccess() && null != ret.getData()) {
                List<ItemSkuDO> skus = new ArrayList<>(1);
                List<ItemSkuDO> originSkuList = detailItemDO.getItemSkuDOList();
                for (ItemSkuDO sku : originSkuList) {
                    if (ret.getData().getStockId().equals(sku.getSkuId())) {
                        sku.setNowPrice(ret.getData().getNowPrice().intValue());
                        skus.add(sku);
                        break;
                    }
                }
                detailItemDO.setItemSkuDOList(skus);
                detailItemDO.setLowNowPrice(NumUtil.formatNum(ret.getData().getNowPrice() / 100D));
                detailItemDO.setHighNowPrice(NumUtil.formatNum(ret.getData().getNowPrice() / 100D));
                detailItemDO.setLowPrice(NumUtil.formatNum(ret.getData().getPrice() / 100D));
                detailItemDO.setHighPrice(NumUtil.formatNum(ret.getData().getPrice() / 100D));
                detailItemDO.setTotalStock(1L);
                detailItemDO.setPromotionPrice(ret.getData().getPrice());
            }
        } catch (Throwable e) {
            LOGGER.error("decorate snapshot price failed : {}", e);
        }
    }

    /**
     * 获取快照sku图片
     *
     * @param skus  sku列表
     * @param skuId skuId
     * @return
     */
    private String getSkuSnapImage(List<Sku> skus, Long skuId) {
        for (Sku sku : skus) {
            if (skuId.equals(sku.getId().longValue())) {
                return sku.getImage();
            }
        }
        return null;
    }

    /**
     * 解析老的sku信息
     *
     * @param skusMap sku集合
     * @return
     */
    private List<Sku> parseOldSkus(Map<String, Object> skusMap) {
        if (null == skusMap || skusMap.isEmpty()) {
            return null;
        }
        List<Map<String, Object>> oldSkusData = (List<Map<String, Object>>) skusMap.get("skus");
        if (CollectionUtils.isEmpty(oldSkusData)) {
            return null;
        }
        List<Sku> skus = new ArrayList<>(oldSkusData.size());
        for (Map<String, Object> data : oldSkusData) {
            Sku sku = new Sku();
            sku.setStock(1);
            sku.setPrice(((Double) data.get("price")).intValue());
            sku.setImage(data.get("image").toString());
            sku.setId(((Double) data.get("id")).intValue());
            sku.setXdSkuId(((Double) data.get("xdSkuId")).intValue());
            sku.setIsDefault(((Double) data.get("isDefault")).intValue());
            Object propertiesObj = data.get("properties");
            if (null != propertiesObj) {
                List<Map<String, Object>> propertiesData = (List<Map<String, Object>>) propertiesObj;
                if (!CollectionUtils.isEmpty(propertiesData)) {
                    List<StockAttribution> attributions = new ArrayList<>(propertiesData.size());
                    for (Map<String, Object> properties : propertiesData) {
                        StockAttribution attribution = new StockAttribution();
                        attribution.setId(Long.parseLong(properties.get("id").toString()));
                        attribution.setName(properties.get("name").toString());
                        attribution.setValue(properties.get("value").toString());
                        attribution.setIsDeleted(0);
                        attribution.setStockId(((Double) properties.get("stockId")).intValue());
                        attributions.add(attribution);
                    }
                    sku.setAttributions(attributions);
                }
            }
            skus.add(sku);
        }
        return skus;
    }


    /**
     * 质检白名单用户
     *
     * @param loginUserId 登录用户id
     * @return
     */
    private boolean isQualityCheck(Long loginUserId) {
        if (null == loginUserId) {
            return false;
        }
        try {
            List<Long> qcList = QcConfigQueryUtil.getCanSeeOrderSnapshootWhiteList();
            return CollectionUtils.isNotEmpty(qcList) && qcList.contains(loginUserId);
        } catch (Throwable e) {
            LOGGER.error("check is qc failed : {}", e);
        }
        return false;
    }

    /**
     * 是否是店铺子账号
     *
     * @param loginUserId 登录用户id
     * @param shopId
     * @return
     */
    private boolean isSubUserOfShop(Long loginUserId, Long shopId) {
        if (null == loginUserId || null == shopId) {
            return false;
        }
        try {
            DataResult<SellerSubUser> dataResult = sellerSubUserService.getSubUserByUserId(loginUserId);
            if (dataResult != null && dataResult.isSuccess() && dataResult.getData() != null) {
                SellerSubUser sellerSubUser = dataResult.getData();
                if (sellerSubUser.getShopId().equals(shopId)) {
                    return true;
                }
            }
        } catch (Throwable e) {
            LOGGER.error("check is customer service failed : {}", e);
        }
        return false;
    }

    /**
     * 是否是管理员
     *
     * @param loginUserId 登录用户id
     * @return
     */
    private Boolean isAdmin(Long loginUserId) {
        if (null == loginUserId) {
            return false;
        }
        try {
            com.mogujie.service.muser.Result<Boolean> ret = userService.isAdmin(loginUserId);
            return ret.getValue();
        } catch (Throwable e) {
            LOGGER.error("check is admin failed : {}", e);
        }
        return false;
    }

    private Boolean isHide610(Map<String, Object> extraData, Long created) {
        if (null == created || !(1497888000 < created && created < 1499961599)) {
            return false;
        }
        Object sourceMarket = extraData.get("sourceMarket");
        Object marketType = extraData.get("marketType");
        return "weixin".equals(sourceMarket) && "market_mogujie".equals(marketType);
    }


}
