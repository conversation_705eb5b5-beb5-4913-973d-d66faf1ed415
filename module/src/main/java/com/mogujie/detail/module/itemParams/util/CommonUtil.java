package com.mogujie.detail.module.itemParams.util;


import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by <PERSON><PERSON><PERSON> on 15/12/7.
 */
public class CommonUtil {

    private static final String DATE_FORMAT = "yyyyMMddHHmmss";

    private static ThreadLocal<DateFormat> threadLocal = ThreadLocal.withInitial(() -> new SimpleDateFormat(DATE_FORMAT));

    /**
     * 将集合每4个进行分割
     * @param values
     * @return
     */
    public static List<List<String>> splitValueIntoCells(Set<String> values) {
        if (CollectionUtils.isEmpty(values)) {
            return Collections.emptyList();
        }

        // 分割后的数组大小，如原来8个，分割后为[2][4]
        int size = (values.size() - 1) / 4 + 1;
        List<List<String>> result = new ArrayList<>();

        int i = 0;
        List<String> sub = new ArrayList<>(4);
        for (String cell: values) {
            sub.add(cell);
            if (++i == 4) {
                result.add(sub);
                i = 0;
                sub = new ArrayList<>(4);
                --size;
            }
        }

        if (size != 0) {
            result.add(sub);
        }

        return result;
    }

    public static Date parse(String dateStr) throws ParseException {

        return threadLocal.get().parse(dateStr);

    }

    public static String format(Date date) {
        return threadLocal.get().format(date);
    }

    public static String formatDate(Date date, String pattern) {
        if (null == date) {
            return StringUtils.EMPTY;
        }
        DateTime dateTime = new DateTime(date);
        return dateTime.toString(pattern);
    }

    /**
     *  销量大于一万需要进行格式化的特殊处理
     */
    public static  String formatSale(String sale){
        int isale = Integer.parseInt(sale);
        if(isale == 0){
            return null;
        }
        if(isale >= 10000){
            return "销量 " + isale/10000 + "." + (isale%10000)/1000 + "万";
        }

        return "销量 " + sale;
    }

}
