package com.mogujie.detail.module.shop.spi;


import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.spi.Exposed;
import com.mogujie.detail.module.shop.domain.ShopDO;

import java.util.List;

/**
 * @AUTUOR: lengshan
 * @DESCRIPTION: 店铺标签相关
 * @DATE: 2020/1/14 下午2:18
 */
@Exposed
public interface IShopLabelProvider {

    /**
     * @DESCRIPTION: 商详店铺标签
     * @param
     * @return
     */
    void listLabel(DetailContext context, ShopDO shopDO);
}
