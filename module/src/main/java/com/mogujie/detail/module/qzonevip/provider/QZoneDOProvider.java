package com.mogujie.detail.module.qzonevip.provider;

import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.module.qzonevip.MgjQZoneVipSkuParser;
import com.mogujie.detail.module.qzonevip.domain.QZoneDO;
import com.mogujie.detail.module.sku.domain.SkuDO;
import com.mogujie.detail.module.sku.spi.ISkuParser;
import com.mogujie.metabase.spring.client.MetabaseClient;
import com.mogujie.service.hummer.constains.RequestConstants;
import com.mogujie.service.hummer.utils.QZoneVipPriceChecker;
import com.mogujie.service.item.domain.basic.ItemDO;

import javax.annotation.Resource;

/**
 * Created by anshi on 17/7/25.
 */
@Module(name = "qzone")
public class QZoneDOProvider implements IModuleDOProvider<QZoneDO> {

    private ISkuParser skuParser;

    @Resource(name = "confMetabaseClient")
    private MetabaseClient metabaseClient;

    @Override
    public QZoneDO emit(DetailContext context) {
        DetailItemDO itemDO = context.getItemDO();
        QZoneVipPriceChecker.QZoneVipPriceRes priceRes = QZoneVipPriceChecker.calcQZoneVipPrice(itemDO.getReservePrice(), itemDO.getItemTags(), RequestConstants.Market.MOGUJIE);
        if (priceRes == null || context.getItemDO().getLowNowPriceVal() <= priceRes.getRealPrice()) {
            return null;
        }

        QZoneDO qZoneDO = new QZoneDO();
        qZoneDO.setIsVipItem(true);
        qZoneDO.setActivityId(IdConvertor.idToUrl(Integer.parseInt(priceRes.getOutId())));
        qZoneDO.setVipPrice(priceRes.getRealPrice());
        qZoneDO.setSkuInfo(getQZoneSkuDO(context));
        qZoneDO.setStartTime(priceRes.getStartTime().intValue());
        qZoneDO.setEndTime(priceRes.getEndTime().intValue());
        return qZoneDO;
    }

    private SkuDO getQZoneSkuDO(DetailContext context) {
        ItemDO item = context.getItemDO();
        SkuDO skuDO = new SkuDO();
        skuDO.setTitle(item.getTitle());
        skuParser.parseSku(context, skuDO);
        return skuDO;
    }


    @Override
    public void init() throws DetailException {
        try {
            skuParser = new MgjQZoneVipSkuParser(metabaseClient);
        } catch (Exception e) {
            throw new DetailException(e);
        }
    }
}
