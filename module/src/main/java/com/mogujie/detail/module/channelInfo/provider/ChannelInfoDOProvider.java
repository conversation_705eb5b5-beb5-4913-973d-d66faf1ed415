package com.mogujie.detail.module.channelInfo.provider;

import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.*;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO;
import com.mogujie.detail.module.channelInfo.util.NormalPriceGetter;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by anshi on 18/1/11.
 */
@Module(name = "channelInfo")
public class ChannelInfoDOProvider implements IModuleDOProvider<ChannelInfoDO> {

    private static final Logger logger = LoggerFactory.getLogger(ChannelInfoDOProvider.class);

//    private PinTuanApi pinTuanApi;

    @Autowired
    private NormalPriceGetter normalPriceGetter;

    @Override
    public ChannelInfoDO emit(DetailContext context) {
        ChannelTag channelTag = context.getChannelTag();
        ChannelMeta channelMeta = context.getChannelMeta();
        if (channelMeta == null || channelTag == null) {
            return null;
        }
        try {
            DetailItemDO itemDO = context.getItemDO();
            ChannelInfoDO channelInfoDO = new ChannelInfoDO();
            channelInfoDO.setChannelMetaInfo(channelMeta);
            if (channelTag.getPrice() != null) {
                //设置一口价
                channelInfoDO.setChannelPrice(channelTag.getPrice());
            } else if (channelTag.getDiscount() != null) {
                //设置区间/折扣价
                Long lowPrice = null;
                Long highPrice = null;
                for (ItemSkuDO sku : itemDO.getItemSkuDOList()) {
                    Long price = channelTag.getDiscount() * sku.getPrice() / 1000;
                    if (lowPrice == null) {
                        lowPrice = price;
                    }
                    if (highPrice == null) {
                        highPrice = price;
                    }
                    lowPrice = price < lowPrice ? price : lowPrice;
                    highPrice = price > highPrice ? price : highPrice;
                }
                channelInfoDO.setLowChannelPrice(lowPrice);
                channelInfoDO.setHighChannelPrice(highPrice);
            }
            //商品现售价格是否是本渠道正式期的价格
            channelInfoDO.setCurrentPriceIsChannelPrice(channelMeta.getPromotionCode().equals(itemDO.getPromotionCode()));
            channelInfoDO.setWarmUpTime(channelTag.getWarmUpTime());
            channelInfoDO.setStartTime(channelTag.getStartTime());
            channelInfoDO.setEndTime(channelTag.getEndTime());
            //商品在该渠道报名时填写的总库存
            if (itemDO.getActOriginalTotalStock() != null) {
                channelInfoDO.setOriginTotalStock(itemDO.getActOriginalTotalStock());
            } else {
                channelInfoDO.setOriginTotalStock(itemDO.getTotalStock());
            }
            channelInfoDO.setExtraTagMap(channelTag.getExtraMap());

            //获取日常普通售卖价
            if (channelMeta.isNeedNormalPrice()) {
                Map<Long, Long> priceMap = normalPriceGetter.getNormalPriceMap(context);
                if (priceMap != null && CollectionUtils.isNotEmpty(priceMap.values())) {
                    Long lowPrice = null, highPrice = null;
                    for (Long price : priceMap.values()) {
                        if (lowPrice == null || price < lowPrice) {
                            lowPrice = price;
                        }
                        if (highPrice == null || price > highPrice) {
                            highPrice = price;
                        }
                    }
                    channelInfoDO.setLowNormalPrice(lowPrice);
                    channelInfoDO.setHighNormalPrice(highPrice);
                }
            }
            return channelInfoDO;
        } catch (Throwable e) {
            logger.error("set channel info do error!", e);
        }

        ///////////////

        /////////////////////

        return null;
    }

    @Override
    public void init() throws DetailException {
        try {
//            pinTuanApi = TeslaServiceConsumerFactory.getTeslaServiceConsumer(PinTuanApi.class);
        } catch (Exception e) {
            throw new DetailException(e);
        }
    }
}
