package com.mogujie.detail.module.shop.spi;


import com.meili.service.shopcenter.domain.entity.ShopInfo;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.spi.Exposed;

/**
 * Created by <PERSON>iaoyao on 16/8/4.
 */
@Exposed
public interface IShopTagProvider {

    /**
     * 获取店铺标签
     * @param context
     * @param shopInfo
     * @return
     */
    public String getShopTag(DetailContext context, ShopInfo shopInfo);
}
