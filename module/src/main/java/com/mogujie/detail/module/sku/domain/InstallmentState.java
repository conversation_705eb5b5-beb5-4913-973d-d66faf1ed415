package com.mogujie.detail.module.sku.domain;

/**
 * App分期、先试后买的用户综合状态
 * Created by anshi on 2018/11/26.
 */
public enum InstallmentState {

    /**
     * 正常使用
     */
    NORMAL(1),

    /**
     * 未开通
     */
    UN_OPEN(2),

    /**
     * 先试后买未偿清,无法使用
     */
    UN_PAY_OFF(3),

    /**
     * 不可用
     */
    UN_USABLE(4);

    private int code;

    public int getCode(){
        return code;
    }

    InstallmentState(int code) {
        this.code = code;
    }

    public static InstallmentState getState(int code) {
        switch (code) {
            case 1:
                return NORMAL;
            case 2:
                return UN_OPEN;
            case 3:
                return UN_PAY_OFF;
            case 4:
                return UN_USABLE;
            default:
                return NORMAL;
        }
    }
}
