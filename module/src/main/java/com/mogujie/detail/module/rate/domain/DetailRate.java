package com.mogujie.detail.module.rate.domain;

import com.mogujie.service.rate.domain.Video;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 15/11/24.
 */
public class DetailRate implements Serializable {
    private static final long serialVersionUID = -7550392595834881856L;

    @Getter
    @Setter
    private RateUserInfo user;

    @Getter
    @Setter
    private List<String> images;

    @Getter
    @Setter
    private List<Video> videos;

    @Getter
    @Setter
    private String rateId;

    @Getter
    @Setter
    private String content;

    @Getter
    @Setter
    private Long created;

    @Getter
    @Setter
    private Integer isAnonymous;

    @Getter
    @Setter
    private String style;

    @Getter
    @Setter
    private Boolean isProbation;

    @Getter
    @Setter
    private String probation;

    @Getter
    @Setter
    private Integer isEmpty = 1;

    @Getter
    @Setter
    private DetailRate append;

    @Getter
    @Setter
    private String explain;

    @Getter
    @Setter
    private String level;

    @Getter
    @Setter
    private Boolean canExplain = false;

    @Getter
    @Setter
    private List<String> extraInfo;

    /**
     * 是否为买家秀
     */
    @Getter
    @Setter
    private boolean isBuyerShow;

    /**
     * 买家秀尺码
     */
    @Getter
    @Setter
    private Map<String, String> sizeInfo;

    /**
     * 买家秀content id
     */
    @Getter
    @Setter
    private Long contentId;

    /**
     * 标签词Names
     */
    @Getter
    @Setter
    private String rateTagNames;

    @Getter
    @Setter
    private List userTags;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        DetailRate that = (DetailRate) o;

        if (isBuyerShow != that.isBuyerShow) return false;
        if (user != null ? !user.equals(that.user) : that.user != null) return false;
        if (images != null ? !images.equals(that.images) : that.images != null) return false;
        if (videos != null ? !videos.equals(that.videos) : that.videos != null) return false;
        if (rateId != null ? !rateId.equals(that.rateId) : that.rateId != null) return false;
        if (content != null ? !content.equals(that.content) : that.content != null) return false;
        if (created != null ? !created.equals(that.created) : that.created != null) return false;
        if (isAnonymous != null ? !isAnonymous.equals(that.isAnonymous) : that.isAnonymous != null) return false;
        if (style != null ? !style.equals(that.style) : that.style != null) return false;
        if (isProbation != null ? !isProbation.equals(that.isProbation) : that.isProbation != null) return false;
        if (probation != null ? !probation.equals(that.probation) : that.probation != null) return false;
        if (isEmpty != null ? !isEmpty.equals(that.isEmpty) : that.isEmpty != null) return false;
        if (append != null ? !append.equals(that.append) : that.append != null) return false;
        if (explain != null ? !explain.equals(that.explain) : that.explain != null) return false;
        if (level != null ? !level.equals(that.level) : that.level != null) return false;
        if (canExplain != null ? !canExplain.equals(that.canExplain) : that.canExplain != null) return false;
        if (extraInfo != null ? !extraInfo.equals(that.extraInfo) : that.extraInfo != null) return false;
        if (sizeInfo != null ? !sizeInfo.equals(that.sizeInfo) : that.sizeInfo != null) return false;
        if (contentId != null ? !contentId.equals(that.contentId) : that.contentId != null) return false;
        return rateTagNames != null ? rateTagNames.equals(that.rateTagNames) : that.rateTagNames == null;
    }

    @Override
    public int hashCode() {
        int result = user != null ? user.hashCode() : 0;
        result = 31 * result + (images != null ? images.hashCode() : 0);
        result = 31 * result + (videos != null ? videos.hashCode() : 0);
        result = 31 * result + (rateId != null ? rateId.hashCode() : 0);
        result = 31 * result + (content != null ? content.hashCode() : 0);
        result = 31 * result + (created != null ? created.hashCode() : 0);
        result = 31 * result + (isAnonymous != null ? isAnonymous.hashCode() : 0);
        result = 31 * result + (style != null ? style.hashCode() : 0);
        result = 31 * result + (isProbation != null ? isProbation.hashCode() : 0);
        result = 31 * result + (probation != null ? probation.hashCode() : 0);
        result = 31 * result + (isEmpty != null ? isEmpty.hashCode() : 0);
        result = 31 * result + (append != null ? append.hashCode() : 0);
        result = 31 * result + (explain != null ? explain.hashCode() : 0);
        result = 31 * result + (level != null ? level.hashCode() : 0);
        result = 31 * result + (canExplain != null ? canExplain.hashCode() : 0);
        result = 31 * result + (extraInfo != null ? extraInfo.hashCode() : 0);
        result = 31 * result + (isBuyerShow ? 1 : 0);
        result = 31 * result + (sizeInfo != null ? sizeInfo.hashCode() : 0);
        result = 31 * result + (contentId != null ? contentId.hashCode() : 0);
        result = 31 * result + (rateTagNames != null ? rateTagNames.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "DetailRate{" +
                "user=" + user +
                ", images=" + images +
                ", videos=" + videos +
                ", rateId='" + rateId + '\'' +
                ", content='" + content + '\'' +
                ", created=" + created +
                ", isAnonymous=" + isAnonymous +
                ", style='" + style + '\'' +
                ", isProbation=" + isProbation +
                ", probation='" + probation + '\'' +
                ", isEmpty=" + isEmpty +
                ", append=" + append +
                ", explain='" + explain + '\'' +
                ", level='" + level + '\'' +
                ", canExplain=" + canExplain +
                ", extraInfo=" + extraInfo +
                ", isBuyerShow=" + isBuyerShow +
                ", sizeInfo=" + sizeInfo +
                ", contentId=" + contentId +
                ", rateTagNames='" + rateTagNames + '\'' +
                '}';
    }
}
