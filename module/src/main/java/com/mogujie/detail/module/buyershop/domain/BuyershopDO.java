package com.mogujie.detail.module.buyershop.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by anshi on 2018/5/23.
 */
public class BuyershopDO implements ModuleDO {

    /**
     * 分享者信息
     */
    @Getter
    @Setter
    private ShareUserInfo shareUserInfo;

    /**
     * 当前登录用户信息（只有当前用户是店主，才会返回）
     */
    @Getter
    @Setter
    private ShareUserInfo currentUserInfo;

    /**
     * 当前登录用户是否为店主
     */
    @Getter
    @Setter
    private boolean isSeller;

    /**
     * 最小佣金数量（分）
     */
    @Getter
    @Setter
    private Integer minCommission;

    /**
     * 最大佣金数量（分）
     */
    @Getter
    @Setter
    private Integer maxCommission;

    /**
     * 当前用户是否已将该商品加入精选
     */
    @Getter
    @Setter
    private boolean hasRecommendThisItem;

    /**
     * 是否是礼包商品
     */
    @Getter
    @Setter
    private boolean isGiftItem;

    /**
     * 当前用户的分享码
     */
    @Getter
    @Setter
    private String inviteCode;

    /**
     * 会员价low
     */
    @Getter
    @Setter
    private Long lowVipUserPrice;

    /**
     * 会员价high
     */
    @Getter
    @Setter
    private Long highVipUserPrice;

    /**
     * 普通用户价low
     */
    @Getter
    @Setter
    private Long lowNormalUserPrice;

    /**
     * 普通用户价high
     */
    @Getter
    @Setter
    private Long highNormalUserPrice;

    /**
     * 当前用户是VIP用户
     */
    @Getter
    @Setter
    private boolean isVipUser;

    /**
     * 当前用户是否已经设置过提醒（快抢预热期）
     */
    @Getter
    @Setter
    private boolean hasSetRemind;
}
