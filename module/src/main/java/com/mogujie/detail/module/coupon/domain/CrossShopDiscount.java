package com.mogujie.detail.module.coupon.domain;

import lombok.Getter;
import lombok.Setter;

/**
 * 跨店满减信息
 * Created by anshi on 2019/1/30.
 */
public class CrossShopDiscount {

    /**
     * 优惠名称
     */
    @Getter
    @Setter
    private String name;

    /**
     * 满xx元
     */
    @Getter
    @Setter
    private Integer limitPrice;

    /**
     * 减xx元
     */
    @Getter
    @Setter
    private Integer cutPrice;

    /**
     * 上限xx元
     */
    @Getter
    @Setter
    private Integer upperLimit;

    /**
     * 描述
     */
    @Getter
    @Setter
    private String decorate;

    /**
     * 购物津贴有效期开始时间
     */
    @Getter
    @Setter
    private Integer startTime;

    /**
     * 购物津贴有效期结束时间
     */
    @Getter
    @Setter
    private Integer endTime;

    /**
     * 优惠券终端: App专享、女装小程序专享
     */
    @Getter
    @Setter
    private String terminal;

    @Getter
    @Setter
    private Long promotionId;

    /**
     * 优惠券code
     */
    @Getter
    @Setter
    private String promotionCode;


}
