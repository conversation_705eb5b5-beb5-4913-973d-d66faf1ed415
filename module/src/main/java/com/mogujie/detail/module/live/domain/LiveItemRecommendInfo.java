package com.mogujie.detail.module.live.domain;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;

/**
 * Created by xuanyue
 * Date: 2021/2/20
 * Introduction:买手店商品推荐信息
 */
@Data
public class LiveItemRecommendInfo {

    /**
     * 商品类型
     */
    private Long itemType;

    /**
     * 跳转链接
     */
    private String link;

    /**
     * h5跳转链接
     */
    private String h5Link;

    /**
     * 小程序跳转链接
     */
    private String xcxLink;

    /**
     * 现价价格
     */
    private String discountPrice;

    /**
     * 商品标题
     */
    private String title;

    /**
     * 商品图片
     */
    private String itemImage;

    /**
     * 销量
     */
    private String sale;

    /**
     * 视频第一帧
     */
    private String firstFrame;

    /**
     * 讲解链接
     */
    private String videoUrl;

    /**
     * H625讲解链接
     */
    private String videoH265Url;

    private String acm;

    public static  LiveItemRecommendInfo convert(JSONObject recommendData){
        if(MapUtils.isEmpty(recommendData)){
            return null;
        }
        LiveItemRecommendInfo liveItemRecommendInfo = new LiveItemRecommendInfo();
        liveItemRecommendInfo.setItemImage(recommendData.getString("itemImage"));
        liveItemRecommendInfo.setItemType(recommendData.getLong("itemType"));
        liveItemRecommendInfo.setLink(recommendData.getString("link"));
        liveItemRecommendInfo.setDiscountPrice(recommendData.getString("discountPrice"));
        liveItemRecommendInfo.setTitle(recommendData.getString("title"));
        liveItemRecommendInfo.setSale(recommendData.getString("sale"));
        liveItemRecommendInfo.setFirstFrame(recommendData.getString("firstFrame"));
        liveItemRecommendInfo.setVideoUrl(recommendData.getString("videoUrl"));
        liveItemRecommendInfo.setH5Link(recommendData.getString("h5Link"));
        liveItemRecommendInfo.setXcxLink(recommendData.getString("xcxLink"));
        liveItemRecommendInfo.setAcm(recommendData.getString("acm"));
        liveItemRecommendInfo.setVideoH265Url(recommendData.getString("videoH265Url"));
        return  liveItemRecommendInfo;
    }
}
