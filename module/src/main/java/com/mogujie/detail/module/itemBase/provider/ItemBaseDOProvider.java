package com.mogujie.detail.module.itemBase.provider;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.meili.service.shopcenter.domain.entity.ShopInfo;
import com.meili.service.shopcenter.util.ShopInfoTagsUtil;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.DetailConstants;
import com.mogujie.detail.core.constant.ItemTag;
import com.mogujie.detail.core.constant.Platform;
import com.mogujie.detail.core.constant.SwitchKey;
import com.mogujie.detail.core.constant.VirtualItemType;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.spi.SpiAutowired;
import com.mogujie.detail.core.util.BizUtil;
import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.core.util.ContextUtil;
import com.mogujie.detail.core.util.ImageUtil;
import com.mogujie.detail.module.detail.util.DetailParseUtil;
import com.mogujie.detail.module.itemBase.domain.*;
import com.mogujie.detail.module.itemBase.spi.ICFavProvider;
import com.mogujie.detail.module.itemBase.spi.IFavInfoProvider;
import com.mogujie.detail.module.itemBase.spi.IItemStateProvider;
import com.mogujie.metabase.spring.client.MetabaseClient;
import com.mogujie.pay.common.dto.Response;
import com.mogujie.pay.mailo.api.MaiLoUserCreditApi;
import com.mogujie.pay.mailo.api.v2.MaiLoUserInfoApi;
import com.mogujie.pay.mailo.api.v2.dto.ForetasteAuthRequestDTO;
import com.mogujie.pay.mailo.api.v2.dto.ForetasteAuthResponseDTO;
import com.mogujie.pay.mailo.dto.UserWhiteResDTO;
import com.mogujie.pay.mailo.dto.request.parameters.BaseUserQueryDTO;
import com.mogujie.service.inventory.api.InventoryReadService;
import com.mogujie.service.inventory.domain.ItemActivityInventoryV2;
import com.mogujie.service.inventory.domain.param.ItemActivityInventoryQueryParamV2;
import com.mogujie.service.inventory.domain.result.PlainResult;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.basic.ItemExtraDO;
import com.mogujie.service.item.domain.basic.ItemImageDO;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.service.item.domain.basic.ItemTagDO;
import com.mogujie.service.muser.Result;
import com.mogujie.service.muser.api.v1.UserService;
import com.mogujie.service.muser.domain.entity.v1.UsersInfo;
import com.mogujie.service.shopcenter.util.AuthorizedShopUtil;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import sun.misc.BASE64Decoder;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by xiaoyao on 16/8/16.
 */
@Module(name = "itemBase")
public class ItemBaseDOProvider implements IModuleDOProvider<ItemBaseDO> {

    private static final Logger LOGGER = LoggerFactory.getLogger(ItemBaseDOProvider.class);

    @Autowired
    protected MaiLoUserCreditApi maiLoUserCreditApi;

    @Autowired
    protected MaiLoUserInfoApi maiLoUserInfoApi;

    private static final int PRE_SALE_ITEM_TYPE = 1; // 预售商品saleType类型
    private static final int COMMON_ITEM_TYPE = 0; // 普通商品saleType类型

    @SpiAutowired
    private IItemStateProvider itemStateProvider;

    @SpiAutowired
    private IFavInfoProvider favInfoProvider;

    @SpiAutowired
    private ICFavProvider icFavProvider;


    @Autowired
    private CommonSwitchUtil commonSwitchUtil;

    @Resource(name = "confMetabaseClient")
    private MetabaseClient metabaseClient;

    private static final String V_USER_UNIQUE = "mzeq4moeas";

    private static final String ADD_CART_SWITCH_TAG = "1257";

    //限量优惠商品标
    private static final String LIMIT_DISCOUNT_ITEM_TAG_KEY = "xlyh";

    private UserService userService;

    private InventoryReadService inventoryReadService;

    private Gson gson;

    @Override
    public void init() throws DetailException {
        try {
            gson = new Gson();
            userService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(UserService.class);
            inventoryReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(InventoryReadService.class);
        } catch (Exception e) {
            LOGGER.error("init module failed, {}", e);
            throw new DetailException(e);
        }
    }

    @Override
    public ItemBaseDO emit(DetailContext context) {
        if (context.isDyn()) {
            return getDynData(context);
        } else {
            return getAllData(context);
        }
    }


    protected ItemBaseDO getAllData(DetailContext context) {
        DetailItemDO item = context.getItemDO();
        ItemBaseDO itemBaseDO = new ItemBaseDO(context.getItemDO());
        decorateLoginUserInfo(itemBaseDO, context);
        itemBaseDO.setVirtualItemType(getVirtualItemType(context));
        itemBaseDO.setState(itemStateProvider.getItemState(context));
        itemBaseDO.setDiscountDesc(item.getDiscountDesc());
        Long loginUserId = context.getLoginUserId();
        boolean isSelf = loginUserId != null && item.getUserId() == loginUserId;
        itemBaseDO.setSelf(isSelf);
        List<ItemTag> itemTags = itemBaseDO.getItemTags();
        itemBaseDO.setSaleType(this.getSaleType(item));
        itemBaseDO.setTopImages(parseTopImages(item));

        // 药品类目-将某些图放在主图列表中
        DetailParseUtil.addDrugImages(item, itemBaseDO.getTopImages());

        //商品收藏数量, 如果总数为0，那么当前用户是一定没收藏过的。两接口的开关打开和关闭要保持一致。否则数据展示有问题
        int cFav=icFavProvider.getCFav(context).intValue();
        itemBaseDO.setCFav(cFav);
        itemBaseDO.setIsFaved(cFav > 0 ? favInfoProvider.isFaved(context) : false);

        boolean isGoodItem = !CollectionUtils.isEmpty(itemTags) && itemTags.contains(ItemTag.MOGU_SELECTION);
        StructuredExtraInfo extraInfo = getStructuredExtraInfo(item, isGoodItem);
        itemBaseDO.setVideo(extraInfo.getVideoInfo());
        itemBaseDO.setTrialReportInfos(extraInfo.getTrialReportInfos());
        itemBaseDO.setThreeDModel(extraInfo.getThreeDModel());
        itemBaseDO.setOverseaItemInfo(extraInfo.getOverseaItemInfo());
        itemBaseDO.setActivityBanner(extraInfo.getActivityBanner());
        itemBaseDO.setPurchaseLimit(extraInfo.getPurchaseLimit());

        if (canInstallment(context)) {
            if (null == itemTags) {
                itemTags = new ArrayList<>(1);
                itemBaseDO.setItemTags(itemTags);
            }
            itemTags.add(ItemTag.INSTALMENT);
        }

        boolean canForetaste = canForetaste(context);
        if (canForetaste) {
            if (null == itemTags) {
                itemTags = new ArrayList<>(1);
                itemBaseDO.setItemTags(itemTags);
            }
            itemTags.add(ItemTag.FORETASTE);
        }

        if (ContextUtil.isLiveInWallItem(context)) {
            fillLiveInWallItemTitle(itemBaseDO, context);
        }

        itemBaseDO.setForetasteAuth(getUserForetasteAuth(context, canForetaste));

        itemBaseDO.setRedPacketSwitch(getRedPacketSwitch(context));
        itemBaseDO.setAddCartTips(getRedPacketSwitch(context));
        itemBaseDO.setNumTags(getNumTags(item.getItemTags()));
        itemBaseDO.setCanApplyInstallment(getInstallmentAppliable(context));
        itemBaseDO.setCanShowStrikethroughPrice(showStrikethroughPrice(context));
        itemBaseDO.setSlogan(getSlogan(item.getItemTags()));
        itemBaseDO.setLimitDiscountInfo(getLimitDiscountInfo(context));
        itemBaseDO.setActivityPrice(BizUtil.getActivityPriceFromTag(context));
        //商品发布改造需要给详情返回尺码图片
        setSizeImageInfo(itemBaseDO,item);
        return itemBaseDO;
    }

    protected ItemBaseDO getDynData(DetailContext context) {
        DetailItemDO item = context.getItemDO();
        ItemBaseDO itemInfo = new ItemBaseDO(item);
        itemInfo.setDiscountDesc(item.getDiscountDesc());
        Long loginUserId = context.getLoginUserId();
        decorateLoginUserInfo(itemInfo, context);
        itemInfo.setVirtualItemType(getVirtualItemType(context));
        itemInfo.setState(itemStateProvider.getItemState(context));
        boolean isSelf = loginUserId != null && item.getUserId() == loginUserId;
        itemInfo.setSelf(isSelf);
        itemInfo.setSaleType(this.getSaleType(item));
        itemInfo.setRedPacketSwitch(getRedPacketSwitch(context));
        itemInfo.setAddCartTips(getRedPacketSwitch(context));
        List<ItemTag> itemTags = itemInfo.getItemTags();
        boolean isGoodItem = !CollectionUtils.isEmpty(itemTags) && itemTags.contains(ItemTag.MOGU_SELECTION);
        StructuredExtraInfo extraInfo = getStructuredExtraInfo(item, isGoodItem);
        itemInfo.setVideo(extraInfo.getVideoInfo());
        itemInfo.setTrialReportInfos(extraInfo.getTrialReportInfos());
        itemInfo.setNumTags(getNumTags(item.getItemTags()));
        itemInfo.setThreeDModel(extraInfo.getThreeDModel());
        itemInfo.setOverseaItemInfo(extraInfo.getOverseaItemInfo());
        itemInfo.setActivityBanner(extraInfo.getActivityBanner());
        itemInfo.setPurchaseLimit(extraInfo.getPurchaseLimit());
        itemInfo.setTopImages(parseTopImages(item));

        // 药品类目-将某些图放在主图列表中
        DetailParseUtil.addDrugImages(item, itemInfo.getTopImages());

        //商品收藏数量, 如果总数为0，那么当前用户是一定没收藏过的。两接口的开关打开和关闭要保持一致。否则数据展示有问题
        int cFav=icFavProvider.getCFav(context).intValue();
        itemInfo.setCFav(cFav);
        itemInfo.setIsFaved(cFav > 0 ? favInfoProvider.isFaved(context) : false);

        if (canInstallment(context)) {
            if (null == itemTags) {
                itemTags = new ArrayList<>(1);
                itemInfo.setItemTags(itemTags);
            }
            itemTags.add(ItemTag.INSTALMENT);
        }

        boolean canForetaste = canForetaste(context);
        if (canForetaste) {
            if (null == itemTags) {
                itemTags = new ArrayList<>(1);
                itemInfo.setItemTags(itemTags);
            }
            itemTags.add(ItemTag.FORETASTE);
        }

        if (ContextUtil.isLiveInWallItem(context)) {
            fillLiveInWallItemTitle(itemInfo, context);
        }
        itemInfo.setForetasteAuth(getUserForetasteAuth(context, canForetaste));

        itemInfo.setCanApplyInstallment(getInstallmentAppliable(context));
        itemInfo.setCanShowStrikethroughPrice(showStrikethroughPrice(context));
        itemInfo.setSlogan(getSlogan(item.getItemTags()));
        itemInfo.setLimitDiscountInfo(getLimitDiscountInfo(context));
        itemInfo.setActivityPrice(BizUtil.getActivityPriceFromTag(context));

        //商品发布改造需要给详情返回尺码图片
        setSizeImageInfo(itemInfo,item);
        return itemInfo;
    }

    protected List<TrialReportInfo> getTrialReport(Map<String, String> features) {
        if (features == null) {
            return null;
        }
        String trialReportStr = features.get("trialReport");
        if (StringUtils.isBlank(trialReportStr)) {
            return null;
        }
        Gson gson = new Gson();
        try {
            List<Map<String, String>> reportMaps = gson.fromJson(trialReportStr, new TypeToken<List<Map<String, String>>>() {
            }.getType());
            if (reportMaps == null) {
                return null;
            }
            List<TrialReportInfo> trialReportInfos = new ArrayList<>();
            for (Map<String, String> map : reportMaps) {
                TrialReportInfo trialReportInfo = new TrialReportInfo();
                trialReportInfo.setTrialSize(map.get("trialSize"));
                trialReportInfo.setModelName(map.get("modelName"));
                trialReportInfo.setHeight(Double.parseDouble(map.get("height")));
                trialReportInfo.setWeight(Double.parseDouble(map.get("weight")));
                trialReportInfo.setChest(Double.parseDouble(map.get("chest")));
                trialReportInfo.setEffect(map.get("effect"));
                trialReportInfos.add(trialReportInfo);
            }
            return trialReportInfos;
        } catch (Throwable e) {
            LOGGER.error("get trial reprot info error.", e);
        }
        return null;
    }

    protected StructuredExtraInfo getStructuredExtraInfo(DetailItemDO itemDO, boolean isGoodItem) {
        StructuredExtraInfo extraInfo = new StructuredExtraInfo();
        extraInfo.setVideoInfo(getVideoInfo(itemDO.getFeatures(), isGoodItem));
        extraInfo.setTrialReportInfos(getTrialReport(itemDO.getFeatures()));
        extraInfo.setThreeDModel(getThreeDModel(itemDO.getFeatures()));
        extraInfo.setOverseaItemInfo(getOverseaInfo(itemDO.getFeatures()));
        extraInfo.setActivityBanner(getActivityBanner(itemDO.getFeatures()));
        extraInfo.setPurchaseLimit(getPurchaseLimit(itemDO.getFeatures()));
        return extraInfo;
    }

    protected ThreeDModel getThreeDModel(Map<String, String> features) {
        if (features == null || features.isEmpty()) {
            return null;
        } else {
            // 3D模型 @凌云
            String threeDModelStr = features.get("3dModel");
            if (StringUtils.isNotBlank(threeDModelStr)) {
                Gson gson = new Gson();
                try {
                    Map threeDModelInfo = gson.fromJson(threeDModelStr, Map.class);
                    if (threeDModelInfo != null && !threeDModelInfo.isEmpty() && null != threeDModelInfo.get("3d")) {
                        ThreeDModel threeDModel = new ThreeDModel();
                        threeDModel.setModel(threeDModelInfo.get("3d").toString());
                        if (StringUtils.startsWith(threeDModelInfo.get("snapshot").toString(), "http")) {
                            threeDModel.setSnapshot(threeDModelInfo.get("snapshot").toString());
                        } else {
                            threeDModel.setSnapshot(ImageUtil.img(threeDModelInfo.get("snapshot").toString()));
                        }
                        return threeDModel;
                    }
                } catch (JsonSyntaxException var7) {
                    LOGGER.error("{}", var7);
                }
            }
        }
        return null;
    }

    protected VideoInfo getVideoInfo(Map<String, String> features, boolean isGoodItem) {
        try {
            if (features == null || features.isEmpty()) {
                return null;
            } else {
                // 封面图视频 @乱道、@凌云
                String coverVideoStr = (String) features.get("coverVideo");
                if (StringUtils.isNotBlank(coverVideoStr)) {
                    Gson gson = new Gson();
                    try {
                        CoverVideoInfo coverVideoInfo = (CoverVideoInfo) gson.fromJson(coverVideoStr, CoverVideoInfo.class);
                        if (coverVideoInfo != null && coverVideoInfo.getAuditStatus() == 2) {
                            VideoInfo videoInfo = new VideoInfo();
                            videoInfo.setCover(ImageUtil.img(coverVideoInfo.getCoverImage()));
                            videoInfo.setVideoId(coverVideoInfo.getVideoId());
                            videoInfo.setWidth(coverVideoInfo.getWidth());
                            videoInfo.setHeight(coverVideoInfo.getHeight());
                            return videoInfo;
                        }
                    } catch (JsonSyntaxException var7) {
                        LOGGER.error("{}", var7);
                    }
                }

                if (!isGoodItem) {
                    return null;
                }
                // 良品视频
                String goodItemInfoStr = (String) features.get("goodItem");
                if (StringUtils.isBlank(goodItemInfoStr)) {
                    return null;
                } else {
                    Gson gson = new Gson();
                    try {
                        GoodItemInfo goodItemInfo = (GoodItemInfo) gson.fromJson(goodItemInfoStr, GoodItemInfo.class);
                        if (null != goodItemInfo && null != goodItemInfo.getLeVideo()) {
                            VideoInfo videoInfo = new VideoInfo();
                            videoInfo.setCover(ImageUtil.img(goodItemInfo.getLeVideo().getImage()));
                            videoInfo.setVId(IdConvertor.idToUrl(goodItemInfo.getLeVideo().getVideoId()));
                            videoInfo.setVUnique(goodItemInfo.getLeVideo().getVideoUnique());
                            videoInfo.setVUserUnique(V_USER_UNIQUE);
                            videoInfo.setVideoId(null != goodItemInfo.getLeVideo().getTencentVideoId() ? Long.parseLong(goodItemInfo.getLeVideo().getTencentVideoId()) : null);
                            return videoInfo;
                        }
                    } catch (JsonSyntaxException var7) {
                        LOGGER.error("{}", var7);
                    }
                }
            }
        } catch (Throwable e) {
            LOGGER.error("getVideoInfo failed : {}", e);
        }
        return null;
    }

    private List<String> parseTopImages(ItemDO item) {
        List<ItemImageDO> images = item.getImages();
        if (!CollectionUtils.isEmpty(images)) {
            List<String> topImages = new ArrayList<>();
            for (ItemImageDO image : images) {
                topImages.add(ImageUtil.img(image.getPath()));
            }
            return topImages;
        } else if (!StringUtils.isEmpty(item.getMainImage())) {
            return Arrays.asList(ImageUtil.img(item.getMainImage()));
        }
        return Collections.emptyList();
    }

    private int getSaleType(DetailItemDO item) {
        int curTime = (int) (System.currentTimeMillis() / 1000);
        if (item.getItemPreSaleDO() != null && curTime > item.getItemPreSaleDO().getStart()
                && curTime < item.getItemPreSaleDO().getEnd()) {
            return PRE_SALE_ITEM_TYPE;
        } else {
            return COMMON_ITEM_TYPE;
        }
    }

    /**
     * 加购抽奖开关
     * 对于全站大促，有开关控制全站展示
     * 对于品牌日，则根据店铺标来做判断
     *
     * @param context
     * @return
     */
    public boolean getRedPacketSwitch(DetailContext context) {
        try {
            String switchTime = metabaseClient.get("mgj_itemInfo_redPacketSwitch");
            String[] times = StringUtils.split(switchTime, ",");
            if (times.length == 2) {
                long start = Long.parseLong(times[0]);
                long end = Long.parseLong(times[1]);
                long now = System.currentTimeMillis() / 1000;
                if (now >= start && now <= end) {
                    return true;
                }
            }
            return isBrandDayAddCart(context);
        } catch (Exception e) {
        }
        return false;
    }

    /**
     * 品牌日活动，这里根据店铺标透出加购抽奖
     *
     * @param context
     * @return
     */
    private boolean isBrandDayAddCart(DetailContext context) {
        try {
            ShopInfo shopInfo = context.getItemDO().getShopInfo();
            if (shopInfo != null
                    && StringUtils.isNotBlank(shopInfo.getTags())
                    && Arrays.asList(shopInfo.getTags().split(",")).contains(ADD_CART_SWITCH_TAG)) {
                return true;
            }
        } catch (Throwable e) {
        }
        return false;
    }

    public boolean getInstallmentAppliable(DetailContext context) {
        Long userId = context.getLoginUserId();
        if (userId == null
                || !commonSwitchUtil.isOn(SwitchKey.PAYMAILO_CHECK_USER_WHITE_LIST)
                || context.getRouteInfo().getBizType() == BizType.SKU) {
            return false;
        }

        Response<UserWhiteResDTO> response = null;
        BaseUserQueryDTO baseUserQueryDTO = new BaseUserQueryDTO();
        baseUserQueryDTO.setUserId(userId);

        try {
            response = maiLoUserCreditApi.checkUserWhiteListAndIsNotOpenDtoNew(baseUserQueryDTO);
        } catch (Throwable e) {
            LOGGER.error("call installment failed.", e);
            return false;
        }
        if (response != null && response.getCode() != null && response.getCode() == 1001 && response.getData() != null) {
            return response.getData().getIsInWhiteListAndNotOpen() != null && response.getData().getIsInWhiteListAndNotOpen() == true;
        }
        return false;
    }

    public boolean showStrikethroughPrice(DetailContext context) {
        List<String> numTags = getNumTags(context.getItemDO().getItemTags());
        if (numTags != null && !numTags.isEmpty()) {
            return numTags.contains("1421");
        }
        return false;
    }

    /**
     * 是否为可分期商品
     *
     * @param context
     * @return
     */
    public boolean canInstallment(DetailContext context) {
        if (context.getRouteInfo().getApp().equals(App.MLS)
                || context.getRouteInfo().getBizType().equals(BizType.SECKILL)
                || context.getRouteInfo().getBizType().equals(BizType.TREASURE)
                || context.getRouteInfo().getPlatform() == Platform.XCX
                || context.getRouteInfo().getApp() == App.XCX
                || context.getRouteInfo().getApp() == App.BH
                || context.getRouteInfo().getApp() == App.MSD
                || (context.getRouteInfo().getBizType() == BizType.CHANNEL && "liveauction".equals(context.getRouteInfo().getChannelType()))) {
            return false;
        }

        DetailItemDO detailItemDO = context.getItemDO();

        //海外跨境商品不支持分期
        if (detailItemDO.getFeatures() != null
                && detailItemDO.getFeatures().containsKey("cbInfo")) {
            return false;
        }

        //实时售价大于10元（任一SKU大于10元即可）；
        boolean priceMoreThan10 = false;
        for (ItemSkuDO sku : detailItemDO.getItemSkuDOList()) {
            if (sku.getNowPrice() > 1000) {
                priceMoreThan10 = true;
                break;
            }
        }
        if (!priceMoreThan10) {
            return false;
        }

        String virtualCateRoot = "#" + metabaseClient.get("virtual_category_root") + "#";
        //充值中心类目，需要不能分期
        if (detailItemDO.getCids().contains(virtualCateRoot)) {
            return false;
        }

        //认证店铺
        if (!AuthorizedShopUtil.isAuthorized(detailItemDO.getShopInfo())) {
            return false;
        }

        //店铺黑名单1311标的店铺不展示
        if (ShopInfoTagsUtil.stringToSet(detailItemDO.getShopInfo().getTags()).contains(1311)) {
            return false;
        }

        //非预售商品
        if (detailItemDO.getItemPreSaleDO() != null &&
                (int) (System.currentTimeMillis() / 1000) < detailItemDO.getItemPreSaleDO().getEnd()) {
            return false;
        }

        return true;
    }

    /**
     * 商品是否可以先付后买
     *
     * @return
     */
    public boolean canForetaste(DetailContext context) {
        //非小程序页面不透出
        if (context.getRouteInfo().getApp() != App.XCX) {
            return false;
        }
        final DetailItemDO itemDO = context.getItemDO();

        //海外跨境商品不支持分期
        if (itemDO.getFeatures() != null
                && itemDO.getFeatures().containsKey("cbInfo")) {
            return false;
        }

        //最低现售价价格低于10元
        if (itemDO.getLowNowPrice() != null && Double.parseDouble(itemDO.getLowNowPrice()) < 10) {
            return false;
        }
        //指定类目不支持
        String cids = metabaseClient.get("foretaste_category_blackList");
        String[] cidArr = cids.split(",");
        if (Arrays.stream(cidArr).anyMatch(cid -> itemDO.getCids().contains("#" + cid + "#"))) {
            return false;
        }
        //黑名单店铺不支持
        String shopTagBlackList = metabaseClient.get("foretaste_shopTag_blackList");
        String[] shopTagBlackListArr = shopTagBlackList.split(",");
        if (itemDO.getShopInfo() != null && StringUtils.isNotBlank(itemDO.getShopInfo().getTags())) {
            String[] shopTagsArr = itemDO.getShopInfo().getTags().split(",");
            if (Arrays.stream(shopTagBlackListArr).anyMatch(shopTag -> Arrays.asList(shopTagsArr).contains(shopTag))) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取用户现付后买状态
     *
     * @param context
     * @return
     */
    public ForetasteAuth getUserForetasteAuth(DetailContext context, boolean canForetaste) {
        ForetasteAuth foretasteAuth;
//        //商品不支持先付后买
//        if (!canForetaste) {
//            foretasteAuth = new ForetasteAuth(0);
//            return foretasteAuth;
//        }
        //无法通过用户id判断用户先付后买状态
        //非小程序
        Long userId = context.getLoginUserId();
        if (userId == null || context.getRouteInfo().getApp() != App.XCX ||
                !commonSwitchUtil.isOn(SwitchKey.PAYMAILO_GET_FORETASTE)) {
            foretasteAuth = new ForetasteAuth(0);
            return foretasteAuth;
        }

        Response<ForetasteAuthResponseDTO> response;
        ForetasteAuthRequestDTO foretasteAuthRequestDTO = new ForetasteAuthRequestDTO();
        foretasteAuthRequestDTO.setUserId(userId);
        foretasteAuthRequestDTO.setAmount(context.getItemDO().getLowNowPriceVal());

        try {
            response = maiLoUserInfoApi.checkForetasteAuth(foretasteAuthRequestDTO);
        } catch (Throwable e) {
            LOGGER.error("call foretasteAuth failed.", e);
            foretasteAuth = new ForetasteAuth(0);
            return foretasteAuth;
        }
        if (response != null && response.getCode() != null && response.getCode() == 1001 && response.getData() != null) {
            foretasteAuth = new ForetasteAuth(response.getData());
        } else {
            foretasteAuth = new ForetasteAuth(0);
        }
        return foretasteAuth;
    }

    private VirtualItemType getVirtualItemType(DetailContext context) {
        if (context == null || context.getItemDO() == null) {
            return VirtualItemType.NORMAL;
        }
        try {
            ItemDO itemDO = context.getItemDO();
            String categoryTypes = metabaseClient.get("virtual_categories");
            String[] pairs = categoryTypes.split(",");
            for (String pair : pairs) {
                String[] p = pair.split(":");
                if (itemDO.getCids().contains("#" + p[0] + "#")) {
                    return VirtualItemType.getType(Integer.parseInt(p[1]));
                }
            }
        } catch (Throwable e) {
        }
        return VirtualItemType.NORMAL;

    }

    private List<String> getNumTags(List<ItemTagDO> itemTagList) {
        if (itemTagList == null || itemTagList.size() == 0) {
            return null;
        }
        List<String> numTags = new ArrayList<>();
        for (ItemTagDO tag : itemTagList) {
            if ("tags".equals(tag.getTagKey())) {
                numTags.add(tag.getTagValue());
            }
        }
        return numTags;
    }

    private void decorateLoginUserInfo(ItemBaseDO itemBaseDO, DetailContext context) {
        try {
            Long loginUserId = context.getLoginUserId();
            if (null != loginUserId) {
                itemBaseDO.setLoginUserId(IdConvertor.idToUrl(loginUserId));
                if (context.getRouteInfo().getApp() == App.MSD) {
                    Result<UsersInfo> userResult = userService.queryUserByUserId(loginUserId);
                    if (userResult != null && userResult.getValue() != null) {
                        UsersInfo usersInfo = userResult.getValue();
                        itemBaseDO.setLoginUserNickname(usersInfo.getUname());
                        itemBaseDO.setLoginUserAvatar(ImageUtil.img(usersInfo.getAvatar()));
                    }
                }
            }
        } catch (Throwable e) {
            LOGGER.error("set login user info error!", e);
        }
    }

    private String getSlogan(List<ItemTagDO> itemTagList) {
        if (itemTagList == null || itemTagList.size() == 0) {
            return null;
        }
        ItemTagDO slgTag = null;
        for (ItemTagDO tag : itemTagList) {
            if ("slg".equals(tag.getTagKey()) && StringUtils.isNotBlank(tag.getTagValue())) {
                slgTag = tag;
                break;
            }
        }

        if (slgTag == null) {
            return null;
        }
        Integer startTime = null, endTime = null;
        String slogan = null;
        String[] pairs = slgTag.getTagValue().split("\\|");
        for (String ps : pairs) {
            String[] kv = ps.split(":");
            if (kv == null && kv.length != 2) {
                continue;
            }
            if ("st".equals(kv[0])) {
                startTime = Integer.parseInt(kv[1]);
            } else if ("et".equals(kv[0])) {
                endTime = Integer.parseInt(kv[1]);
            } else if ("txt".equals(kv[0])) {
                slogan = kv[1];
            }
        }
        if (startTime == null || endTime == null || slogan == null) {
            return null;
        }
        int now = (int) (System.currentTimeMillis() / 1000);
        if (startTime < now && now < endTime && StringUtils.isNotBlank(slogan)) {
            try {
                return new String(new BASE64Decoder().decodeBuffer(slogan));
            } catch (IOException e) {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * 获取前N件立减（2019年3月大促需求-限量立减）的商品活动信息
     *
     * @return
     */
    public LimitDiscountInfo getLimitDiscountInfo(DetailContext context) {
        DetailItemDO itemDO = context.getItemDO();
        if (itemDO == null
                || CollectionUtils.isEmpty(itemDO.getItemTags())
                || ContextUtil.isLiveSkuDetail(context)
                || context.getRouteInfo().getBizType() == BizType.TTNORMAL) {
            return null;
        }
        try {
            Optional<String> tagValue = itemDO.getItemTags().stream()
                    .filter(tag -> LIMIT_DISCOUNT_ITEM_TAG_KEY.equals(tag.getTagKey()))
                    .map(ItemTagDO::getTagValue)
                    .findAny();
            if (!tagValue.isPresent()) {
                return null;
            }
            LimitDiscountInfo limitDiscountInfo = new LimitDiscountInfo();
            String v = tagValue.get();
            Arrays.stream(v.split("\\|"))
                    .forEach(pairString -> {
                        String[] kvPair = pairString.split(":");
                        if (kvPair == null || kvPair.length != 2) {
                            return;
                        }
                        switch (kvPair[0]) {
                            case "st":
                                //开始时间
                                limitDiscountInfo.setStartTime(Integer.parseInt(kvPair[1]));
                                break;
                            case "et":
                                //结束时间
                                limitDiscountInfo.setEndTime(Integer.parseInt(kvPair[1]));
                                break;
                            case "ai":
                                //活动id
                                limitDiscountInfo.setActivityId(Long.parseLong(kvPair[1]));
                                break;
                            case "ccp":
                                //限量立减的价格
                                Integer discountPrice=Integer.parseInt(kvPair[1]);
                                limitDiscountInfo.setDicountPirce(discountPrice);
                                limitDiscountInfo.setLimitPirce(itemDO.getLowNowPriceVal().intValue()-discountPrice);
                                break;
                            case "cln":
                                //限量的件数
                                limitDiscountInfo.setLimitCount(Integer.parseInt(kvPair[1]));
                                break;
                            default:
                                break;
                        }
                    });
            limitDiscountInfo.setStock(getLimitDiscountStock(itemDO, limitDiscountInfo));
            return limitDiscountInfo;
        } catch (Throwable e) {
        }
        return null;
    }

    /**
     * 获取前N件立减活动的剩余库存
     *
     * @param itemDO
     * @param limitDiscountInfo
     * @return
     */
    public Integer getLimitDiscountStock(DetailItemDO itemDO, LimitDiscountInfo limitDiscountInfo) {
        if (itemDO == null || limitDiscountInfo == null) {
            return 0;
        }
        try {
            ItemActivityInventoryQueryParamV2 param = new ItemActivityInventoryQueryParamV2();
            param.setTradeItemId(itemDO.getItemId());
            param.setActivityId(limitDiscountInfo.getActivityId());
            param.setChannelId(1);
            PlainResult<ItemActivityInventoryV2> plainResult = inventoryReadService.queryItemActivityInventoryV2(param);
            if (plainResult != null && plainResult.isSuccess() && plainResult.getData() != null) {
                return plainResult.getData().getStock();
            }
        } catch (Throwable e) {
        }
        return 0;
    }

    protected OverseaItemInfo getOverseaInfo(Map<String, String> features) {
        try {
            if (features == null || features.isEmpty()) {
                return null;
            } else {
                // 1110海外商品数据
                String overseaInfo = features.get("cbInfo");
                if (StringUtils.isNotBlank(overseaInfo)) {
                    JSONObject jsonObject = (JSONObject) JSONObject.parse(overseaInfo);
                    //商品类型(0 进口保税，1 海外直邮)
                    OverseaItemInfo overseaItemInfo = new OverseaItemInfo();
                    int cbType = jsonObject.getIntValue("type");
                    switch (cbType) {
                        case 0:
                            overseaItemInfo.setOverseaType(OverseaItemEnum.BONDED_ITEM);
                            break;
                        case 1:
                            overseaItemInfo.setOverseaType(OverseaItemEnum.OVERSEA_DIRECT_MAIL);
                            break;
                    }
                    //税率
                    Double cbTariffRate = jsonObject.getDouble("tariffRate");
                    if (cbTariffRate != null) {
                        //11.50转成1150
                        overseaItemInfo.setTaxRate((int) (cbTariffRate.doubleValue() * 100));
                    }
                    return overseaItemInfo;
                }
            }
        } catch (Throwable e) {
            LOGGER.error("get oversea info error : {}", e);
        }
        return null;
    }

    protected ActivityBanner getActivityBanner(Map<String, String> features) {
        try {
            if (features == null || features.isEmpty()) {
                return null;
            } else {
                // 品牌特卖活动banner
                String xcxBrandInfo = features.get("xcxBrandBanner");
                if (StringUtils.isNotBlank(xcxBrandInfo)) {
                    JSONObject jsonObject = (JSONObject) JSONObject.parse(xcxBrandInfo);
                    int now = (int) (System.currentTimeMillis() / 1000);
                    if (jsonObject == null
                            || jsonObject.getInteger("start") > now
                            || jsonObject.getInteger("end") < now) {
                        return null;
                    }
                    ActivityBanner activityBanner = new ActivityBanner();
                    activityBanner.setImg(ImageUtil.img(jsonObject.getString("banner")));
                    activityBanner.setLink(jsonObject.getString("url"));
                    return activityBanner;
                }
            }
        } catch (Throwable e) {
            LOGGER.error("get activity banner error : {}", e);
        }
        return null;
    }
    protected Integer getPurchaseLimit(Map<String, String> features) {
        try {
            if (features == null || features.isEmpty()) {
                return null;
            } else {
                String purchaseLimit = features.get("purchaseLimit");
                if (StringUtils.isNotBlank(purchaseLimit)) {
                    return Integer.parseInt(purchaseLimit);
                }
            }
        } catch (Throwable e) {
            LOGGER.error("get purchaseLimit error : {}", e);
        }
        return null;
    }

    /**
     * //商品发布改造需要给详情返回尺码图片
     * @param itemBaseDO
     * @param item
     */

    private void setSizeImageInfo(ItemBaseDO itemBaseDO,DetailItemDO item){
        itemBaseDO.setImageInfo(getSizeImageInfo(item));
    }

    public static List<ImageInfo> getSizeImageInfo(DetailItemDO item) {
        if (MapUtils.isNotEmpty(item.getFeatures()) && StringUtils.isNotEmpty(item.getFeatures().get("size_image"))) {
            return JSON.parseArray(item.getFeatures().get("size_image"), ImageInfo.class);
        }
        return Collections.EMPTY_LIST;
    }


    private void fillLiveInWallItemTitle(ItemBaseDO itemBaseDO, DetailContext context) {
        ItemExtraDO itemExtraDO = context.getItemDO().getItemExtraDO();
        String anchorItemActivityInfo = itemExtraDO.getFeatures().get(DetailConstants.ItemExtraDOKeys.ANCHOR_ITEM_ACTIVITY_INFO);
        JSONObject anchorItemInfoJson = JSON.parseObject(anchorItemActivityInfo);
        String activityTitle = anchorItemInfoJson.getString("activityTitle");
        if (!StringUtils.isBlank(activityTitle)) {
            itemBaseDO.setTitle(activityTitle);
        }
    }
}
