package com.mogujie.detail.module.activity.util;

import com.google.gson.Gson;
import com.mogujie.detail.core.util.MaitUtil;
import com.mogujie.detail.core.util.NumUtil;
import com.mogujie.detail.module.activity.domain.*;
import com.mogujie.service.hummer.constains.PromotionConstants;
import com.mogujie.service.hummer.constains.RequestConstants;
import com.mogujie.service.hummer.utils.RedPacketChecker;
import com.mogujie.tesla.common.CollectionUtils;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 从麦田获取活动信息的接口
 * Created by y<PERSON><PERSON> on 16/2/22.
 */
public class ActivityUtil {
    private static org.slf4j.Logger LOGGER = org.slf4j.LoggerFactory.getLogger(ActivityUtil.class);

    public static final long RED_PACKAET_INFO_CODE = 2097L;
    public static final long EVENT_INFO = 2107L;

    public static final long H5_DOWNLOAD_BANNER_CODE = 5694L; // H5 详情资源为下载banner位

    public static final Integer C1_TAG = 16;


    public static RedPacketResource getInfos(List<Map<String, Object>> redPacketRes) {
        if (CollectionUtils.isEmpty(redPacketRes)) {
            return null;
        }

        Map<String, Object> res = redPacketRes.get(0);
        if (!CollectionUtils.isEmpty(res)) {
            String icon = res.get("icon") == null ? "" : res.get("icon").toString();
            String info = res.get("info") == null ? "" : res.get("info").toString();
            String btnTitle = res.get("btnTitle") == null ? "" : res.get("btnTitle").toString();
            String btnLink = res.get("btnLink") == null ? "" : res.get("btnLink").toString();
            String prefix = res.get("redPacketPrefix") == null ? "" : res.get("redPacketPrefix").toString();
            String suffix = res.get("redPacketSuffix") == null ? "" : res.get("redPacketSuffix").toString();

            RedPacketResource redPacketResource = new RedPacketResource(icon, info, btnTitle, btnLink, prefix, suffix);
            return redPacketResource;
        }
        return null;
    }

    /**
     * 设置红包信息redPackets -> Gift
     *
     * @param extra
     * @return
     */
    public static Gift getRedPacketsGift(String extra, Integer flag) {
        Long price = RedPacketChecker.getSupportMoney(extra, RequestConstants.Market.MOGUJIE);
        if (price == null) {
            return null;
        }

        if (null != flag && flag == PromotionConstants.RedpacketFlag.COMMON) {
            return null;
        } else {
            List<Map<String, Object>> redResource = MaitUtil.getMaitData(ActivityUtil.RED_PACKAET_INFO_CODE);
            RedPacketResource redPacketResource = ActivityUtil.getInfos(redResource);
            if (redPacketResource == null) {
                return null;
            }

            Gift gift = new Gift();
            String priceStr = NumUtil.formatNum(price / 100.0F) + "元";
            gift.setTitle(redPacketResource.getPrefix() + priceStr + redPacketResource.getSuffix());
            gift.setHighlightText(priceStr);
            gift.setHighlightColor("#ff2255");
            gift.setIcon(redPacketResource.getIcon());
            gift.setLink(redPacketResource.getBtnLink());
            gift.setArrowDesc(StringUtils.removeEnd(redPacketResource.getBtnTitle(), ">"));
            gift.setShowArrow(true);
            gift.setType(GiftType.REDPACKET);
            return gift;
        }
    }

    public static Map<String, Object> getEventTagsMap(List<Map<String, Object>> tagRes) {
        if (CollectionUtils.isEmpty(tagRes)) {
            return null;
        }

        Map<String, Object> map = new HashMap<>(2);
        List<EventTag> eventTags = new ArrayList<>(2);
        for (Map<String, Object> res : tagRes) {
            EventTag tag = new EventTag();
            Object eventText = res.get("eventText");
            Object eventBgColor = res.get("eventBgColorV2");
            Object link = res.get("link");
            Object isFirstTag = res.get("isFirstTag");
            Object pintuanPriceTag = res.get("pintuanPriceTag");
            tag.setPintuanTagText(pintuanPriceTag == null ? "" : pintuanPriceTag.toString());
            tag.setTagText(eventText == null ? "" : eventText.toString());
            tag.setTagBgColor(eventBgColor == null ? "" : eventBgColor.toString());
            tag.setLink(link == null ? "" : link.toString());
            if (isFirstTag != null && true == Boolean.valueOf(isFirstTag.toString()) && !map.containsKey("priceTag")) {
                map.put("priceTag", tag);
            } else {
                eventTags.add(tag);
            }
        }
        map.put("eventTags", eventTags);
        return map;
    }

    public static String getTags(String extra) {
        if (StringUtils.isEmpty(extra)) {
            return null;
        }
        try {
            Gson gson = new Gson();
            Map<String, String> extraInfo = gson.fromJson(extra, HashMap.class);
            if (null == extraInfo || extraInfo.isEmpty()) {
                return null;
            }
            String tags = extraInfo.get("tags");
            if (com.mogujie.metabase.utils.StringUtils.isEmpty(tags)) {
                return null;
            }

            return tags;
        } catch (Throwable e) {
            LOGGER.error("parse extra.tags failed : {}. {}", extra, e);
        }
        return null;
    }

    public static class RedPacketResource {

        public RedPacketResource(String icon, String info, String btnTitle, String btnLink, String prefix, String suffix) {
            this.btnLink = btnLink;
            this.icon = icon;
            this.btnTitle = btnTitle;
            this.info = info;
            this.prefix = prefix;
            this.suffix = suffix;
        }

        @Getter
        @Setter
        private String icon;

        @Getter
        @Setter
        private String info;

        @Getter
        @Setter
        private String btnTitle;

        @Getter
        @Setter
        private String btnLink;

        @Getter
        @Setter
        private String prefix;

        @Getter
        @Setter
        private String suffix;
    }

}
