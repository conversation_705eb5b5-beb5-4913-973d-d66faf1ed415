package com.mogujie.detail.module.shop.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON>iaoyao on 16/8/16.
 */
public class ShopDO implements ModuleDO {


    @Setter
    @Getter
    private List<ShopDsr> score;


    @Setter
    @Getter
    private List<ShopService> services;

    @Getter
    @Setter
    private Integer itemPromiseDeliveryTime;

    @Setter
    @Getter
    private List<ShopService> goodItemServices;


    @Setter
    @Getter
    private List<DetailShopCategory> categories;


    @Setter
    @Getter
    private Integer cFans;


    @Setter
    @Getter
    private Integer cSells;


    @Setter
    @Getter
    private Boolean isMarked;


    @Setter
    @Getter
    private Integer cGoods;


    @Setter
    @Getter
    private String userId;


    @Setter
    @Getter
    private String shopLogo;


    @Setter
    @Getter
    private String name;


    @Setter
    @Getter
    private String shopId;


    /**
     * 店铺图标
     */
    @Setter
    @Getter
    private String tag;


    @Setter
    @Getter
    private Integer type;


    @Setter
    @Getter
    private Map<String, Object> shopHeader;


    @Setter
    @Getter
    private Integer level;

    /**
     * 店铺数字标
     */
    @Getter
    @Setter
    private List<Integer> tags;

    /**
     * 是否应该在私聊按钮上展示气泡
     */
    @Getter
    @Setter
    private Boolean imTips;

    /**
     *  店铺主营类目ID
     */
    @Getter
    @Setter
    private Integer tagId;

    /**
     * 店铺标签（商详入口，非店铺标）（非静态化标签集合）
     */
    @Setter
    @Getter
    private List<ShopLabel> dynLabels;

    /**
     * 店铺标签（商详入口，非店铺标）（静态化标签集合）
     */
    @Setter
    @Getter
    private List<ShopLabel> labels;

    /**
     * 店铺平均发货时长
     */
    @Getter
    @Setter
    private Long shopAvgDeliveryTime;

    /**
     * 综合体验分星级，支持半星
     */
    @Getter
    @Setter
    private String star;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ShopDO shopDO = (ShopDO) o;

        if (score != null ? !score.equals(shopDO.score) : shopDO.score != null) return false;
        if (services != null ? !services.equals(shopDO.services) : shopDO.services != null) return false;
        if (goodItemServices != null ? !goodItemServices.equals(shopDO.goodItemServices) : shopDO.goodItemServices != null)
            return false;
        if (categories != null ? !categories.equals(shopDO.categories) : shopDO.categories != null) return false;
        if (cFans != null ? !cFans.equals(shopDO.cFans) : shopDO.cFans != null) return false;
        if (cSells != null ? !cSells.equals(shopDO.cSells) : shopDO.cSells != null) return false;
        if (isMarked != null ? !isMarked.equals(shopDO.isMarked) : shopDO.isMarked != null) return false;
        if (cGoods != null ? !cGoods.equals(shopDO.cGoods) : shopDO.cGoods != null) return false;
        if (userId != null ? !userId.equals(shopDO.userId) : shopDO.userId != null) return false;
        if (shopLogo != null ? !shopLogo.equals(shopDO.shopLogo) : shopDO.shopLogo != null) return false;
        if (name != null ? !name.equals(shopDO.name) : shopDO.name != null) return false;
        if (shopId != null ? !shopId.equals(shopDO.shopId) : shopDO.shopId != null) return false;
        if (tag != null ? !tag.equals(shopDO.tag) : shopDO.tag != null) return false;
        if (type != null ? !type.equals(shopDO.type) : shopDO.type != null) return false;
        if (shopHeader != null ? !shopHeader.equals(shopDO.shopHeader) : shopDO.shopHeader != null) return false;
        if (level != null ? !level.equals(shopDO.level) : shopDO.level != null) return false;
        if (tags != null ? !tags.equals(shopDO.tags) : shopDO.tags != null) return false;
        if (imTips != null ? !imTips.equals(shopDO.imTips) : shopDO.imTips != null) return false;
        if (tagId != null ? !tagId.equals(shopDO.tagId) : shopDO.tagId != null) return false;
        if (dynLabels != null ? !dynLabels.equals(shopDO.dynLabels) : shopDO.dynLabels != null) return false;
        if (labels != null ? labels.equals(shopDO.labels) : shopDO.labels == null) return false;
        return star != null ? !star.equals(shopDO.star) : shopDO.star != null;
    }

    @Override
    public int hashCode() {
        int result = score != null ? score.hashCode() : 0;
        result = 31 * result + (services != null ? services.hashCode() : 0);
        result = 31 * result + (goodItemServices != null ? goodItemServices.hashCode() : 0);
        result = 31 * result + (categories != null ? categories.hashCode() : 0);
        result = 31 * result + (cFans != null ? cFans.hashCode() : 0);
        result = 31 * result + (cSells != null ? cSells.hashCode() : 0);
        result = 31 * result + (isMarked != null ? isMarked.hashCode() : 0);
        result = 31 * result + (cGoods != null ? cGoods.hashCode() : 0);
        result = 31 * result + (userId != null ? userId.hashCode() : 0);
        result = 31 * result + (shopLogo != null ? shopLogo.hashCode() : 0);
        result = 31 * result + (name != null ? name.hashCode() : 0);
        result = 31 * result + (shopId != null ? shopId.hashCode() : 0);
        result = 31 * result + (tag != null ? tag.hashCode() : 0);
        result = 31 * result + (type != null ? type.hashCode() : 0);
        result = 31 * result + (shopHeader != null ? shopHeader.hashCode() : 0);
        result = 31 * result + (level != null ? level.hashCode() : 0);
        result = 31 * result + (tags != null ? tags.hashCode() : 0);
        result = 31 * result + (imTips != null ? imTips.hashCode() : 0);
        result = 31 * result + (tagId != null ? tagId.hashCode() : 0);
        result = 31 * result + (dynLabels != null ? dynLabels.hashCode() : 0);
        result = 31 * result + (labels != null ? labels.hashCode() : 0);
        result = 31 * result + (star != null ? star.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "ShopDO{" +
                "score=" + score +
                ", services=" + services +
                ", goodItemServices=" + goodItemServices +
                ", categories=" + categories +
                ", cFans=" + cFans +
                ", cSells=" + cSells +
                ", isMarked=" + isMarked +
                ", cGoods=" + cGoods +
                ", userId='" + userId + '\'' +
                ", shopLogo='" + shopLogo + '\'' +
                ", name='" + name + '\'' +
                ", shopId='" + shopId + '\'' +
                ", tag='" + tag + '\'' +
                ", type=" + type +
                ", shopHeader=" + shopHeader +
                ", level=" + level +
                ", tags=" + tags +
                ", imTips=" + imTips +
                ", tagId=" + tagId +
                ", dynLabels=" + dynLabels +
                ", labels=" + labels +
                ", shopAvgDeliveryTime=" + shopAvgDeliveryTime +
                ", star=" + star +
                '}';
    }
}
