package com.mogujie.detail.module.normalCountdown.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by anshi on 18/2/6.
 */
public class NormalCountdownDO implements ModuleDO {

    /**
     * 倒计时信息map，已商品标为key
     * 如countdownInfoMap.get("xsbk")即显示爆款的倒计时信息
     */
    @Getter
    @Setter
    private Map<String, CountdownInfo> countdownInfoMap = new HashMap<>();

}
