package com.mogujie.detail.module.extra.domain;

import com.mogujie.service.trade.service.logistics.address.v1.domain.dto.query.TradeAddressDto;
import com.mogujie.service.trade.service.logistics.address.v1.domain.dto.query.TradeUserAddressResDto;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * Created by eryi
 * Date: 2020/12/16
 * Time: 4:11 下午
 * Introduction:
 * Document:
 * Actions:
 */
@Data
public class UserRecvAddressInfo {

    /**
     * 地址Id，主键
     */
    private Long addressId;

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    public UserRecvAddressInfo() {
    }

    public UserRecvAddressInfo(String province) {
        this.province = province;
    }

    public static UserRecvAddressInfo convert(TradeUserAddressResDto tradeUserAddressResDto) {
        if (tradeUserAddressResDto == null) {
            return null;
        }
        UserRecvAddressInfo userAddressInfo = new UserRecvAddressInfo();
        BeanUtils.copyProperties(tradeUserAddressResDto, userAddressInfo);
        return userAddressInfo;
    }

    public static UserRecvAddressInfo convert(TradeAddressDto tradeAddressDto) {
        if (tradeAddressDto == null) {
            return null;
        }
        UserRecvAddressInfo userAddressInfo = new UserRecvAddressInfo();
        BeanUtils.copyProperties(tradeAddressDto, userAddressInfo);
        return userAddressInfo;
    }
}
