package com.mogujie.detail.module.daren.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import lombok.Data;

/**
 * @AUTUOR: lengshan
 * @DESCRIPTION: 关联商品
 * @DATE: 2019/8/27 上午10:19
 */
@Data
public class RelatedGoods implements ModuleDO {

    /**
     * 商品ID
     */
    private String itemId;
    /**
     * 图片
     */
    private String image;

    /**
     * 标题
     */
    private String title;

    /**
     * 详情页链接
     */
    private String link;

    /**
     * 预估收益  只有登录用户是达人才有数据
     */
    private Integer earn = 0;
}
