package com.mogujie.detail.module.itemTags.provider;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.module.itemTags.domain.ItemTagsDO;

/**
 * Created by xiaoyao on 16/8/16.
 */
@Module(name = "itemTags")
public class ItemTagsDOProvider implements IModuleDOProvider<ItemTagsDO> {

    @Override
    public void init() throws DetailException {

    }

    @Override
    public ItemTagsDO emit(DetailContext context) {
        return null;

    }
}
