package com.mogujie.detail.module.pintuan;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.module.pintuan.domain.PinTuanDO;
import com.mogujie.metabase.spring.client.MetabaseClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

/**
 * Created by xiaoyao on 17/3/24.
 */
@Module(name = "pintuan")
public class PinTuanDOProvider implements IModuleDOProvider<PinTuanDO> {

    private MgjPinTuanSkuParser skuParser;

    @Resource(name = "confMetabaseClient")
    private MetabaseClient metabaseClient;

    private static final Logger LOGGER = LoggerFactory.getLogger(PinTuanDOProvider.class);

    @Override
    public PinTuanDO emit(DetailContext context) {
        return null;
    }

    @Override
    public void init() throws DetailException {
        try {
            skuParser = new MgjPinTuanSkuParser(metabaseClient);
        } catch (Exception e) {
            throw new DetailException(e);
        }
    }

}
