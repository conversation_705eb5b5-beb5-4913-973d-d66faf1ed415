package com.mogujie.detail.module.itemBase.domain;

import lombok.Getter;
import lombok.Setter;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/3/15.
 */
public class VideoInfo {

    /**
     * 封面图
     */
    @Getter
    @Setter
    private String cover;

    @Getter
    @Setter
    private String vUserUnique;

    @Getter
    @Setter
    private String vId;

    @Getter
    @Setter
    private String vUnique;

    @Getter
    @Setter
    private Long videoId;

    @Getter
    @Setter
    private Integer width;

    @Getter
    @Setter
    private Integer height;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof VideoInfo)) return false;

        VideoInfo videoInfo = (VideoInfo) o;

        if (cover != null ? !cover.equals(videoInfo.cover) : videoInfo.cover != null) return false;
        if (vId != null ? !vId.equals(videoInfo.vId) : videoInfo.vId != null) return false;
        if (vUnique != null ? !vUnique.equals(videoInfo.vUnique) : videoInfo.vUnique != null) return false;
        if (vUserUnique != null ? !vUserUnique.equals(videoInfo.vUserUnique) : videoInfo.vUserUnique != null)
            return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = cover != null ? cover.hashCode() : 0;
        result = 31 * result + (vUserUnique != null ? vUserUnique.hashCode() : 0);
        result = 31 * result + (vId != null ? vId.hashCode() : 0);
        result = 31 * result + (vUnique != null ? vUnique.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "VideoInfo{" +
                "cover='" + cover + '\'' +
                ", vUserUnique='" + vUserUnique + '\'' +
                ", vId='" + vId + '\'' +
                ", vUnique='" + vUnique + '\'' +
                '}';
    }
}
