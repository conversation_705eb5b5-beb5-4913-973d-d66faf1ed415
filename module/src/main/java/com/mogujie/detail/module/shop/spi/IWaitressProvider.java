package com.mogujie.detail.module.shop.spi;


import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.spi.Exposed;
import com.mogujie.detail.module.shop.domain.ShopService;
import com.mogujie.detail.module.shop.domain.ShopServicesDO;

import java.util.List;

/**
 * 店铺服务体系相关
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/4/10.
 */
@Exposed
public interface IWaitressProvider {

    /**
     * 列举店铺服务体系
     * @param context
     * @return
     */
    ShopServicesDO listService(DetailContext context);
}
