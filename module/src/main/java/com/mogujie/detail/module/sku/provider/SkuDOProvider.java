package com.mogujie.detail.module.sku.provider;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.meili.service.shopcenter.domain.entity.ShopInfo;
import com.meili.service.shopcenter.util.ShopInfoTagsUtil;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.Platform;
import com.mogujie.detail.core.constant.SwitchKey;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.spi.SpiAutowired;
import com.mogujie.detail.core.util.*;
import com.mogujie.detail.module.shop.provider.ShopDOProvider;
import com.mogujie.detail.module.sku.domain.InstallmentData;
import com.mogujie.detail.module.sku.domain.InstallmentState;
import com.mogujie.detail.module.sku.domain.SkuDO;
import com.mogujie.detail.module.sku.domain.SkuData;
import com.mogujie.detail.module.sku.spi.ISkuParser;
import com.mogujie.metabase.spring.client.MetabaseClient;
import com.mogujie.pay.common.dto.Response;
import com.mogujie.pay.mailo.api.MaiLoInstallmentApi;
import com.mogujie.pay.mailo.dto.InstallmentInfoDTO;
import com.mogujie.pay.mailo.dto.TrialInstallmentResponseDTO;
import com.mogujie.pay.mailo.dto.request.parameters.ComputeInstallmentRequestDTO;
import com.mogujie.service.inventory.api.InventoryReadService;
import com.mogujie.service.inventory.domain.ActivityInventoryV2;
import com.mogujie.service.inventory.domain.param.BatchActivityInventoryQueryParamV2;
import com.mogujie.service.inventory.domain.result.MapResult;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.basic.ItemPreSaleDO;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.service.pay.insurance.api.constants.BuyerInsuranceConstants;
import com.mogujie.service.pay.insurance.api.core.BuyerInsuranceService;
import com.mogujie.service.pay.insurance.domain.core.BuyerInsuranceReqDTO;
import com.mogujie.service.pay.insurance.domain.core.BuyerInsuranceRespDTO;
import com.mogujie.service.pay.insurance.domain.core.InsuranceInfoDTO;
import com.mogujie.service.pay.insurance.enums.InsureSceneEnum;
import com.mogujie.service.shopcenter.util.AuthorizedShopUtil;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by xiaoyao on 16/8/17.
 */
@Module(name = "sku")
public class SkuDOProvider implements IModuleDOProvider<SkuDO> {

    private static final Logger LOGGER = LoggerFactory.getLogger(ShopDOProvider.class);

    @SpiAutowired
    private ISkuParser skuParser;

    @Autowired
    private MaiLoInstallmentApi maiLoInstallmentApi;

    private InventoryReadService inventoryReadService;

    private static final String MAIN_PRICE_DESC = "定金";

    private static final String SUB_PRICE_DESC = "总价";

    @Resource(name = "confMetabaseClient")
    private MetabaseClient metabaseClient;

    @Autowired
    private CommonSwitchUtil commonSwitchUtil;

    private BuyerInsuranceService buyerinsuranceservice;

    /**
     * 白付美分期免息标资源位ID
     */
    private static final Long FREE_INTEREST_TAG = 153460L;
    /**
     * 白付美分期优惠标资源位ID
     */
    private static final Long DISCOUNT_TAG = 153461L;

    @Override
    public void init() throws DetailException {
        try {
            inventoryReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(InventoryReadService.class);
            buyerinsuranceservice = TeslaServiceConsumerFactory.getTeslaServiceConsumer(BuyerInsuranceService.class);
        } catch (Exception e) {
            LOGGER.error("init service failed : {}", e);
        }
    }

    @Override
    public SkuDO emit(DetailContext context) {
        DetailItemDO item = context.getItemDO();
        List<ItemSkuDO> skus = item.getItemSkuDOList();
        if (CollectionUtils.isEmpty(skus)) {
            LOGGER.error("Item : {} has no skus", context.getItemId());
            return null;
        }
        SkuDO skuDO = new SkuDO();
        skuDO.setTitle(item.getTitle());
        skuDO.setOtherPrices(item.getOtherPrices());
        skuDO.setPromotionPrice(item.getPromotionPrice());

        skuParser.parseSku(context, skuDO);

        LimitInfo limitInfo = getLimitInfo(item.getJsonExtra());
        if (ContextUtil.isNormalBizDetail(context)
                && null != limitInfo
                && context.getLoginUserId() != null
                && context.getLoginUserId() > 0) {
            skuDO.setLimitTotalStock(limitInfo.getLimitTotalStock());
        }
        if (null != item.getLimitNum()) {
            skuDO.setLimitDesc("单次购买" + item.getLimitNum() + "件以内享优惠，超过则恢复原价");
            skuDO.setLimitNum(item.getLimitNum());
            updateLimitStock(skuDO, limitInfo.getActivityId());
        }

        if (ContextUtil.isNormalBizDetail(context)) {
            this.updateSkuData(skuDO, item);
        }

        if (canInstallment(skuDO, context)) {
            decorateInstallment(skuDO, context);
        }

        if (canInsurance(skuDO, context)) {
            decorateInsurance(skuDO, context);
        }
        return skuDO;
    }

    /**
     * 预售商品，需要重新刷一遍sku信息
     *
     * @param skuDO
     * @param item
     */
    private void updateSkuData(SkuDO skuDO, ItemDO item) {
        ItemPreSaleDO itemPreSale = item.getItemPreSaleDO();
        int time = (int) (System.currentTimeMillis() / 1000);
        // 若预售时间已过或未开始，走普通商品逻辑
        if (itemPreSale == null || time < itemPreSale.getStart() || time > itemPreSale.getEnd()) {
            return;
        }

        // 刷价格
        int nowPrice = itemPreSale.getPrice();
        skuDO.setPriceRange(formatPrice(nowPrice));
        skuDO.setDefaultPrice(formatPrice(nowPrice));

        for (SkuData sku : skuDO.getSkus()) {
            sku.setNowprice(nowPrice);
            sku.setMainPriceStr(MAIN_PRICE_DESC + formatPrice(itemPreSale.getDeposit()));
            sku.setSubPriceStr(SUB_PRICE_DESC + formatPrice(itemPreSale.getPrice()));
        }

        skuDO.setMainPriceStr(MAIN_PRICE_DESC + formatPrice(itemPreSale.getDeposit()));
        skuDO.setSubPriceStr(SUB_PRICE_DESC + formatPrice(itemPreSale.getPrice()));
    }

    private String formatPrice(double price) {
        return "¥" + NumUtil.formatNum(price / 100D);
    }

    /**
     * 商品是否可分期
     *
     * @param context
     * @return
     */
    private boolean canInstallment(SkuDO skuDO, DetailContext context) {
        if (context.getRouteInfo().getApp().equals(App.MLS)
                || context.getRouteInfo().getBizType().equals(BizType.SECKILL)
                || context.getRouteInfo().getBizType().equals(BizType.TREASURE)
                || skuDO == null
                || CollectionUtils.isEmpty(skuDO.getSkus())
                || context.getRouteInfo().getPlatform() == Platform.XCX
                || context.getRouteInfo().getApp() == App.XCX
                || context.getRouteInfo().getApp() == App.BH
                || context.getRouteInfo().getApp() == App.MSD) {
            return false;
        }

        DetailItemDO itemDO = context.getItemDO();

        //海外跨境商品不支持分期
        if (itemDO.getFeatures() != null
                && itemDO.getFeatures().containsKey("cbInfo")) {
            return false;
        }

        String virtualCateRoot = "#" + metabaseClient.get("virtual_category_root") + "#";
        //充值中心类目，需要不能分期
        if (itemDO.getCids().contains(virtualCateRoot)) {
            return false;
        }

        //认证店铺
        ShopInfo shopInfo = (ShopInfo) itemDO.getShopInfo();
        if (!AuthorizedShopUtil.isAuthorized(shopInfo)) {
            return false;
        }

        //店铺黑名单1311标的店铺不展示
        if (ShopInfoTagsUtil.stringToSet(shopInfo.getTags()).contains(1311)) {
            return false;
        }

        //非预售商品
        if (context.getRouteInfo().getBizType() == BizType.NORMAL && itemDO.getItemPreSaleDO() != null) {
            int now = (int) (System.currentTimeMillis() / 1000);
            if (now > itemDO.getItemPreSaleDO().getStart() && now < itemDO.getItemPreSaleDO().getEnd()) {
                return false;
            }
        }

        //未登录则不显示分期数据
        Long userId = context.getLoginUserId();
        if (userId == null || !commonSwitchUtil.isOn(SwitchKey.PAYMAILO_GET_USER_STATUS)) {
            return false;
        }
        return true;
    }

    /**
     * sku刷入分期信息
     * 接口文档参见：http://wiki.mogujie.org/pages/viewpage.action?pageId=70348845
     */
    private void decorateInstallment(SkuDO skuDO, DetailContext context) {
        List<SkuData> skuList = skuDO.getSkus();
        List<Long> priceList = new ArrayList<>();
        List<SkuData> toBeDecorateList = new ArrayList<>();
        for (int i = 0; i < skuList.size(); i++) {
                priceList.add(skuList.get(i).getNowprice().longValue());
                toBeDecorateList.add(skuList.get(i));
        }

        try {
            ComputeInstallmentRequestDTO requestDTO = new ComputeInstallmentRequestDTO();
            requestDTO.setAmounts(priceList);
            requestDTO.setUserId(context.getLoginUserId());
            Map<String, String> tagMap = TagUtil.getExtraInfo(context.getItemDO().getJsonExtra());
            if (tagMap != null && org.apache.commons.lang3.StringUtils.isNotBlank(tagMap.get("tags"))) {
                requestDTO.setTags(Arrays.asList(tagMap.get("tags").split(",")));
            }
            ShopInfo shopInfo = context.getItemDO().getShopInfo();
            if (shopInfo != null && org.apache.commons.lang.StringUtils.isNotBlank(shopInfo.getTags())) {
                requestDTO.setMerchantTags(Arrays.asList(shopInfo.getTags().split(",")));
            }

            Response<TrialInstallmentResponseDTO> ret = maiLoInstallmentApi.trialInstallmentPlan(requestDTO);
            if (ret != null && ret.getData() != null) {
                //此处设置用户是否可以使用白付美状态
                switch (ret.getData().getErrorCode()) {
                    //未开通
                    case 150012:
                        skuDO.setInstallmentState(InstallmentState.UN_OPEN);
                        break;
                    //未偿清
                    case 151504:
                    case 150601:
                        skuDO.setInstallmentState(InstallmentState.UN_PAY_OFF);
                        break;
                    //不可用
                    case 151501:
                    case 151502:
                    case 151503:
                    case 151505:
                    case 151405:
                    case 1513801:
                        skuDO.setInstallmentState(InstallmentState.UN_USABLE);
                        break;
                    //默认正常
                    default:
                        skuDO.setInstallmentState(InstallmentState.NORMAL);
                        break;
                }
                // 设置白付美剩余额度
                    skuDO.setAvailableQuota(ret.getData().getAvailableQuota());
                //此处设置每个sku的分期金额
                List<List<InstallmentInfoDTO>> resultList = Optional.ofNullable(ret.getData().getInstallmentPlanList()).orElse(Lists.newArrayList());
                //Map<优惠标对应资源位ID，Map<分期数or最大免息期数，图标链接>
                Map<Long, Map<Integer, String>> tagIconMap = Maps.newHashMap();
                //免息标
                tagIconMap.put(FREE_INTEREST_TAG
                        , getTagIconMap(
                                resultList.stream()
                                        .flatMap(List::stream)
                                        .map(InstallmentInfoDTO::getMaxFreeFeeTerms)
                                        .filter(maxFreeFeeTerms -> maxFreeFeeTerms
                                                != null && maxFreeFeeTerms > 0)
                                        .distinct()
                                        .collect(Collectors.toList())
                                , FREE_INTEREST_TAG));
                //优惠标
                tagIconMap.put(DISCOUNT_TAG
                        , getTagIconMap(
                                resultList.stream()
                                        .flatMap(List::stream)
                                        .filter(installmentInfoDTO -> installmentInfoDTO.getMaxFreeFeeTerms() == null
                                                || installmentInfoDTO.getMaxFreeFeeTerms().equals(0))
                                        .map(InstallmentInfoDTO::getTotalCount)
                                        .filter(totalCount -> totalCount
                                                != null && totalCount > 0)
                                        .distinct().collect(Collectors.toList())
                                , DISCOUNT_TAG));

                if (CollectionUtils.isNotEmpty(resultList)) {
                    for (int i = 0; i < resultList.size(); i++) {
                        SkuData skuData = toBeDecorateList.get(i);
                        List<InstallmentData> installment = new ArrayList<>();
                        for (InstallmentInfoDTO installmentInfoDTO : resultList.get(i)) {
                            InstallmentData installmentItem = new InstallmentData();
                            installmentItem.setFee(installmentInfoDTO.getFee().intValue());
                            installmentItem.setNum(installmentInfoDTO.getTotalCount());
                            installmentItem.setPerPrice(installmentInfoDTO.getSubAmount().intValue());
                            String icon;
                            if (installmentInfoDTO.getMaxFreeFeeTerms() > 0) {
                                icon = tagIconMap.get(FREE_INTEREST_TAG).get(installmentInfoDTO.getMaxFreeFeeTerms());
                            } else {
                                icon = tagIconMap.get(DISCOUNT_TAG).get(installmentInfoDTO.getTotalCount());
                            }
                            installmentItem.setFreeMaxNum(installmentInfoDTO.getMaxFreeFeeTerms());
                            installmentItem.setIcon(icon);
                            installmentItem.setFeeTitle(installmentInfoDTO.getFeeTitle());
                            installment.add(installmentItem);
                            if (installmentInfoDTO.getMaxFreeFeeTerms() != null && installmentInfoDTO.getMaxFreeFeeTerms() > 0) {
                                skuDO.setFreePhases(installmentInfoDTO.getMaxFreeFeeTerms());
                            }
                        }
                        skuData.setInstallment(installment);
                        skuDO.setCanInstallment(true);
                    }
                }
            }
        } catch (Throwable e) {
            LOGGER.error("get installment price error.", e);
        }
    }


    private boolean canInsurance(SkuDO skuDO, DetailContext context) {
        //加上一道开关判断
        if (!commonSwitchUtil.isOn(SwitchKey.PAYMAILO_GET_INSURANCE)) {
            return false;
        }

        if (context.getRouteInfo().getApp().equals(App.MLS)
                || context.getRouteInfo().getBizType().equals(BizType.SECKILL)
                || context.getRouteInfo().getBizType().equals(BizType.FASTBUY)
                || context.getRouteInfo().getBizType().equals(BizType.TREASURE)
                || skuDO == null
                || CollectionUtils.isEmpty(skuDO.getSkus())
                || context.getRouteInfo().getApp() == App.BH
                || context.getRouteInfo().getApp() == App.MSD) {
            return false;
        }

        DetailItemDO itemDO = context.getItemDO();
        //海外跨境商品不支持分期
        if (itemDO.getFeatures() != null
                && itemDO.getFeatures().containsKey("cbInfo")) {
            return false;
        }

        //sku价格校验,只限制了最低价
        Optional findRelsut = skuDO.getSkus().stream().filter(sku -> sku.getNowprice() >= 2000).findAny();
        if (!findRelsut.isPresent()) {
            return false;
        }

        //非预售商品
        if (context.getRouteInfo().getBizType() == BizType.NORMAL && itemDO.getItemPreSaleDO() != null) {
            int now = (int) (System.currentTimeMillis() / 1000);
            if (now > itemDO.getItemPreSaleDO().getStart() && now < itemDO.getItemPreSaleDO().getEnd()) {
                return false;
            }
        }
        return true;
    }

    /**
     * 1380新保险需求
     * 接口文档地址： http://wiki.mogujie.org/pages/viewpage.action?pageId=78407636
     * 具体需求地址: https://mogu.feishu.cn/docs/doccnRdlx54Fpw7DLMxbnxCzjef#
     */
    private void decorateInsurance(SkuDO skuDO, DetailContext context) {
        if (CollectionUtils.isEmpty(skuDO.getSkus()) || context.getItemDO() == null) {
            return;
        }

        //组装查询条件
        DetailItemDO detailItemDO = context.getItemDO();
        List<BuyerInsuranceReqDTO> buyerInsuranceReqDTOList = Lists.newArrayList();
        for (SkuData skuData : skuDO.getSkus()) {
            if (skuData.getNowprice() < 2000) { //低于20块钱的商品不处理了。
                continue;
            }
            BuyerInsuranceReqDTO insuranceReqDTO = new BuyerInsuranceReqDTO();
            HashMap<String, Object> bizData = new HashMap<>();
            bizData.put(BuyerInsuranceConstants.NOW_PRICE, skuData.getNowprice());
            bizData.put(BuyerInsuranceConstants.BUYER_INSURE_GREY_KEY, context.getLoginUserId());
            insuranceReqDTO.setSkuId(IdConvertor.urlToId(skuData.getStockId()));
            insuranceReqDTO.setCategories(detailItemDO.getCids());
            insuranceReqDTO.setActualPrice(skuData.getNowprice().longValue());
            insuranceReqDTO.setInsureSceneEnum(InsureSceneEnum.BUYER_INSURANCE_CONFIRM_PAGE);
            insuranceReqDTO.setBizData(bizData);
            buyerInsuranceReqDTOList.add(insuranceReqDTO);
        }

        Response<BuyerInsuranceRespDTO> response = null;
        try {
            response = buyerinsuranceservice.getBuyerInsuranceBySku(buyerInsuranceReqDTOList);

        } catch (Exception ex) {
            LOGGER.error("调用保险失败", ex);
            return;
        }
        if (response == null || response.getData() == null) {
            return;
        }

        BuyerInsuranceRespDTO buyerInsuranceRespDTO = response.getData();
        List<InsuranceInfoDTO> insuranceInfoDTOList = buyerInsuranceRespDTO.getInsuranceInfoDTOS();
        if (CollectionUtils.isEmpty(insuranceInfoDTOList)) {
            return;
        }

        //对查询结果进行分类，之所以按照Code做了Group，是为了避免出现一个Code下面多个保险的情况，但是现在只有一种，担心出现类似分期那种6期 12期这种情况
        Map<String /** skuId **/, Map<Integer /** code **/, List<InsuranceInfoDTO>>> codeInsuranceMap = insuranceInfoDTOList.stream()
                .collect(Collectors.groupingBy(info -> IdConvertor.idToUrl(info.getSkuId()),
                        Collectors.groupingBy(InsuranceInfoDTO::getInsuranceCode, Collectors.toList())));

        //把数据塞入到sku里面去，
        for (SkuData skuData : skuDO.getSkus()) {
            skuData.setInsuranceMap(codeInsuranceMap.get(skuData.getXdSkuId()));
        }
    }

    private void updateLimitStock(SkuDO skuDO, Long activityId) {
        List<Long> skuIdList = new ArrayList<>();
        for (SkuData skuData : skuDO.getSkus()) {
            skuIdList.add(IdConvertor.urlToId(skuData.getStockId()));
        }
        BatchActivityInventoryQueryParamV2 param = new BatchActivityInventoryQueryParamV2();
        param.setSkuIds(skuIdList);
        param.setChannelId(2014);
        param.setActivityId(activityId);
        MapResult<Long, ActivityInventoryV2> inventoryMapResult = inventoryReadService.batchQueryActivityInventoryV3(param);
        if (null != inventoryMapResult && inventoryMapResult.isSuccess()) {
            Map<Long, ActivityInventoryV2> inventoryMap = inventoryMapResult.getData();
            if (null != inventoryMap) {
                for (SkuData sku : skuDO.getSkus()) {
                    ActivityInventoryV2 inventory = inventoryMap.get(IdConvertor.urlToId(sku.getStockId()));
                    sku.setStock(null == inventory ? 0 : inventory.getStock());
                }
            }
        }
    }

    private LimitInfo getLimitInfo(String extra) {
        if (StringUtils.isEmpty(extra)) {
            return null;
        }
        try {
            Gson gson = new Gson();
            Map<String, String> extraInfo = gson.fromJson(extra, HashMap.class);
            if (null == extraInfo || extraInfo.isEmpty()) {
                return null;
            }
            String fl = extraInfo.get("fl");
            if (com.mogujie.metabase.utils.StringUtils.isEmpty(fl)) {
                return null;
            }
            String[] flPairs = fl.split("\\|");
            LimitInfo limitInfo = new LimitInfo();
            for (String flPair : flPairs) {
                String[] pair = flPair.split(":");
                if (pair.length != 2) {
                    continue;
                }
                if ("ai".equals(pair[0])) {
                    limitInfo.setActivityId(Long.parseLong(pair[1]));
                } else if ("xg".equals(pair[0])) {
                    limitInfo.setLimitTotalStock(Integer.parseInt(pair[1]));
                } else if ("st".equals(pair[0])) {
                    if ((System.currentTimeMillis() / 1000) < Integer.parseInt(pair[1])) {
                        return null;
                    }
                } else if ("et".equals(pair[0])) {
                    if ((System.currentTimeMillis() / 1000) > Integer.parseInt(pair[1])) {
                        return null;
                    }
                }
            }

            return limitInfo;
        } catch (Throwable e) {
            LOGGER.error("parse extra.tags failed : {}. {}", extra, e);
        }
        return null;
    }

    /**
     * 获取免息标/优惠标
     * @param countList 分期期数列表
     * @param resourceId 麦田资源位ID
     * @return 标图片链接
     */
    private Map<Integer, String> getTagIconMap(List<Integer> countList, Long resourceId) {
        Map<Integer, String> tagIconMap = Maps.newHashMap();
        //查询麦田免息标/优惠标资源位开关
        if (commonSwitchUtil.isOn(SwitchKey.SWT_CLOSE_INSTALLMENT_ICON) || countList.isEmpty()) {
            return tagIconMap;
        }
        if (commonSwitchUtil.isOn(SwitchKey.SWT_CLOSE_INSTALLMENT_ICON_DISCOUNT_TAG) && DISCOUNT_TAG.equals(resourceId)) {
            return tagIconMap;
        }

        List<Map<String, Object>> resources;
        if (FREE_INTEREST_TAG.equals(resourceId)) {
            resources = MaitUtil.getMaitData(FREE_INTEREST_TAG);
        } else {
                resources = MaitUtil.getTargetedMaitData(DISCOUNT_TAG);
        }
        if (CollectionUtils.isNotEmpty(resources)) {
            for (Map<String, Object> resource : resources) {
                int num = Integer.parseInt(String.valueOf(resource.get("num")));
                //有分期优惠标对应期数的素材，则返回对应标
                if (countList.contains(num)) {
                    tagIconMap.put(num, String.valueOf(resource.get("skuIcon")));
                }
            }
        }
        return tagIconMap;
    }


    private static class LimitInfo {

        /**
         * 限购总库存
         */
        @Getter
        @Setter
        private Integer limitTotalStock;

        /**
         * 活动id
         */
        @Getter
        @Setter
        private Long activityId;

    }


}
