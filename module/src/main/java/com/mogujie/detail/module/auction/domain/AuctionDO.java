package com.mogujie.detail.module.auction.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import com.mogujie.marketing.auction.enums.AuctionStatus;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * @AUTUOR: lengshan
 * @DESCRIPTION: 拍卖
 * @DATE: 2019/8/12 上午10:51
 */
public class AuctionDO implements ModuleDO {

    /**
     * 拍卖参与者
     * 拍卖未开始 = null，拍卖进行中 = 当前最高出价者，拍卖结束 = 最终得拍者
     */
    @Getter
    @Setter
    private Long userId;

    /**
     * 竞价时间(unix时间，单位秒)
     * 拍卖未开始 = null，拍卖进行中 = 出价时间，拍卖结束 = 最终成交时间
     */
    @Getter
    @Setter
    private Long biddingTime;

    /**
     * 竞价（单位到分）
     * 拍卖未开始 = null，拍卖进行中 = 最新出价，拍卖结束 = 最终成交价
     */
    @Getter
    @Setter
    private String biddingPrice;

    /**
     * 渠道库存
     */
    @Getter
    @Setter
    private Integer quantity;

    /**
     * 拍卖状态
     * 未开始:0; 进行中:10; 完成未付款:99; 完成已付款-拍卖最终状态:100; 流拍:-1; 拍卖结束未付款-拍卖最终状态:-100
     */
    @Getter
    @Setter
    private Integer status;

    /**
     * 拍卖期望结拍时间
     */
    @Getter
    @Setter
    private Long expectTime;

    /**
     * 是否为中拍者
     */

    private Boolean isWinner = false;

    public Boolean getIsWinner() {
        return isWinner;
    }

    public void setIsWinner(Boolean isWinner) {
        this.isWinner = isWinner;
    }

    /**
     * 下单资格使用状态，只有status = FINISH_UNPAID 时返回，true有资格，false没资格
     */
    private boolean haveOrderQual = false;

    public boolean getHaveOrderQual() {
        return haveOrderQual;
    }

    public void setHaveOrderQual(boolean haveOrderQual) {
        this.haveOrderQual = haveOrderQual;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        AuctionDO auctionDO = (AuctionDO) o;

        if (haveOrderQual != auctionDO.haveOrderQual) return false;
        if (userId != null ? !userId.equals(auctionDO.userId) : auctionDO.userId != null) return false;
        if (biddingTime != null ? !biddingTime.equals(auctionDO.biddingTime) : auctionDO.biddingTime != null)
            return false;
        if (biddingPrice != null ? !biddingPrice.equals(auctionDO.biddingPrice) : auctionDO.biddingPrice != null)
            return false;
        if (quantity != null ? !quantity.equals(auctionDO.quantity) : auctionDO.quantity != null) return false;
        if (status != null ? !status.equals(auctionDO.status) : auctionDO.status != null) return false;
        if (expectTime != null ? !expectTime.equals(auctionDO.expectTime) : auctionDO.expectTime != null) return false;
        return isWinner != null ? isWinner.equals(auctionDO.isWinner) : auctionDO.isWinner == null;
    }

    @Override
    public int hashCode() {
        int result = userId != null ? userId.hashCode() : 0;
        result = 31 * result + (biddingTime != null ? biddingTime.hashCode() : 0);
        result = 31 * result + (biddingPrice != null ? biddingPrice.hashCode() : 0);
        result = 31 * result + (quantity != null ? quantity.hashCode() : 0);
        result = 31 * result + (status != null ? status.hashCode() : 0);
        result = 31 * result + (expectTime != null ? expectTime.hashCode() : 0);
        result = 31 * result + (isWinner != null ? isWinner.hashCode() : 0);
        result = 31 * result + (haveOrderQual ? 1 : 0);
        return result;
    }

    @Override
    public String toString() {
        return "AuctionDO{" +
                "userId=" + userId +
                ", biddingTime=" + biddingTime +
                ", biddingPrice=" + biddingPrice +
                ", quantity=" + quantity +
                ", status=" + status +
                ", expectTime=" + expectTime +
                ", isWinner=" + isWinner +
                '}';
    }
}
