package com.mogujie.detail.module.shop.domain;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @AUTUOR: lengshan
 * @DESCRIPTION: 店铺入口标签
 * @DATE: 2020/2/3 下午1:45
 */
public class ShopLabel implements Serializable {

    private static final long serialVersionUID = 760779656961779064L;


    @Setter
    @Getter
    private String name;


    @Setter
    @Getter
    private Integer sort;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ShopLabel shopLabel = (ShopLabel) o;

        if (name != null ? !name.equals(shopLabel.name) : shopLabel.name != null) return false;
        return sort != null ? sort.equals(shopLabel.sort) : shopLabel.sort == null;
    }

    @Override
    public int hashCode() {
        int result = name != null ? name.hashCode() : 0;
        result = 31 * result + (sort != null ? sort.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "ShopLabelEnum{" +
                "name='" + name + '\'' +
                ", sort=" + sort +
                '}';
    }
}
