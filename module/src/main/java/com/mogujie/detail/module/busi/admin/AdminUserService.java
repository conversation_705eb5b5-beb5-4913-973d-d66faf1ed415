package com.mogujie.detail.module.busi.admin;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.RetData;
import com.mogujie.detail.core.adt.Status;
import com.mogujie.detail.core.constant.StatusCode;
import com.mogujie.detail.core.constant.SwitchKey;
import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.core.util.StrategyUpUtil;
import com.mogujie.service.category2.api.CategoryService;
import com.mogujie.service.cpcstandardjudge.common.api.CpcStandardJudge;
import com.mogujie.service.cpcstandardjudge.common.collections.ResultSupport;
import com.mogujie.service.item.api.basic.ItemPreSaleService;
import com.mogujie.service.item.api.basic.ItemReadService;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.basic.ItemPreSaleDO;
import com.mogujie.service.item.domain.external.IcException;
import com.mogujie.service.item.domain.query.QueryItemOptions;
import com.mogujie.service.item.search.api.ItemSearchService;
import com.mogujie.service.item.search.domain.ItemSearchResult;
import com.mogujie.service.item.search.domain.query.ItemSearchQuery;
import com.mogujie.service.itemaudit.api.firstaudit.TradeItemAuditResultService;
import com.mogujie.service.itemaudit.domain.TradeItemAuditResultQuery;
import com.mogujie.service.itemaudit.domain.entity.firstaudit.TradeItemAuditResult;
import com.mogujie.service.itemaudit.tool.DefaultConfig;
import com.mogujie.service.rate.api.RateDsrService;
import com.mogujie.service.rate.domain.ItemDsr;
import com.mogujie.service.rate.util.RateUtils;
import com.mogujie.service.tagcenter.api.read.ItemTagReadService;
import com.mogujie.service.tagcenter.domain.entity.query.ItemTagQueryOption;
import com.mogujie.service.tagcenter.domain.entity.result.BaseResultDO;
import com.mogujie.service.tagcenter.domain.entity.result.ItemTagDO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @auther huasheng
 * @time 19/3/26 11:52
 */

@Component
public class AdminUserService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AdminUserService.class);


    private ItemTagReadService itemTagReadService;

    private ItemSearchService itemSearchService;

    private CategoryService categoryService;

    private RateDsrService dsrService;

    private ItemReadService itemReadService;

    private TradeItemAuditResultService auditResultService;

    private CpcStandardJudge cpcStandardJudge;

    protected ItemPreSaleService itemPreSaleService;

    @Autowired
    private CommonSwitchUtil commonSwitchUtil;

    public AdminUserService() throws Exception {
        itemTagReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ItemTagReadService.class);
        itemSearchService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ItemSearchService.class);
        categoryService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(CategoryService.class);
        dsrService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(RateDsrService.class);
        itemReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ItemReadService.class);
        auditResultService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(TradeItemAuditResultService.class);
        cpcStandardJudge = TeslaServiceConsumerFactory.getTeslaServiceConsumer(CpcStandardJudge.class);
        itemPreSaleService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ItemPreSaleService.class);
    }

    /**
     * 得到管理员的商品信息
     *
     * @param iid
     * @return
     */
    public RetData<Map<String, Object>> getAdminItemInfo(String iid) {
        RetData<Map<String, Object>> ret = new RetData();
        Map<String, Object> data = new HashMap<>();

        ItemDO item = getItemDO(IdConvertor.urlToId(iid));
        data.put("xdItemId", item.getXdItemId());
        data.put("tradeItemId", item.getItemId());
        data.put("shopId", item.getShopId());
        data.put("userId", item.getUserId());
        //商品标签

        ItemTagQueryOption option = new ItemTagQueryOption();
        option.setItemId(item.getItemId());
        option.setDirectQueryDB(false);
        BaseResultDO<List<ItemTagDO>> tagRet = itemTagReadService.queryItemTag(option);
        if (null != tagRet && CollectionUtils.isNotEmpty(tagRet.getResult())) {
            List<String> tags = new ArrayList<>();
            for (ItemTagDO tag : tagRet.getResult()) {
                tags.add(tag.getTagValue());
            }
            String tagsInTagcenter = Joiner.on(",").join(tags);
            data.put("tags", tagsInTagcenter);
        }


        data.put("snapListUrl", StrategyUpUtil.upUrl((commonSwitchUtil.isOn(SwitchKey.SWT_NEW_SNAP_LIST)
                ? "https://pc.mogu.com/detail/snaplist.html?itemId="
                : "https://www.mogujie.com/trade/item/snaplist/")
                + iid));
        //上架时间
        ItemSearchQuery itemSearchQuery = new ItemSearchQuery();
        itemSearchQuery.setTradeItemIds(Lists.newArrayList(item.getItemId()));
        ItemSearchResult itemSearchResult = itemSearchService.queryItems(itemSearchQuery);
        if (CollectionUtils.isNotEmpty(itemSearchResult.getDocs())) {
            long onlineTime = itemSearchResult.getDocs().get(0).getLastOnlineTime_it();
            data.put("onlineTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(1000 * onlineTime)));
        }

        Map<Integer, String> category = categoryService.mapCategoryPathName(Arrays.asList(item.getCategoryId()));
        if (category != null && StringUtils.isNotEmpty(category.get(item.getCategoryId()))) {
            String categoryStr = StringUtils.replace(StringUtils.remove(category.get(item.getCategoryId()), "#"), " ", "-> ");
            data.put("category", categoryStr);
        }

        TradeItemAuditResultQuery query = new TradeItemAuditResultQuery();
        query.setTradeItemId(IdConvertor.urlToId(iid));
        query.setPageIndex(1);
        query.setPageSize(1);
        query.setOrderBy("updated");
        query.setSort("DESC");
        List<TradeItemAuditResult> auditResults = auditResultService.queryAuditResult(query);
        if (CollectionUtils.isNotEmpty(auditResults)) {
            Integer status = auditResults.get(0).getStatus();
            data.put("auditStatus", DefaultConfig.statusConfig.get(status));
        }
        //商品DSR数据
        ItemDsr itemDsr = dsrService.getItemDsr(item.getItemId());
        if (itemDsr != null) {
            Map<String, Float> dsrMap = new HashMap<>();
            dsrMap.put("quality", itemDsr.getQuality());
            dsrMap.put("desc", itemDsr.getDesc());
            dsrMap.put("avg", RateUtils.getDsrAvg(Lists.newArrayList(itemDsr.getQuality(), itemDsr.getDesc())).floatValue() / 100);
            data.put("dsr", dsrMap);
        }

        int processType = item.getProcessType();
        if (processType == 1) { // 非预售商品
            ItemPreSaleDO itemPreSale = itemPreSaleService.queryByItemId(item.getItemId());
            if (itemPreSale != null) {
                Map<String, Object> presaleInfo = new HashMap<>();
                presaleInfo.put("start", itemPreSale.getStart());
                presaleInfo.put("end", itemPreSale.getEnd());
                presaleInfo.put("presaleId", itemPreSale.getPresaleId());
                data.put("presale", presaleInfo);
            }
        }

        data.put("cpc", 0);
        try {
            Map<String, Map<String, Long>> itemIndoParam = new HashMap<>();
            Map<String, Long> infoMap = new HashMap<>();
            infoMap.put("tradeItemId", item.getItemId());
            infoMap.put("price", 0L);
            infoMap.put("saleThirty_it", 0L);
            itemIndoParam.put(item.getItemId() + "", infoMap);
            ResultSupport resultSupport = cpcStandardJudge.getCpsCommissionInfo(itemIndoParam);
            if (resultSupport != null && resultSupport.getSuccess() && resultSupport.getResult() != null) {
                Map<String, Object> resultMap = (Map<String, Object>) resultSupport.getResult();
                Map<String, Long> cpcMap = (Map<String, Long>) resultMap.get(item.getItemId() + "");
                data.put("cpc", cpcMap.get("commission_it"));
            }
        } catch (Throwable e) {
        }
        ret.setResult(data);
        ret.setStatus(new Status(StatusCode.SUCCESS, ""));
        return ret;
    }

    private ItemDO getItemDO(Long itemId) {
        QueryItemOptions queryItemOptions = new QueryItemOptions();
        queryItemOptions.setIncludeItemExtraInfo(true);
        queryItemOptions.setQueryBasicItem(true);
        queryItemOptions.setQueryInventory(true);
        queryItemOptions.setQuerySkuInfo(true);
        queryItemOptions.setIncludeDeleted(false);
        queryItemOptions.setQueryItemDetail(true);
        queryItemOptions.setQueryPresaleDO(false);
        queryItemOptions.setQuerySkuAttribute(true);
        queryItemOptions.setQueryTgrcStock(true);
        queryItemOptions.setQueryExpressTmpl(true);
        queryItemOptions.setQueryItemIdPropertyMap(true);
        queryItemOptions.setQueryItemTag(true);
        try {
            com.mogujie.service.item.domain.result.BaseResultDO<ItemDO> baseResultDO = itemReadService.queryItemById(itemId, queryItemOptions);
            if (baseResultDO != null && baseResultDO.isSuccess() && baseResultDO.getResult() != null) {
                return baseResultDO.getResult();
            } else {
                LOGGER.debug("item not found : {}", itemId);
                return null;
            }
        } catch (IcException e) {
            LOGGER.debug("ic error!", e);
            return null;
        }
    }
}
