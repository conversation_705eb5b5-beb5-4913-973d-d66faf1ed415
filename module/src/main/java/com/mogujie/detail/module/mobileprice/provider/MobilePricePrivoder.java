package com.mogujie.detail.module.mobileprice.provider;

import com.mogujie.commons.utils.EnvUtil;
import com.mogujie.darwin.util.CollectionUtil;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.constant.SwitchKey;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.core.util.MaitUtil;
import com.mogujie.detail.core.util.NumUtil;
import com.mogujie.detail.module.activity.util.ActivityUtil;
import com.mogujie.detail.module.mobileprice.domain.MobilePriceDO;
import com.mogujie.service.hummer.api.PromotionReadService;
import com.mogujie.service.hummer.constains.RequestConstants;
import com.mogujie.service.hummer.domain.dto.*;
import com.mogujie.service.hummer.domain.dto.result.Result;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.session.SessionContextHolder;
import com.mogujie.stable.spirit.EntryUtil;
import com.mogujie.stable.spirit.exception.BlockException;
import com.mogujie.stable.spirit.limit.Entry;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import com.mogujie.tesla.common.CollectionUtils;
import com.mogujie.tesla.core.ReferConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;

/**
 * @auther huasheng
 * @time 19/3/7 17:32
 */
@Module(name = "mobilePrice")
public class MobilePricePrivoder implements IModuleDOProvider<MobilePriceDO> {

    private static final Logger LOGGER = LoggerFactory.getLogger(MobilePricePrivoder.class);


    private PromotionReadService promotionReadService;

    @Autowired
    private CommonSwitchUtil commonSwitchUtil;

    @Override
    public MobilePriceDO emit(DetailContext context) {
        String mobilePrice = getMobilePrice(context);

        List<Map<String, Object>> resources = MaitUtil.getMaitData(ActivityUtil.H5_DOWNLOAD_BANNER_CODE);
        String downloadBanner = getH5DownloadBanner(resources);

        MobilePriceDO mobilePriceDO=new MobilePriceDO();
        if (null != mobilePrice && null != downloadBanner) {
            mobilePriceDO.setMobileDownloadLink(downloadBanner);
            mobilePriceDO.setMobilePrice(mobilePrice);
        }
        return mobilePriceDO;
    }

    @Override
    public void init() throws DetailException {
        try {
            ReferConfig<PromotionReadService> referConfig = new ReferConfig<>(PromotionReadService.class);
            if (EnvUtil.isOnlineEnv()) {
                referConfig.setGroup("DETAIL-GROUP");
            }
            referConfig.setTimeout(200);
            this.promotionReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(referConfig);
        } catch (Exception e) {
            throw new DetailException(e);
        }
    }

    public String getH5DownloadBanner(List<Map<String, Object>> resources) {
        if (CollectionUtils.isEmpty(resources)) {
            return null;
        }
        Map<String, Object> res = resources.get(0);
        String link = Objects.toString(res.get("midLink"),"");
        return link;
    }



    private String getMobilePrice(DetailContext context) {
        if (!commonSwitchUtil.isOn(SwitchKey.USE_MOBILE_PRICE)) {
            return null;
        }
        DetailItemDO item = context.getItemDO();

        Map<Long, Long> originSkuMap = getSkuPriceMap(item);
        ItemDetailRequestV2 request = new ItemDetailRequestV2();
        Pbuyer pbuyer = new Pbuyer();
        pbuyer.setBuyerId(SessionContextHolder.getUserId());
        Pseller pSeller = new Pseller();
        pSeller.setSellerId(item.getUserId());
        PitemDetail pitemDetail = new PitemDetail();
        pitemDetail.setExtra(item.getJsonExtra());
        pitemDetail.setItemId(item.getItemId());
        pitemDetail.setSkuPriceMap(originSkuMap);
        pitemDetail.setItemPrice(getHighestPrice(item.getItemSkuDOList()));
        InvokeInfo invokeInfo = new InvokeInfo();
        invokeInfo.setChannel((int) RequestConstants.Channel.UNKNOW);
        invokeInfo.setMarket((int) RequestConstants.Market.MOGUJIE);
        invokeInfo.setSource(RequestConstants.Source.DETAIL);
        invokeInfo.setTerminal(RequestConstants.Terminal.APP);
        request.setPitemDetail(pitemDetail);
        request.setSeller(pSeller);
        request.setPbuyer(pbuyer);
        request.setInvokeInfo(invokeInfo);

        Entry entry = null;
        Result<ItemDetailPromotion> ret = null;
        try {
            entry = EntryUtil.entry("com.mogujie.service.hummer.api.PromotionReadService:calcForItemDetailPromotion");
            ret = promotionReadService.calcForItemDetailPromotion(request);
        } catch (BlockException e) {
        } catch (Throwable e) {
            LOGGER.error("tesla calling failed. ", e);
        } finally {
            if (entry != null) {
                entry.exit();
            }
        }
        if (null != ret && ret.isSuccess() && null != ret.getData()) {
            Long mobilePrice = getLowestPrice(ret.getData().getSkuRealPriceMap().values());
            if (null != mobilePrice && mobilePrice < BigDecimal.valueOf(Double.parseDouble(item.getLowNowPrice()) * 100).longValue()) {
                return NumUtil.formatNum(mobilePrice / 100D);
            }
        }
        return null;
    }

    private Long getLowestPrice(final Collection<Long> priceList) {
        if (CollectionUtil.isEmpty(priceList)) {
            return 0L;
        }
        Long lowestPrice = priceList.iterator().next();
        for (Long price : priceList) {
            if (price < lowestPrice) {
                lowestPrice = price;
            }
        }
        return lowestPrice;
    }

    private Map<Long, Long> getSkuPriceMap(ItemDO item) {
        Map<Long, Long> skuPriceMap = new HashMap<>();
        for (ItemSkuDO sku : item.getItemSkuDOList()) {
            skuPriceMap.put(sku.getSkuId(), sku.getPrice().longValue());
        }
        return skuPriceMap;
    }

    private Long getHighestPrice(final List<ItemSkuDO> skuList) {
        if (CollectionUtil.isEmpty(skuList)) {
            return 0L;
        }
        Long highestPrice = skuList.get(0).getPrice().longValue();
        for (ItemSkuDO sku : skuList) {
            if (sku.getPrice().longValue() > highestPrice) {
                highestPrice = sku.getPrice().longValue();
            }
        }
        return highestPrice;
    }
}
