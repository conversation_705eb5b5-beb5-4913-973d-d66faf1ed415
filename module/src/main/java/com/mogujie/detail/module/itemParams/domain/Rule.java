package com.mogujie.detail.module.itemParams.domain;

import com.alibaba.druid.util.StringUtils;
import com.mogujie.metabase.utils.CollectionUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 尺码说明
 * Created by <PERSON><PERSON><PERSON> on 15/12/7.
 */
public class Rule implements Serializable {
    private static final long serialVersionUID = -3455793419160539987L;
    public static final String SIZE_TABLE_NAME = "尺码";
    public static final Integer TABLE_SIZE = 5;
    public static final String DETAIL_MODULE_NAME = "size_info";

    /**
     * 尺码说明的描述
     */
    private String desc;
    /**
     * 尺码说明的表格
     */
    private List<List<List<String>>> tables;
    /**
     * 图片
     */
    private List<String> images;
    /**
     * 模块名
     */
    private String key;
    /**
     * 锚点, 用于pc详情页,一般为: size_info
     */
    private String anchor;

    /**
     * 免责声明
     */
    private String disclaimer;

    public Rule() {
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public List<List<List<String>>> getTables() {
        return tables;
    }

    public void setTables(List<List<List<String>>> tables) {
        this.tables = tables;
    }

    public List<String> getImages() {
        return images;
    }

    public void setImages(List<String> images) {
        this.images = images;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getAnchor() {
        return anchor;
    }

    public void setAnchor(String anchor) {
        this.anchor = anchor;
    }

    public String getDisclaimer() {
        return disclaimer;
    }

    public void setDisclaimer(String disclaimer) {
        this.disclaimer = disclaimer;
    }

    public boolean isEmpty() {
        if (!StringUtils.isEmpty(desc) || !CollectionUtils.isEmpty(tables) || !CollectionUtils.isEmpty(images)) {
            return false;
        } else {
            return true;
        }
    }

    @Override
    public String toString() {
        return "Rule{" +
                "desc='" + desc + '\'' +
                ", tables=" + tables +
                ", images=" + images +
                ", key='" + key + '\'' +
                ", anchor='" + anchor + '\'' +
                ", disclaimer='" + disclaimer + '\'' +
                '}';
    }
}
