package com.mogujie.detail.module.rate.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by <PERSON><PERSON><PERSON> on 15/11/25.
 */
public class RateUtil {

    private static Pattern pattern = Pattern.compile("(^mobile_)|(^qq_)|(^sina_)|(^tsina_)|(^weixin_)|([0-9]{8})", Pattern.CASE_INSENSITIVE);

    public static String hideUname(String uname) {
        if (!validate(uname)) {
            return "***";
        }
        return uname.charAt(0) + "***" + uname.charAt(uname.length() - 1);
    }


    /**
     * 隐藏手机号，qq号，微信号，新浪号
     * @param uname
     * @return
     */
    public static String hideUnameMobile(String uname) {
        if (!validate(uname)) {
            return "***";
        }

        Matcher matcher = pattern.matcher(uname);

        if (matcher.find()) {
            return RateUtil.hideUname(uname);
        }
        return uname;
    }


    private static boolean validate(String obj) {
        return !(obj == null || obj.trim().isEmpty());
    }
}