package com.mogujie.detail.module.groupbuying.util;

import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.util.TagUtil;
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO;

import java.util.Map;

/**
 * Created by anshi on 17/3/31.
 */
public class GroupbuyingUtil {

    public static boolean isUZhiItem(DetailItemDO itemDO) {
        Map<String, String> extraInfo = TagUtil.getExtraInfo(itemDO.getJsonExtra());
        if (null == extraInfo) {
            return false;
        }

        String hdInfoStr = extraInfo.get("utg");
        if (hdInfoStr == null) {
            return false;
        }
        String[] hdInfoPairs = hdInfoStr.split("\\|");
        if (hdInfoPairs == null || hdInfoPairs.length < 1) {
            return false;
        }
        Long nowTime = System.currentTimeMillis() / 1000;
        GroupbuyingDO groupbuyingDO = new GroupbuyingDO();
        for (String hdInfoPair : hdInfoPairs) {
            String[] hd = hdInfoPair.split(":");
            if (hd.length != 2) {
                continue;
            }
            if ("st".equals(hd[0])) {
                groupbuyingDO.setStartTime(Long.parseLong(hd[1]));
            } else if ("et".equals(hd[0])) {
                groupbuyingDO.setEndTime(Long.parseLong(hd[1]));
            }
        }

        if (groupbuyingDO.getEndTime() == null || groupbuyingDO.getStartTime() == null) {
            return false;
        }
        if (groupbuyingDO.getStartTime() < nowTime && nowTime < groupbuyingDO.getEndTime()) {
            return true;
        }
        return false;
    }


}
