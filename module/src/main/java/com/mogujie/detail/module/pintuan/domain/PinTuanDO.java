package com.mogujie.detail.module.pintuan.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import com.mogujie.detail.module.sku.domain.SkuDO;

/**
 * Created by <PERSON><PERSON>oya<PERSON> on 17/3/24.
 */
public class PinTuanDO implements ModuleDO {

    /**
     * 开团数量
     */
    private int tuanNum;

    /**
     * 是否新人团
     */
    private boolean isNew;

    /**
     * 剩余时间
     */
    private long remainTime;

    /**
     * 开始时间
     */
    private long startTime;

    /**
     * 结束时间
     */
    private long endTime;

    /**
     * 成功拼团数量
     */
    private int successTuanNum;

    /**
     * 是否过期
     */
    private boolean isExpire;

    /**
     * 是否限购
     */
    private boolean isLimit;

    /**
     * 限购数量
     */
    private int limitNum;

    /**
     * 团类型,新人团:1, 普通团:2, 抽奖团:3
     */
    private Integer tuanType;

    /**
     * 抽奖状态,未开奖:1, 抽奖中:2, 已开奖:3
     */
    private Integer lotteryProcess;

    /**
     * 活动可中奖人数
     */
    private Integer awardNum;

    private SkuDO skuInfo;

    /**
     * 促销渠道类型
     */
    private Integer outType;

    /**
     * 优惠券团是否已达到最大可领取数
     * true:表示用户已不可再领取
     */
    private Boolean isCouponLimited;

    /**
     * 免单开团次数
     */
    private Integer couponFreeNum;

    /**
     * 是否在类似双11的预热和正式期间
     */
    private boolean inC1Activity;

    /**
     * 超级团预热价格
     */
    private Integer activityPrice;

    /**
     * 拼团预热价格：最低价
     */
    private Integer lowActivityPrice;

    /**
     * 拼团预热价格：最高价
     */
    private Integer highActivityPrice;

    /**
     * 是否为招商拼团
     */
    private Boolean isSystem;

    /**
     * 活动id
     */
    private String activityId;


    public Integer getTuanType() {
        return tuanType;
    }

    public void setTuanType(Integer tuanType) {
        this.tuanType = tuanType;
    }

    public Integer getLotteryProcess() {
        return lotteryProcess;
    }

    public void setLotteryProcess(Integer lotteryProcess) {
        this.lotteryProcess = lotteryProcess;
    }

    public Integer getAwardNum() {
        return awardNum;
    }

    public void setAwardNum(Integer awardNum) {
        this.awardNum = awardNum;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public int getTuanNum() {
        return tuanNum;
    }

    public void setTuanNum(int tuanNum) {
        this.tuanNum = tuanNum;
    }

    public boolean isNew() {
        return isNew;
    }

    public void setNew(boolean aNew) {
        isNew = aNew;
    }

    public long getRemainTime() {
        return remainTime;
    }

    public void setRemainTime(long remainTime) {
        this.remainTime = remainTime;
    }

    public int getSuccessTuanNum() {
        return successTuanNum;
    }

    public void setSuccessTuanNum(int successTuanNum) {
        this.successTuanNum = successTuanNum;
    }

    public boolean isExpire() {
        return isExpire;
    }

    public void setExpire(boolean expire) {
        isExpire = expire;
    }

    public SkuDO getSkuInfo() {
        return skuInfo;
    }

    public void setSkuInfo(SkuDO skuInfo) {
        this.skuInfo = skuInfo;
    }


    public boolean isLimit() {
        return isLimit;
    }

    public void setLimit(boolean limit) {
        isLimit = limit;
    }

    public int getLimitNum() {
        return limitNum;
    }

    public void setLimitNum(int limitNum) {
        this.limitNum = limitNum;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public Integer getOutType() {
        return outType;
    }

    public void setOutType(Integer outType) {
        this.outType = outType;
    }

    public Boolean getCouponLimited() {
        return isCouponLimited;
    }

    public void setCouponLimited(Boolean couponLimited) {
        isCouponLimited = couponLimited;
    }

    public Integer getCouponFreeNum() {
        return couponFreeNum;
    }

    public void setCouponFreeNum(Integer couponFreeNum) {
        this.couponFreeNum = couponFreeNum;
    }

    public boolean isInC1Activity() {
        return inC1Activity;
    }

    public void setInC1Activity(boolean inC1Activity) {
        this.inC1Activity = inC1Activity;
    }

    public Integer getActivityPrice() {
        return activityPrice;
    }

    public void setActivityPrice(Integer activityPrice) {
        this.activityPrice = activityPrice;
    }

    public Integer getLowActivityPrice() {
        return lowActivityPrice;
    }

    public void setLowActivityPrice(Integer lowActivityPrice) {
        this.lowActivityPrice = lowActivityPrice;
    }

    public Integer getHighActivityPrice() {
        return highActivityPrice;
    }

    public void setHighActivityPrice(Integer highActivityPrice) {
        this.highActivityPrice = highActivityPrice;
    }

    public Boolean getSystem() {
        return isSystem;
    }

    public void setSystem(Boolean system) {
        isSystem = system;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }
}
