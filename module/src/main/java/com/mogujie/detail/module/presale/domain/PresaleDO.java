package com.mogujie.detail.module.presale.domain;

import com.mogujie.detail.core.adt.ModuleDO;

/**
 * Created by xiaoyao on 16/10/24.
 */
public class PresaleDO implements ModuleDO {

    /**
     * 总价
     */
    private String totalPrice;

    /**
     * 订金
     */
    private String deposit;

    /**
     * 膨胀金
     */
    private String expandMoney;

    /**
     * 付定金时间
     */
    private String presaleDate;

    /**
     * 订金结束时间
     */
    private String presaleEndDate;

    /**
     * 预售开始时间
     */
    private Integer startTime;

    /**
     * 预售结束时间
     */
    private Integer endTime;

    /**
     * 尾款开始时间
     */
    private Integer payStartTime;

    /**
     * 尾款结束时间
     */
    private Integer payEndTime;

    /**
     * 描述文案
     */
    private String presaleDesc;

    /**
     * 预售规则标题文案
     */
    private String rule;

    /**
     * 预售的具体规则
     */
    private RuleDesc ruleDesc;

    /**
     * 预售小图标
     */
    private String titleIcon;

    /**
     * 倒计时icon;
     */
    private String countdownIcon;

    private String deductionDesc;

    private long salesNum;

    public String getDeductionDesc() {
        return deductionDesc;
    }

    public void setDeductionDesc(String deductionDesc) {
        this.deductionDesc = deductionDesc;
    }

    public String getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(String totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getDeposit() {
        return deposit;
    }

    public void setDeposit(String deposit) {
        this.deposit = deposit;
    }

    public String getPresaleDate() {
        return presaleDate;
    }

    public void setPresaleDate(String presaleDate) {
        this.presaleDate = presaleDate;
    }

    public String getPresaleDesc() {
        return presaleDesc;
    }

    public void setPresaleDesc(String presaleDesc) {
        this.presaleDesc = presaleDesc;
    }

    public String getRule() {
        return rule;
    }

    public void setRule(String rule) {
        this.rule = rule;
    }

    public String getTitleIcon() {
        return titleIcon;
    }

    public void setTitleIcon(String titleIcon) {
        this.titleIcon = titleIcon;
    }

    public String getPresaleEndDate() {
        return presaleEndDate;
    }

    public void setPresaleEndDate(String presaleEndDate) {
        this.presaleEndDate = presaleEndDate;
    }

    public RuleDesc getRuleDesc() {
        return ruleDesc;
    }

    public void setRuleDesc(RuleDesc ruleDesc) {
        this.ruleDesc = ruleDesc;
    }

    public String getCountdownIcon() {
        return countdownIcon;
    }

    public void setCountdownIcon(String countdownIcon) {
        this.countdownIcon = countdownIcon;
    }

    public long getSalesNum() {
        return salesNum;
    }

    public void setSalesNum(long salesNum) {
        this.salesNum = salesNum;
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public Integer getPayStartTime() {
        return payStartTime;
    }

    public void setPayStartTime(Integer payStartTime) {
        this.payStartTime = payStartTime;
    }

    public Integer getPayEndTime() {
        return payEndTime;
    }

    public void setPayEndTime(Integer payEndTime) {
        this.payEndTime = payEndTime;
    }

    public String getExpandMoney() {
        return expandMoney;
    }

    public void setExpandMoney(String expandMoney) {
        this.expandMoney = expandMoney;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        PresaleDO preSale = (PresaleDO) o;

        if (totalPrice != null ? !totalPrice.equals(preSale.totalPrice) : preSale.totalPrice != null) return false;
        if (deposit != null ? !deposit.equals(preSale.deposit) : preSale.deposit != null) return false;
        if (expandMoney != null ? !expandMoney.equals(preSale.expandMoney) : preSale.expandMoney != null) return false;
        if (presaleDate != null ? !presaleDate.equals(preSale.presaleDate) : preSale.presaleDate != null) return false;
        if (presaleEndDate != null ? !presaleEndDate.equals(preSale.presaleEndDate) : preSale.presaleEndDate != null)
            return false;
        if (presaleDesc != null ? !presaleDesc.equals(preSale.presaleDesc) : preSale.presaleDesc != null) return false;
        if (rule != null ? !rule.equals(preSale.rule) : preSale.rule != null) return false;
        if (ruleDesc != null ? !ruleDesc.equals(preSale.ruleDesc) : preSale.ruleDesc != null) return false;
        return titleIcon != null ? titleIcon.equals(preSale.titleIcon) : preSale.titleIcon == null;

    }

    @Override
    public int hashCode() {
        int result = totalPrice != null ? totalPrice.hashCode() : 0;
        result = 31 * result + (deposit != null ? deposit.hashCode() : 0);
        result = 31 * result + (expandMoney != null ? expandMoney.hashCode() : 0);
        result = 31 * result + (presaleDate != null ? presaleDate.hashCode() : 0);
        result = 31 * result + (presaleEndDate != null ? presaleEndDate.hashCode() : 0);
        result = 31 * result + (presaleDesc != null ? presaleDesc.hashCode() : 0);
        result = 31 * result + (rule != null ? rule.hashCode() : 0);
        result = 31 * result + (ruleDesc != null ? ruleDesc.hashCode() : 0);
        result = 31 * result + (titleIcon != null ? titleIcon.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "PreSale{" +
                "totalPrice='" + totalPrice + '\'' +
                ", deposit='" + deposit + '\'' +
                ", expandMoney='" + expandMoney + '\'' +
                ", presaleDate='" + presaleDate + '\'' +
                ", presaleEndDate='" + presaleEndDate + '\'' +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", payStartTime=" + payStartTime +
                ", payEndTime=" + payEndTime +
                ", presaleDesc='" + presaleDesc + '\'' +
                ", rule='" + rule + '\'' +
                ", ruleDesc=" + ruleDesc +
                ", titleIcon='" + titleIcon + '\'' +
                ", countdownIcon='" + countdownIcon + '\'' +
                ", deductionDesc='" + deductionDesc + '\'' +
                ", salesNum=" + salesNum +
                '}';
    }

}
