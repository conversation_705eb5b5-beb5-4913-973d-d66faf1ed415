package com.mogujie.detail.module.buyuser.provider;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.constant.SwitchKey;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.module.buyuser.domain.BuyuserInfoDO;
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO;
import com.mogujie.member.api.planet.PlanetUserService;
import com.mogujie.member.domain.enumtype.AppType;
import com.mogujie.member.domain.enumtype.ModelType;
import com.mogujie.member.domain.model.RpcResult;
import com.mogujie.member.domain.model.user.UserModelInfo;
import com.mogujie.service.muser.Result;
import com.mogujie.service.muser.api.UserService;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 当前买家相关信息提供
 *
 * @auther huasheng
 * @time 19/3/13 14:55
 */
@Module(name = "buyuserInfo")
public class BuyuserInfoProvider implements IModuleDOProvider<BuyuserInfoDO> {

    private UserService userService;
    private PlanetUserService planetUserService;

    private static final Logger LOGGER = LoggerFactory.getLogger(FastbuyDO.class);

    @Override
    public void init() throws DetailException {
        try {
            planetUserService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(PlanetUserService.class);
            userService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(UserService.class);
        } catch (Exception e) {
            LOGGER.error("init service failed : {}", e);
        }
    }


    @Autowired
    private CommonSwitchUtil commonSwitchUtil;

    @Override
    public BuyuserInfoDO emit(DetailContext context) {
        BuyuserInfoDO buyuserInfoDO = new BuyuserInfoDO();

        isAdmin(context, buyuserInfoDO);

        isNewComer(context, buyuserInfoDO);

        return buyuserInfoDO;
    }

    private void isNewComer(DetailContext context, BuyuserInfoDO buyuserInfoDO) {
        try {
            if(context.getLoginUserId()==null){
                return;
            }
            RpcResult<UserModelInfo> result = planetUserService.getUserModelInfo(AppType.MOGUJIE.getCode(), context.getLoginUserId(), ModelType.WHETHER_BUY.getCode());
            if (null != result && result.isSuccess() && null != result.getValue()) {
                boolean isNewComer = result.getValue().getModelValue().equals("L1");
                buyuserInfoDO.setNewComer(isNewComer);
            }
        } catch (Throwable e) {
            LOGGER.error("rpc call isNewPerson failed : {}", e);
        }
    }

    private void isAdmin(DetailContext context, BuyuserInfoDO buyuserInfoDO) {
        if (null != context.getLoginUserId() && commonSwitchUtil.isOn(SwitchKey.MUSER_IS_ADMIN)) {
            Result<Boolean> ret = userService.isAdmin(context.getLoginUserId());
            if (null != ret && ret.getValue()) {
                buyuserInfoDO.setAdmin(true);
            }
        }
    }


}
