package com.mogujie.detail.module.mycoupon.provider;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.meili.service.shopcenter.util.ShopInfoTagsUtil;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.Platform;
import com.mogujie.detail.core.constant.SwitchKey;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.core.util.ContextUtil;
import com.mogujie.detail.core.util.MaitUtil;
import com.mogujie.detail.module.mycoupon.domain.MyCoupon;
import com.mogujie.detail.module.mycoupon.domain.MyCouponDO;
import com.mogujie.detail.module.mycoupon.domain.MyMoDouCoupon;
import com.mogujie.detail.module.mycoupon.domain.ParameterKey;
import com.mogujie.service.diana.DianaLaunchService;
import com.mogujie.service.diana.dto.ItemProTagDTO;
import com.mogujie.service.diana.query.QueryContext;
import com.mogujie.service.diana.result.NatashaResult;
import com.mogujie.service.hummer.api.BonusReadService;
import com.mogujie.service.hummer.api.BuyerResourceReadService;
import com.mogujie.service.hummer.constains.PromotionConstants;
import com.mogujie.service.hummer.constains.RequestConstants;
import com.mogujie.service.hummer.domain.dto.BuyerResourceDTO;
import com.mogujie.service.hummer.domain.dto.BuyerResourceQuery;
import com.mogujie.service.hummer.domain.dto.PlatformCouponDTO;
import com.mogujie.service.hummer.domain.dto.ResourcePoolDTO;
import com.mogujie.service.hummer.domain.dto.UserAccountDTO;
import com.mogujie.service.hummer.domain.dto.UserAccountQuery;
import com.mogujie.service.hummer.domain.dto.compatible.CampaignDTO;
import com.mogujie.service.hummer.domain.dto.compatible.PromotionParticipateDTO;
import com.mogujie.service.hummer.domain.dto.result.Result;
import com.mogujie.service.hummer.utils.FeatureMap;
import com.mogujie.service.hummer.utils.NumberUtils;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.basic.ItemPreSaleDO;
import com.mogujie.service.item.domain.basic.ItemTagDO;
import com.mogujie.service.modou.api.virtualcoin.VirtualCoinQueryService;
import com.mogujie.service.modou.contants.AccountType;
import com.mogujie.service.modou.contants.CoinType;
import com.mogujie.service.modou.dto.virtualcoin.AccountInfoDTO;
import com.mogujie.service.modou.dto.virtualcoin.QueryAccountParam;
import com.mogujie.service.modou.result.VirtualCoinResult;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by anshi on 2019/1/24.
 */
@Module(name = "mycoupon")
public class MyCouponDOProvider implements IModuleDOProvider<MyCouponDO> {

    private static final Logger LOGGER = LoggerFactory.getLogger(MyCouponDOProvider.class);

    private static final long APP_COUPONS_MAIT_ID = 133975L;

    private static final String APP_COUPONS_MAIT_KEY = "couponIds";

    private BuyerResourceReadService buyerResourceReadService;

    private BonusReadService bonusReadService;

    private DianaLaunchService dianaLaunchService;

    private VirtualCoinQueryService virtualCoinQueryService;

    @Autowired
    private CommonSwitchUtil commonSwitchUtil;

    @Override
    public void init() throws DetailException {
        try {
            buyerResourceReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(BuyerResourceReadService.class);
            bonusReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(BonusReadService.class);
            dianaLaunchService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(DianaLaunchService.class);
            virtualCoinQueryService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(VirtualCoinQueryService.class);

        } catch (Throwable e) {
            LOGGER.error("init MyCouponDOProvider error!");
        }
    }

    @Override
    public MyCouponDO emit(DetailContext context) {
        MyCouponDO myCouponDO = new MyCouponDO();
        if (context.getLoginUserId() == null || context.getLoginUserId() <= 0) {
            return myCouponDO;
        }
        List<MyCoupon> couponsForApp = getUnusedAppPlatformCoupon(context);
        if (CollectionUtils.isNotEmpty(couponsForApp)) {
            myCouponDO.setMyAppCoupons(couponsForApp);
        }
        myCouponDO.setBonusCount(getBonusOfUser(context));
        //构建蘑豆券相关信息

        if(commonSwitchUtil.isOn(SwitchKey.SWT_MODOU_CONVERT)){
            buildPlatformCouponAndModou(context,myCouponDO);
        }

        return myCouponDO;
    }

    /**
     * 小程序上，需要显示用户应领取、券已生效、尚未使用的优惠券
     *
     * @param context
     * @return
     */
    private List<MyCoupon> getUnusedAppPlatformCoupon(DetailContext context) {
        try {
            if (context.getRouteInfo().getApp() != App.XCX && context.getRouteInfo().getPlatform() != Platform.XCX) {
                return Collections.EMPTY_LIST;
            }
            if (context.getLoginUserId() == null || context.getLoginUserId() <= 0) {
                return Collections.EMPTY_LIST;
            }

            List<Map<String, Object>> maitConfigMaps = MaitUtil.getMaitData(APP_COUPONS_MAIT_ID);
            if (CollectionUtils.isEmpty(maitConfigMaps) || maitConfigMaps.get(0).get(APP_COUPONS_MAIT_KEY) == null) {
                return Collections.EMPTY_LIST;
            }

            List<Long> couponIds = Arrays.stream(maitConfigMaps.get(0).get(APP_COUPONS_MAIT_KEY).toString().split(","))
                    .map(String::trim)
                    .filter(org.apache.commons.lang.math.NumberUtils::isDigits)
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(couponIds)) {
                return Collections.EMPTY_LIST;
            }

            List<MyCoupon> coupons = new ArrayList<>();
            BuyerResourceQuery query = new BuyerResourceQuery();
            query.setBuyerId(context.getLoginUserId());
            query.setStatus(PromotionConstants.BuyerResourceStatus.UNUSED);
            query.setValid(RequestConstants.BuyerResourceTimeValid.IN);
            query.setNeedCampaignInfo(true);
            query.setNeedResourcePoolInfo(true);
            query.setCampaignIds(couponIds);
            query.setLimit(50);
            int offset = 0;
            while (true) {
                query.setOffset(offset);
                offset += 50;
                Result<List<BuyerResourceDTO>> buyerSources = buyerResourceReadService.getResourcesOfUser(query);
                if (buyerSources == null || !buyerSources.isSuccess() || CollectionUtils.isEmpty(buyerSources.getData())) {
                    break;
                }
                coupons.addAll(buyerSources.getData().stream()
                        //这里先将buyerResource转成compainInfo
                        .map(buyerSource -> convertCampaignToPlatformCoupon(buyerSource.getCampaignDTO(), buyerSource.getResourcePoolDTO()))
                        .filter(campaign -> campaign != null)
                        //然后将campaignInfo转成platformCouponV2
                        .map(couponDTO -> {
                            MyCoupon myCouponItem = new MyCoupon();
                            myCouponItem.setCouponId(couponDTO.getId());
                            myCouponItem.setCutPrice(couponDTO.getCutPrice());
                            myCouponItem.setLimitPrice(couponDTO.getLimitPrice());
                            myCouponItem.setName(couponDTO.getName());
                            myCouponItem.setDecorate(couponDTO.getDecorate());
                            myCouponItem.setDiscount(couponDTO.getDiscount() != null ? couponDTO.getDiscount().intValue() : null);
                            myCouponItem.setMaxDecrease(couponDTO.getMaxDecrease());
                            String terminal = "";
                            if (couponDTO.getTerminalType() != null) {
                                switch (couponDTO.getTerminalType()) {
                                    case 1:
                                        terminal = "App专享";
                                        break;
                                    case 2:
                                        terminal = "PC专享";
                                        break;
                                    case 3:
                                        terminal = "女装小程序专享";
                                        break;
                                    default:
                                        break;
                                }
                            }
                            myCouponItem.setTerminal(terminal);
                            myCouponItem.setStartTime(couponDTO.getStartTime() != null ? couponDTO.getStartTime().intValue() : null);
                            myCouponItem.setEndTime(couponDTO.getEndTime() != null ? couponDTO.getEndTime().intValue() : null);
                            return myCouponItem;
                        })
                        .collect(Collectors.toList()));
            }
            return coupons;
        } catch (Throwable e) {
            LOGGER.error("get unused coupons error!", e);
        }
        return Collections.EMPTY_LIST;
    }

    /**
     * 获取用户当前的津贴
     *
     * @param context
     * @return
     */
    private Integer getBonusOfUser(DetailContext context) {
        if (!ContextUtil.isBonusItem(context)) {
            return null;
        }
        if (context.getLoginUserId() == null || context.getLoginUserId() <= 0) {
            return null;
        }
        try {
            UserAccountQuery query = new UserAccountQuery();
            query.setUserId(context.getLoginUserId());
            query.setType(PromotionConstants.UserAccountType.PLATFORM_BONUS);
            Result<UserAccountDTO> result = bonusReadService.getUserAccount(query);
            if (result != null && result.isSuccess() && result.getData() != null) {
                return result.getData().getBalance();
            }
        } catch (Throwable e) {
            LOGGER.error("query bonus error!", e);
        }
        return 0;
    }

    /**
     * 这段代码，来自促销团队 @婉悦，专门用来做对象转换
     *
     * @param campaignDTO
     * @return
     */
    public static PlatformCouponDTO convertCampaignToPlatformCoupon(CampaignDTO campaignDTO, ResourcePoolDTO resourcePoolDTO) {
        if (campaignDTO == null || resourcePoolDTO == null) {
            return null;
        }
        PlatformCouponDTO platformCouponDTO = new PlatformCouponDTO();
        org.springframework.beans.BeanUtils.copyProperties(campaignDTO, platformCouponDTO);

        platformCouponDTO.setMaxNum(resourcePoolDTO.getMaxNum());
        platformCouponDTO.setSendNum(resourcePoolDTO.getSendNum());
        platformCouponDTO.setUsedNum(resourcePoolDTO.getUsedNum());
        platformCouponDTO.setSendStartTime(resourcePoolDTO.getSendStartTime());
        platformCouponDTO.setSendEndTime(resourcePoolDTO.getSendEndTime());
        platformCouponDTO.setStatus(campaignDTO.getStatus());
        platformCouponDTO.setSubParticipateType(campaignDTO.getSubParticipateType());

        Map<String, String> resourceExtraMap = FeatureMap.stringToMap(resourcePoolDTO.getExtra());
        if (resourceExtraMap.get(PromotionConstants.ResourcePoolExtraKey.PERSON_LIMIT_NUM) != null) {
            platformCouponDTO.setPersonLimitNum(Integer.parseInt(resourceExtraMap.get(PromotionConstants.ResourcePoolExtraKey.PERSON_LIMIT_NUM)));
        }
        if (resourceExtraMap.get(PromotionConstants.ResourcePoolExtraKey.PERSON_DAILY_LIMIT_NUM) != null) {
            platformCouponDTO.setPersonDailyLimitNum(Integer.parseInt(resourceExtraMap.get(PromotionConstants.ResourcePoolExtraKey.PERSON_DAILY_LIMIT_NUM)));
        }
        if (resourceExtraMap.get(PromotionConstants.ResourcePoolExtraKey.EFFECTIVE_TIME) != null) {
            platformCouponDTO.setEffectiveTime(NumberUtils.getLong(resourceExtraMap.get(PromotionConstants.ResourcePoolExtraKey.EFFECTIVE_TIME)));
        }

        List<PromotionParticipateDTO> participateDTOList = campaignDTO.getPromotionParticipateList();
        if (!CollectionUtils.isEmpty(participateDTOList)) {
            List<String> partPlatformCouponItemTags = Lists.newArrayList();
            List<String> partPlatformCouponShopTags = Lists.newArrayList();
            for (PromotionParticipateDTO participateDTO : participateDTOList) {
                if (participateDTO.getParticipateType().equals(PromotionConstants.ParticipateType.ITEMTAG)) {
                    partPlatformCouponItemTags.add(participateDTO.getParticipateId().toString());
                } else if (participateDTO.getParticipateType().equals(PromotionConstants.ParticipateType.SHOPTAG)) {
                    partPlatformCouponShopTags.add(participateDTO.getParticipateId().toString());
                }
            }
            platformCouponDTO.setPartPlatformCouponItemTags(partPlatformCouponItemTags);
            platformCouponDTO.setPartPlatformCouponShopTags(partPlatformCouponShopTags);
        }

        Map<String, String> parameter = campaignDTO.getParameter();
        platformCouponDTO.setLimitPrice(Integer.parseInt(
                parameter.get(ParameterKey.LIMIT_PRICE)
        ));
        if (parameter.get(ParameterKey.CUT_PRICE) != null) {
            platformCouponDTO.setCutPrice(Integer.parseInt(
                    parameter.get(ParameterKey.CUT_PRICE)
            ));
        } else if (parameter.get(ParameterKey.DISCOUNT) != null) {
            platformCouponDTO.setDiscount(Long.parseLong(
                    parameter.get(ParameterKey.DISCOUNT)
            ));
            if (parameter.get(ParameterKey.MAX_DECREASE) != null) {
                platformCouponDTO.setMaxDecrease(Long.parseLong(parameter.get(ParameterKey.MAX_DECREASE)));
            }
        }
        if (parameter.get(ParameterKey.TERMINA_TYPE) != null) {
            platformCouponDTO.setTerminalType(Integer.parseInt(parameter.get(ParameterKey.TERMINA_TYPE)));
        }
        if (parameter.get(ParameterKey.CLIENT_NAME) != null) {
            platformCouponDTO.setClientName(parameter.get(ParameterKey.CLIENT_NAME));
        }
        if (parameter.get("newPeCheck") != null) {
            platformCouponDTO.setNewPeople(parameter.get("newPeCheck").equals("true"));
        }
        if (parameter.get(PromotionConstants.ParameterKey.PROMOTION_SUB_CODE) != null) {
            platformCouponDTO.setPromotionSubCode(parameter.get(PromotionConstants.ParameterKey.PROMOTION_SUB_CODE));
        }
        if (StringUtils.isNotBlank(parameter.get(ParameterKey.CHANNEL))) {
            String[] channelStrs = parameter.get(ParameterKey.CHANNEL).split(",");
            if (channelStrs.length > 0) {
                List<Short> channels = Lists.newArrayList();
                for (String channelStr : channelStrs) {
                    channels.add(Short.parseShort(channelStr));
                }
                platformCouponDTO.setChannels(channels);
            }
        }
        // 部分商品券的退款类型信息
        if (parameter.get(PromotionConstants.ParameterKey.REFUND_TYPE) != null) {
            platformCouponDTO.setPlatformCouponRefundType(Integer.parseInt(parameter.get(PromotionConstants.ParameterKey.REFUND_TYPE)));
        }

        // 优惠券是否可转赠
        String val;
        if ((val = parameter.get(PromotionConstants.ParameterKey.TRANSFERABLE)) != null) {
            platformCouponDTO.setIsTransferable(Boolean.parseBoolean(val));
        }

        Map<String, String> extraMap = FeatureMap.stringToMap(campaignDTO.getExtra());
        if (extraMap.get(PromotionConstants.CampaignExtraKey.PROMOTION_ACTIVITYID) != null) {
            platformCouponDTO.setPromotionActivityId(Long.valueOf(extraMap.get(PromotionConstants.CampaignExtraKey.PROMOTION_ACTIVITYID)));
        }
        if (extraMap.get(PromotionConstants.CampaignExtraKey.RESTRICT_DESC) != null) {
            platformCouponDTO.setRestrictDesc(extraMap.get(PromotionConstants.CampaignExtraKey.RESTRICT_DESC));
        }
        if (extraMap.get(PromotionConstants.CampaignExtraKey.ACTIVITY_Name) != null) {
            platformCouponDTO.setActivityName(extraMap.get(PromotionConstants.CampaignExtraKey.ACTIVITY_Name));
        }
        if (extraMap.get(PromotionConstants.CampaignExtraKey.ACTIVITY_Url) != null) {
            platformCouponDTO.setActivityUrl(extraMap.get(PromotionConstants.CampaignExtraKey.ACTIVITY_Url));
        }

        if (Boolean.parseBoolean(parameter.get("itemTagCheck"))) {
            platformCouponDTO.setItemTags(Lists.newArrayList(parameter.get(ParameterKey.ITEM_TAGS).split(",")));
        }
        return platformCouponDTO;
    }

    /**
     * 获取蘑豆兑换优惠券相关数据
     * @param context
     * @return
     */
    private void buildPlatformCouponAndModou(DetailContext context, MyCouponDO couponDO) {
        Long loginUserId = context.getLoginUserId();
        //查询用户剩余的蘑豆信息
        QueryAccountParam queryAccountParam = new QueryAccountParam();
        queryAccountParam.setUserId(loginUserId);
        queryAccountParam.setAccountType(AccountType.NORMAL_USER);
        queryAccountParam.setCoinType(CoinType.MO_DOU);
        VirtualCoinResult<AccountInfoDTO> userModouResult = virtualCoinQueryService.queryCoinAccountInfo(queryAccountParam);
        if (userModouResult != null && userModouResult.isSuccess()
                && userModouResult.getValue() != null) {
            couponDO.setBalanceModouAmount(userModouResult.getValue().getBalance());
        }
        //查询用户等级对应的券
        QueryContext queryContext = bulidDianaParams(context.getItemDO(),loginUserId);
        NatashaResult result = dianaLaunchService.execute(queryContext);
        if (result != null && result.isSuccess() && result.getData() != null) {
            HashMap data = (HashMap) result.getData();
            int level = (int)data.get("level");
            couponDO.setUserVipLevel(level);
            List<JSONObject> couponList = (List<JSONObject>) data.get("list");
            String couponListStr = JSON.toJSONString(couponList);
            List<MyMoDouCoupon> myMoDouCouponList = JSON.parseArray(couponListStr, MyMoDouCoupon.class);
            couponDO.setModouCoupons(myMoDouCouponList);

        }

    }


    private QueryContext bulidDianaParams(DetailItemDO itemDO,Long loginUserId){
        QueryContext queryContext = new QueryContext();
        // 场景code ，detailvipcoupon表示会员优惠券列表
        queryContext.setCode("detailvipcoupon");
        queryContext.setUserId(loginUserId);
        //设置商品feature标（KV标）
        List<ItemTagDO> itemTagDOS = itemDO.getItemTags();
        List<ItemProTagDTO> tagDTOList = Lists.newArrayList();
        if (itemTagDOS != null) {
            tagDTOList = itemTagDOS.stream()
                    .map(tag -> {
                        ItemProTagDTO proTag = new ItemProTagDTO();
                        proTag.setBizId(tag.getBizId());
                        proTag.setTagKey(tag.getTagKey());
                        proTag.setTagValue(tag.getTagValue());
                        proTag.setStartTime(tag.getStartTime());
                        proTag.setEndTime(tag.getEndTime());
                        return proTag;
                    })
                    .collect(Collectors.toList());
        }
        //设置店铺数字标
        Set<Integer> shopTags = ShopInfoTagsUtil.stringToSet(itemDO.getShopInfo().getTags());
        List<Long> shopTagsList = Lists.newArrayList();
        if (shopTags != null) {
            shopTagsList = shopTags.stream()
                    .map(tag -> tag.longValue())
                    .collect(Collectors.toList());
        }

        // 传入商品ID
        Map<String, Object> request = new HashMap<>();
        request.put("itemId", itemDO.getItemId());
        // 是否预售
        request.put("preSale",isPresale(itemDO));
        // 商品kv标 :
        // 格式  [{"tagKey":"xxx","tagValue":111,"startTime":1111,"endTime":2222},...]
        request.put("itemKvTag",tagDTOList);
        // 店铺数字标
        request.put("shopTag",shopTagsList);
        queryContext.setRequest(request);

        return queryContext;

    }

    /**
     * 判断是否是预售商品
     * @param item
     * @return
     */
    private static Boolean isPresale(ItemDO item) {
        int nowTime = (int) (System.currentTimeMillis() / 1000);
        if (null != item.getItemPreSaleDO()) {
            ItemPreSaleDO preSale = item.getItemPreSaleDO();
            if (preSale.getStart() < nowTime && nowTime < preSale.getEnd()) {
                return true;
            }
        }
        return false;
    }


}
