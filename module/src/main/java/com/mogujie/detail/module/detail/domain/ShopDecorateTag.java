package com.mogujie.detail.module.detail.domain;

import java.io.Serializable;
import java.util.Map;

/**
 * Created by anshi on 16/9/26.
 */
public class ShopDecorateTag implements Serializable{

    private Integer start;
    private Integer end;
    private String path;
    private String title;
    private String status;
    /**
     * 1 - 系统检测垃圾(雪藏)
     * 2 - 人工审核垃圾(删除)
     * 3 - 人工审核正常(恢复)
     * 4 - 系统检测正常
     */
    private Integer imageStatus;
    private Map<String, String> link;

    public Integer getStart() {
        return start;
    }

    public void setStart(Integer start) {
        this.start = start;
    }

    public Integer getEnd() {
        return end;
    }

    public void setEnd(Integer end) {
        this.end = end;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getImageStatus() {
        return imageStatus;
    }

    public void setImageStatus(Integer imageStatus) {
        this.imageStatus = imageStatus;
    }

    public Map<String, String> getLink() {
        return link;
    }

    public void setLink(Map<String, String> link) {
        this.link = link;
    }
}
