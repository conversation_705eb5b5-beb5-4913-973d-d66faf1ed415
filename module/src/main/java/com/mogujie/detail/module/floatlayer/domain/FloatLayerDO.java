package com.mogujie.detail.module.floatlayer.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by xiaoyao on 15/12/4.
 */
public class FloatLayerDO implements ModuleDO {

    @Getter
    @Setter
    private int type;

    @Getter
    @Setter
    private int duration;

    @Getter
    @Setter
    private List<String> materials;

    @Getter
    @Setter
    private List<Integer> skuIds;

    @Getter
    @Setter
    private String link;

    private Boolean isShow;

    public Boolean getIsShow() {
        return isShow;
    }

    public void setIsShow(Boolean isShow) {
        this.isShow = isShow;
    }
}
