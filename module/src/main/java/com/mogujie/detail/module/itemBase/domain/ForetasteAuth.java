package com.mogujie.detail.module.itemBase.domain;

import com.mogujie.pay.mailo.api.v2.dto.ForetasteAuthResponseDTO;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

/**
 * Created by ho<PERSON> on 18/6/28.
 */
public class ForetasteAuth {

    /**
     * 用户先穿后付状态
     * 0:不可展示（针对没有资格的商品以及没有资格用户）
     * 1:用户开通可正常先付后买
     * 2:用户未开通可正常先付后买
     * 3.用户可不先付后买
     */
    @Getter
    @Setter
    private Integer state;

    /**
     * 提示信息
     * 当state!=0的情况下 需要展示message
     */
    @Getter
    @Setter
    private String message = "";


    /**
     * 用户开通可正常先付后买的情况下 用来判断 是否第一次打开
     */
    @Getter
    @Setter
    private Boolean first = false;

    /**
     * 是否选中（先试后买）
     */
    @Getter
    @Setter
    private Boolean selected = false;

    public ForetasteAuth(Integer state) {
        this.state = state;
    }

    public ForetasteAuth(ForetasteAuthResponseDTO foretasteAuthResponseDTO) {
        if (StringUtils.isNotBlank(foretasteAuthResponseDTO.getMessage())) {
            this.message = foretasteAuthResponseDTO.getMessage();
        }
        if (foretasteAuthResponseDTO.getVerifyed() != null) {
            this.first = !foretasteAuthResponseDTO.getVerifyed();
        }
        if (foretasteAuthResponseDTO.getSelected() != null) {
            this.selected = foretasteAuthResponseDTO.getSelected();
        }
        switch (foretasteAuthResponseDTO.getForetasteAuthEnumDTO()) {
            case UN_OPEN_UNUSABLE_UNSHOW:
                this.state = 0;
                break;
            case OPENED_UNUSABLE_UNSHOW:
                this.state = 0;
                break;
            case OPENED_CAN_USE_SHOW:
                this.state = 1;
                break;
            case UN_OPEN_CAN_USE_SHOW:
                this.state = 2;
                break;
            case OPENED_UNUSABLE_SHOW:
                this.state = 3;
                break;
            default:
                this.state = 0;
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ForetasteAuth)) return false;

        ForetasteAuth foretasteAuth = (ForetasteAuth) o;

        if (state != null ? !state.equals(foretasteAuth.state) : foretasteAuth.state != null) return false;
        if (message != null ? !message.equals(foretasteAuth.message) : foretasteAuth.message != null) return false;
        if (first != null ? !first.equals(foretasteAuth.first) : foretasteAuth.first != null) return false;
        if (selected != null ? !selected.equals(foretasteAuth.selected) : foretasteAuth.selected != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = state != null ? state.hashCode() : 0;
        result = 31 * result + (message != null ? message.hashCode() : 0);
        result = 31 * result + (first != null ? first.hashCode() : 0);
        result = 31 * result + (selected != null ? selected.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "ForetasteAuth{" +
                "state=" + state +
                ", message='" + message + '\'' +
                ", first=" + first +
                ", selected=" + selected +
                '}';
    }

}
