package com.mogujie.detail.module.sku.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mogujie.metabase.spring.client.MetabaseClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @auther huasheng
 * @time 19/4/1 18:17
 */
@Component
public class SkuColorUtil {
    @Resource(name = "confMetabaseClient")
    private MetabaseClient metabaseClient;

    private Map<String, JSONObject> skuColorConfigMap = new LinkedHashMap<>();

    @PostConstruct
    public void init() throws Exception {
        String colorConfigStr = metabaseClient.get("sku_color_config");
        initColorMap(colorConfigStr);
    }

    public void initColorMap(String text) throws Exception {
        JSONArray colorArray = JSONObject.parseArray(text);
        for (int i = 0; i < colorArray.size(); i++) {
            JSONObject colorConfig = colorArray.getJSONObject(i);
            String name = colorConfig.getString("name");
            if (StringUtils.isEmpty(name)) {
                continue;
            }
            skuColorConfigMap.put(name, colorConfig);
        }
    }

    public String getRgb(String name) {
        if(StringUtils.isEmpty(name)){
            return StringUtils.EMPTY;
        }
        JSONObject skuColorConfig = skuColorConfigMap.get(name);
        if (skuColorConfig == null || StringUtils.isEmpty(skuColorConfig.getString("rgb"))) {
            return StringUtils.EMPTY;
        }
        String rgbValue = skuColorConfig.getString("rgb");
        if (rgbValue.indexOf("#") < 0 || rgbValue.length() > 9) {
            return StringUtils.EMPTY;
        }
        return rgbValue;
    }
}
