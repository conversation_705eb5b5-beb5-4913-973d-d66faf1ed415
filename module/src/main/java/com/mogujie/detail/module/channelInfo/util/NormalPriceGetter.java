package com.mogujie.detail.module.channelInfo.util;

import com.mogujie.commons.utils.EnvUtil;
import com.mogujie.darwin.util.CollectionUtil;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.Platform;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.ContextUtil;
import com.mogujie.detail.core.util.MetabaseTool;
import com.mogujie.detail.core.util.TagUtil;
import com.mogujie.service.hummer.api.PromotionReadService;
import com.mogujie.service.hummer.constains.RequestConstants;
import com.mogujie.service.hummer.domain.dto.*;
import com.mogujie.service.hummer.domain.dto.result.Result;
import com.mogujie.service.hummer.utils.PromotionConvertUtils;
import com.mogujie.service.hummer.utils.SystemDiscountChecker;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import com.mogujie.tesla.core.ReferConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by anshi on 18/4/26.
 */
@Component
public class NormalPriceGetter {
    private static final Logger logger = LoggerFactory.getLogger(NormalPriceGetter.class);

    private PromotionReadService promotionReadService;

    public Map<Long, Long> getNormalPriceMap(DetailContext context) {
        DetailItemDO item = context.getItemDO();
        try {
            Map<Long, Long> originSkuMap = getSkuPriceMap(item);
            Map<Long, Long> realSkuMap = null;
            if (!TagUtil.isFlItem(item.getJsonExtra()) && useLocalDiscount()) {
                SystemDiscountChecker.SystemDiscountRes ret = SystemDiscountChecker.calcSystemDiscount(item.getJsonExtra(), item.getReservePrice(), false, (int) RequestConstants.Market.MOGUJIE, originSkuMap);
                if (null != ret) {
                    realSkuMap = ret.getSkuRalPrice();
                }
            }
            if (null == realSkuMap) {
                ItemDetailRequestV2 request = new ItemDetailRequestV2();
                Pbuyer pbuyer = new Pbuyer();
                pbuyer.setBuyerId(context.getLoginUserId());
                Pseller pSeller = new Pseller();
                pSeller.setSellerId(item.getUserId());
                PitemDetail pitemDetail = new PitemDetail();
                pitemDetail.setExtra(item.getJsonExtra());
                pitemDetail.setItemId(item.getItemId());
                pitemDetail.setSkuPriceMap(originSkuMap);
                pitemDetail.setItemPrice(getHighestPrice(item.getItemSkuDOList()));
                pitemDetail.setNumber(1L);
                pitemDetail.setItemTagList(PromotionConvertUtils.convertItemTagToItemProTag(context.getItemDO().getItemTags()));
                InvokeInfo invokeInfo = new InvokeInfo();
                invokeInfo.setChannel((int) RequestConstants.Channel.UNKNOW);
                invokeInfo.setMarket(ContextUtil.getMarketByContext(context));
                invokeInfo.setSource(RequestConstants.Source.DETAIL);
                invokeInfo.setTerminal(context.getRouteInfo().getPlatform() == Platform.APP ? RequestConstants.Terminal.APP : RequestConstants.Terminal.PC);
                request.setPitemDetail(pitemDetail);
                request.setSeller(pSeller);
                request.setPbuyer(pbuyer);
                request.setInvokeInfo(invokeInfo);
                Result<ItemDetailPromotion> ret = promotionReadService.calcForItemDetailPromotion(request);
                if (null != ret && ret.isSuccess() && null != ret.getData()) {
                    realSkuMap = ret.getData().getSkuRealPriceMap();
                }
            }
            if (realSkuMap!=null){
                return realSkuMap;
            }else {
                return originSkuMap;
            }
        } catch (Throwable e) {
            logger.error("get normal discount info failed : {}", e);
            return Collections.EMPTY_MAP;
        }
    }

    private Map<Long, Long> getSkuPriceMap(ItemDO item) {
        Map<Long, Long> skuPriceMap = new HashMap<>();
        for (ItemSkuDO sku : item.getItemSkuDOList()) {
            skuPriceMap.put(sku.getSkuId(), sku.getPrice().longValue());
        }
        return skuPriceMap;
    }

    private boolean useLocalDiscount() {
        try {
            Boolean useLocal = Boolean.parseBoolean(MetabaseTool.getValue("discount_useLocal"));
            if (null != useLocal) {
                return useLocal;
            }
        } catch (Exception e) {
            ;
        }
        return true;
    }

    private Long getHighestPrice(final List<ItemSkuDO> skuList) {
        if (CollectionUtil.isEmpty(skuList)) {
            return 0L;
        }
        Long highestPrice = skuList.get(0).getPrice().longValue();
        for (ItemSkuDO sku : skuList) {
            if (sku.getPrice().longValue() > highestPrice) {
                highestPrice = sku.getPrice().longValue();
            }
        }
        return highestPrice;
    }

    @PostConstruct
    public void init() throws DetailException {
        try {
            ReferConfig<PromotionReadService> referConfig = new ReferConfig<>(PromotionReadService.class);
            if (EnvUtil.isOnlineEnv()) {
                referConfig.setGroup("DETAIL-GROUP");
            }
            referConfig.setTimeout(200);
            promotionReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(referConfig);
        } catch (Exception e) {
            throw new DetailException(e);
        }
    }
}
