package com.mogujie.detail.module.shop.spi;


import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.spi.Exposed;
import com.mogujie.detail.module.shop.domain.ShopDO;
import com.mogujie.detail.module.shop.domain.ShopDsr;

import java.util.List;

/**
 * Created by xiaoyao on 16/5/21.
 */
@Exposed
public interface IShopDsrProvider {


    /**
     * 获取店铺dsr
     * @param context
     * @param  shopDo
     * @return
     */
    List<ShopDsr> listShopDsr(DetailContext context, ShopDO shopDo);
}
