package com.mogujie.detail.module.activity.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 取自风车配置：
 * http://hd.mogujie.org/internal-tyrael/rule?tempId=276
 * 商品详情页App标签
 * Created by <PERSON>iaoya<PERSON> on 16/10/26.
 */
public class ActivityDO implements ModuleDO {

    /**
     * 老版APP正式倒计时文案
     */
    @Getter
    @Setter
    private String countdownTitle;

    /**
     * 0 表示显示倒计时  1 表示显示文本
     */
    @Getter
    @Setter
    private int type;

    /**
     * 倒计时剩余时间，单位为秒
     */
    @Getter
    @Setter
    private Long countdown;

    /**
     * 活动正式开始时间
     */
    @Getter
    @Setter
    private int startTime;

    /**
     * 活动结束时间
     */
    @Getter
    @Setter
    private int endTime;

    /**
     * 活动状态：1-预热；2-正式
     */
    @Getter
    @Setter
    private int activityState;

    /**
     * 已废弃
     */
    @Getter
    @Setter
    @Deprecated
    private List<EventColumn> eventList;

    /**
     * 倒计时背景图（预热）
     */
    @Getter
    @Setter
    private String countdownBgImg;

    /**
     * 红包（基本废弃了）
     */
    @Getter
    @Setter
    @Deprecated
    private RedPackets redPackets;

    /**
     * 正式活动价格文案
     */
    @Getter
    @Setter
    private String priceDesc;

    @Setter
    @Getter
    private String priceGuarantee;

    /**
     * 正式活动价格颜色
     */
    @Getter
    @Setter
    private String priceColor;

    /**
     * 是否隐藏价格描述字段
     */
    @Getter
    @Setter
    private boolean hideDiscount;

    @Getter
    @Setter
    private List<Gift> giftList;

    @Getter
    @Setter
    @Deprecated
    private String eventTagIcon;

    /**
     * 预热价格
     */
    @Getter
    @Setter
    private WarmUpPrice warmUpPrice;

    @Getter
    @Setter
    private RedPacketPrice redPacketPrice;

    @Getter
    @Setter
    private List<EventTag> eventTags;

    @Getter
    @Setter
    private String warmUpTitle;

    /**
     * 是大促商品, 且在大促期间
     */
    @Getter
    @Setter
    private boolean inActivityItem;

    /**
     * 新版app、H5预热期背景图片
     */
    @Getter
    @Setter
    private String activityPreImage;

    /**
     * 新版app、H5正式期背景图片
     */
    @Getter
    @Setter
    private String activityInImage;

    /**
     * 正式期标题颜色（默认#ffffff即可）
     */
    @Getter
    @Setter
    private String activityInTitleColor;


    /* 1110版本新增字段 start */
    /**
     * 1120后app、H5、小程序预热期背景图片
     */
    @Getter
    @Setter
    private String preActivityInImage1110;

    /**
     * 1110后app、H5、小程序正式期背景图片
     */
    @Getter
    @Setter
    private String activityInImage1110;

    /**
     * 1110后app、H5、小程序氛围图（新增此块区域，以前没有）
     */
    @Getter
    @Setter
    private String activitySphereImage;

    /**
     * 1110后app、H5、小程序活动tag图，如616大促（一张小标签图片）
     */
    @Getter
    @Setter
    private String activityTitleImage;

    /**
     * 1110后app、H5、小程序活动倒计时文字的颜色（距活动结束仅剩）
     */
    @Getter
    @Setter
    private String endTimeHintColor;
    /* 1110版本新增字段 end */

    /**
     * 1130后 app xcx详情页分享卡片氛围
     */
    @Getter
    @Setter
    private String activityIcon;

    /**
     * 券后价格颜色
     */
    @Getter
    @Setter
    private String discountPriceColor;

    /**
     * 券后价格背景颜色
     */
    @Getter
    @Setter
    private String discountPriceBgColor;
}
