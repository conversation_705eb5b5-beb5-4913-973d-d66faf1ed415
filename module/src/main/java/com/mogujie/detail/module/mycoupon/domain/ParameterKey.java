package com.mogujie.detail.module.mycoupon.domain;

/**
 * 这段代码，来自促销团队 @婉悦，在优惠券对象转换的时候会使用到
 * Created by anshi on 2019/1/24.
 */
public interface ParameterKey {
    /**
     * 一口价
     */
    String REL_PRICE = "relPrice";

    /**
     * 目标
     */
    String TARGET = "target";

    /**
     * 打折
     */
    String DISCOUNT = "discount";

    /**
     * 限制金额
     */
    String LIMIT_PRICE = "limitPrice";

    /**
     * 满件
     */
    String ITEM_COUNT_OVER = "itemCountOver";

    /**
     * 减钱
     */
    String CUT_PRICE = "cutPrice";

    /**
     * 礼物
     */
    String GIFT = "gift";
    /**
     * 是否是无线端
     */
    String IS_WEB = "isWeb";

    /**
     * 是否是优选
     */
    String IS_YOUXUAN = "isNeedYouXuan";

    /**
     * 是否是美妆
     */
    String IS_MEIZHUANG = "categoryPro";

    /**
     * 是否是优店
     */
    String IS_FORYOUDIAN = "onlyForYouDian";

    /**
     * 市场
     */
    String RANGE_LIMIT = "rangeLimit";

    /**
     * 市场标记
     */
    String RANGE_IDS = "rangeIds";

    /**
     * 用户类型
     */
    String MEMBER_SHIP_TYPE = "memberType";

    /**
     * 礼物类型
     */
    String GIFT_TYPE = "giftType";

    /**
     * 描述信息
     */
    String DECORATE = "decroate";

    /**
     * 资源id
     */
    String POOL_ID = "poolId";

    /**
     * 渠道
     */
    String CHANNEL = "channel";

    /**
     * 数量限制
     */
    String ITEM_COUNT_LIMIT = "itemCountLimit";

    /**
     * 外部业务ID
     */
    String OUT_ID = "outId";

    /**
     * 外部业务类型
     */
    String OUT_TYPE = "outType";

    /**
     * 限购数量
     */
    String LIMIT_COUNT = "limitCount";

    /**
     * 限购业务类型
     */
    String LIMIT_TYPE = "limitType";

    /**
     * 限购时间类型
     */
    String LIMIT_TIME_TYPE = "lTimeType";

    /**
     * vip等级类型
     */
    String VIP = "vip";

    /**
     * 不免邮区域
     */
    String EXCLUDE = "exclude";

    /**
     * 商品tag
     */
    String ITEM_TAGS = "itemTags";

    /**
     * 美美豆抵现类型
     */
    String GOLD_TYPE = "goldType";

    /**
     * 美美豆抵现值
     */
    String GOLD_VALUE = "goldValue";


    /**
     * 终端类型(可根据终端类型决定终端折扣)
     */
    String TERMINA_TYPE = "terminalType";

    /**
     * 最高可抵扣金额
     */
    String MAX_DECREASE = "maxDec";

    /**
     * 满N免N
     */
    String FREEN = "freeN";
    /**
     * 精确度
     */
    String PRECISION = "precision";

    /**
     * 跨店搭配购商品数量,用于在interrupt中去判断,不在计价中起任何作用
     */
    String TOTAL_ITEM_NUM = "totalItemNum";

    /**
     * 购买方式
     */
    String BUY_TYPE = "buyType";

    /**
     * 虚拟货币抵现类型
     */
    String VIRTUAL_TYPE = "virtualType";

    /**
     * 虚拟货币抵现值
     */
    String VIRTUAL_VALUE = "virtualValue";

    /**
     * 终端名称
     */
    String CLIENT_NAME = "clientName";

    /**
     * 是否折扣
     */
    String NEED_DISCOUNT = "needDiscount";

    /**
     * 是否满减
     */
    String NEED_CUT_PRICE = "needCutPrice";

    /**
     * 买手会员价校验
     */
    String NEED_BUYER_MEMBER_CHECK = "needBuyerMemberCheck";

    /**
     * 买手
     */
    String NEED_LIVE_SHARED_CHECK = "needLiveSharedCheck";

    /**
     * 资源重复使用
     */
    String NEED_RESOURCE_REUSE_CHECK = "needResourceReuseCheck";

    /**
     * 商品Id
     */
    String ITEM_ID = "itemId";
}