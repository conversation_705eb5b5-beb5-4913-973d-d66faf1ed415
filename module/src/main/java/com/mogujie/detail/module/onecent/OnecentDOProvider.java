package com.mogujie.detail.module.onecent;

import com.google.gson.Gson;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.module.onecent.domain.OnecentDO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by anshi on 17/5/25.
 */
@Module(name = "onecent")
public class OnecentDOProvider implements IModuleDOProvider<OnecentDO>{

    private static final Logger logger = LoggerFactory.getLogger(OnecentDOProvider.class);

    @Override
    public OnecentDO emit(DetailContext context) {
        String extra = context.getItemDO().getJsonExtra();
        if (StringUtils.isEmpty(extra)) {
            return null;
        }
        try {
            Gson gson = new Gson();
            Map<String, String> extraInfo = gson.fromJson(extra, HashMap.class);
            if (null == extraInfo || extraInfo.isEmpty()) {
                return null;
            }
            String oneCentStr = extraInfo.get("yf");

            if (StringUtils.isEmpty(oneCentStr)) {
                return null;
            }

            String[] oneCentPairs = oneCentStr.split("\\|");
            if (oneCentPairs.length < 1) {
                return null;
            }
            OnecentDO onecentDO = new OnecentDO();
            for (String oneCentPair : oneCentPairs) {
                String[] oneCent = oneCentPair.split(":");
                if (oneCent.length != 2) {
                    continue;
                }
                if ("it".equals(oneCent[0])) {
                    onecentDO.setOriginItemId(IdConvertor.idToUrl(Long.parseLong(oneCent[1])));
                }
                if ("lt".equals(oneCent[0])) {
                    Long lotteryTime = Long.parseLong(oneCent[1]);
                    Long nowTime = System.currentTimeMillis() / 1000;
                    Integer state = 0;
                    if (nowTime < lotteryTime) {
                        Long countdown = lotteryTime - nowTime;
                        if (countdown < 0) {
                            state = 1;
                        }
                        onecentDO.setState(state);
                        onecentDO.setCountdown(countdown);
                    }
                }
            }
            return onecentDO;
        } catch (Throwable e) {
            logger.error("parse extra failed : {}. {}", extra, e);
        }
        return null;
    }

    @Override
    public void init() throws DetailException {
    }
}
