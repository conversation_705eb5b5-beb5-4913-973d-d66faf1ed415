package com.mogujie.detail.module.qualitycheck.domain;

import com.mogujie.detail.core.adt.ModuleDO;

import java.util.List;

/**
 * Created by xiaoyao on 16/6/2.
 */
public class QualityCheckDO implements ModuleDO {

    private CheckInfo checkInfo;

    private List<CheckDetail> detailList;

    public CheckInfo getCheckInfo() {
        return checkInfo;
    }

    public void setCheckInfo(CheckInfo checkInfo) {
        this.checkInfo = checkInfo;
    }

    public List<CheckDetail> getDetailList() {
        return detailList;
    }

    public void setDetailList(List<CheckDetail> detailList) {
        this.detailList = detailList;
    }

}
