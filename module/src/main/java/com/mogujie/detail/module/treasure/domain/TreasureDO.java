package com.mogujie.detail.module.treasure.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import com.mogujie.marketing.duobao.enums.BizStatus;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by anshi on 2018/5/8.
 */
public class TreasureDO implements ModuleDO {

    /**
     * 活动开始时间
     */
    @Getter
    @Setter
    private Integer startTime;

    /**
     * 活动结束时间
     */
    @Getter
    @Setter
    private Integer endTime;

    /**
     * 已获取的夺宝码数量
     */
    @Getter
    @Setter
    private Integer treasureCodeNums;

    /**
     * 夺宝状态
     */
    @Getter
    @Setter
    private BizStatus status;

    /**
     * 夺宝用户排行榜列表
     */
    @Getter
    @Setter
    private List<TreasureUser> rankList;

    /**
     * 夺宝中奖用户列表
     */
    @Getter
    @Setter
    private List<TreasureAwardUser> awardList;

    /**
     * 试用名额
     */
    @Getter
    @Setter
    private Integer userCount;

    /**
     * 参与人数
     */
    @Getter
    @Setter
    private Integer joinNums;
}
