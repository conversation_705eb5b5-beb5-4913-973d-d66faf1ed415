package com.mogujie.detail.module.buyershow.provider;

import com.mogujie.contentcenter.domain.constant.ContentListItemType;
import com.mogujie.contentcenter.domain.oldbusiness.ContentDetailDto;
import com.mogujie.contentcenter.domain.oldbusiness.ContentFigureDto;
import com.mogujie.contentcenter.domain.oldbusiness.ContentImageDto;
import com.mogujie.contentcenter.domain.oldbusiness.ContentListItemDto;
import com.mogujie.contentcenter.service.ContentService;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.ImageUtil;
import com.mogujie.detail.module.buyershow.domain.*;
import com.mogujie.metabase.utils.StringUtils;
import com.mogujie.service.muser.Result;
import com.mogujie.service.muser.api.UserService;
import com.mogujie.service.muser.domain.entity.User;
import com.mogujie.service.socialchannel.api.BuyerShowService;
import com.mogujie.service.socialchannel.parameter.QueryBuyerShowRelatedIdsParam;
import com.mogujie.service.socialchannel.vo.BuyerShowRelatedIdsVo;
import com.mogujie.service.socialchannel.vo.ResponseVO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * Created by xiaoyao on 16/10/31.
 */
@Module(name = "buyershow")
public class BuyerShowDOProvider implements IModuleDOProvider<BuyerShowDO> {


    private BuyerShowService buyerShowService;

    private UserService userService;

    @Autowired
    private ContentService contentService;

    private static final Logger LOGGER = LoggerFactory.getLogger(BuyerShowDOProvider.class);

    @Override
    public BuyerShowDO emit(DetailContext context) {

        QueryBuyerShowRelatedIdsParam param = new QueryBuyerShowRelatedIdsParam();
        param.setItemIds(Arrays.asList(context.getItemId()));
        try {
            ResponseVO<List<BuyerShowRelatedIdsVo>> ret = buyerShowService.queryBuyerShowRelatedIds(param);
            if (null != ret && ret.isStatus() && !CollectionUtils.isEmpty(ret.getData())) {
                List<BuyerShowRelatedIdsVo> voList = ret.getData();
                List<Long> contentIdList = new ArrayList<>(voList.size());
                List<Long> userIdList = new ArrayList<>(voList.size());
                List<BuyerShowItem> itemList = new ArrayList<>();
                for (BuyerShowRelatedIdsVo vo : voList) {
                    contentIdList.add(vo.getContentId());
                    userIdList.add(vo.getUserId());
                }
                Map<Long, ContentDetailDto> contentRet = contentService.getDetails(contentIdList);
                if (null == contentRet) {
                    return null;
                }
                Map<Long, UserInfo> userInfoMap = getUserInfoMap(userIdList);
                for (BuyerShowRelatedIdsVo vo : voList) {
                    ContentDetailDto contentDetailDto = contentRet.get(vo.getContentId());
                    if (null != contentDetailDto && !CollectionUtils.isEmpty(contentDetailDto.getContentList())) {
                        BuyerShowItem buyerShowItem = new BuyerShowItem();
                        buyerShowItem.setContentId(contentDetailDto.getId());
                        buyerShowItem.setRateId(vo.getRateId());
                        ContentListItemDto imagesContentDto = getCertainTypeContent(contentDetailDto.getContentList(),ContentListItemType.IMAGES);
                        ContentListItemDto itemsContentDto = getCertainTypeContent(contentDetailDto.getContentList(),ContentListItemType.ITEMS);
                        buyerShowItem.setContent(contentDetailDto.getDesc());
                        buyerShowItem.setImgs(null != imagesContentDto ? getImgs(imagesContentDto.getImagesData()) : null);
                        buyerShowItem.setUserInfo(userInfoMap.get(vo.getUserId()));
                        buyerShowItem.setSizeInfo(null != itemsContentDto ? getSizeInfo(itemsContentDto.getFigureData()) : null);
                        buyerShowItem.setSource(vo.getSource() == 2 ? Source.SELLER : Source.EDITOR);
                        itemList.add(buyerShowItem);
                    }
                }
                BuyerShowDO buyerShowDO = new BuyerShowDO();
                buyerShowDO.setCount(itemList.size());
                buyerShowDO.setItems(itemList);
                return buyerShowDO;
            }
        } catch (Throwable e) {
            LOGGER.error("get buyershow info failed : ", e);
        }
        return null;
    }

    private ContentListItemDto getCertainTypeContent(List<ContentListItemDto> itemDtoList, ContentListItemType type) {
        for (ContentListItemDto itemDto : itemDtoList) {
            if (itemDto.getType() == type) {
                return itemDto;
            }
        }
        return null;
    }

    private SizeInfo getSizeInfo(ContentFigureDto figureDto) {
        if (null == figureDto) {
            return null;
        }
        SizeInfo sizeInfo = new SizeInfo();
        String desc = StringUtils.isEmpty(figureDto.getSuitBody()) ? figureDto.getSuitFoot() : figureDto.getSuitBody();
        sizeInfo.setDesc(desc);
        sizeInfo.setHeight(figureDto.getHeight());
        sizeInfo.setWeight(figureDto.getWeight());
        sizeInfo.setIsSuit(StringUtils.isEmpty(desc) ? false : (desc.startsWith("合") ? true : false));
        return sizeInfo;
    }

    private List<String> getImgs(List<ContentImageDto> imageDtos) {
        List<String> imgs = new ArrayList<>(5);
        if (CollectionUtils.isEmpty(imageDtos)) {
            return imgs;
        }
        for (ContentImageDto imageDto : imageDtos) {
            imgs.add(ImageUtil.img(imageDto.getPath()));
        }
        return imgs;
    }

    private Map<Long, UserInfo> getUserInfoMap(List<Long> userIds) {
        Result<List<User>> userRet = userService.getUserByIds(userIds);
        if (null != userRet && !CollectionUtils.isEmpty(userRet.getValue())) {
            Map<Long, UserInfo> userInfoMap = new HashedMap<>(userIds.size());
            for (User user : userRet.getValue()) {
                UserInfo userInfo = new UserInfo();
                userInfo.setAvatar(ImageUtil.img(user.getAvatar()));
                userInfo.setName(user.getUname());
                userInfo.setDaren(false);
                userInfoMap.put(user.getUserId(), userInfo);
            }
            return userInfoMap;
        }
        return Collections.emptyMap();
    }


    @Override
    public void init() throws DetailException {
        try {
            buyerShowService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(BuyerShowService.class);
            userService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(UserService.class);
        } catch (Exception e) {
            LOGGER.error("init service failed : {}", e);
        }
    }
}
