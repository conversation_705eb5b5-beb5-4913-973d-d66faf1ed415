package com.mogujie.detail.module.collocation.domain;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mogujie.detail.core.adt.ModuleDO;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;

/**
 * Created by eryi
 * Date: 2020/10/28
 * Time: 10:36 AM
 * Introduction:详情页1460班车搭配购模块原始数据
 * Document:https://mogu.feishu.cn/docs/doccn6rKzCxAeEykwucMrhLbwrh#
 * Actions:
 */
@Data
public class CollocationDO implements ModuleDO {

    /**
     * 搭配id
     */
    private String groupId;

    /**
     * acm
     */
    private String acm;

    /**
     * 按钮左侧文案，如共6件商品
     */
    private String buttonLeftDesc;

    /**
     * 按钮右侧文案，如去看看
     */
    private String buttonRightDesc;

    /**
     * 小程序链接
     */
    private String xcxLink;

    /**
     * h5链接
     */
    private String h5Link;

    /**
     * 搭配图、锚点、价格等信息
     */
    private JSONArray imageAndBorderList;

    /**
     * 转换方法，将从图墙返回的数据转换为Collocation
     *
     * @param source 图墙返回的原始数据
     * @return
     */
    public static CollocationDO convert(JSONObject source){
        if(MapUtils.isEmpty(source)){
            return null;
        }
        CollocationDO collocationDO = new CollocationDO();
        collocationDO.setGroupId(source.getString("groupId"));
        collocationDO.setAcm(source.getString("acm"));
        collocationDO.setButtonLeftDesc(source.getString("buttonLeftDesc"));
        collocationDO.setButtonRightDesc(source.getString("buttonRightDesc"));
        collocationDO.setH5Link(source.getString("h5Link"));
        collocationDO.setXcxLink(source.getString("xcxLink"));
        collocationDO.setImageAndBorderList(source.getJSONArray("imageAndBorderList"));
        return collocationDO;
    }


}
