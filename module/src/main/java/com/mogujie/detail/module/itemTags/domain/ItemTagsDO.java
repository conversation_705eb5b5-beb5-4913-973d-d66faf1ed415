package com.mogujie.detail.module.itemTags.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON>iaoyao on 16/8/16.
 */
public class ItemTagsDO implements ModuleDO {

    @Getter
    @Setter
    private List<ItemBizTag> normalTags;

    @Getter
    @Setter
    private List<ItemBizTag> goodTags;

    public static final String MOGU_GOOD_NAME = "蘑菇良品";
}
