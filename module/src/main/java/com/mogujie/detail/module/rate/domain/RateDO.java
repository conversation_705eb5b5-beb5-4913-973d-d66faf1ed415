package com.mogujie.detail.module.rate.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import com.mogujie.fashion.api.result.ContentVO;
import com.mogujie.service.rate.domain.ItemDsr;
import com.mogujie.service.rate.domain.tag.RateTag;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/8/16.
 */
public class RateDO<T> implements ModuleDO {

    /**
     * 评价列表
     */
    @Getter
    @Setter
    private List<T> list;

    /**
     * 商品评价总数
     */
    @Getter
    @Setter
    private Integer cRate = 0;

    /**
     * 商品晒图评价总数
     */
    @Getter
    @Setter
    private Integer imgTotal = 0;

    /**
     * 评价标签列表
     */
    @Getter
    @Setter
    private List<RateTag> rateTags;

    /**
     * 商品DSR
     */
    @Getter
    @Setter
    private ItemDsr itemDsr;

    /**
     * 内容开关
     */
    @Getter
    @Setter
    private boolean switchContent;

    /**
     * 晒单总数
     */
    @Getter
    @Setter
    private Long shareTotal;

    /**
     * 精选晒单总数
     */
    @Getter
    @Setter
    private Long contentTotal;

    /**
     * 精选晒单更多链接
     */
    @Getter
    @Setter
    private String contentMoreLink;

    /**
     * 精选晒单列表
     */
    @Getter
    @Setter
    private List<ContentInfo> contentInfos;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        RateDO<?> rateDO = (RateDO<?>) o;

        if (switchContent != rateDO.switchContent) return false;
        if (list != null ? !list.equals(rateDO.list) : rateDO.list != null) return false;
        if (cRate != null ? !cRate.equals(rateDO.cRate) : rateDO.cRate != null) return false;
        if (imgTotal != null ? !imgTotal.equals(rateDO.imgTotal) : rateDO.imgTotal != null) return false;
        if (rateTags != null ? !rateTags.equals(rateDO.rateTags) : rateDO.rateTags != null) return false;
        if (itemDsr != null ? !itemDsr.equals(rateDO.itemDsr) : rateDO.itemDsr != null) return false;
        if (shareTotal != null ? !shareTotal.equals(rateDO.shareTotal) : rateDO.shareTotal != null) return false;
        if (contentTotal != null ? !contentTotal.equals(rateDO.contentTotal) : rateDO.contentTotal != null)
            return false;
        if (contentMoreLink != null ? !contentMoreLink.equals(rateDO.contentMoreLink) : rateDO.contentMoreLink != null)
            return false;
        return contentInfos != null ? contentInfos.equals(rateDO.contentInfos) : rateDO.contentInfos == null;
    }

    @Override
    public int hashCode() {
        int result = list != null ? list.hashCode() : 0;
        result = 31 * result + (cRate != null ? cRate.hashCode() : 0);
        result = 31 * result + (imgTotal != null ? imgTotal.hashCode() : 0);
        result = 31 * result + (rateTags != null ? rateTags.hashCode() : 0);
        result = 31 * result + (itemDsr != null ? itemDsr.hashCode() : 0);
        result = 31 * result + (switchContent ? 1 : 0);
        result = 31 * result + (shareTotal != null ? shareTotal.hashCode() : 0);
        result = 31 * result + (contentTotal != null ? contentTotal.hashCode() : 0);
        result = 31 * result + (contentMoreLink != null ? contentMoreLink.hashCode() : 0);
        result = 31 * result + (contentInfos != null ? contentInfos.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "RateDO{" +
                "list=" + list +
                ", cRate=" + cRate +
                ", imgTotal=" + imgTotal +
                ", rateTags=" + rateTags +
                ", itemDsr=" + itemDsr +
                ", switchContent=" + switchContent +
                ", shareTotal=" + shareTotal +
                ", contentTotal=" + contentTotal +
                ", contentMoreLink='" + contentMoreLink + '\'' +
                ", contentInfos=" + contentInfos +
                '}';
    }

}
