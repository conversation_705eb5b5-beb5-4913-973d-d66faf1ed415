package com.mogujie.detail.module.sizeHelper.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import lombok.Getter;
import lombok.Setter;

/**
 * 尺码助手信息
 * Created by anshi on 17/3/27.
 */
public class SizeHelperDO implements ModuleDO {

    /**
     * 用户是否已经填写尺码信息
     */
    @Getter
    @Setter
    private boolean userInfoFilled;

    /**
     * 用户尺码信息id
     */
    @Getter
    @Setter
    private Long userInfoId;

    /**
     * 用户头像
     */
    @Getter
    @Setter
    private String avartar;

    /**
     * 推荐号型
     */
    @Getter
    @Setter
    private String matchedSizeType;

    /**
     * 身高
     */
    @Getter
    @Setter
    private Double height;

    /**
     * 体重
     */
    @Getter
    @Setter
    private Double weight;

    /**
     * 胸围
     */
    @Getter
    @Setter
    private Double chest;

    /**
     * 腰围
     */
    @Getter
    @Setter
    private Double waist;

    /**
     * 臀围
     */
    @Getter
    @Setter
    private Double hipline;

    /**
     * 是否展示三维数据
     */
    @Getter
    @Setter
    private boolean isShow = true;

}
