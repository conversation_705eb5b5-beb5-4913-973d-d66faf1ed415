package com.mogujie.detail.module.live.domain;

import com.meili.service.shopcenter.util.ImgUtil;
import com.mogujie.detail.core.util.ImageUtil;
import com.mogujie.mogulive.domain.LivingInfo;
import com.mogujie.tesla.common.util.ImgUtils;
import lombok.Data;

/**
 * Created by eryi
 * Date: 2020/8/14
 * Time: 12:21 PM
 * Introduction:店铺自播弹窗信息
 * Document:https://mogu.feishu.cn/docs/doccnNtfDS5LvMq20gzJlXQiW5e
 * Actions:
 */
@Data
public class ShopLiveInfo {

    /**
     * 直播的id
     */
    private long liveId;

    /**
     * 是否正在直播
     */
    private boolean isLiving;

    /**
     * 主播userId
     */
    private long actUserId;

    /**
     * 当前在线人数
     */
    private long onlineUserCount;

    /**
     * app直播间跳转短链
     */
    private String appJumpUrl;

    /**
     * 小程序端跳转地址
     */
    private String xcxJumpUrl;

    /**
     * acm
     */
    private String acm;

    /**
     * 直播地址信息
     */
    private String address;

    /**
     * 主播头像
     */
    private String actAvatar;

    /**
     * 通过直播信息和用户头像组装出店铺自播信息
     *
     * @param livingInfo 直播信息
     * @param avatar     主播头像
     * @return
     */
    public static ShopLiveInfo convert(LivingInfo livingInfo, String avatar) {
        ShopLiveInfo shopLiveInfo = new ShopLiveInfo();
        shopLiveInfo.setAcm(livingInfo.getAcm());
        shopLiveInfo.setActUserId(livingInfo.getActUserId());
        shopLiveInfo.setAddress(livingInfo.getAddress());
        shopLiveInfo.setAppJumpUrl(livingInfo.getAppJumpUrl());
        shopLiveInfo.setLiveId(livingInfo.getLiveId());
        shopLiveInfo.setLiving(livingInfo.isLiving());
        shopLiveInfo.setXcxJumpUrl(livingInfo.getXcxJumpUrl());
        shopLiveInfo.setOnlineUserCount(livingInfo.getOnlineUserCount());
        shopLiveInfo.setActAvatar(ImageUtil.img(avatar));
        return shopLiveInfo;
    }
}
