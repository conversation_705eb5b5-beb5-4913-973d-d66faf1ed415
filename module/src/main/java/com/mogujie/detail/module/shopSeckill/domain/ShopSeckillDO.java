package com.mogujie.detail.module.shopSeckill.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import lombok.Getter;
import lombok.Setter;

/**
 * 店铺秒杀活动基础数据
 *
 * 渠道: 2021
 * 产品: @索飞
 * Created by anshi on 17/12/19.
 */
public class ShopSeckillDO implements ModuleDO {

    /**
     * 0. 未开始
     * 1. 活动中
     * 2. 活动已结束
     */
    @Getter
    @Setter
    private int state;

    @Getter
    @Setter
    private int startTime;

    @Getter
    @Setter
    private int endTime;

    /**
     * 报名原始总库存
     */
    @Getter
    @Setter
    private Long originTotalStock;

    @Getter
    @Setter
    private Long activityId;
}
