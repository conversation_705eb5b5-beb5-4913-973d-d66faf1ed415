package com.mogujie.detail.module.live.domain;

import com.mogujie.live.mogulive.api.response.MoguLiveItemExplainInfoResponse;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * Created by eryi
 * Date: 2020/8/14
 * Time: 11:19 AM
 * Introduction: 1430添加的主播讲解视频弹窗信息
 * Document:https://mogu.feishu.cn/docs/doccnNtfDS5LvMq20gzJlXQiW5e#
 * Actions:
 */
@Data
public class ExplainWindowInfo {

    /**
     * 直播间id
     */
    private Long liveId;
    /**
     * 主播id
     */
    private Long actUserId;
    /**
     * 讲解视频的文件Url
     */
    private String fileUrl;
    /**
     * 1.1.22-SNAPSHOT版本讲解url
     */
    private String videoH265Url;
    /**
     * 5秒动图
     */
    private String gif;
    /**
     * 客户端短链
     */
    private String appLink;
    /**
     * 视频的首桢、这是一张图片
     */
    private String firstFrame;

    /**
     * 将直播返回的对象转换为详情页自己定义的数据。屏蔽一些不必要的信息
     *
     * @param moguLiveItemExplainInfo 直播返回的商品讲解原始信息
     * @return
     */
    public static ExplainWindowInfo convert(MoguLiveItemExplainInfoResponse moguLiveItemExplainInfo) {
        ExplainWindowInfo explainWindowInfo = new ExplainWindowInfo();
        BeanUtils.copyProperties(moguLiveItemExplainInfo, explainWindowInfo);
        explainWindowInfo.videoH265Url = moguLiveItemExplainInfo.getH265FileUrl();
        return explainWindowInfo;
    }

}
