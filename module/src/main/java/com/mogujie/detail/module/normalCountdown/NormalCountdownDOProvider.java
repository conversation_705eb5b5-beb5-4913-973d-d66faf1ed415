package com.mogujie.detail.module.normalCountdown;

import com.alibaba.fastjson.JSONObject;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.ImageUtil;
import com.mogujie.detail.core.util.NumUtil;
import com.mogujie.detail.module.normalCountdown.domain.CountdownInfo;
import com.mogujie.detail.module.normalCountdown.domain.CountdownState;
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO;
import com.mogujie.metabase.spring.client.MetabaseClient;
import com.mogujie.service.item.domain.basic.ItemExtraDO;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.service.item.domain.basic.ItemTagDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by anshi on 18/2/6.
 */
@Module(name = "normalCountdown")
public class NormalCountdownDOProvider implements IModuleDOProvider<NormalCountdownDO> {

    private static final Logger logger = LoggerFactory.getLogger(NormalCountdownDOProvider.class);

    @Resource(name = "confMetabaseClient")
    private MetabaseClient metabaseClient;

    private String COUNTDOWN_CONFIG_KEY = "normalCountdownTags";

    private String SHAN_GO = "shango";

    @Override
    public NormalCountdownDO emit(DetailContext context) {
        List<ItemTagDO> tags = context.getItemDO().getItemTags();
        String tagKeyStr = metabaseClient.get(COUNTDOWN_CONFIG_KEY);
        //遍历商品标，找到属于倒计时类标，解析出该活动的倒计时信息
        NormalCountdownDO normalCountdownDO = null;
        if (CollectionUtils.isNotEmpty(tags) && StringUtils.isNotBlank(tagKeyStr) && null == context.getItemDO().getLimitNum()) {
            String[] tagConfigs = tagKeyStr.split("\\|");
            for (ItemTagDO tag : tags) {
                for (String tagConfig : tagConfigs) {
                    String[] tagConfigPair = tagConfig.split(":");
                    String tagKey = StringUtils.trim(tagConfigPair[0]);
                    String maitId = StringUtils.trim(tagConfigPair[1]);
                    if (tag.getTagKey().equals(tagKey)) {
                        CountdownInfo countdownInfo = new CountdownInfo();
                        countdownInfo.setTagKey(tag.getTagKey());
                        countdownInfo.setMaitId(Long.parseLong(maitId));
                        if (tagConfigPair.length > 2) {
                            countdownInfo.setMaitId1110(Long.parseLong(StringUtils.trim(tagConfigPair[2])));
                        }
                        String[] tagValuePairs = tag.getTagValue().split("\\|");
                        for (String tagValuePair : tagValuePairs) {
                            String[] pair = tagValuePair.split(":");
                            if (pair.length != 2) {
                                continue;
                            }
                            if ("st".equals(pair[0])) {
                                countdownInfo.setStartTime(Long.parseLong(pair[1]));
                            } else if ("et".equals(pair[0])) {
                                countdownInfo.setEndTime(Long.parseLong(pair[1]));
                            } else if ("ws".equals(pair[0])) {
                                countdownInfo.setWarmUpTime(Long.parseLong(pair[1]));
                            } else if ("pp".equals(pair[0])) {
                                countdownInfo.setPrice(Long.parseLong(pair[1]));
                            } else if ("mp".equals(pair[0])) {
                                countdownInfo.setMemberPrice(Long.parseLong(pair[1]));
                            } else if ("ai".equals(pair[0])) {
                                countdownInfo.setActivityId(Long.parseLong(pair[1]));
                            } else if ("pd".equals(pair[0])) {
                                //普通折扣
                                countdownInfo.setPriceMap(getPriceMapByDiscount(context.getItemDO().getItemSkuDOList(), Integer.parseInt(pair[1])));
                            } else if ("md".equals(pair[0])) {
                                //会员折扣
                                countdownInfo.setMemberPriceMap(getPriceMapByDiscount(context.getItemDO().getItemSkuDOList(), Integer.parseInt(pair[1])));
                            } else if ("qx".equals(pair[0])) {
                                //闪购价格曲线图
                                countdownInfo.setPriceHistoryImg(ImageUtil.img(pair[1]));
                            } else if ("wwj".equals(pair[0])) {
                                String outNetPrice = pair[1];
                                if (StringUtils.isNotBlank(outNetPrice) && StringUtils.isNumeric(outNetPrice)) {
                                    //走闪购策略的全网比价外网价
                                    countdownInfo.setOutNetPrice(NumUtil.formatNum(Long.valueOf(outNetPrice) / 100d));
                                }
                            } else if ("wwt".equals(pair[0])) {
                                String outNetImage = pair[1];
                                if (StringUtils.isNotBlank(outNetImage)) {
                                    //走闪购策略的全网比价外网图
                                    countdownInfo.setOutNetImage(ImageUtil.img(outNetImage));
                                }
                            } else if ("nbt".equals(pair[0])) {
                                // 在一个kv标代表的活动里又细分子活动。目前只有shango是这样的
                                // 这时候再根据nbt取maitId
                                String nbt = pair[1];
                                if (tagConfigPair.length > 3) {
                                    String[] nbtMaitStrs = tagConfigPair[3].split(",");
                                    for (String pairStr : nbtMaitStrs) {
                                        String[] nbtMaitPair = pairStr.split("-");
                                        if (nbtMaitPair.length != 3) {
                                            continue;
                                        }
                                        if (nbtMaitPair[1].equals(nbt)) {
                                            countdownInfo.setMaitId1110(Long.parseLong(StringUtils.trim(nbtMaitPair[2])));
                                            countdownInfo.setNbt(nbt);
                                        }
                                    }
                                }
                            }
                        }
                        int currentTime = (int) (System.currentTimeMillis() / 1000);
                        if (currentTime > countdownInfo.getWarmUpTime() && currentTime < countdownInfo.getStartTime()) {
                            countdownInfo.setState(CountdownState.WARM_UP);
                            countdownInfo.setCountdown(countdownInfo.getStartTime() - currentTime);
                        } else if (currentTime >= countdownInfo.getStartTime() && currentTime < countdownInfo.getEndTime()) {
                            countdownInfo.setState(CountdownState.IN_ACTIVITY);
                            countdownInfo.setCountdown(countdownInfo.getEndTime() - currentTime);
                        }
                        if (countdownInfo.getState() != null) {
                            if (normalCountdownDO == null) {
                                normalCountdownDO = new NormalCountdownDO();
                            }
                            normalCountdownDO.getCountdownInfoMap().put(tag.getTagKey(), countdownInfo);
                        }
                        break;
                    }
                }
            }
        }

        CountdownInfo comparePrice = this.buildSliceCompare(normalCountdownDO, context.getItemDO());
        if (comparePrice != null) {
            if (normalCountdownDO == null) {
                normalCountdownDO = new NormalCountdownDO();
            }
            normalCountdownDO.getCountdownInfoMap().put("comparePrice", comparePrice);
        }
        return normalCountdownDO;
    }

    /**
     * 校验是否有切片侧的全网比价
     */
    private CountdownInfo buildSliceCompare(NormalCountdownDO normalCountdownDO, DetailItemDO itemDO) {
        if (normalCountdownDO != null && normalCountdownDO.getCountdownInfoMap().containsKey(SHAN_GO)) {
            return null;
        }
        ItemExtraDO itemExtraDO = itemDO.getItemExtraDO();
        if (itemExtraDO == null || MapUtils.isEmpty(itemExtraDO.getFeatures())) {
            return null;
        }
        String comparePriceInfo = itemExtraDO.getFeatures().get("comparePriceInfo");
        if (StringUtils.isBlank(comparePriceInfo)) {
            return null;
        }
        JSONObject comparePrice = JSONObject.parseObject(comparePriceInfo);
        if (MapUtils.isEmpty(comparePrice)) {
            return null;
        }
        String outerItemImage = comparePrice.getString("outerItemScreenshot");
        Long outerItemPrice = comparePrice.getLong("outerItemPrice");
        if (StringUtils.isBlank(outerItemImage) || outerItemPrice == null) {
            return null;
        }
        CountdownInfo countdownInfo = new CountdownInfo();
        countdownInfo.setOutNetPrice(NumUtil.formatNum(outerItemPrice / 100d));
        countdownInfo.setOutNetImage(ImageUtil.img(outerItemImage));
        return countdownInfo;
    }

    /**
     * 根据折扣计算sku的折扣价
     *
     * @param skus
     * @param discount
     * @return
     */
    private Map<Long, Long> getPriceMapByDiscount(List<ItemSkuDO> skus, int discount) {
        Map<Long, Long> priceMap = new HashMap<>();
        try {
            if (skus != null) {
                for (ItemSkuDO sku : skus) {
                    priceMap.put(sku.getSkuId(), sku.getPrice() * discount / 1000);
                }
            }
        } catch (Throwable e) {
            logger.error("get price map error.", e);
        }
        return priceMap;
    }

    @Override
    public void init() throws DetailException {

    }
}
