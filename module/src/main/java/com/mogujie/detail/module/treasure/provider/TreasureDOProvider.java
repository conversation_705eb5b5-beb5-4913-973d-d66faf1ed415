package com.mogujie.detail.module.treasure.provider;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.module.treasure.domain.TreasureDO;


/**
 * 快抢夺宝 @侃侃 @不屈
 * Created by anshi on 2018/5/8.
 */
@Module(name = "treasure")
public class TreasureDOProvider implements IModuleDOProvider<TreasureDO> {

    @Override
    public TreasureDO emit(DetailContext context) {
        return null;
    }

    @Override
    public void init() throws DetailException {

    }
}
