package com.mogujie.detail.module.live.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by anshi on 17/12/6.
 */
public class LiveDO implements ModuleDO {

    /**
     * 直播do类型
     */
    @Getter
    @Setter
    private LiveType liveType;

    /**
     * 供货商id
     */
    @Getter
    @Setter
    private Long providerId;

    /**
     * 供货商名称
     */
    @Getter
    @Setter
    private String providerName;

    /**
     * 营业执照图片地址
     */
    @Getter
    @Setter
    private String licenseImage;

    /**
     * 主播信息
     */
    @Getter
    @Setter
    private List<LiveAnchorInfo> liveAnchorInfos;

    /**
     * 1510商详进房 商品对应直播间信息
     */
    @Getter
    @Setter
    private List<LiveItemInfo> liveItemInfos;

    /**
     * 主播小窗信息
     */
    @Getter
    @Setter
    private LiveAnchorInfo actUserInfo;
}
