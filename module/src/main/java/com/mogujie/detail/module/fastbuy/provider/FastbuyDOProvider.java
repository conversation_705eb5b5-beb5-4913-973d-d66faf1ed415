package com.mogujie.detail.module.fastbuy.provider;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.SwitchKey;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.core.util.MetabaseTool;
import com.mogujie.detail.core.util.TagUtil;
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO;
import com.mogujie.marketing.aston.api.dto.ApiResultDTO;
import com.mogujie.marketing.ferrari.api.dto.RushInfoDTO;
import com.mogujie.marketing.remind.RushNocticeServiceApi;
import com.mogujie.marketing.remind.dto.IncrexItemFocusedParam;
import com.mogujie.member.api.planet.PlanetUserService;
import com.mogujie.member.domain.enumtype.AppType;
import com.mogujie.member.domain.enumtype.ModelType;
import com.mogujie.member.domain.model.RpcResult;
import com.mogujie.member.domain.model.user.UserModelInfo;
import com.mogujie.service.item.domain.basic.ItemTagDO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Created by xiaoyao on 16/8/16.
 */
@Module(name = "fastbuy")
public class FastbuyDOProvider implements IModuleDOProvider<FastbuyDO> {

    private static final Logger LOGGER = LoggerFactory.getLogger(FastbuyDO.class);

    private PlanetUserService planetUserService;

    @Autowired
    private RushNocticeServiceApi rushNocticeServiceApi;

    private static final String FASTBUY_TYPE_MAIT_KEY = "fastbuy_type_mait_mapping";

    /**
     * 存放部分快抢信息的商品kv标名称
     */
    private static final String FAST_BUY_TAG_NAME = "fastbuy";

    /**
     * kv标志限购个数的字段名称
     */
    private static final String FAST_BUY_LIMIT_NUM = "ln";


    @Autowired
    private CommonSwitchUtil commonSwitchUtil;

    @Override
    public void init() throws DetailException {
        try {
            planetUserService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(PlanetUserService.class);
        } catch (Exception e) {
            LOGGER.error("init service failed : {}", e);
        }
    }


    @Override
    public FastbuyDO emit(DetailContext context) {
        Object rushInfoObj = context.getContext("fastbuyInfo");
        if (null != rushInfoObj) {
            RushInfoDTO rushInfoDTO = (RushInfoDTO) rushInfoObj;
            if (null != rushInfoDTO) {
                int nowTime = (int) (System.currentTimeMillis() / 1000);
                if (nowTime < rushInfoDTO.getEndTime()) {
                    DetailItemDO item = context.getItemDO();
                    FastbuyDO fastbuyDO = new FastbuyDO();
                    Long totalStock = item.getTotalStock();
                    fastbuyDO.setAllStock(totalStock + rushInfoDTO.getSales());
                    fastbuyDO.setTotalStock(totalStock);
                    fastbuyDO.setStartTime(rushInfoDTO.getStartTime());
                    fastbuyDO.setEndTime(rushInfoDTO.getEndTime());
                    fastbuyDO.setState(getState(totalStock, rushInfoDTO));
                    fastbuyDO.setLeftUser(rushInfoDTO.getUnPay());
                    if (context.getRouteInfo().getBizType() == BizType.SKU
                            && context.getRouteInfo().getVersion().startsWith("live.fastbuy")) {
                        fastbuyDO.setActivityId(IdConvertor.urlToId(context.getParam("activityId")).toString());
                    } else {
                        fastbuyDO.setActivityId(context.getParam("fastbuyId"));
                    }
                    fastbuyDO.setFollowed(rushInfoDTO.getIsFollowed() == 1);
                    fastbuyDO.setExtra(rushInfoDTO.getExtra());
                    if (MapUtils.isNotEmpty(rushInfoDTO.getExtra())) {
                        fastbuyDO.setProgressBar(MapUtils.getDouble(rushInfoDTO.getExtra(), "progressBar"));
                    }
                    if (rushInfoDTO.getIsNewBuyerItem() == 1) {
                        fastbuyDO.setNewComerItem(true);
                        if (isNewComer(context)) {
                            fastbuyDO.setNewComerUser(true);
                        }
                    }
                    //2018.12.27 快抢细分多种业务类型，这里根据业务类型，设置对应的麦田id
                    fastbuyDO.setMaitId(getMaitId(rushInfoDTO));

                    // 1460班车，快抢善品限购支持x件
                    fastbuyDO.setLimitNum(this.buildListNum(context.getItemDO()));

                    //活动开始前展示关注人数
                    if (nowTime < rushInfoDTO.getStartTime()) {
                        Long fastbuyId = !StringUtils.isNumeric(fastbuyDO.getActivityId()) ? null : Long.parseLong(fastbuyDO.getActivityId());
                        IncrexItemFocusedParam increxItemFocusedParam = new IncrexItemFocusedParam();
                        increxItemFocusedParam.setActivityKey("rushNotice");//仅做业务标识
                        increxItemFocusedParam.setActivityId(fastbuyId);
                        ApiResultDTO<Integer> apiResultDTO = rushNocticeServiceApi.increxItemFocusedNum(increxItemFocusedParam);
                        if (apiResultDTO != null && apiResultDTO.getData() != null) {
                            fastbuyDO.setNoticeNum(apiResultDTO.getData());
                        }
                    }
                    return fastbuyDO;
                }
            }
        }
        return null;
    }

    /**
     * 获取快抢商品的限购件数
     *
     * @param itemDO 原始商品信息
     * @return
     */
    private int buildListNum(DetailItemDO itemDO) {
        int defaultNum = 1;
        try {
            List<ItemTagDO> itemTags = itemDO.getItemTags();
            if (CollectionUtils.isEmpty(itemTags)) {
                return defaultNum;
            }
            Optional<ItemTagDO> first = itemTags
                    .stream()
                    .filter(itemTagDO -> itemTagDO != null
                            && FAST_BUY_TAG_NAME.equals(itemTagDO.getTagKey())
                            && StringUtils.isNotBlank(itemTagDO.getTagValue()))
                    .findFirst();
            if (!first.isPresent()) {
                return defaultNum;
            }
            String tagValue = first.get().getTagValue();
            Map<String, String> kvTags = TagUtil.parseKVTags(tagValue);
            if (MapUtils.isEmpty(kvTags) || StringUtils.isBlank(kvTags.get(FAST_BUY_LIMIT_NUM))) {
                return defaultNum;
            }
            return Integer.parseInt(kvTags.get(FAST_BUY_LIMIT_NUM));
        } catch (Exception e) {
            LOGGER.error("parse fastbuy tag get limit num error!", e);
            return defaultNum;
        }
    }

    private int getState(Long totalStock, RushInfoDTO rushInfoDTO) {
        if (!isStarted(rushInfoDTO)) {
            int nowTime = (int) (System.currentTimeMillis() / 1000);
            if (nowTime > rushInfoDTO.getStartTime() - 5 * 60) {
                /**
                 * 提前5分钟
                 */
                return 5;
            } else {
                return 0;
            }
        }
        if (totalStock > 0) {
            return 1;
        }

        if (rushInfoDTO.getUnPay() > 0) {
            return 4;
        } else {
            return 2;
        }
    }

    private boolean isStarted(RushInfoDTO rushInfoDTO) {
        Integer nowTime = (int) (System.currentTimeMillis() / 1000);
        return rushInfoDTO.getStartTime() < nowTime && nowTime < rushInfoDTO.getEndTime();
    }

    /**
     * 当前登录用户是否为新人
     *
     * @param context
     * @return
     */
    public boolean isNewComer(DetailContext context) {
        Long loginUserId = context.getLoginUserId();
        if (null == loginUserId) {
            return false;
        }

        if (!commonSwitchUtil.isOn(SwitchKey.SWT_IS_NEW_COMER)) {
            return false;
        }
        try {
            RpcResult<UserModelInfo> rpcResult = planetUserService.getUserModelInfo(AppType.MOGUJIE.getCode(), loginUserId,
                    ModelType.WHETHER_BUY.getCode());
            if (!rpcResult.isSuccess() || rpcResult.getValue() == null) {
                return false;
            }

            if (rpcResult.getValue().getModelValue() != null && rpcResult.getValue().getModelValue().equals("L1")) {
                return true;
            }
        } catch (Throwable e) {
            LOGGER.error("call is new user error.", e);
            return false;
        }

        return false;
    }

    /**
     * 根据快抢类型获取对应的麦田氛围id
     *
     * @param rushInfoDTO
     * @return
     */
    private static Integer getMaitId(RushInfoDTO rushInfoDTO) {
        try {
            JSONObject jsonObject = JSON.parseObject(MetabaseTool.getValue(FASTBUY_TYPE_MAIT_KEY));
            //主快抢默认氛围
            Integer maitId = jsonObject.getInteger("mainKQ");
            if (rushInfoDTO == null
                    || rushInfoDTO.getExtra() == null
                    || rushInfoDTO.getExtra().get("bizType") == null) {
                return maitId;
            }

            String bizType = rushInfoDTO.getExtra().get("bizType").toString();
            Integer va = jsonObject.getInteger(bizType);
            if (va != null && va > 0) {
                maitId = va;
            }
            return maitId;
        } catch (Throwable e) {
            LOGGER.error("get fastbuy mait mapping error!", e);
        }
        return null;
    }
}
