package com.mogujie.detail.module.rate.spi;


import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.spi.Exposed;

/**
 * Created by xiaoyao on 16/5/19.
 */
@Exposed
public interface IUserProfileProvider {

    /**
     * 获取用户个人主页链接
     * @return
     */
    String getProfilePrefix(DetailContext context);


    /**
     *  匿名用户的头像
     *
     * @param userId
     * @return
     */
    String getAnonymousImg(DetailContext context, long userId);
}
