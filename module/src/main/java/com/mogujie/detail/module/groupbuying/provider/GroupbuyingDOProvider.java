package com.mogujie.detail.module.groupbuying.provider;

import com.google.gson.Gson;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.ItemTag;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.NumUtil;
import com.mogujie.detail.module.groupbuying.constants.TuanBizType;
import com.mogujie.detail.module.groupbuying.constants.TuanStatus;
import com.mogujie.detail.module.groupbuying.constants.TuanType;
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO;
import com.mogujie.marketing.minicooper.domain.entity.AuditItemDTO;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by xiaoyao on 16/10/26.
 */
@Module(name = "groupbuying")
public class GroupbuyingDOProvider implements IModuleDOProvider<GroupbuyingDO> {

    private static Logger LOGGER = LoggerFactory.getLogger(GroupbuyingDOProvider.class);

    @Override
    public GroupbuyingDO emit(DetailContext context) {
        DetailItemDO itemDO = context.getItemDO();
        if (BizType.PTG.equals(context.getRouteInfo().getBizType())) {
            return getPTGDO(context);
        } else {
            if (itemDO.getLimitNum() == null) {
                return getGroupbuyingDO(itemDO);
            } else {
                return null;
            }
        }
    }

    protected GroupbuyingDO getPTGDO(DetailContext context) {
        Object obj = context.getContext("ptgInfo");
        if (null != obj) {
            AuditItemDTO auditItemDTO = (AuditItemDTO) obj;
            GroupbuyingDO groupbuyingDO = new GroupbuyingDO();
            groupbuyingDO.setStartTime(auditItemDTO.getStartTime());
            groupbuyingDO.setEndTime(auditItemDTO.getEndTime());
            groupbuyingDO.setActivityId(auditItemDTO.getActivityId());
            return groupbuyingDO;
        }
        return null;
    }

    /**
     * 解析extra字段tg标
     * <p>
     * 若解析的是utg标,则为优质团商品
     * tg标和utg标结构相同
     *
     * @param itemDO
     * @return
     */
    protected GroupbuyingDO getGroupbuyingDO(DetailItemDO itemDO) {
        Map<String, String> extraInfo = getExtraInfo(itemDO.getJsonExtra());
        if (null == extraInfo) {
            return null;
        }

        //优质团
        String hdInfoStr = extraInfo.get("utg");
        GroupbuyingDO utgGroupbuyingDO = resolveGroupbuyingDO(hdInfoStr, itemDO.getItemSkuDOList());
        if (utgGroupbuyingDO != null) {
            utgGroupbuyingDO.setBizType(TuanBizType.UZHI);
            // 设置团购仓库类型
            utgGroupbuyingDO.setType(TuanType.NORMAL);
            if (!CollectionUtils.isEmpty(itemDO.getItemBizTags()) && (itemDO.getItemBizTags().contains(ItemTag.INSTORE) || itemDO.getItemBizTags().contains(ItemTag.TRANSFER))) {
                utgGroupbuyingDO.setType(TuanType.STORE);
            }
            if (utgGroupbuyingDO.getStatus() == TuanStatus.IN) {
                return utgGroupbuyingDO;
            }
        }

        //品牌团
        hdInfoStr = extraInfo.get("ptg");
        GroupbuyingDO ptgGroupbuyingDO = resolveGroupbuyingDO(hdInfoStr, itemDO.getItemSkuDOList());
        if (ptgGroupbuyingDO != null) {
            ptgGroupbuyingDO.setBizType(TuanBizType.PINPAI);
            // 设置团购仓库类型
            ptgGroupbuyingDO.setType(TuanType.NORMAL);
            if (!CollectionUtils.isEmpty(itemDO.getItemBizTags()) && (itemDO.getItemBizTags().contains(ItemTag.INSTORE) || itemDO.getItemBizTags().contains(ItemTag.TRANSFER))) {
                ptgGroupbuyingDO.setType(TuanType.STORE);
            }
            if (ptgGroupbuyingDO.getStatus() == TuanStatus.IN) {
                return ptgGroupbuyingDO;
            }
        }

        //普通团
        hdInfoStr = extraInfo.get("tg");
        GroupbuyingDO groupbuyingDO = resolveGroupbuyingDO(hdInfoStr, itemDO.getItemSkuDOList());
        if (groupbuyingDO != null) {
            groupbuyingDO.setBizType(TuanBizType.NORMAL);
            // 设置团购仓库类型
            groupbuyingDO.setType(TuanType.NORMAL);
            if (!CollectionUtils.isEmpty(itemDO.getItemBizTags()) && (itemDO.getItemBizTags().contains(ItemTag.INSTORE) || itemDO.getItemBizTags().contains(ItemTag.TRANSFER))) {
                groupbuyingDO.setType(TuanType.STORE);
            }
            if (groupbuyingDO.getStatus() == TuanStatus.IN) {
                return groupbuyingDO;
            }
        }

        if (utgGroupbuyingDO != null) {
            return utgGroupbuyingDO;
        }
        if (ptgGroupbuyingDO != null) {
            return ptgGroupbuyingDO;
        }
        if (groupbuyingDO != null) {
            return groupbuyingDO;
        }
        return null;
    }


    /**
     * 解析团购标(tg或utg)
     * 获取团购时间、价格、仓库类型等数据
     * at: 团购仓库种类 0普通 1入仓 2中转仓
     * st: 开始时间
     * ed: 结束时间
     * dc: 折扣(折扣价)
     * ap: 原价(折扣价)
     * pp: 现价(一口价)
     *
     * @param hdInfoStr
     * @return
     */
    protected GroupbuyingDO resolveGroupbuyingDO(String hdInfoStr, List<ItemSkuDO> skuDOList) {
        if (StringUtils.isBlank(hdInfoStr)) {
            return null;
        }
        String[] hdInfoPairs = hdInfoStr.split("\\|");
        if (hdInfoPairs == null || hdInfoPairs.length < 1) {
            return null;
        }
        Long realPrice = null;
        Long discount = null;
        Long nowTime = System.currentTimeMillis() / 1000;
        GroupbuyingDO groupbuyingDO = new GroupbuyingDO();
        for (String hdInfoPair : hdInfoPairs) {
            String[] hd = hdInfoPair.split(":");
            if (hd.length != 2) {
                continue;
            }
            if ("st".equals(hd[0])) {
                groupbuyingDO.setStartTime(Long.parseLong(hd[1]));
            } else if ("et".equals(hd[0])) {
                groupbuyingDO.setEndTime(Long.parseLong(hd[1]));
            } else if ("dc".equals(hd[0])) {
                discount = Long.parseLong(hd[1]);
            } else if ("pp".equals(hd[0])) {
                realPrice = Long.parseLong(hd[1]);
            }
        }
        //discount 优先
        if (null != discount) {
            Long lowPrice = Long.MAX_VALUE;
            Long highPrice = Long.MIN_VALUE;
            for (ItemSkuDO itemSkuDO : skuDOList) {
                lowPrice = Long.min(itemSkuDO.getPrice(), lowPrice);
                highPrice = Long.max(itemSkuDO.getPrice(), highPrice);
            }
            if (lowPrice.equals(highPrice)) {
                groupbuyingDO.setPrice("¥" + NumUtil.formatNum(1.0 * lowPrice / 100 * discount / 1000));
            } else {
                groupbuyingDO.setPrice("¥" + NumUtil.formatNum(1.0 * lowPrice / 100 * discount / 1000) + "~" +
                        NumUtil.formatNum(1.0 * highPrice / 100 * discount / 1000));
            }
        } else if (null != realPrice) {
            groupbuyingDO.setPrice("¥" + NumUtil.formatNum(1.0 * realPrice / 100));
        } else {
            LOGGER.error("invalid tuan extra info : {}", hdInfoStr);
            return null;
        }
        if (groupbuyingDO.getEndTime() == null || groupbuyingDO.getStartTime() == null) {
            return null;
        }
        if (nowTime <= groupbuyingDO.getStartTime() && (nowTime + 86400 > groupbuyingDO.getStartTime())) {
            // 团购预热阶段
            groupbuyingDO.setStatus(TuanStatus.PRE);
        } else if (nowTime > groupbuyingDO.getStartTime() && nowTime <= groupbuyingDO.getEndTime()) {
            // 团购活动中
            groupbuyingDO.setStatus(TuanStatus.IN);
        } else {
            // 团购未开始预热/已结束
            return null;
        }
        return groupbuyingDO;
    }

    /**
     * 将extra字段映射为map
     *
     * @param extra
     * @return
     */
    protected Map<String, String> getExtraInfo(String extra) {
        if (StringUtils.isEmpty(extra)) {
            return null;
        }
        try {
            Gson gson = new Gson();
            return gson.fromJson(extra, HashMap.class);
        } catch (Exception e) {
            LOGGER.debug("decode extrainfo failed, {}", e);
        }
        return null;
    }

    @Override
    public void init() throws DetailException {

    }
}
