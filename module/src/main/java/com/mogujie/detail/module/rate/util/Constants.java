package com.mogujie.detail.module.rate.util;

/**
 * Created by <PERSON><PERSON><PERSON> on 15/12/7.
 */
public interface Constants {
    public static final String STOCK_DEFAULT_STYLE = "颜色:默认";

    public static final String IMG_SMALL_SIZE = "100x100";
    public static final String IMG_BIG_SIZE = "468x468";
    public static final int IMG_BIG_WIDTH = 468;
    public static final int IMG_BIG_HEIGHT = 468;

    public static final String PROBATION_URL = "http://www.mogujie.com/x6/member/freeuse";
    public static final String PROBATION_URL_REMOVE_PROTOCOL = "//www.mogujie.com/x6/member/freeuse";


    int STOCK_MAX_LENGTH = 10;

    // user info
    String[] ANONYMOUS_ICON = new String[]{
            "/p1/160105/idid_ifrtgyjxmjrwgntfguzdambqhayde_160x160.jpg",
            "/p1/160105/idid_ifrwenbygrsggntfguzdambqhayde_160x160.jpg",
            "/p1/160105/idid_ifrwenjuhfsggntfguzdambqhayde_160x160.jpg",
            "/p1/160105/idid_ifrtmoddmnsggntfguzdambqhayde_160x160.jpg",
            "/p1/160105/idid_ifrtcnbzgbswgntfguzdambqhayde_160x160.jpg",
            "/p1/160105/idid_ifrwem3ggvswgntfguzdambqhayde_160x160.jpg"
    };

    String EXPLAIN_PREFIX = "[商家回复]:  ";


    String APP_PROFILE_URL_PREFIX = "mgj://user?uid=";
    String H5_PROFILE_URL_PREFIX = "http://m.mogujie.com/x6/account/getprofile?uid=";
    String H5_PROFILE_URL_PREFIX_REMOVE_PROTOCOL = "//m.mogujie.com/x6/account/getprofile?uid=";

}
