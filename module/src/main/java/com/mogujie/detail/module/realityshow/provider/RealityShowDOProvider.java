package com.mogujie.detail.module.realityshow.provider;

import com.google.gson.Gson;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.ImageUtil;
import com.mogujie.detail.module.realityshow.domain.RealityShow;
import com.mogujie.detail.module.realityshow.domain.RealityShowDO;
import com.mogujie.detail.module.realityshow.domain.StarInfo;
import com.mogujie.tesla.common.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by xy on 2017/7/10.
 */
@Module(name = "realityshow")
public class RealityShowDOProvider implements IModuleDOProvider<RealityShowDO> {

    private static final Logger LOGGER = LoggerFactory.getLogger(RealityShowDOProvider.class);

    @Override
    public RealityShowDO emit(DetailContext context) {
        DetailItemDO item = context.getItemDO();
        if (null == item) {
            return null;
        }
        List<RealityShow> showList = getShowList(item.getFeatures());
        if (CollectionUtils.isEmpty(showList)) {
            return null;
        }
        RealityShowDO realityShowDO = new RealityShowDO();
        realityShowDO.setCid(getRootCid(item.getCids()));
        realityShowDO.setShowList(showList);
        return realityShowDO;
    }

    @Override
    public void init() throws DetailException {

    }

    public static List<RealityShow> getShowList(Map<String, String> features) {
        try {
            if (features == null || features.isEmpty()) {
                return null;
            } else {
                // 封面图视频 @乱道、@凌云
                String showVideosStr = features.get("showVideos");
                if (StringUtils.isNotBlank(showVideosStr)) {
                    Gson gson = new Gson();
                    List<Object> showVideos = gson.fromJson(showVideosStr, ArrayList.class);
                    if (!CollectionUtils.isEmpty(showVideos)) {
                        List<RealityShow> realityShows = new ArrayList<>(showVideos.size());
                        for (Object showVideo : showVideos) {
                            try {
                                Map<String, Object> dataMap = (Map) showVideo;
                                RealityShow show = new RealityShow();
                                Integer auditStatus = ((Double) (dataMap.get("auditStatus"))).intValue();
                                if (2 != auditStatus) {
                                    continue;
                                }
                                show.setAuditStatus(auditStatus);
                                show.setCoverImg(ImageUtil.img(dataMap.get("coverImage").toString()));
                                show.setVideoId((Long.parseLong(dataMap.get("videoId").toString())));
                                show.setSkuInfo(null != dataMap.get("sku") ? (Map) (dataMap.get("sku")) : null);
                                show.setStarInfo(getStarInfo((Map) (dataMap.get("hongren"))));
                                show.setWidth(null != dataMap.get("width") ? (int)Double.parseDouble(dataMap.get("width").toString()) : null);
                                show.setHeight(null != dataMap.get("height") ? (int)Double.parseDouble(dataMap.get("height").toString()) : null);
                                realityShows.add(show);
                            } catch (Exception e) {
                                LOGGER.error("parse reality show failed : {}", e);
                            }
                        }
                        return realityShows;
                    }
                }
            }
        } catch (Throwable e) {
            LOGGER.error("getVideoInfo failed : {}", e);
        }
        return null;
    }

    private static StarInfo getStarInfo(Map<String, Object> hongRenInfo) {
        if (null == hongRenInfo || hongRenInfo.isEmpty()) {
            return null;
        }
        StarInfo starInfo = new StarInfo();
        starInfo.setEffect(null != hongRenInfo.get("effect") ? hongRenInfo.get("effect").toString() : null);
        starInfo.setHeight(null != hongRenInfo.get("height") ? (Double) (hongRenInfo.get("height")) : null);
        starInfo.setWeight(null != hongRenInfo.get("weight") ? (Double) (hongRenInfo.get("weight")) : null);
        starInfo.setName(null != hongRenInfo.get("name") ? hongRenInfo.get("name").toString() : null);
        starInfo.setAvatar(null != hongRenInfo.get("name") ? ImageUtil.img(hongRenInfo.get("avatar").toString()) : null);
//        starInfo.setId(((Double)(hongRenInfo.get("id"))).longValue());
        return starInfo;
    }

    /**
     * 格式化cid，将 "#3856# #1734# #999# #2946#"  转为： "3856,1734,999,2946"
     *
     * @param cids
     * @return
     */
    private Integer getRootCid(String cids) {
        if (StringUtils.isBlank(cids)) {
            return null;
        }

        if (!cids.contains("#")) {
            return null;
        }

        String[] cidArr = cids.replace("# #", ",").replace("#", "").split(",");
        if (cidArr.length > 0) {
            return Integer.parseInt(cidArr[0]);
        }
        return null;
    }

}
