package com.mogujie.detail.module.shop.provider;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.meili.service.shopcenter.domain.entity.ShopCategory;
import com.meili.service.shopcenter.domain.entity.ShopInfo;
import com.meili.service.shopcenter.result.Result;
import com.meili.service.shopcenter.util.AttributesUtil;
import com.meili.service.shopcenter.util.ShopInfoTagsUtil;
import com.mogujie.cart.api.CartQueryService;
import com.mogujie.cart.domain.dto.request.CartQueryReqDTO;
import com.mogujie.cart.domain.dto.response.CartQueryResDTO;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailContextHolder;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.SwitchKey;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.spi.SpiAutowired;
import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.core.util.ImageUtil;
import com.mogujie.detail.core.util.MaitUtil;
import com.mogujie.detail.core.util.MetabaseTool;
import com.mogujie.detail.module.extra.domain.BuyerInfo;
import com.mogujie.detail.module.shop.domain.DetailShopCategory;
import com.mogujie.detail.module.shop.domain.ShopDO;
import com.mogujie.detail.module.shop.domain.ShopDsr;
import com.mogujie.detail.module.shop.domain.ShopServicesDO;
import com.mogujie.detail.module.shop.spi.IShopCategoryUrlProvider;
import com.mogujie.detail.module.shop.spi.IShopCollectInfoProvider;
import com.mogujie.detail.module.shop.spi.IShopDsrProvider;
import com.mogujie.detail.module.shop.spi.IShopLabelProvider;
import com.mogujie.detail.module.shop.spi.IShopSalesProvider;
import com.mogujie.detail.module.shop.spi.IShopTagProvider;
import com.mogujie.detail.module.shop.spi.IShopUrlProvider;
import com.mogujie.detail.module.shop.spi.IWaitressProvider;
import com.mogujie.dts.api.query.Query;
import com.mogujie.dts.api.query.column.OrderByColumn;
import com.mogujie.dts.api.query.condition.Conditions;
import com.mogujie.dts.api.query.constant.Operator;
import com.mogujie.dts.api.result.QueryResult;
import com.mogujie.dts.api.result.RowResult;
import com.mogujie.dts.api.service.DtsQueryService;
import com.mogujie.dts.utils.TokenUtil;
import com.mogujie.metabase.spring.client.MetabaseClient;
import com.mogujie.service.item.api.basic.SkuReadService;
import com.mogujie.service.item.api.basic.facade.ShopFacade;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.service.item.domain.query.QuerySkuOptions;
import com.mogujie.service.item.domain.result.BaseResultDO;
import com.mogujie.service.item.search.api.ItemSearchService;
import com.mogujie.service.relation.api.RelationReadFacade;
import com.mogujie.service.relation.domain.RelationDto;
import com.mogujie.service.relation.domain.enums.AppIdEnums;
import com.mogujie.service.relation.domain.enums.AssociationsTypeEnums;
import com.mogujie.service.relation.domain.enums.CounterType;
import com.mogujie.service.relation.domain.enums.Direction;
import com.mogujie.service.relation.domain.response.RpcResult;
import com.mogujie.service.relation.domain.response.counter.RelationCounterReponseDto;
import com.mogujie.service.shopcenter.client.ShopCategoryServiceClient;
import com.mogujie.service.trade.microservice.order.api.query.shoporder.ShopOrderQueryService;
import com.mogujie.service.trade.microservice.order.domain.dto.query.req.QueryOrderIdsByBuyerReqDTO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import com.mogujie.trade.response.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by xiaoyao on 16/8/16.
 */
@Module(name = "shop")
public class ShopDOProvider implements IModuleDOProvider<ShopDO> {

    private static final Logger LOGGER = LoggerFactory.getLogger(ShopDOProvider.class);

    @Autowired
    protected ShopFacade shopFacade;

    protected Gson gson;

    @SpiAutowired
    protected IShopUrlProvider shopUrlProvider;

    @SpiAutowired
    protected IWaitressProvider waitressProvider;

    @SpiAutowired
    protected IShopSalesProvider shopSalesProvider;

    @SpiAutowired
    protected IShopCategoryUrlProvider shopCategoryUrlProvider;

    @SpiAutowired
    protected IShopCollectInfoProvider shopCollectInfoProvider;

    @SpiAutowired
    protected IShopTagProvider shopTagProvider;

    @SpiAutowired
    private IShopDsrProvider shopDsrProvider;

    @SpiAutowired
    protected IShopLabelProvider shopLabelProvider;

    @Autowired
    private CommonSwitchUtil commonSwitchUtil;

    @Resource(name = "confMetabaseClient")
    protected MetabaseClient metabaseClient;

    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    private CartQueryService cartQueryService;

    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    private ShopOrderQueryService shopOrderQueryService;

    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    private SkuReadService skuReadService;

    public static final long IMTIP_MAIT_ID = 138669L;

    @Autowired
    private ItemSearchService itemSearchService;

    private RelationReadFacade relationReadFacade;

    private DtsQueryService dtsQueryService;

    @Override
    public void init() throws DetailException {
        try {
            relationReadFacade = TeslaServiceConsumerFactory.getTeslaServiceConsumer(RelationReadFacade.class);
            dtsQueryService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(DtsQueryService.class);
            gson = new Gson();
        } catch (Exception e) {
            LOGGER.error("init shopDetailModule failed : {}", e);
            throw new DetailException(e);
        }
    }

    @Override
    public ShopDO emit(DetailContext context) {
        if (context.isDyn()) {
            return getShopInfoDyn(context);
        } else {
            ShopDO shopDO = this.getShopInfo(context);
            if (null != shopDO && commonSwitchUtil.isOn(SwitchKey.SHOPCENTER_SHOP_CATEGORY)) {
                ItemDO item = context.getItemDO();
                try {
                    Result<List<ShopCategory>> shopCategoryRet = ShopCategoryServiceClient.getAllShopCategories(item.getShopId());
                    if (null != shopCategoryRet && shopCategoryRet.isSuccess()) {
                        List<ShopCategory> shopCategories = shopCategoryRet.getData();
                        if (!CollectionUtils.isEmpty(shopCategories)) {
                            List<DetailShopCategory> categories = new ArrayList<>(shopCategories.size());
                            for (ShopCategory category : shopCategories) {
                                if (category.getStatus() != 1) {
                                    continue;
                                }
                                DetailShopCategory shopCategory = new DetailShopCategory();
                                shopCategory.setName(category.getName());
                                shopCategory.setLink(shopCategoryUrlProvider.getShopCategoryUrl(context, category.getId().intValue()));
                                categories.add(shopCategory);
                            }
                            shopDO.setCategories(categories);
                        }
                    }
                } catch (Throwable e) {
                    LOGGER.error("get shop category failed : {}", e);
                }
            }
            return shopDO;
        }
    }

    protected ShopDO getShopInfoDyn(DetailContext context) {
        ShopDO shopDO = new ShopDO();

        // 设置店铺标
        ShopInfo baseShopInfo = context.getItemDO().getShopInfo();
        if (null == baseShopInfo) {
            return shopDO;
        }

        // 设置是否收藏、店铺收藏数、店铺登记
        setBaseShopInfoDyn(context, shopDO);

        //店铺体系缓存灰度开关设置
        int rate = Integer.parseInt(MetabaseTool.getValue("waitress_cache_rate"));
        if (context.getItemDO().getItemId() % 100 >= rate) {
            // 设置服务体系
            ShopServicesDO services = waitressProvider.listService(context);
            shopDO.setServices(services == null ? null : services.getNormalServices());
            shopDO.setGoodItemServices(services == null ? null : services.getGoodItemServices());
            shopDO.setItemPromiseDeliveryTime(services == null ? null : services.getItemPromiseDeliveryTime());
        }

        List<Integer> tagsList = new ArrayList<>();
        if (StringUtils.isNotBlank(baseShopInfo.getTags())) {
            String[] tagsStr = baseShopInfo.getTags().split(",");
            for (String tag : tagsStr) {
                tagsList.add(Integer.parseInt(tag));
            }
        }
        shopDO.setTags(tagsList);

        // 当天加购、下单信息
        setAddCartAndBillInfo(context, shopDO);

        // 设置店铺标签（商详入口，非店铺标）
        if (commonSwitchUtil.isOn(SwitchKey.SWT_SHOW_SHOP_LABELS)) {
            shopLabelProvider.listLabel(context, shopDO);
        }

        if (context.getItemDO() != null && context.getItemDO().getShopInfo() != null) {
            shopDO.setName(context.getItemDO().getShopInfo().getName());
        }

        List<ShopDsr> shopDsrs = shopDsrProvider.listShopDsr(context, shopDO);
        shopDO.setScore(shopDsrs);

        return shopDO;
    }

    protected ShopDO getShopInfo(DetailContext context) {
        ShopDO shopDO = new ShopDO();

        //店铺体系缓存灰度开关设置
        int rate = Integer.parseInt(MetabaseTool.getValue("waitress_cache_rate"));
        if (context.getItemDO().getItemId() % 100 >= rate) {
            // 设置服务体系
            ShopServicesDO services = waitressProvider.listService(context);
            shopDO.setServices(services == null ? null : services.getNormalServices());
            shopDO.setGoodItemServices(services == null ? null : services.getGoodItemServices());
            shopDO.setItemPromiseDeliveryTime(services == null ? null : services.getItemPromiseDeliveryTime());
        }

        // 设置店铺的基本信息，包括店铺名, logo, shopId, allGoodsUrl, shopUrl, isMarked
        setBaseShopInfo(context, shopDO);

        // 设置shopDsr
        //List<ShopDsr> shopDsrs = getShopDsrs(commonItem.getShopId());
        List<ShopDsr> shopDsrs = shopDsrProvider.listShopDsr(context, shopDO);
        shopDO.setScore(shopDsrs);

        // 当天加购、下单信息
        setAddCartAndBillInfo(context, shopDO);

        // 店铺标签（商详入口，非店铺标）
        if (commonSwitchUtil.isOn(SwitchKey.SWT_SHOW_SHOP_LABELS)) {
            shopLabelProvider.listLabel(context, shopDO);
        }

        return shopDO;
    }

    private String getSellDsrStar(Long shopId) {
        String star = "0";
        try {
            String visit_date_begin = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now().minusDays(2));
            String visit_date_end = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now().minusDays(1));

            Query query = new Query();
            query.addColumn("226_sell_star");
            query.addColumn("226_sell_visit_date");
            query.setCondition(
                    Conditions.and(
                            Conditions.condition("visit_date", Operator.GE, visit_date_begin),
                            Conditions.condition("visit_date", Operator.LE, visit_date_end),
                            Conditions.condition("shopid", Operator.EQ, shopId)
                    )
            );

            query.setOrderBy(OrderByColumn.desc("226_sell_visit_date"));
            String token = TokenUtil.produce("detail", "detail_dts");
            QueryResult queryResult = dtsQueryService.query(query, "detail", token);
            if (queryResult != null && !CollectionUtils.isEmpty(queryResult.getRowResults())) {
                RowResult rowResult = queryResult.getRowResults().get(0);
                Double starVal = rowResult.get("226_sell_star").asInteger() / 100d;
                    star = String.valueOf(starVal);
                }
        } catch (Exception e) {
            LOGGER.error("获取星级信息失败, {}", e.getMessage(), e);
        }
        return star;
    }

    private void setAddCartAndBillInfo(DetailContext context, ShopDO shopDO) {
        if (context == null || context.getItemDO() == null || shopDO == null) return;
        shopDO.setImTips(true);
        // 查询当天是否在本店加购过
        if (commonSwitchUtil.isOn(SwitchKey.SWT_SHOW_IM_TIPS) /* metabase降级 */
                && inShopWhiteList(shopDO) /* 麦田配置店铺标 */) {
            if (context.getLoginUserId() == null) {
                // 未登录用户认为未加购未购买
                shopDO.setImTips(true);
            } else {
                Long shopId = context.getItemDO().getShopId();
                Long sellerId = context.getItemDO().getUserId();
                try {
                    boolean addCartToday = false;
                    CartQueryReqDTO reqDTO = new CartQueryReqDTO();
                    reqDTO.setAddTime(timeSecondsWhenTodayBegin());
                    reqDTO.setBuyerUserId(context.getLoginUserId());
                    Response<List<CartQueryResDTO>> ret = cartQueryService.querySkuInfoByUserIdAndUpdated(reqDTO);
                    List<CartQueryResDTO> skus = ret.getData();
                    if (skus != null && !skus.isEmpty()) {
                        // 接口只支持最多一次查询50个sku，最多查询6次
                        int index = 0;
                        for (int reqTimes = 0; reqTimes < 6 && index < skus.size(); reqTimes++) {
                            List<Long> skuIds = new ArrayList<>();
                            for (int nextIndex = index + 50; index < nextIndex && index < skus.size(); index++) {
                                skuIds.add(skus.get(index).getStockId());
                            }
                            QuerySkuOptions options = new QuerySkuOptions();
                            options.setQueryBasicItem(true);
                            BaseResultDO<List<ItemSkuDO>> baseResultDO = skuReadService.queryItemSkuDOListByIds(skuIds, options);
                            for (ItemSkuDO itemSkuDO : baseResultDO.getResult()) {
                                if (shopId == itemSkuDO.getItemDO().getShopId()) {
                                    addCartToday = true;
                                    break;
                                }
                            }

                            if (addCartToday) {
                                break;
                            }
                        }
                        if (addCartToday) {
                            shopDO.setImTips(false);
                        }
                    }
                } catch (Throwable e) {
                    LOGGER.error("get addcart info fail , ", e);
                }

                // 查询当天是否在本店下单过
                try {
                    QueryOrderIdsByBuyerReqDTO orderReqDTO = new QueryOrderIdsByBuyerReqDTO();
                    orderReqDTO.setCreateFrom(timeSecondsWhenTodayBegin());
                    orderReqDTO.setSellerUserId(sellerId);
                    orderReqDTO.setPage(1);
                    orderReqDTO.setPageSize(1);
                    orderReqDTO.setBuyerUserId(context.getLoginUserId());
                    Response<List<Long>> ret = shopOrderQueryService.getShopOrderIdsByBuyerAndSeller(orderReqDTO);
                    List<Long> orders = ret.getData();
                    boolean billToday = orders != null && !orders.isEmpty();
                    if (billToday) {
                        shopDO.setImTips(false);
                    }
                } catch (Throwable e) {
                    LOGGER.error("get order info fail , ", e);
                }
            }
        } else {
            shopDO.setImTips(false);
        }
    }

    private boolean inShopWhiteList(ShopDO shopDO) {
        List<Map<String, Object>> resources = MaitUtil.getMaitData(IMTIP_MAIT_ID);
        if (resources != null && !resources.isEmpty()) {
            Map<String, Object> maitData = resources.get(0);
            String shopTags = String.valueOf(maitData.get("shopTags"));
            boolean in = false;
            for (String tag : shopTags.split(",")) {
                try {
                    int tagInt = Integer.valueOf(tag);
                    in |= shopDO.getTags().contains(tagInt);
                } catch (NumberFormatException e) {
                    LOGGER.warn(String.format("parse mait: %s error，\"shopTags\" should be nums", IMTIP_MAIT_ID), e);
                }
            }
            return in;
        }
        return false;
    }

    private long timeSecondsWhenTodayBegin() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis() / 1000L;
    }

    public void setBaseShopInfo(DetailContext context, ShopDO shopDO) {
        try {
            DetailItemDO item = context.getItemDO();
            // 店主userId
            String sellUserId = IdConvertor.idToUrl(item.getUserId());
            shopDO.setUserId(sellUserId);

            ShopInfo baseShopInfo = item.getShopInfo();
            if (null == baseShopInfo) {
                return;
            }

            String shopLogo = baseShopInfo.getLogo();
            shopDO.setShopLogo(ImageUtil.img(shopLogo));
            shopDO.setType(baseShopInfo.getType());
            shopDO.setTagId(baseShopInfo.getTagId());
            if (ShopInfoTagsUtil.stringToSet(baseShopInfo.getTags()).contains(750)) {
                shopDO.setLevel(-1);
            } else {
                shopDO.setLevel(baseShopInfo.getLevel());
                if (metabaseClient.getBoolean("shopLevelSwitch")) {
                    Map<String, String> map = AttributesUtil.fromString(baseShopInfo.getAttributes());
                    if (map.containsKey("level") && StringUtils.isNotBlank(map.get("level"))) {
                        shopDO.setLevel(Integer.parseInt(map.get("level")));
                    }
                }
            }

            String shopName = baseShopInfo.getName();
            shopDO.setName(shopName);

            String shopId = IdConvertor.idToUrl(item.getShopId());
            shopDO.setShopId(shopId);
            shopDO.setCGoods(getGoodsTotal(context.getItemDO().getShopId()));

            shopDO.setStar(getSellDsrStar(baseShopInfo.getShopId()));

            shopDO.setTag(shopTagProvider.getShopTag(context, baseShopInfo));
            // 设置店铺标
            List<Integer> tagsList = new ArrayList<>();
            if (StringUtils.isNotBlank(baseShopInfo.getTags())) {
                String[] tagsStr = baseShopInfo.getTags().split(",");
                for (String tag : tagsStr) {
                    tagsList.add(Integer.parseInt(tag));
                }
            }
            shopDO.setTags(tagsList);

            // 店铺总销量
            shopDO.setCSells(shopSalesProvider.getShopSales(context).intValue());
            Integer cfans = 0;
            if (commonSwitchUtil.isOn(SwitchKey.SHOPFAVORITE_SHOP_FAN_COUNT)) {
                cfans = this.getShopCFans(item.getShopId());
            }
            shopDO.setCFans(cfans);

            // 设置是否收藏
            Long curUserId = context.getLoginUserId();
            if (null == curUserId || curUserId == -1L) {
                shopDO.setIsMarked(false);
            } else {
                shopDO.setIsMarked(shopCollectInfoProvider.isCollected(context));
            }
        } catch (Exception e) {
            LOGGER.warn("get shopInfo error! ", e);
        }
    }

    public void setBaseShopInfoDyn(DetailContext context, ShopDO shopDO) {
        try {
            DetailItemDO item = context.getItemDO();
            // 店主userId
            String sellUserId = IdConvertor.idToUrl(item.getUserId());
            shopDO.setUserId(sellUserId);

            String shopId = IdConvertor.idToUrl(item.getShopId());
            shopDO.setShopId(shopId);

            ShopInfo baseShopInfo = item.getShopInfo();
            if (null == baseShopInfo) {
                return;
            }

            shopDO.setTagId(baseShopInfo.getTagId());
            // 是否收藏
            Long curUserId = context.getLoginUserId();
            if (null == curUserId) {
                shopDO.setIsMarked(false);
            } else {
                shopDO.setIsMarked(shopCollectInfoProvider.isCollected(context));
            }
            // 店铺总销量
            shopDO.setCSells(shopSalesProvider.getShopSales(context).intValue());

            // 店铺收藏数（粉丝数）
            Integer cfans = 0;
            if (commonSwitchUtil.isOn(SwitchKey.SHOPFAVORITE_SHOP_FAN_COUNT)) {
                cfans = this.getShopCFans(item.getShopId());
            }
            shopDO.setCFans(cfans);

            // 店铺登记
            if (ShopInfoTagsUtil.stringToSet(baseShopInfo.getTags()).contains(750)) {
                shopDO.setLevel(-1);
            } else {
                shopDO.setLevel(baseShopInfo.getLevel());
                if (metabaseClient.getBoolean("shopLevelSwitch")) {
                    Map<String, String> map = AttributesUtil.fromString(baseShopInfo.getAttributes());
                    if (map.containsKey("level") && StringUtils.isNotBlank(map.get("level"))) {
                        shopDO.setLevel(Integer.parseInt(map.get("level")));
                    }
                }
            }


        } catch (Exception e) {
            LOGGER.warn("get shopInfo error! ", e);
        }
    }

    /**
     * 获取店铺粉丝数
     *
     * @param shopId 店铺ID
     * @return
     */
    private int getShopCFans(long shopId) {
        try {
            RelationDto relationDto = new RelationDto();
            relationDto.setToId(shopId);
            relationDto.setAssociationsType(AssociationsTypeEnums.SHOP_LIKE);
            relationDto.setAppId(AppIdEnums.MoGuJie);
            RpcResult<List<RelationCounterReponseDto>> rpcResult = relationReadFacade.getCounterMuliter(Lists.newArrayList(relationDto), Direction.TO2FROM, CounterType.PV);
            if (null == rpcResult || !rpcResult.isSuccess() || CollectionUtils.isEmpty(rpcResult.getValue())) {
                return 0;
            }
            return rpcResult.getValue().get(0).getCount().intValue();
        } catch (Exception e) {
            LOGGER.error("getShopCFans from relation service Exception!shopId=" + shopId, e);
            return 0;
        }
    }


    private Integer getCGoods(Long sellerId) {
        try {
            if (App.BH.equals(DetailContextHolder.get().getRouteInfo().getApp())
                    || App.MGJ.equals(DetailContextHolder.get().getRouteInfo().getApp())) {
                return shopFacade.countShopOnlineItems(sellerId);
            }
        } catch (Throwable e) {
            LOGGER.error("get shop onlineitems failed , ", e);
        }
        return 0;
    }


    private Integer getGoodsTotal(Long shopId) {
        // 查询店铺实时推荐开关，设置true查询，false直接返回
        if (!MetabaseTool.isOn(SwitchKey.SWT_GOODS_TOTAL, false)) {
            return 0;
        }
        if (shopId == null)
            return 0;
        try {

            com.mogujie.service.item.search.domain.query.ItemSearchQuery query = new com.mogujie.service.item.search.domain.query.ItemSearchQuery();
            query.setStatuses(Arrays.asList(0, 1, 2, 10, 22));//过滤商品
            query.setShopIds(Arrays.asList(shopId));
            Map<String, Object> notMap = new HashMap<>();
            notMap.put("tags_ik", "317,800");
            query.setNotMap(notMap);

            return itemSearchService.countItems(query);
        } catch (Throwable e) {
            LOGGER.error("get shop goodsTotal failed , ", e);
        }
        return 0;
    }
}
