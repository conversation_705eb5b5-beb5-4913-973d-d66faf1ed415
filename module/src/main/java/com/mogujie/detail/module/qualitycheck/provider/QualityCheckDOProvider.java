package com.mogujie.detail.module.qualitycheck.provider;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.module.qualitycheck.domain.QualityCheckDO;

/**
 * Created by xiaoyao on 16/10/20.
 */
@Module(name = "qualitycheck")
public class QualityCheckDOProvider implements IModuleDOProvider<QualityCheckDO> {

    @Override
    public QualityCheckDO emit(DetailContext context) {
        return null;
    }

    @Override
    public void init() throws DetailException {
    }
}
