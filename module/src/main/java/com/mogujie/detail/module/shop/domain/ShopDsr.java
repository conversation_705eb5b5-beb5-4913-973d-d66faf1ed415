package com.mogujie.detail.module.shop.domain;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 15/12/3.
 */
public class ShopDsr implements Serializable {

    private static final long serialVersionUID = 760779656961779064L;


    @Setter
    @Getter
    private String name;


    @Setter
    @Getter
    private double score;


    @Setter
    @Getter
    private Boolean isBetter;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ShopDsr)) return false;

        ShopDsr shopDsr = (ShopDsr) o;

        if (Double.compare(shopDsr.getScore(), getScore()) != 0) return false;
        if (getName() != null ? !getName().equals(shopDsr.getName()) : shopDsr.getName() != null) return false;
        return getIsBetter() != null ? getIsBetter().equals(shopDsr.getIsBetter()) : shopDsr.getIsBetter() == null;
    }

    @Override
    public int hashCode() {
        int result;
        long temp;
        result = getName() != null ? getName().hashCode() : 0;
        temp = Double.doubleToLongBits(getScore());
        result = 31 * result + (int) (temp ^ (temp >>> 32));
        result = 31 * result + (getIsBetter() != null ? getIsBetter().hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "ShopDsr{" +
                "name='" + name + '\'' +
                ", score=" + score +
                ", isBetter=" + isBetter +
                '}';
    }
}
