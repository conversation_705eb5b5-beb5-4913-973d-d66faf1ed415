package com.mogujie.detail.module.collocation.provider;

import com.alibaba.fastjson.JSONObject;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.MetabaseTool;
import com.mogujie.detail.core.util.StringUtils;
import com.mogujie.detail.module.collocation.domain.CollocationDO;
import com.mogujie.detail.module.wrapper.PaganiSearchWrapper;
import com.mogujie.service.item.domain.basic.ItemTagDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Optional;

/**
 * Created by eryi
 * Date: 2020/10/28
 * Time: 10:37 AM
 * Introduction:详情页1460搭配购节点数据提供者
 * Document:
 * Actions:
 */
@Module(name = "collocation")
public class CollocationDOProvider implements IModuleDOProvider<CollocationDO> {

    /**
     * 搭配购商品才有的搭配标，用来拦截达到图墙的流量
     */
    private static final String COLLOCATION_ITEM_TAG = "3002";

    /**
     * metabase key 详情页搭配购商品类目白名单
     */
    private static final String DETAIL_COLLOCATION_ITEM_CIDS = "detail_collocation_item_cids";

    @Autowired
    private PaganiSearchWrapper paganiSearchWrapper;

    /**
     * 模块喷吐数据
     *
     * @param context
     * @return
     */
    @Override
    public CollocationDO emit(DetailContext context) {
        DetailItemDO itemDO = context.getItemDO();
        if (itemDO == null) {
            return null;
        }
        BizType bizType = context.getRouteInfo().getBizType();
        if (bizType == null || bizType != BizType.NORMAL) {
            return null;
        }
        // 标签校验
        if (!checkItemTagPass(itemDO)) {
            return null;
        }
        // 类目校验
        if (!checkItemCatePass(itemDO)) {
            return null;
        }
        // 数据查询与组装
        JSONObject sourceData = paganiSearchWrapper.getItemCollocationData(itemDO.getItemId(), itemDO.getShopId());
        if (sourceData == null) {
            return null;
        }
        return CollocationDO.convert(sourceData);
    }

    /**
     * 校验当前商品所在类目是否是需要展示搭配购信息的商品类目
     *
     * @param itemDO 当前商品信息
     * @return
     */
    private boolean checkItemCatePass(DetailItemDO itemDO) {
        String whiteCid = MetabaseTool.getValue(DETAIL_COLLOCATION_ITEM_CIDS);
        if(StringUtils.isEmpty(whiteCid)){
            return Boolean.FALSE;
        }
        String[] cidArr = whiteCid.split(",");
        for (String cid : cidArr) {
            if (itemDO.getCids().contains("#" + cid + "#")) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 校验当前商品是否有3002标签
     *
     * @param itemDO 当前商品信息
     * @return
     */
    private boolean checkItemTagPass(DetailItemDO itemDO) {
        List<ItemTagDO> itemTags = itemDO.getItemTags();
        if(CollectionUtils.isEmpty(itemTags)){
            return Boolean.FALSE;
        }
        Optional<ItemTagDO> optional = itemTags
                .stream()
                .filter(itemTagDO -> itemDO != null
                        && "tags".equals(itemTagDO.getTagKey())
                        && COLLOCATION_ITEM_TAG.equals(itemTagDO.getTagValue()))
                .findFirst();
        return optional.isPresent();
    }

    /**
     * 初始化
     */
    @Override
    public void init() throws DetailException {

    }

}
