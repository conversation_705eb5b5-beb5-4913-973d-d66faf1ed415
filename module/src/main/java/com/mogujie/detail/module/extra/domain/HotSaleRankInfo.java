package com.mogujie.detail.module.extra.domain;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;

/**
 * Created by eryi
 * Date: 2020/10/23
 * Time: 2:16 PM
 * Introduction: 1460班车热卖榜单信息
 * Document:https://mogu.feishu.cn/docs/doccn6rKzCxAeEykwucMrhLbwrh#
 * Actions:
 */
@Data
public class HotSaleRankInfo {

    /**
     * 榜单背景图片
     */
    private String bgImage;

    /**
     * 榜单跳转链接
     */
    private String link;

    /**
     * 榜单名前缀
     */
    private String prefixTitle;

    /**
     * 榜单名后缀
     */
    private String suffixTitle;

    /**
     * 排行数字
     */
    private Long rankNumber;

    /**
     * 榜单id的数字形式
     */
    private String rankIdNum;

    /**
     * 榜单id的url形式
     */
    private String rankId;

    /**
     * 排名数字颜色
     */
    private String numberColor;

    public HotSaleRankInfo() {
    }

    public HotSaleRankInfo(String bgImage, String link) {
        this.bgImage = bgImage;
        this.link = link;
    }

    /**
     * 类型转换
     *
     * @param source 原始数据
     * @return
     */
    public static HotSaleRankInfo convert(JSONObject source) {
        if (MapUtils.isEmpty(source)) {
            return null;
        }
        HotSaleRankInfo hotSaleRankInfo = new HotSaleRankInfo();
        hotSaleRankInfo.setLink(source.getString("link"));
        hotSaleRankInfo.setBgImage(source.getString("bgImage"));
        hotSaleRankInfo.setPrefixTitle(source.getString("prefixTitle"));
        hotSaleRankInfo.setSuffixTitle(source.getString("suffixTitle"));
        hotSaleRankInfo.setRankId(source.getString("rankId"));
        hotSaleRankInfo.setRankIdNum(source.getString("rankIdNum"));
        hotSaleRankInfo.setRankNumber(source.getLong("rankNumber"));
        hotSaleRankInfo.setNumberColor(source.getString("numberColor"));
        return hotSaleRankInfo;
    }

}
