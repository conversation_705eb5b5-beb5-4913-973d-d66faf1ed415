package com.mogujie.detail.module.detail.util;


import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Created by poyun on 15/12/17.
 * <p/>
 * 获取类目类型信息
 */
public class CategoryTypeUtil {

    private List<Integer> cids;
    private Integer rootCid = null;

    public CategoryTypeUtil(String path) {
        cids = parsePath(path);
        if (CollectionUtils.isNotEmpty(cids))
            rootCid = cids.get(0);
    }

    private List<Integer> parsePath(String path) {
        if (StringUtils.isBlank(path)) {
            return Collections.emptyList();
        }
        path = StringUtils.replace(path, "#", "");
        String[] cidStrs = StringUtils.split(path, " ");
        List<Integer> cids = new ArrayList<>();
        for (String cidStr : cidStrs) {
            cids.add(NumberUtils.toInt(cidStr));
        }
        return cids;
    }

    public boolean isBeauty() {
        return 1160 == rootCid;
    }

    public boolean isVirtual() {
        return 1264 == rootCid;
    }

//    public boolean isSuit() {
//        return 703 == rootCid || 756 == rootCid;
//    }
//
//    public boolean isPlaything() {
//        return 1594 == rootCid;
//    }
//
//    public boolean isUnderWear() {
//        List<Integer> underWear = Arrays.asList(1263,//食品
//                1395,//内衣
//                722,//文胸
//                723,//文胸套装
//                724,//内裤
//                729,//睡袍、浴袍
//                730,//塑身衣
//                817,//短袜/打底袜/丝袜/美腿袜
//                737//泳衣
//        );
//        for (Integer cid : this.cids) {
//            if (underWear.contains(cid))
//                return true;
//        }
//        return false;
//    }
//
//    public boolean isApparel() {
//        // 683 衣服 757 鞋子 777 包包 795 配饰
//        List<Integer> apparel = Arrays.asList(683, 757, 777, 795);
//        for (Integer cid : this.cids) {
//            if (apparel.contains(cid))
//                return true;
//        }
//        return false;
//    }

    public boolean isClothes() {
        // 683 女装 822 男装
        return cids.contains(683) || cids.contains(822);
    }

//    public boolean isGirlClothes() {
//        return cids.contains(683);
//    }

    public boolean isShoes() {
        // 757 女鞋 869 男鞋
        return cids.contains(757) || cids.contains(869);
    }

//    public boolean isGirlShoes() {
//        return cids.contains(757);
//    }

    public boolean isBags() {
        // 777 女包 889 男包
        return cids.contains(777) || cids.contains(889);
    }

    public boolean isAccessories() {  // 配饰
        return cids.contains(795) || cids.contains(1584);
    }

    public boolean isHome() {  // 家居
        return cids.contains(925);
    }

    public boolean isDigital() {  // 数码
        return 1049 == rootCid;
    }

//    public boolean isBaby() {  // 母婴
//        return cids.contains(1417);
//    }
//
//    public boolean isUnderclothes() {  // 内衣
//        return cids.contains(1395);
//    }
//
//    public boolean isSexyUnderclothes() {  // 情趣内衣
//        return cids.contains(1410);
//    }

    public boolean isHousehold() {   // 家电
        return 1089 == rootCid;
    }

    public boolean isFood() {  // 食品
        return 1263 == rootCid;
    }

//    public boolean isOther() {  // 其他
//        return 1266 == rootCid;
//    }

    public boolean isTravel() {
        return cids.contains(1622);
    }

    public boolean isHotel() {
        return cids.contains(1623);
    }

    public boolean isCar() {
        return cids.contains(1603);
    }

//    public boolean isNutrition() {
//        return cids.contains(2072);
//    }
    public List<Integer> cids(){return  cids;}
}
