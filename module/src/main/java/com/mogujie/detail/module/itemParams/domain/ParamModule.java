package com.mogujie.detail.module.itemParams.domain;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 16/4/19.
 */
public class ParamModule {
    private String key;
    private String anchor;
    private String desc;
    private List<String> imgs;

    public ParamModule() {
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getAnchor() {
        return anchor;
    }

    public void setAnchor(String anchor) {
        this.anchor = anchor;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public List<String> getImgs() {
        return imgs;
    }

    public void setImgs(List<String> imgs) {
        this.imgs = imgs;
    }

    @Override
    public String toString() {
        return "ParamModule{" +
                "key='" + key + '\'' +
                ", anchor='" + anchor + '\'' +
                ", desc='" + desc + '\'' +
                ", imgs=" + imgs +
                '}';
    }
}
