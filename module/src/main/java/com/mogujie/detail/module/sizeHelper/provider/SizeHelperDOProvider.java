package com.mogujie.detail.module.sizeHelper.provider;

import com.mogujie.darling.daren.model.SizeMatchDTO;
import com.mogujie.darling.daren.service.SearchUserSizeApi;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailContextHolder;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.SwitchKey;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.core.util.ImageUtil;
import com.mogujie.detail.module.groupbuying.util.GroupbuyingUtil;
import com.mogujie.detail.module.sizeHelper.domain.SizeHelperDO;
import com.mogujie.market.base.model.ResultBase;
import com.mogujie.metabase.spring.client.MetabaseClient;
import com.mogujie.service.muser.Result;
import com.mogujie.service.muser.api.v1.UserService;
import com.mogujie.service.muser.domain.entity.v1.UsersInfo;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

/**
 * 尺码助手
 * Created by anshi on 17/3/27.
 */
@Module(name = "sizeHelper")
public class SizeHelperDOProvider implements IModuleDOProvider<SizeHelperDO> {

    private static final Logger logger = LoggerFactory.getLogger(SizeHelperDOProvider.class);

    @Autowired
    private SearchUserSizeApi searchUserSizeApi;

    private UserService userService;

    private static final String DEFAULT_AVATAR = "/mlcdn/e5265e/170328_316lj459hfeg59b38lda201e1h9cd_120x120.png";

    private static final String[] SIZE_HELPER_CIDS = {"#705#", "#706#", "#684#", "#710#"};

    @Resource(name = "confMetabaseClient")
    private MetabaseClient metabaseClient;

    @Autowired
    private CommonSwitchUtil commonSwitchUtil;

    @Override
    public SizeHelperDO emit(DetailContext context) {
        //过滤
        if (!isSizeHelperItem(context.getItemDO()) || DetailContextHolder.get().getRouteInfo().getBizType() == BizType.SECKILL) {
            return null;
        }
        SizeHelperDO sizeHelperDO = new SizeHelperDO();
        Long userId = context.getLoginUserId();
        if (userId != null && userId != -1) {
            try {
                // 设置用户头像
                Result<UsersInfo> userResult = userService.queryUserByUserId(userId);
                if (userResult == null || userResult.getValue() != null) {
                    UsersInfo usersInfo = userResult.getValue();
                    sizeHelperDO.setAvartar(ImageUtil.img(usersInfo.getAvatar()));
                } else {
                    sizeHelperDO.setAvartar(ImageUtil.img(DEFAULT_AVATAR));
                }

                // 设置用户尺码信息
                ResultBase<SizeMatchDTO> result = searchUserSizeApi.queryUserSizeInfo(userId, true);
                if (result != null && result.isSuccess() && result.getValue() != null) {
                    SizeMatchDTO sizeMatchDTO = result.getValue();
                    sizeHelperDO.setMatchedSizeType(sizeMatchDTO.getMatchIngCode());
                    sizeHelperDO.setUserInfoId(sizeMatchDTO.getId());
                    sizeHelperDO.setHeight(sizeMatchDTO.getHeight());
                    sizeHelperDO.setWeight(sizeMatchDTO.getWeight());
                    sizeHelperDO.setHipline(sizeMatchDTO.getHipline());
                    sizeHelperDO.setChest(sizeMatchDTO.getChest());
                    sizeHelperDO.setWaist(sizeMatchDTO.getWaist());
                    sizeHelperDO.setShow(sizeMatchDTO.getIsShow() != null && sizeMatchDTO.getIsShow() == 1);
                    sizeHelperDO.setUserInfoFilled(true);
                }
            } catch (Throwable e) {
                logger.error("calling searchUserSizeApi error.", e);
            }
        } else {
            // 未登录用户的默认头像
            sizeHelperDO.setAvartar(ImageUtil.img(DEFAULT_AVATAR));
        }
        return sizeHelperDO;
    }

    /**
     * 是否为尺码助手商品
     * 类目属于:
     * 连衣裙：服饰鞋包682-女装683-裙子704-连衣裙705
     * 裙子：服饰鞋包682-女装683-裙子704-半身裙706
     * 上衣：服饰鞋包682-女装683-上装684
     * 裤子：服饰鞋包682-女装683-裤子710
     *
     * @param itemDO
     * @return
     */
    private boolean isSizeHelperItem(DetailItemDO itemDO) {
        String cids = itemDO.getCids();
        if (StringUtils.isBlank(cids)) {
            return false;
        }

        for (String cid : SIZE_HELPER_CIDS) {
            if (cids.contains(cid) &&
                    ((commonSwitchUtil.isOn(SwitchKey.SIZEHELPER_UZHI_FILTER) && GroupbuyingUtil.isUZhiItem(itemDO))
                            || !commonSwitchUtil.isOn(SwitchKey.SIZEHELPER_UZHI_FILTER))) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void init() throws DetailException {
        try {
            userService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(UserService.class);
        } catch (Exception e) {
            logger.error("init tesla service failed!");
            throw new DetailException(e);
        }
    }
}
