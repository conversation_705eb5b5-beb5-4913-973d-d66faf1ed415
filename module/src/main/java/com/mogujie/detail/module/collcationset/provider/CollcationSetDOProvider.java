package com.mogujie.detail.module.collcationset.provider;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.ContextUtil;
import com.mogujie.detail.core.util.ImageUtil;
import com.mogujie.detail.module.collcationset.domain.CollcationSetDO;
import com.mogujie.service.hummer.api.CollocationSetReadService;
import com.mogujie.service.hummer.constains.RequestConstants;
import com.mogujie.service.hummer.domain.dto.collocationset.CollocationSetCampaignForDetailDTO;
import com.mogujie.service.hummer.domain.dto.collocationset.CollocationSetDetailQueryDTO;
import com.mogujie.service.hummer.domain.dto.collocationset.CollocationSetItemGroupDTO;
import com.mogujie.service.hummer.domain.dto.result.Result;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * Created by xiaoyao on 16/10/20.
 */
@Module(name = "collcationset")
public class CollcationSetDOProvider implements IModuleDOProvider<CollcationSetDO> {

    @Autowired
    private CollocationSetReadService collocationSetReadService;

    private static final Logger LOGGER = LoggerFactory.getLogger(CollcationSetDOProvider.class);

    @Override
    public CollcationSetDO emit(DetailContext context) {
        return getCollcationSet(context);
    }

    @Override
    public void init() throws DetailException {
    }

    private CollcationSetDO getCollcationSet(DetailContext context) {
        if (context.isDyn()) {
            return null;
        }
        DetailItemDO itemDO = context.getItemDO();
        CollocationSetDetailQueryDTO query = new CollocationSetDetailQueryDTO();
        query.setItemId(itemDO.getItemId());
        query.setCreator(itemDO.getUserId());
        query.setExtra(itemDO.getJsonExtra());
        query.setMarket((short) ContextUtil.getMarketByContext(context));
        Result<List<CollocationSetCampaignForDetailDTO>> ret = collocationSetReadService.queryCampaignByItemIds(query);
        if (null != ret && ret.isSuccess() && !CollectionUtils.isEmpty(ret.getData())) {
            CollcationSetDO collcationSet = new CollcationSetDO();
            for (CollocationSetCampaignForDetailDTO obj : ret.getData()) {
                if (CollectionUtils.isNotEmpty(obj.getCollocationSetItemGroupDTOs())) {
                    for (CollocationSetItemGroupDTO groupDTO : obj.getCollocationSetItemGroupDTOs()) {
                        groupDTO.setImg(ImageUtil.img(groupDTO.getImg()));
                    }
                }
                collcationSet.add(obj);
            }
            return collcationSet;
        }
        return null;
    }
}
