package com.mogujie.detail.module.sku.domain;

import com.mogujie.service.pay.insurance.domain.core.InsuranceInfoDTO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * Created by xiaoyao on 15/12/7.
 */
public class SkuData {

    @Setter
    @Getter
    private String stockId;

    @Setter
    @Getter
    private String xdSkuId;

    @Setter
    @Getter
    private Integer price;

    @Setter
    @Getter
    private Integer nowprice;

    @Setter
    @Getter
    private String img;

    @Setter
    @Getter
    private String currency;

    @Setter
    @Getter
    private int stock;

    @Setter
    @Getter
    private String style;

    @Setter
    @Getter
    private String color;

    @Setter
    @Getter
    private String size;

    @Setter
    @Getter
    private int styleId;

    @Setter
    @Getter
    private int sizeId;

    /**
     * 预售定金
     */
    @Setter
    @Getter
    private String mainPriceStr;

    /**
     * 预售总价
     */
    @Setter
    @Getter
    private String subPriceStr;

    /**
     * 分期信息
     */
    @Getter
    @Setter
    private List<InstallmentData> installment;

    /**
     * 号型
     */
    @Getter
    @Setter
    private String sizeType;

    /**
     * 延迟发货时间：x日x分x秒
     * v1
     */
    @Getter
    @Setter
    private Integer delayTime;

    /**
     * 延迟发货时间：xx小时内发货
     * v2
     */
    @Getter
    @Setter
    private Integer delayHours;

    /**
     * 延迟发货原因code:delayReasonCode
     *
     */
    @Getter
    @Setter
    private String delayReasonCode;

    /**
     * 延迟发货原因:delayReason
     *
     */
    @Getter
    @Setter
    private String delayReason;

    /**
     * sku限购
     */
    @Getter
    @Setter
    private Integer purchaseSkuLimit;

    /**
     * 保险信息
     */
    @Getter
    @Setter
    private Map<Integer, List<InsuranceInfoDTO>> insuranceMap;
}
