package com.mogujie.detail.module.detail.util;

import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Map;

/**
 * 模块的标题解析工具
 * Created by <PERSON><PERSON><PERSON> on 16/4/19.
 */
public class ModuleNameUtil {
    private static final String CUSTOM_DETAIL_MODULE_NAME_PREFIX = "custom_module_";


    /**
     * 获取指定类目商品的所有模块
     * @param category
     * @return
     */
    public static Map<String, String> getModuleNames(String category) {
        Map<String, String> maps = DetailModuleConfUtil.getCategoryModules(category);

        // 没有类目的商品，图文详情不显示
        if(CollectionUtils.isEmpty(maps)) {
            return Collections.emptyMap();
        }

        // 下面的逻辑是为了兼容部分商品的baipai模块key值不一样的问题
        if (maps.containsKey("baipai_img2")) {
            maps.put("baipai_img3", maps.get("baipai_img2"));
        } else if (maps.containsKey("baipai_img3")) {
            maps.put("baipai_img2", maps.get("baipai_img3"));
        }

        return maps;
    }


    /**
     * 获取模块对应的中文名
     * @param key 模块的key值, 不能为null
     * @return
     */
    public static String getModuleName(String key, Map<String, String> maps) {
        if (StringUtils.isEmpty(key)) { // key为null,表示为topImgs补充的模块
            return null;
        }

        if (key.startsWith(CUSTOM_DETAIL_MODULE_NAME_PREFIX)) {
            // 自定义模块的显示名，就是后面的
            int len = CUSTOM_DETAIL_MODULE_NAME_PREFIX.length();
            int index = key.indexOf("_", len);
            return key.substring(index + 1);
        } else {
            // 需要查询转换
            return maps.get(key);
        }
    }
}
