
package com.mogujie.detail.module.presale.util;

import com.mogujie.detail.core.util.MaitUtil;
import com.mogujie.detail.module.presale.domain.PreSaleResouce;
import com.mogujie.metabase.utils.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by y<PERSON><PERSON> on 16/4/27.
 */
public class PreSaleUtil {
    private static final long PRE_SOURCE_ID = 5191L;

//    private static SimpleDateFormat simpleDateFormat;
//    private static SimpleDateFormat fullDateFormat;
//    private static SimpleDateFormat mlsDateFormat;

//    static {
//        simpleDateFormat = new SimpleDateFormat("MM.dd");
//        fullDateFormat = new SimpleDateFormat("MM.dd HH:mm");
//        mlsDateFormat = new SimpleDateFormat("yyyy.MM.dd HH:mm");
//    }

    public static String formateFullDate(long time) {
        SimpleDateFormat fullDateFormat = new SimpleDateFormat("MM.dd HH:mm");
        return fullDateFormat.format(
                new Date(1000 * time)
        );
    }

    public static String formatPrice(int price) {
        return String.format("¥%.2f", price / 100.0f);
    }

    /**
     * 从麦田获取预售资源
     *
     * @return
     */
    public static PreSaleResouce getPreResources() {
        List<Map<String, Object>> resources = MaitUtil.getMaitData(PRE_SOURCE_ID);
        if (CollectionUtils.isEmpty(resources)) {
            return null;
        }

        Map<String, Object> res = resources.get(0);

        Object ruleIcon = res.get("ruleIcon");
        Object declare = res.get("declare");
        Object preIcon = res.get("preIcon");
        Object ruleTitle = res.get("ruleTitle");
        Object ruleDesc = res.get("ruleDesc");
        Object preIconV2 = res.get("preIconV2");

        PreSaleResouce preSaleResouce = new PreSaleResouce();
        preSaleResouce.setRuleIcon(ruleIcon == null ? null : ruleIcon.toString());
        preSaleResouce.setDeclare(declare == null ? null : declare.toString());
        preSaleResouce.setPreIcon(preIcon == null ? null : preIcon.toString());
        preSaleResouce.setRuleTitle(ruleTitle == null ? null : ruleTitle.toString());
        preSaleResouce.setRuleDesc(getRules(ruleDesc == null ? null : ruleDesc.toString()));
        preSaleResouce.setPreIconV2(preIconV2 == null ? null : preIconV2.toString());
        return preSaleResouce;
    }


    private static List<String> getRules(String ruleDesc) {
        if (ruleDesc == null) {
            return null;
        }

        String[] rules = StringUtils.split(ruleDesc, ";");
        return Arrays.asList(rules);
    }
}
