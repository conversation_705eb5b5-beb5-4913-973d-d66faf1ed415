package com.mogujie.detail.module.presale.domain;

import java.io.Serializable;
import java.util.List;

/**
 * 预售规则
 * Created by <PERSON><PERSON><PERSON> on 16/4/27.
 */
public class RuleDesc implements Serializable{
    private static final long serialVersionUID = -4443018242521020339L;
    private String img;
    private List<String> rules;

    public RuleDesc() {
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public List<String> getRules() {
        return rules;
    }

    public void setRules(List<String> rules) {
        this.rules = rules;
    }

    @Override
    public String toString() {
        return "RuleDesc{" +
                "img='" + img + '\'' +
                ", rules='" + rules + '\'' +
                '}';
    }
}
