package com.mogujie.detail.module.itemParams.util;

import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.service.item.domain.basic.SkuAttributionDO;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * Created by <PERSON><PERSON><PERSON> on 16/4/19.
 */
public class SizeUtil {

    /**
     * 获取商品的stock attrs
     * @param item
     * @param attrKeys 用于保存sku属性的key值  如： {'脚长', '脚宽', '筒高'}
     * @return attrValues: { 35: {脚长: 245, 脚宽: 95}, 36: {脚长: 248, 脚宽: 99} }
     */
    public static Map<String, Map<String, String>> buildStockAttrs(ItemDO item, Set<String> attrKeys) {
        List<ItemSkuDO> skuList = item.getItemSkuDOList();
        if (CollectionUtils.isEmpty(skuList)) {
            return Collections.emptyMap();
        }

        Map<String, Map<String, String>> sizeTable = new LinkedHashMap<>();

        for (ItemSkuDO sku : skuList) {
            List<SkuAttributionDO> stockAttributions = sku.getAttributions();
            if (CollectionUtils.isEmpty(stockAttributions)) {
                continue;
            }
            SizeUtil.setStockAttribution(stockAttributions, sizeTable, attrKeys);
        }

        if (CollectionUtils.isEmpty(sizeTable)) {
            return Collections.emptyMap();
        }

        return sizeTable;
    }


    /**
     * 根据stock attribution属性设置尺码参数
     *
     * @param stockAttributions
     * @param stockAttrMap   { 35: {脚长: 245, 脚宽: 95}, 36: {脚长: 248, 脚宽: 99} }
     * @param attrKeys  属性的key值 : {脚长, 脚宽}
     */
    private static void setStockAttribution(List<SkuAttributionDO> stockAttributions,
                                     Map<String, Map<String, String>> stockAttrMap,
                                     Set<String> attrKeys) {
        for (SkuAttributionDO attribution : stockAttributions) {
            String properties = attribution.getProperties();
            if (StringUtils.isBlank(properties)) {
                continue;
            }

            String key = attribution.getValue();
            if (stockAttrMap.containsKey(key)) {
                return;
            }

            Map<String, String> attrMap = splitSkuProperties(properties, attrKeys);
            stockAttrMap.put(key, attrMap);
        }
    }

    /**
     * 将sku属性转为map
     *
     * @param skuProperties "脚长:245;脚宽:95;筒围:;筒高:;"
     * @param attrKeys      保存的是属性的key值 : {脚长, 脚宽}
     * @return 返回 {"脚长":245, "脚宽":95}
     */
    private static Map<String, String> splitSkuProperties(String skuProperties, Set<String> attrKeys) {
        Map<String, String> map = new LinkedHashMap<>();
        String[] properties = StringUtils.split(skuProperties, ";");
        for (String property : properties) {
            String[] kvs = StringUtils.split(property, ":");
            if (kvs.length != 2) { // 没有value的丢掉
                continue;
            }

            attrKeys.add(kvs[0]);

            map.put(kvs[0], kvs[1]);
        }

        return map;
    }
}
