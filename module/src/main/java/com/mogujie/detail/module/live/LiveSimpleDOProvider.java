package com.mogujie.detail.module.live;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.meili.service.shopcenter.domain.entity.ShopInfo;
import com.meili.service.shopcenter.util.ShopInfoTagsUtil;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.constant.DetailConstants;
import com.mogujie.detail.core.constant.SwitchKey;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.core.util.ContextUtil;
import com.mogujie.detail.module.live.domain.*;
import com.mogujie.detail.module.wrapper.PaganiSearchWrapper;
import com.mogujie.dts.api.query.Query;
import com.mogujie.dts.api.query.condition.Conditions;
import com.mogujie.dts.api.query.constant.Operator;
import com.mogujie.dts.api.result.QueryResult;
import com.mogujie.dts.api.service.DtsQueryService;
import com.mogujie.dts.utils.TokenUtil;
import com.mogujie.live.mogulive.api.enums.MoguLiveItemExplainFromEnum;
import com.mogujie.live.mogulive.api.request.MoguLiveExplainInfoQuery;
import com.mogujie.live.mogulive.api.response.MoguLiveItemExplainInfoResponse;
import com.mogujie.live.mogulive.api.result.MoguLiveRpcResult;
import com.mogujie.live.mogulive.api.service.MoguLiveItemExplainService;
import com.mogujie.live.prada.api.enums.PradaActorDsrLevelEnum;
import com.mogujie.live.prada.api.response.PradaActorDsrInfoResponse;
import com.mogujie.live.prada.api.result.PradaRpcResult;
import com.mogujie.live.prada.api.service.PradaActorDsrService;
import com.mogujie.livelist.service.LiveItemService;
import com.mogujie.market.common.util.CollectionUtil;
import com.mogujie.metabase.spring.client.MetabaseClient;
import com.mogujie.mogulive.domain.LivingInfo;
import com.mogujie.mogulive.domain.MoguLiveResponse;
import com.mogujie.mogulive.domian.actor.ActorTagInfo;
import com.mogujie.mogulive.domian.response.RpcResult;
import com.mogujie.mogulive.service.LiveReadService;
import com.mogujie.mogulive.service.SeveralInfoWithLiveTeslaService;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.basic.ItemExtraDO;
import com.mogujie.service.item.domain.basic.ItemTagDO;
import com.mogujie.service.muser.Result;
import com.mogujie.service.muser.api.v1.UserService;
import com.mogujie.service.muser.domain.entity.v1.UsersInfo;
import com.mogujie.service.rate.api.RateReadService;
import com.mogujie.service.rate.domain.RateLabelCount;
import com.mogujie.service.rate.domain.basic.req.ItemRateQueryReq;
import com.mogujie.service.rate.domain.tag.RateTag;
import com.mogujie.service.relation.api.RelationReadFacade;
import com.mogujie.service.relation.domain.RelationDto;
import com.mogujie.service.relation.domain.enums.AppIdEnums;
import com.mogujie.service.relation.domain.enums.AssociationsTypeEnums;
import com.mogujie.service.relation.domain.enums.CounterType;
import com.mogujie.service.relation.domain.enums.Direction;
import com.mogujie.service.relation.domain.response.counter.RelationCounterReponseDto;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by anshi on 17/12/6.
 */
@Module(name = "liveSimple")
public class LiveSimpleDOProvider implements IModuleDOProvider<LiveSimpleDO> {
    private static final Logger logger = LoggerFactory.getLogger(LiveSimpleDOProvider.class);

    private static final String ITEM_SHOW_EXPLAIN = "showExplain";

    private LiveItemService liveItemService;

    private SeveralInfoWithLiveTeslaService severalInfoWithLiveTeslaService;

    private RelationReadFacade relationReadFacade;

    private MoguLiveItemExplainService moguLiveItemExplainService;

    private LiveReadService liveReadService;

    private UserService userService;

    private PradaActorDsrService pradaActorDsrService;

    private DtsQueryService dtsQueryService;

    private RateReadService rateReadService;

    @Autowired
    private CommonSwitchUtil commonSwitchUtil;

    @Autowired
    private PaganiSearchWrapper paganiSearchWrapper;

    @Resource(name = "confMetabaseClient")
    private MetabaseClient metabaseClient;

    @Override
    public void init() throws DetailException {
        try {
            liveItemService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(LiveItemService.class);
            severalInfoWithLiveTeslaService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(SeveralInfoWithLiveTeslaService.class);
            relationReadFacade = TeslaServiceConsumerFactory.getTeslaServiceConsumer(RelationReadFacade.class);
            moguLiveItemExplainService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(MoguLiveItemExplainService.class);
            liveReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(LiveReadService.class);
            userService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(UserService.class);
            pradaActorDsrService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(PradaActorDsrService.class);
            rateReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(RateReadService.class);
            dtsQueryService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(DtsQueryService.class);
        } catch (Exception e) {
            logger.error("liveSimple/seller service init error.", e);
        }
    }

    @Override
    public LiveSimpleDO emit(DetailContext context) {
        LiveSimpleDO liveSimpleDO = new LiveSimpleDO();
        if (ContextUtil.isLiveInWallItem(context)) {
            getPickedExplainInfo(context, liveSimpleDO);
        } else {
            getAnchorInfo(context, liveSimpleDO);
            getExplainInfo(context, liveSimpleDO);
            getLiveItemRecommendInfo(context, liveSimpleDO);
            this.getExplainWindowInfo(context, liveSimpleDO);
        }
        this.getShopLiveInfo(context, liveSimpleDO);
        return liveSimpleDO;

    }

    /**
     * 1430店铺自播，如果当前店主正在直播，需要展示店铺直播弹窗在商品主图上面
     *
     * @param context      详情页上下文
     * @param liveSimpleDO 直播相关数据DO
     */
    private void getShopLiveInfo(DetailContext context, LiveSimpleDO liveSimpleDO) {
        // 1.直播降级开关
        if (!commonSwitchUtil.isOn(SwitchKey.SWT_SHOW_LIVE_INFO)) {
            return;
        }
        // 2.根据商品标进行过滤，只有主商品才展示
        List<ItemTagDO> itemTags = context.getItemDO().getItemTags();
        if (CollectionUtils.isNotEmpty(itemTags)) {
            Optional<ItemTagDO> shadow = itemTags.stream().filter(itemTagDO -> "800".equals(itemTagDO.getTagValue())).findFirst();
            if (shadow.isPresent()) {
                return;
            }
        }
        // 3.根据店铺标进行过滤，只有14809的店铺才可以进行店铺自播
        ShopInfo shopInfo = context.getItemDO().getShopInfo();
        if (null == shopInfo || null == shopInfo.getUserId()) {
            return;
        }
        if (!ShopInfoTagsUtil.stringToSet(shopInfo.getTags()).contains(14809)) {
            return;
        }
        Long userId = shopInfo.getUserId();
        // 4.调用直播接口，判断该店铺是否正在直播，可以降低直播接口压力
        MoguLiveResponse<List<LivingInfo>> liveResponse = liveReadService.queryUserLive(Lists.newArrayList(userId));
        if (null == liveResponse || !liveResponse.isSuccess() || CollectionUtils.isEmpty(liveResponse.getResult())) {
            return;
        }
        LivingInfo livingInfo = liveResponse.getResult().get(0);
        // 5.根据用户id，获取主播头像
        Result<UsersInfo> usersInfoResult = userService.queryUserByUserId(userId);
        if (null == usersInfoResult || null == usersInfoResult.getValue()) {
            return;
        }
        String avatar = usersInfoResult.getValue().getAvatar();
        // 6.通过直播信息主播头像组装店铺自播信息对象
        liveSimpleDO.setShopLiveInfo(ShopLiveInfo.convert(livingInfo, avatar));
    }

    /**
     * 1430班车详情页添加商品的最后一条直播讲解视频切片
     *
     * @param context      详情页上下文
     * @param liveSimpleDO 直播相关DO
     */
    private void getExplainWindowInfo(DetailContext context, LiveSimpleDO liveSimpleDO) {
        try {
            //直播降级开关
            if (!commonSwitchUtil.isOn(SwitchKey.SWT_SHOW_LIVE_INFO)) {
                return;
            }
            DetailItemDO itemDO = context.getItemDO();
            long itemId = itemDO.getItemId();
            if (itemDO.getIsDeleted() == 1 || itemDO.getIsShelf() == 1) {
                return;
            }
            Map<String, String> extra = Optional.ofNullable(context.getItemDO())
                    .map(ItemDO::getItemExtraDO)
                    .map(ItemExtraDO::getFeatures)
                    .orElse(null);
            if (MapUtils.isNotEmpty(extra) && extra.containsKey(ITEM_SHOW_EXPLAIN) && MapUtils.getInteger(extra, ITEM_SHOW_EXPLAIN).equals(0)) {
                return;
            }
            String actUserId = context.getParam("actUserId");
            MoguLiveExplainInfoQuery moguLiveExplainInfoQuery = new MoguLiveExplainInfoQuery();
            moguLiveExplainInfoQuery.setActorId(StringUtils.isNotBlank(actUserId) ? IdConvertor.urlToId(actUserId) : null);
            moguLiveExplainInfoQuery.setItemId(itemId);
            moguLiveExplainInfoQuery.setFrom(MoguLiveItemExplainFromEnum.DETAIL.getFrom());
            moguLiveExplainInfoQuery.setShopUserId(context.getItemDO().getUserId());
            moguLiveExplainInfoQuery.setClientVersion(ContextUtil.getClientVersion(context));
            MoguLiveRpcResult<MoguLiveItemExplainInfoResponse> explainInfo = moguLiveItemExplainService.getExplainInfoDetail(moguLiveExplainInfoQuery);
            if (null == explainInfo || !explainInfo.isSuccess() || null == explainInfo.getValue()) {
                return;
            }
            liveSimpleDO.setExplainWindowInfo(ExplainWindowInfo.convert(explainInfo.getValue()));
        } catch (Exception e) {
            logger.error("获取直播切片信息失败! 商品ID:" + context.getItemId(), e);
        }
    }

    /**
     * 1470版本，从直播间、买手店、直播切片过来时候要在详情页展示主播信息 具体看PRD
     *
     * @link https://mogu.feishu.cn/docs/doccnt5c5OIiTn4EbFhw5Tl897f
     */
    private void getAnchorInfo(DetailContext context, LiveSimpleDO liveSimpleDO) {
        if (context.getParams() == null || context.getParam("actUserId") == null) {
            return;
        }

        //买手店开关
        if (commonSwitchUtil.isOn(SwitchKey.SWT_CLOSE_BUYER_SHOP_DISPLAY)) {
            return;
        }

        //买手店黑名单
        String shopIds = metabaseClient.get("buyer_shop_black_list");
        if (StringUtils.isNotBlank(shopIds) && Arrays.stream(shopIds.split(","))
                .anyMatch(shopId -> shopId.equals(String.valueOf(context.getItemDO().getShopId())))) {
            return;
        }

        //先拿到Params参数，从中解析出主播字段
        Long actUserId = IdConvertor.urlToId(context.getParam("actUserId"));

        //主播头像、名字信息从用户拿
        Result<UsersInfo> usersInfoResult = userService.queryUserByUserId(actUserId);
        if (null == usersInfoResult || null == usersInfoResult.getValue()) {
            return;
        }

        UsersInfo usersInfo = usersInfoResult.getValue();
        LiveAnchorInfo liveAnchorInfo = new LiveAnchorInfo();
        liveAnchorInfo.setName(usersInfo.getUname());
        liveAnchorInfo.setAvatar(usersInfo.getAvatar());
        liveAnchorInfo.setUserId(usersInfo.getUserId());
        liveAnchorInfo.setMonthlySales(getAnchorMonthlySales(actUserId));
        liveAnchorInfo.setFansNum(getAnchorFansNum(actUserId));

        //从直播获取主播的DSR信息，三木统一给包掉。
        PradaRpcResult<PradaActorDsrInfoResponse> pradaRpcResult = pradaActorDsrService.getActorDsrInfo(actUserId);
        if (pradaRpcResult != null && pradaRpcResult.isSuccess()
                && pradaRpcResult.getValue() != null
                && pradaRpcResult.getValue().getScore() != null) {
            liveAnchorInfo.setDsr(pradaRpcResult.getValue().getScore());
            int dsrLevel = pradaRpcResult.getValue().getLevel();
            Optional<PradaActorDsrLevelEnum> levelEnum = Arrays.stream(PradaActorDsrLevelEnum.values())
                    .filter(level -> level.getValue() == dsrLevel).findAny();
            liveAnchorInfo.setDsrLevel(levelEnum.isPresent() ? levelEnum.get().getDesc() : "");
        }

        //获取主播的评价标签
        liveAnchorInfo.setRateTags(getRateTags(actUserId));

        //开关，后期可以去掉，前期产品要控制这这玩意要不要透出的。
        String display = metabaseClient.get("live_actor_dsr_display");
        if (display != null && display.equals("10")) {
            liveAnchorInfo.setDsr(null);
        } else if (display != null && display.equals("01")) {
            liveAnchorInfo.setDsrLevel("");
        } else if (display != null && display.equals("11")) {
            liveAnchorInfo.setDsr(null);
            liveAnchorInfo.setDsrLevel("");
        }
        liveSimpleDO.setActUserInfo(liveAnchorInfo);
    }

    /**
     * 1500版本，直播/切片商详页新增推荐模块 具体见PRD
     *
     * @link https://mogu.feishu.cn/docs/doccnEOYVmIwgEckR0s2B6BAyIq#
     */
    private void getLiveItemRecommendInfo(DetailContext context, LiveSimpleDO liveSimpleDO) {
        if (context.getParams() == null || context.getParam("actUserId") == null) {
            return;
        }

        //切片商品推荐模块开关
        if (commonSwitchUtil.isOn(SwitchKey.SWT_CLOSE_LIVE_ITEM_RECOMMEND)) {
            return;
        }

        String actUserId = context.getParam("actUserId");
        ItemDO detailItem = context.getItemDO();
        if (detailItem == null) {
            return;
        }

        //获取商品的叶子类目
        String cid = String.valueOf(detailItem.getCategoryId());
        List<JSONObject> recommendDataList = paganiSearchWrapper.getliveItemRecommendData(actUserId, cid, context);
        if (CollectionUtils.isEmpty(recommendDataList)) {
            return;
        }
        List<LiveItemRecommendInfo> liveItemRecommendInfos = new ArrayList<>();
        //一共推荐三个买手店商品
        for (int i = 0; i < recommendDataList.size(); i++) {
            if (liveItemRecommendInfos.size() == 3) {
                break;
            }
            liveItemRecommendInfos.add(LiveItemRecommendInfo.convert(recommendDataList.get(i)));
        }
        liveSimpleDO.setLiveItemRecommendInfos(liveItemRecommendInfos);
    }

    private void getPickedExplainInfo(DetailContext context, LiveSimpleDO liveSimpleDO) {
        try {
            ItemExtraDO itemExtraDO = context.getItemDO().getItemExtraDO();
            String anchorItemActivityInfo = itemExtraDO.getFeatures().get(DetailConstants.ItemExtraDOKeys.ANCHOR_ITEM_ACTIVITY_INFO);
            if (StringUtils.isBlank(anchorItemActivityInfo)) {
                return;
            }

            JSONObject anchorItemJson = JSON.parseObject(anchorItemActivityInfo);
            if (anchorItemJson == null) {
                return;
            }

            String activitySlogan = anchorItemJson.getString("activitySlogan");

            JSONObject anchorInfo = anchorItemJson.getJSONObject("anchorInfo");
            if (anchorInfo == null) {
                return;
            }


            Long anchorId = anchorInfo.getLong("anchorId");
            if (anchorId == null) {
                return;
            }
            List<TagInfo> anchorTags = Lists.newArrayList();
            if (!StringUtils.isBlank(activitySlogan)) {
                TagInfo tagInfo = new TagInfo();
                tagInfo.setName(activitySlogan);
                anchorTags.add(tagInfo);
            }

            ExplainInfo pickedExplainInfo = new ExplainInfo();
            pickedExplainInfo.setSlice(anchorInfo.getString("slice"));
            pickedExplainInfo.setVideoId(anchorInfo.getLong("videoId"));
            pickedExplainInfo.setActUserId(anchorId.toString());

            RpcResult<ActorTagInfo> rpcResult = severalInfoWithLiveTeslaService.getActorTagInfoByActorId(anchorId);
            if (rpcResult.isSuccess() && rpcResult.getValue() != null) {
                ActorTagInfo actorTagInfo = rpcResult.getValue();
                pickedExplainInfo.setActUserId(actorTagInfo.getActUserId());
                pickedExplainInfo.setActUserName(actorTagInfo.getActUserName());
                pickedExplainInfo.setAvatar(actorTagInfo.getAvatar());
                pickedExplainInfo.setActHeight(actorTagInfo.getActHeight());
                pickedExplainInfo.setActWeight(actorTagInfo.getActWeight());
                anchorTags.addAll(assembleTagInfos(actorTagInfo.getTags()));
                pickedExplainInfo.setTags(anchorTags);
                pickedExplainInfo.setCity(actorTagInfo.getCity());
                pickedExplainInfo.setFansNum(getAnchorFansNum(anchorId));
            }

            liveSimpleDO.setPickedExplainInfo(pickedExplainInfo);
        } catch (Exception e) {
            logger.error("直播商品进商城商品获取直播切片信息失败! 商品ID:{}", context.getItemId(), e);
        }
    }

    private void getExplainInfo(DetailContext context, LiveSimpleDO liveSimpleDO) {

        try {
            DetailItemDO item = context.getItemDO();
            if (null == item) {
                return;
            }
            Map<String, String> features = item.getFeatures();
            if (features == null || features.isEmpty()) {
                return;
            }
            String liveVideoStr = features.get("liveVideo");
            if (StringUtils.isEmpty(liveVideoStr)) {
                return;
            }

            ExplainInfo explainInfo = new ExplainInfo();
            liveSimpleDO.setExplainInfo(explainInfo);
            JSONObject liveVideo = JSON.parseObject(liveVideoStr);
            explainInfo.setVideoId(liveVideo.getLongValue("videoId"));
            explainInfo.setItemId(IdConvertor.idToUrl(liveVideo.getLongValue("itemId")));
            long actorId = liveVideo.getLongValue("userId");
            if (actorId <= 0) {
                return;
            }
            RpcResult<ActorTagInfo> rpcResult = severalInfoWithLiveTeslaService.getActorTagInfoByActorId(actorId);
            if (rpcResult.isSuccess() && rpcResult.getValue() != null) {
                ActorTagInfo actorTagInfo = rpcResult.getValue();
                explainInfo.setActUserId(actorTagInfo.getActUserId());
                explainInfo.setActUserName(actorTagInfo.getActUserName());
                explainInfo.setAvatar(actorTagInfo.getAvatar());
                explainInfo.setActHeight(actorTagInfo.getActHeight());
                explainInfo.setActWeight(actorTagInfo.getActWeight());
                explainInfo.setTags(assembleTagInfos(actorTagInfo.getTags()));
            }
        } catch (Throwable e) {
            logger.error("get ExplainInfo error!", e);
        }

        return;
    }

    private List<TagInfo> assembleTagInfos(List<ActorTagInfo.TagInfo> tags) {
        if (CollectionUtil.isEmpty(tags)) {
            return Lists.newArrayList();
        }
        List<TagInfo> tagInfos = new ArrayList<>();
        for (ActorTagInfo.TagInfo tag : tags) {
            TagInfo tagInfo = new TagInfo();
            tagInfo.setId(tag.getId());
            tagInfo.setName(tag.getName());
            tagInfo.setTimestamp(tag.getTimestamp());

            tagInfos.add(tagInfo);
        }
        return tagInfos;
    }

    private Integer getAnchorFansNum(Long anchorId) {
        RelationDto relationDto = new RelationDto();
        relationDto.setToId(anchorId);
        relationDto.setAppId(AppIdEnums.MoGuJie);
        relationDto.setAssociationsType(AssociationsTypeEnums.FANS);
        com.mogujie.service.relation.domain.response.RpcResult<List<RelationCounterReponseDto>> rpcResult =
                relationReadFacade.getCounterMuliter(Lists.newArrayList(relationDto), Direction.TO2FROM, CounterType.PV);
        if (rpcResult.isSuccess() && !CollectionUtil.isEmpty(rpcResult.getValue())) {
            return rpcResult.getValue().get(0).getCount().intValue();
        }
        return 0;
    }

    private Integer getAnchorMonthlySales(Long anchorId) {
        try {
            String payDate = DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDate.now().minusMonths(1));
            Query query = new Query();
            query.addColumn("199_payDate");
            query.addColumn("199_actUserId");
            query.addColumn("199_channelId");
            query.addColumn("199_payitemnum");
            query.setCondition(
                    Conditions.and(
                            Conditions.condition("payDate", Operator.GE, payDate),
                            Conditions.condition("actUserId", Operator.EQ, anchorId)
                    )
            );
            String token = TokenUtil.produce("rate_web", "06f32b490bfa05718eb6cb4410e92c4f");
            QueryResult queryResult = dtsQueryService.query(query, "rate_web", token);
            if (!CollectionUtils.isEmpty(queryResult.getRowResults())) {
                return queryResult.getRowResults().stream().filter(rowResult -> rowResult.get("199_payitemnum") != null)
                        .mapToInt(rowResult -> rowResult.get("199_payitemnum").asInteger()).sum();
            }
        } catch (Exception e) {
            logger.error("getAnchor30Sales failed!");
        }
        return 0;
    }

    private List<RateTag> getRateTags(Long actorId) {
        ItemRateQueryReq itemRateQueryReq = new ItemRateQueryReq().setActorId(actorId)
                .setBeginTime(LocalDateTime.now().minusDays(90).atZone(ZoneId.systemDefault()).toEpochSecond());
        Map<Long, RateLabelCount> rateLabelCountMap = rateReadService.labelCount(itemRateQueryReq);
        if (rateLabelCountMap == null || rateLabelCountMap.isEmpty()) {
            return Lists.newArrayList();
        }

        Map<String, List<RateLabelCount>> resultMap = rateLabelCountMap.values().stream()
                .filter(rateLabelCount -> rateLabelCount.getRateLabel().getExclusive() == 0)
                .collect(Collectors.groupingBy(rateLabelCount -> rateLabelCount.getRateLabel().getEmotionName()));

        List<RateTag> rateTags = Lists.newArrayList();
        resultMap.forEach((key, value) -> {
            RateTag rateTag = new RateTag(value.get(0));
            rateTag.setNum((int) value.stream().map(RateLabelCount::getUseCount).mapToLong(x -> x).sum());
            rateTag.setLabelIds(value.stream().map(count -> count.getRateLabel().getId().toString())
                    .collect(Collectors.joining(",")));
            rateTag.setLabelId(0L);
            rateTags.add(rateTag);
        });
        Collections.sort(rateTags);
        return rateTags;
    }
}
