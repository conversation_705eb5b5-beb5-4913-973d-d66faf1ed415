package com.mogujie.detail.module.mycoupon.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by anshi on 2019/1/24.
 */
public class MyCouponDO implements ModuleDO {

    /**
     * 我的优惠券（当前用户已领的平台券透出，用于小程序上显示已领的app券，引导到app）
     * 其他平台都不会返回
     */
    @Getter
    @Setter
    private List<MyCoupon> myAppCoupons;

    /**
     * 用户当前拥有的津贴数量
     */
    @Getter
    @Setter
    private Integer bonusCount;

    /**
     * 用户等级
     */
    @Getter
    @Setter
    private Integer userVipLevel;


    /**
     * 剩余蘑豆数量
     */
    @Getter
    @Setter
    private Long balanceModouAmount;
    /**
     * 1350班车磨豆兑换可用豆券
     */
    @Getter
    @Setter
    private List<MyMoDouCoupon> modouCoupons;
}
