package com.mogujie.detail.module.daren.provider;

import com.google.common.base.Strings;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.darling.daren.DarenIdentityQuery;
import com.mogujie.darling.daren.model.DarenIdentityDto;
import com.mogujie.darling.daren.service.DarenService;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.ImageUtil;
import com.mogujie.detail.module.daren.domain.DarenDO;
import com.mogujie.detail.module.daren.domain.RelatedGoods;
import com.mogujie.fashion.api.DarenItemService;
import com.mogujie.fashion.api.common.RemoteResult;
import com.mogujie.fashion.api.query.ItemParamsQuery;
import com.mogujie.fashion.api.result.ItemParamsResult;
import com.mogujie.market.base.model.ResultBase;
import com.mogujie.metabase.utils.CollectionUtils;
import com.mogujie.search.ara.abtest.common.utils.MoguUuidUtil;
import com.mogujie.service.item.api.basic.ItemReadService;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.query.QueryItemOptions;
import com.mogujie.service.item.domain.result.BaseResultDO;
import com.mogujie.service.muser.Result;
import com.mogujie.service.muser.api.UserService;
import com.mogujie.service.muser.domain.entity.User;
import com.mogujie.session.SessionContextHolder;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import com.mogujie.union.molitongkit.api.report.UnionRtDataService;
import com.mogujie.union.molitongkit.domain.entity.AdInfoEntity;
import com.mogujie.union.molitongkit.domain.entity.Response;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by nanyang on 19/7/16.
 */
@Module(name = "daren")
public class DarenDOProvider implements IModuleDOProvider<DarenDO> {


    private static final Logger logger = LoggerFactory.getLogger(DarenDOProvider.class);

    private DarenService darenService;

    private UserService userService;

    private UnionRtDataService unionRtDataService;

    private ItemReadService itemReadService;

    private DarenItemService darenItemService;

    @Override
    public void init() throws DetailException {
        try {
            darenService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(DarenService.class);
            userService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(UserService.class);
            unionRtDataService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(UnionRtDataService.class);
            itemReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ItemReadService.class);
            darenItemService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(DarenItemService.class);
        } catch (Exception e) {
            logger.error("DarenService service init error.", e);
        }
    }


    @Override
    public DarenDO emit(DetailContext context) {
        Long uid = context.getLoginUserId();
        Long did = null;
        try {
            DarenDO darenDO = new DarenDO();
            if(!Strings.isNullOrEmpty(context.getParam("did"))) {
                did = Long.valueOf(context.getParam("did"));
            }
            if (isCpoDaren(uid)) {
                darenDO.setIsDaren(true);
                darenDO.setEarn(getEarnByItemId(context.getItemId()));
            }
            else {
                darenDO.setIsDaren(false);
            }
            if(isCpoDaren(did)){
                User daren = getUserByUid(did);
                darenDO.setAvatar(ImageUtil.img(daren.getAvatar()));
                darenDO.setName(daren.getUname());
            }
            darenDO.setRelatedGoods(getRelatedGoods(context, darenDO.getIsDaren()));

            return darenDO;
        } catch (Exception e) {
            logger.error("get daren  do error!   uid: {} did: {}", e,uid,did);
        }
        return new DarenDO();
    }

    /**
     * 通过商品ID获取预估收益
     *
     * @param itemId
     * @return
     */
    private Integer getEarnByItemId(Long itemId) {
        Set<Long> itemIds = new HashSet<>();
        itemIds.add(itemId);
        try {
            Response<Map<Long, AdInfoEntity>> adMapRet = unionRtDataService.queryLookAdIdByItemId(itemIds);
            if (adMapRet != null && adMapRet.isSuccess() && adMapRet.getData().size() != 0) {
                return adMapRet.getData().get(itemId).getAdPrice();
            }
        } catch (Exception e) {
            logger.error("get union  queryLookAdIdByItemId do error! itemId: {}",itemId, e);
        }
        return null;
    }

    /**
     * 通过商品IDs获取预估收益
     *
     * @param itemIds
     * @return
     */
    private Map<Long, AdInfoEntity> getEarnByItemIds(Set<Long> itemIds) {
        try {
            if (CollectionUtils.isEmpty(itemIds)){
                return new HashMap<>();
            }
            Response<Map<Long, AdInfoEntity>> adMapRet = unionRtDataService.queryLookAdIdByItemId(itemIds);
            if (adMapRet != null && adMapRet.isSuccess() && adMapRet.getData().size() != 0) {
                return adMapRet.getData();
            }
        } catch (Exception e) {
            logger.error("get union queryLookAdIdByItemId do error! itemIds: {}", itemIds, e);
        }
        return new HashMap<>();
    }

    /**
     * 获取用户信息
     *
     * @param uid
     * @return
     */
    private User getUserByUid(Long uid) {
        List<Long> userIds = new ArrayList<>();
        userIds.add(uid);
        try {
            Result<List<User>> userRet = userService.getUserByIds(userIds);
            if (null != userRet && !CollectionUtils.isEmpty(userRet.getValue())) {
                return userRet.getValue().get(0);
            }
        } catch (Exception e) {
            logger.error("get user  getUserByIds do error! uid:{}", e,uid);
        }
        return new User();
    }

    /**
     * 判断是否是达人
     *
     * @param uid 用户ID
     * @return
     */
    private boolean isCpoDaren(Long uid) {
        if (uid != null) {
            try {
                DarenIdentityQuery darenIdentityQuery = new DarenIdentityQuery();
                darenIdentityQuery.setUserId(uid);
                darenIdentityQuery.setGetIsCpoDaren(true);
                ResultBase<DarenIdentityDto> darenIdentityDtoResultBase = darenService.getDarenIdentity(darenIdentityQuery);
                if (darenIdentityDtoResultBase != null && darenIdentityDtoResultBase.isSuccess() && darenIdentityDtoResultBase.getValue() != null) {
                    return darenIdentityDtoResultBase.getValue().getIsCpoDaren();
                }
            } catch (Exception e) {
                logger.error("get user  getDarenIdentity do error! uid: {}", e,uid);
            }
        }
        return false;
    }

    /**
     * 获取关联商品
     *
     * @param context
     * @param isDaren
     * @return
     */
    private List<RelatedGoods> getRelatedGoods(DetailContext context, Boolean isDaren) {
        // 关联商品IDS
        String relatedIds = context.getParam("relatedIds");
        String fashionId = context.getParam("fashionId");

        List<RelatedGoods> relatedGoods = new ArrayList<>();
        if (Strings.isNullOrEmpty(relatedIds)){
            return relatedGoods;
        }
        try {
            List<String> relatedIdList = Arrays.asList(relatedIds.split(","));
            List<Long> itemIdList = new ArrayList<>();
            for (String idUrl : relatedIdList){
                itemIdList.add(IdConvertor.urlToId(idUrl));
            }

            // 获取商品信息
            QueryItemOptions queryItemOptions = new QueryItemOptions();
            BaseResultDO<List<ItemDO>> ret = itemReadService.queryItemList(itemIdList, queryItemOptions);
            if (null == ret || !ret.isSuccess() || null == ret.getResult()) {
                return relatedGoods;
            }

            // 获取预估收益
            Set<Long> itemIdSet = new HashSet<>(itemIdList);
            Map<Long, AdInfoEntity> adInfoEntityMap = new HashMap<>();
            if (isDaren){
                adInfoEntityMap = getEarnByItemIds(itemIdSet);
            }

            // 获取itemParams
            Map<Long,ItemParamsResult> itemParamsResultMap = getItemFashionParams(context, fashionId, itemIdList);

            // 封装RelatedGoods 需要排序IC返回的list没有按照入参id的顺序
            List<ItemDO> itemDOList = ret.getResult();
            Map<Long, ItemDO> itemDOMap = itemDOList.stream().collect(Collectors.toMap(ItemDO::getItemId, itemDO -> itemDO));
            for (Long itemId : itemIdList){
                ItemDO itemDO = itemDOMap.get(itemId);
                if (itemDO == null){
                    continue;
                }
                RelatedGoods goods = new RelatedGoods();
                goods.setItemId(IdConvertor.idToUrl(itemDO.getItemId()));
                goods.setTitle(itemDO.getTitle());
                goods.setImage(ImageUtil.img(itemDO.getMainImage()));
                goods.setLink(getRelatedGoodsLink(itemDO, itemParamsResultMap.get(itemDO.getItemId()), fashionId));
                if (isDaren && !CollectionUtils.isEmpty(adInfoEntityMap) && adInfoEntityMap.get(itemDO.getItemId()) != null){
                    AdInfoEntity adInfoEntity = adInfoEntityMap.get(itemDO.getItemId());
                    if (adInfoEntity.getAdPrice() != null){
                        goods.setEarn(adInfoEntity.getAdPrice());
                    }
                }
                relatedGoods.add(goods);
            }
        } catch (Exception e) {
            logger.error("getRelatedGoods queryItemList error.", e);
        }

        return relatedGoods;
    }

    /**
     * 获取fashionParams
     *
     * @param context
     * @param itemIdList
     * @return
     */
    private Map<Long,ItemParamsResult> getItemFashionParams(DetailContext context, String fashionId, List<Long> itemIdList) {
        if (StringUtils.isEmpty(fashionId)){
            return new HashMap<>();
        }
        String uuid= SessionContextHolder.getUUID();
        if(StringUtils.isBlank(uuid)){
            String mwTTid=context.getParam("mwTTid");
            String mwDid=context.getParam("mwDid");
            uuid= MoguUuidUtil.getMobileMgjuuid(mwTTid,mwDid);
        }
        ItemParamsQuery itemParamsQuery = new ItemParamsQuery();
        itemParamsQuery.setUserId(IdConvertor.urlToId(fashionId));
        itemParamsQuery.setUuid(uuid);
        itemParamsQuery.setTradeItemIds(itemIdList);
        RemoteResult<Map<Long,ItemParamsResult>> ret = darenItemService.queryItemFashionParams(itemParamsQuery);
        if (null == ret || !ret.isSuccess() || null == ret.getValue()) {
            return new HashMap<>();
        }
        return ret.getValue();
    }

    /**
     * 获取RelatedGoodsLink
     *
     * @param itemParamsResult
     * @return
     */
    private String getRelatedGoodsLink(ItemDO itemDO, ItemParamsResult itemParamsResult, String fashionId) {

        StringBuilder link = new StringBuilder();
        link.append("/pages/detail/pages/normal/index?itemId=");
        link.append(IdConvertor.idToUrl(itemDO.getItemId()));
        if (StringUtils.isNotEmpty(fashionId)){
            link.append("&fashionId=");
            link.append(fashionId);
        }
        if (itemParamsResult != null) {
            link.append("&fashionParams=");
            link.append(itemParamsResult.getFashionParams());
            link.append("&cparam=");
            link.append(itemParamsResult.getCparam());
        }
        return link.toString();
    }

}
