package com.mogujie.detail.module.itemParams.util;

import com.mogujie.detail.core.util.ImageUtil;
import com.mogujie.detail.module.itemParams.domain.ParamModule;
import com.mogujie.detail.module.itemParams.domain.ProductInfo;
import com.mogujie.detail.module.itemParams.domain.Rule;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 16/4/19.
 */
public class DetailUtil {
    private static final org.slf4j.Logger LOGGER = org.slf4j.LoggerFactory.getLogger(DetailUtil.class);
    private static final String SIZE_ANCHOR = "size_info";
    private static final String SIZE_KEY = "尺码说明";
    private static final String DEFAULT_DISCLAIMER = "※ 以上尺寸为实物人工测量，因测量方式不同会有1-2cm误差，相关数据仅作参考，以收到实物为准。";
    private static final String NUTRITION_DISCLAIMER = "※ 本品不可替代药物";

    private static final String NUTRITION_CATEGORY = "2072"; // 保健品类目

    private static final String PRODUCT_ANCHOR = "product_info";
    private static final String PRODUCT_KEY = "产品参数";

    /**
     * 获取尺码说明, 补全图文详情相关参数
     * @param detail
     * @param cids
     * @return
     */
    public static Rule getBaseRule(String detail, String cids) {
        Rule rule = new Rule();
        // 设置面这声明
        rule.setDisclaimer(getSizeDisclimar(cids));

        // 设置描述&图片
        ParamModule paramModule = getParamModule(detail, "size_info:::");

        rule.setDesc(paramModule.getDesc());
        rule.setImages(paramModule.getImgs());
        rule.setAnchor(SIZE_ANCHOR);
        rule.setKey(SIZE_KEY);
        return rule;
    }

    /**
     * 获取productInfo, 补全图文详情相关参数
     * @param detail
     * @return
     */
    public static ProductInfo getBaseProductInfo(String detail) {
        ProductInfo productInfo = new ProductInfo();

        ParamModule paramModule = getParamModule(detail, "product_info:::");

        productInfo.setDesc(paramModule.getDesc());
        productInfo.setImages(paramModule.getImgs());
        productInfo.setAnchor(PRODUCT_ANCHOR);
        productInfo.setKey(PRODUCT_KEY);
        return productInfo;
    }


    private static ParamModule getParamModule(String detail, String moduleName) {
        ParamModule module = new ParamModule();
        setDescAndImg(module, detail, moduleName);
        return module;
    }

    /**
     * 解析图文详情字段,设置模块相关信息
     * @param paramModule
     * @param detail 图文详情字符串,格式为: moduleName:::desc+++img###imgDesc&&&img###imgDesc^^^moduleName...
     */
    private static void setDescAndImg(ParamModule paramModule, String detail, String moduleName) {
        if (StringUtils.isBlank(detail) || !detail.contains(moduleName)) { // 不存在该模块
            return;
        }

        String moduleInfo = getModuleInfo(detail, moduleName);
        if (moduleInfo == null || moduleInfo.length() <= 3) {
            // 信息不存在
            return;
        }

        // 设置尺码说明的描述
        String sizeImgs = setSizeDesc(paramModule, moduleInfo);
        // 设置尺码说明的图片
        setSizeImg(paramModule, sizeImgs);
    }

    /**
     * 获取模块信息
     * @param detail
     * @return 返回模块信息中的描述和图片串,null时表示模块无相关信息
     */
    private static String getModuleInfo(String detail, String moduleName) {
        // 获取size模块的信息
        int index = detail.indexOf(moduleName);
        int last = detail.indexOf("^^^", index);
        // last == -1 表示该模块为最后一个
        if (last < index) {
            last = detail.length();
        }

        String ruleDetail = detail.substring(index, last);
        String[] values = StringUtils.splitByWholeSeparatorPreserveAllTokens(ruleDetail, ":::");
        if (values.length != 2) {
            return null;
        }

        return values[1];
    }

    /**
     * 设置模块文字描述, 并返回图片信息
     * @param paramModule
     * @param module : desc+++imgPath###&&&imgPath###
     * @return imgPath###imgdesc&&&imgPath###
     */
    private static String setSizeDesc(ParamModule paramModule, String module) {
        int index = module.indexOf("+++");
        if (index <= 0) {
            // 表示没有描述, 直接返回图片串
            return module.substring(3);
        }

        // 设置描述,并返回图片信息
        String desc = module.substring(0, index);
        paramModule.setDesc(desc);
        return module.substring(index + 3);
    }


    /**
     * 设置模块的图片
     * @param paramModule
     * @param imgs imgPath###imgDesc&&&imgPath###imgDesc
     * @return
     */
    private static void setSizeImg(ParamModule paramModule, String imgs) {
        String[] imgList = StringUtils.splitByWholeSeparatorPreserveAllTokens(imgs, "&&&");
        if (imgList.length <= 0) {
            return;
        }

        List<String> list = new ArrayList<>(imgList.length);
        String imgUrl;
        for (String img : imgList) {
            imgUrl = getImgUrl(img);
            if (imgUrl == null) {
                continue;
            }
            list.add(getImgUrl(img));
        }

        paramModule.setImgs(list);
    }

    /**
     * 获取图片http链接
     * @param strImg  imgPath###imgDesc
     * @return 图片的http格式
     */
    private static String getImgUrl(String strImg) {
        if (strImg == null || strImg.startsWith("###")) {
            return null; // 不存在图片
        }

        String[] values = StringUtils.splitByWholeSeparatorPreserveAllTokens(strImg, "###");
        if (values == null || values.length == 0) {
            return null;
        }

        try {
            return ImageUtil.img(values[0]);
        } catch (Exception e) {
            LOGGER.error("get img http url error! img: {} exception: {}", values[0], e);
            return null;
        }
    }


    /**
     * 设置免责声明
     * @param cids
     * @return
     */
    private static String getSizeDisclimar(String cids) {
        if (StringUtils.isBlank(cids)) {
            return DEFAULT_DISCLAIMER;
        }

        String path = StringUtils.replace(cids, "#", "");
        String[] cidStrs = StringUtils.split(path, " ");
        for (String cid : cidStrs) {
            if (NUTRITION_CATEGORY.equals(cid)) {
                return NUTRITION_DISCLAIMER;
            }
        }

        return DEFAULT_DISCLAIMER;
    }
}
