package com.mogujie.detail.module.rate.provider;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.BaseConstants;
import com.mogujie.detail.core.constant.DetailConstants;
import com.mogujie.detail.core.constant.Platform;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.spi.SpiAutowired;
import com.mogujie.detail.core.util.ContextUtil;
import com.mogujie.detail.core.util.ImageUtil;
import com.mogujie.detail.core.util.MetabaseTool;
import com.mogujie.detail.module.rate.domain.ContentInfo;
import com.mogujie.detail.module.rate.domain.DetailRate;
import com.mogujie.detail.module.rate.domain.RateDO;
import com.mogujie.detail.module.rate.domain.RateUserInfo;
import com.mogujie.detail.module.rate.spi.IUserProfileProvider;
import com.mogujie.fashion.api.OrderShareService;
import com.mogujie.fashion.api.common.RemoteResult;
import com.mogujie.fashion.api.result.ContentVO;
import com.mogujie.fashion.api.result.ItemContentResult;
import com.mogujie.service.item.domain.basic.ItemExtraDO;
import com.mogujie.service.rate.adt.ResponseDO;
import com.mogujie.service.rate.api.RateWebService;
import com.mogujie.service.rate.domain.basic.req.ItemRatesReqDO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by xiaoyao on 16/8/16.
 */
@Module(name = "rate")
public class RateDOProvider implements IModuleDOProvider<RateDO> {

    private static Logger LOGGER = LoggerFactory.getLogger(RateDOProvider.class);

    private RateWebService rateWebService;

    private OrderShareService orderShareService;

    @SpiAutowired
    private IUserProfileProvider userProfileProvider;

    private Gson gson;

    // 大促氛围场景
    private static final String SWITCH_CONTENT = "Switch_Content";
    private static final String CONTENT_MORE_LINK = "https://act.mogu.com/goods?itemId=";

    @Override
    public void init() throws DetailException {
        try {
            gson = new Gson();
            rateWebService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(RateWebService.class);
            orderShareService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(OrderShareService.class);
        } catch (Exception e) {
            LOGGER.error("init service failed : {}", e);
//            throw new DetailException(e);
        }
    }

    @Override
    public RateDO emit(DetailContext context) {
        ItemRatesReqDO itemRatesReqDO = new ItemRatesReqDO();
        itemRatesReqDO.setClientVersion(ContextUtil.getClientVersion(context));

        if(ContextUtil.getClientVersion(context)>= BaseConstants.AppClientVerion.V1200) {
            itemRatesReqDO.setPlatform(context.getRouteInfo().getPlatform().name());
        }
        // 女装小程序 APP==XCX,Platform==H5才是女装小程序，不知道啥逻辑。。。
        if(context.getRouteInfo() != null && context.getRouteInfo().getApp() == App.XCX && context.getRouteInfo().getPlatform() == Platform.H5){
            itemRatesReqDO.setPlatform(Platform.XCX.name());
        }
        itemRatesReqDO.setItemId( (long) context.getItemId());
        if (ImageUtil.useHttps()) {
            itemRatesReqDO.setUseHttps(true);
        }
//        if ("100063".equals(context.getParam("appkey"))) {
//            //女装小程序要求详情页只展示好评
//            itemRatesReqDO.setOnlyGoodRate(true);
//        }
        if (context.getRouteInfo() != null && context.getRouteInfo().getApp() != null && App.MLS == context.getRouteInfo().getApp()) {
            itemRatesReqDO.setMarketType("market_meilishuo");
        } else {
            itemRatesReqDO.setMarketType("market_mogujie");
        }
        if(context.getRouteInfo() != null
                && Platform.APP == context.getRouteInfo().getPlatform()
                && ContextUtil.getClientVersion(context)>= BaseConstants.AppClientVerion.V1260) {
            itemRatesReqDO.setQueryLabel(true);
        }
        if(context.getRouteInfo() != null
                && context.getRouteInfo().getApp() == App.XCX
                && context.getRouteInfo().getPlatform() == Platform.H5
                && ContextUtil.getClientVersion(context)>= BaseConstants.XCXClientVerion.V3630) {
            itemRatesReqDO.setQueryLabel(true);
        }

        //如果是直播商品进商城，评价是挑选的，需要按照评价ID来进行处理。
        if (ContextUtil.isLiveInWallItem(context)) {
            List<Long> rateIds = Lists.newArrayList();
            ItemExtraDO itemExtraDO = context.getItemDO().getItemExtraDO();
            String anchorItemActivityInfo = itemExtraDO.getFeatures().get(DetailConstants.ItemExtraDOKeys.ANCHOR_ITEM_ACTIVITY_INFO);
            JSONObject anchorItemInfoJson = JSON.parseObject(anchorItemActivityInfo);
            if (anchorItemActivityInfo != null) {
                JSONArray jsonArray = anchorItemInfoJson.getJSONArray("remarks");
                for (int index = 0; index < jsonArray.size(); ++index) {
                    rateIds.add(jsonArray.getLong(index));
                }
                itemRatesReqDO.setTopRateIdList(rateIds);
            }
        }

        try {
            RateDO<com.mogujie.detail.module.rate.domain.DetailRate> rateDO = new RateDO();
            // 是否展示精选晒单开关
            rateDO.setSwitchContent(MetabaseTool.isOn(SWITCH_CONTENT, false));
            ResponseDO<com.mogujie.service.rate.domain.RateDO> rateResponse = rateWebService.queryRates4Detail(itemRatesReqDO);
            if (rateResponse != null && rateResponse.isSuccess()) {
                convertRateDO(rateResponse.getResult(), rateDO);
            }else{
                // 如果无评价，内容也不取直接返回null
                return null;
            }
            if (rateDO.isSwitchContent()){
                convertContent(itemRatesReqDO.getItemId(), rateDO);
            }
            return rateDO;
        } catch (Throwable e) {
            LOGGER.error("rate service query failed.", e);
            return null;
        }

        // RecordTagType.BUYERSHOW_RATE_TYPE.getType() == rate.getTag();
    }

    private void convertRateDO(com.mogujie.service.rate.domain.RateDO rateDO, RateDO<DetailRate> target) {
        if (target == null || rateDO == null)
            return;
        target.setCRate(rateDO.getCRate());
        target.setImgTotal(rateDO.getImgTotal());
        target.setItemDsr(rateDO.getItemDsr());
        target.setRateTags(rateDO.getRateTags());
        target.setList(convertDetailRates(rateDO.getList()));
    }

    private void convertContent(long itemId, RateDO<DetailRate> target) {
        if (target == null || itemId <= 0)
            return;
        RemoteResult<ItemContentResult> contentResponse = orderShareService.getItemOrderShareResult(itemId);
        if (contentResponse != null && contentResponse.isSuccess() && contentResponse.getValue() != null && !CollectionUtils.isEmpty(contentResponse.getValue().getContentVOS())) {
            ItemContentResult itemContentResult = contentResponse.getValue();
            target.setShareTotal(itemContentResult.getTotalNum());
            target.setContentTotal(itemContentResult.getSelectNum());
            target.setContentMoreLink(CONTENT_MORE_LINK + IdConvertor.idToUrl(itemId));
            List<ContentInfo> contentInfos = new ArrayList<>();
            for (ContentVO contentVO : itemContentResult.getContentVOS()){
                ContentInfo contentInfo = new ContentInfo();
                contentInfo.setContentId(contentVO.getContentId());
                contentInfo.setCover(contentVO.getCover());
                contentInfo.setLink(contentVO.getLink());
                contentInfos.add(contentInfo);
            }
            target.setContentInfos(contentInfos);
        }
    }

    private List<DetailRate> convertDetailRates(List<com.mogujie.service.rate.domain.DetailRate> sources) {
        if (CollectionUtils.isEmpty(sources))
            return null;
        List<DetailRate> result = Lists.newArrayList();
        for (com.mogujie.service.rate.domain.DetailRate source : sources) {
            DetailRate detailRate = convertDetailRate(source);
            if(detailRate==null){
                continue;
            }
            result.add(detailRate);
        }
        return result;
    }

    private DetailRate convertDetailRate(com.mogujie.service.rate.domain.DetailRate source) {
        if (source == null){
            return null;
        }
        //如果无文案，原因是1200将评价改成卡片的形式
//        if(StringUtil.isEmpty(source.getContent())){
//            return null;
//        }
        DetailRate detailRate = new DetailRate();
        if (source.getAppend() != null) {
            detailRate.setAppend(convertDetailRate(source.getAppend()));
        }
        detailRate.setBuyerShow(source.isBuyerShow());
        detailRate.setCanExplain(source.getCanExplain());
        detailRate.setContent(source.getContent());
        detailRate.setCreated(source.getCreated());
        detailRate.setContentId(source.getContentId());
        detailRate.setExplain(source.getExplain());
        detailRate.setExtraInfo(source.getExtraInfo());
        detailRate.setImages(source.getImages());
        detailRate.setVideos(source.getVideos());
        detailRate.setIsAnonymous(source.getIsAnonymous());
        detailRate.setIsEmpty(source.getIsEmpty());
        detailRate.setIsProbation(source.getIsProbation());
        detailRate.setLevel(source.getLevel());
        detailRate.setRateId(source.getRateId());
        detailRate.setProbation(source.getProbation());
        detailRate.setSizeInfo(source.getSizeInfo());
        detailRate.setStyle(source.getStyle());
        detailRate.setUser(convertRateUserInfo(source.getUser()));
        detailRate.setRateTagNames(source.getRateTagNames());
        detailRate.setUserTags(source.getUserTags());
        return detailRate;
    }

    private RateUserInfo convertRateUserInfo(com.mogujie.service.rate.domain.RateUserInfo source) {
        if (source == null)
            return null;
        RateUserInfo result = new RateUserInfo();
        result.setAvatar(source.getAvatar());
        result.setProfileUrl(source.getProfileUrl());
        result.setUid(source.getUid());
        result.setUname(source.getUname());
        result.setIsAgainUser(source.isAgainUser());
        result.setUserLevel(source.getUserLevel());
        return result;
    }
}
