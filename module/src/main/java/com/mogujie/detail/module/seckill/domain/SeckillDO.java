package com.mogujie.detail.module.seckill.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by anshi on 17/3/8.
 */
public class SeckillDO implements ModuleDO {

    /**
     * 下一个状态倒计时(秒)
     */
    @Getter
    @Setter
    private int countdown;

    /**
     * 开始时间
     */
    @Getter
    @Setter
    private int startTime;

    /**
     * 结束时间
     */
    @Getter
    @Setter
    private int endTime;

    /**
     * 参与秒杀总库存
     */
    @Getter
    @Setter
    private int enrollStock;

    @Getter
    @Setter
    private long outerId;

    /**
     * 价格(分)
     */
    @Getter
    @Setter
    private int price;

    /**
     * 秒杀id(url)
     */
    @Getter
    @Setter
    private String secKillId;

    /**
     * 秒杀状态
     * 0. 未开始
     * 1. 预热中
     * 2. 进行中
     * 3. 结束
     */
    @Getter
    @Setter
    private int status;
}
