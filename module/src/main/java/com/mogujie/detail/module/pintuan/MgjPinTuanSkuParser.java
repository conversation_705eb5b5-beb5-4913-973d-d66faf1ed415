package com.mogujie.detail.module.pintuan;

import com.mogujie.commons.utils.EnvUtil;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.darwin.util.CollectionUtil;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailContextHolder;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.util.ContextUtil;
import com.mogujie.detail.core.util.ImageUtil;
import com.mogujie.detail.core.util.NumUtil;
import com.mogujie.detail.core.util.SkuUtil;
import com.mogujie.detail.module.sku.domain.*;
import com.mogujie.metabase.spring.client.MetabaseClient;
import com.mogujie.service.hummer.api.PromotionReadService;
import com.mogujie.service.hummer.constains.PromotionConstants;
import com.mogujie.service.hummer.constains.RequestConstants;
import com.mogujie.service.hummer.domain.dto.*;
import com.mogujie.service.hummer.domain.dto.result.Result;
import com.mogujie.service.hummer.utils.PromotionConvertUtils;
import com.mogujie.service.hummer.utils.SystemDiscountChecker;
import com.mogujie.service.inventory.api.InventoryReadService;
import com.mogujie.service.inventory.domain.ActivityInventoryV2;
import com.mogujie.service.inventory.domain.param.BatchActivityInventoryQueryParamV2;
import com.mogujie.service.inventory.domain.result.MapResult;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.service.item.domain.basic.SkuAttributionDO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import com.mogujie.tesla.core.ReferConfig;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * Created by xiaoyao on 16/4/12.
 */

public class MgjPinTuanSkuParser {

    private static final Logger LOGGER = LoggerFactory.getLogger(MgjPinTuanSkuParser.class);

    private MetabaseClient metabaseClient;

    private InventoryReadService inventoryReadService;

    private PromotionReadService promotionReadService;

    public MgjPinTuanSkuParser(MetabaseClient metabaseClient) {
        try {
            this.metabaseClient = metabaseClient;
            inventoryReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(InventoryReadService.class);
            ReferConfig<PromotionReadService> referConfig = new ReferConfig<>(PromotionReadService.class);
            if (EnvUtil.isOnlineEnv()) {
                referConfig.setGroup("DETAIL-GROUP");
            }
            referConfig.setTimeout(200);
            this.promotionReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(referConfig);
        } catch (Exception e) {
            LOGGER.error("init service failed : ", e);
        }
    }

    public ParseResult parseSku(DetailContext context, SkuDO skuDO, Integer outType, Boolean isNormalPinTuan) {
        DetailItemDO item = context.getItemDO();
        List<ItemSkuDO> oldSkus = item.getItemSkuDOList();
        String strActivityId = context.getParam("activityId");
        Map<Long, ActivityInventoryV2> invertoryMap = null;
        Map<Long, Long> realPriceMap = null;
        ParseResult ret = new ParseResult(null, true);
        if (isNormalPinTuan) {
            PriceMapParseResult result = null;
            if (useLocalDiscount()) {
                Map<Long, Long> skuPriceMap = new HashMap<>();
                for (ItemSkuDO sku : item.getItemSkuDOList()) {
                    skuPriceMap.put(sku.getSkuId(), sku.getPrice());
                }
                SystemDiscountChecker.SystemDiscountReq req = new SystemDiscountChecker.SystemDiscountReq();
                req.setExtra(item.getJsonExtra());
                req.setOrgiPrice(item.getReservePrice());
                req.setIsDisplay(false);
                req.setMarket(ContextUtil.getMarketByContext(context));
                req.setSkuPriceMap(skuPriceMap);
                SystemDiscountChecker.SystemDiscountRes res = SystemDiscountChecker.calcSystemDiscount(req);
                if (null != res && res.getIsGroupShopping()) {
                    result = new PriceMapParseResult(res.getCampaignId(), res.getSkuRalPrice(), true);
                }
            }
            if (null == result) {
                result = getNormalPriceMap(context);
            }
            if (null == result || null == result.getCampaignId()) {
                return null;
            }
            realPriceMap = result.getPriceMap();
            ret = new ParseResult(result.getCampaignId(), result.getSystem());
        } else {
            invertoryMap = getInventoryMap(oldSkus, IdConvertor.urlToId(strActivityId));
            realPriceMap = getPriceMap(context, IdConvertor.urlToId(strActivityId), outType);
            if (null == realPriceMap) {
                return null;
            }
        }

        List<ItemSkuDO> skus = new ArrayList<>();
        Integer totalStock = 0;
        for (ItemSkuDO sku : oldSkus) {
            ItemSkuDO newSku = new ItemSkuDO();
            newSku.setSkuId(sku.getSkuId());
            newSku.setXdSkuId(sku.getXdSkuId());
            newSku.setAttributions(sku.getAttributions());
            newSku.setItemId(sku.getItemId());
            newSku.setImage(sku.getImage());
            newSku.setCode(sku.getCode());
            newSku.setPrice(sku.getPrice());
            newSku.setNowPrice(realPriceMap.get(sku.getSkuId()) == null ? sku.getPrice().intValue() : realPriceMap.get(sku.getSkuId()).intValue());
            newSku.setIsDefault(sku.getIsDefault());
            newSku.setExtra(sku.getExtra());
            if (isNormalPinTuan) {
                newSku.setQuantity(sku.getQuantity());
            } else {
                newSku.setQuantity(invertoryMap.get(sku.getSkuId()) == null ? 0 : invertoryMap.get(sku.getSkuId()).getStock());
            }
            totalStock += newSku.getQuantity();
            skus.add(newSku);
        }

        List<SkuData> skuDatas = new ArrayList<>();
        if (CollectionUtils.isEmpty(skus)) {
            return ret;
        }
        boolean canBuy = this.canBuy(item, totalStock);
        String styleKey = "";
        String sizeKey = "";
        boolean isSizeDefault = false;
        boolean isStyleDefault = false;
        List<String> tmpStyle = new ArrayList<>();
        List<String> tmpSize = new ArrayList<>();
        List<StylePropData> styles = new ArrayList<>();
        List<SizePropData> sizes = new ArrayList<>();
        int styleId = 1;
        int sizeId = 100;
        Map<String, Integer> styleMap = new HashMap<>();
        Map<String, Integer> sizeMap = new HashMap<>();

        int index = 0;

        int lowestPrice = 0;
        int highestPrice = 0;
        int lowestNowPrice = 0;
        int highestNowPrice = 0;
        Map<String, Map<String, String>> attrsMap = new HashMap<>();
        for (ItemSkuDO sku : skus) {
            SkuData skuData = new SkuData();
            skuData.setCurrency("¥");
            skuData.setImg(null == sku.getImage() ? ImageUtil.img(item.getMainImage()) : ImageUtil.img(sku.getImage()));
            skuData.setNowprice(sku.getNowPrice());
            skuData.setPrice(sku.getPrice().intValue());
            skuData.setStock(canBuy ? sku.getQuantity() : 0);
            skuData.setXdSkuId(IdConvertor.idToUrl(sku.getXdSkuId()));
            skuData.setStockId(IdConvertor.idToUrl(sku.getSkuId()));
            skuData.setDelayTime(ContextUtil.contain30dayDeliveryService(context) ? SkuUtil.getDelayedDeliveryTimeWithoutNewYear(sku) : SkuUtil.getDelayedDeliveryTimeWithNewYear(item, sku));            skuData.setDelayHours(SkuUtil.getDelayDeliveryPeriod(sku.getExtra()));
            parseSizeType(skuData, sku.getAttributions());
            attrsMap.put(skuData.getStockId(), parseProps(skuData, sku.getAttributions()));
            skuDatas.add(skuData);
        }
        skuDO.setTotalStock(totalStock);

        for (SkuData skuData : skuDatas) {
            Map<String, String> attrMap = attrsMap.get(skuData.getStockId());
            styleKey = attrMap.get("style");
            sizeKey = attrMap.get("size");

            String realStyle = attrMap.get("style") == null ? "" : (attrMap.get("style").equals(styleKey)) ? skuData.getStyle() : skuData.getSize();
            String realSize = attrMap.get("size") == null ? "" : (attrMap.get("size").equals(sizeKey)) ? skuData.getSize() : skuData.getStyle();

            skuData.setStyle(StringUtils.isEmpty(realStyle) ? null : realStyle);
            skuData.setSize(StringUtils.isEmpty(realSize) ? null : realSize);

            if (!StringUtils.isEmpty(skuData.getStyle()) && !tmpStyle.contains(skuData.getStyle())) {
                tmpStyle.add(skuData.getStyle());
                if (StringUtils.isEmpty(realStyle) && false == isStyleDefault) {
                    isStyleDefault = true;
                }
                StylePropData styleProp = new StylePropData();
                styleProp.setDefault(false);//StringUtils.isEmpty(realStyle));
                styleProp.setType("style");
                styleProp.setName(skuData.getStyle());
                styleProp.setIndex(styleId);
                styleProp.setStyleId(styleId);
                styles.add(styleProp);
                styleMap.put(skuData.getStyle(), styleId);
                styleId++;
            }

            if (!StringUtils.isEmpty(skuData.getSize()) && !tmpSize.contains(skuData.getSize())) {
                tmpSize.add(skuData.getSize());
                if (StringUtils.isEmpty(realSize) && false == isSizeDefault) {
                    isSizeDefault = true;
                }

                SizePropData sizeProp = new SizePropData();
                sizeProp.setDefault(false);//StringUtils.isEmpty(realSize));
                sizeProp.setType("size");
                sizeProp.setName(skuData.getSize());
                sizeProp.setIndex(sizeId);
                sizeProp.setSizeId(sizeId);
                sizes.add(sizeProp);
                sizeMap.put(skuData.getSize(), sizeId);
                sizeId++;
            }

            // 给sku添加index
            if (!StringUtils.isEmpty(skuData.getStyle()) && null != styleMap.get(skuData.getStyle())) {
                skuData.setStyleId(styleMap.get(skuData.getStyle()));
            }

            if (!StringUtils.isEmpty(skuData.getSize()) && null != sizeMap.get(skuData.getSize())) {
                skuData.setSizeId(sizeMap.get(skuData.getSize()));
            }

            if (index == 0) {
                highestPrice = lowestPrice = skuData.getPrice();
                highestNowPrice = lowestNowPrice = skuData.getNowprice();
            }

            if (skuData.getPrice() > highestPrice) {
                highestPrice = skuData.getPrice();
            }

            if (skuData.getNowprice() > highestNowPrice) {
                highestNowPrice = skuData.getNowprice();
            }

            if (skuData.getPrice() < lowestPrice) {
                lowestPrice = skuData.getPrice();
            }

            if (skuData.getNowprice() < lowestNowPrice) {
                lowestNowPrice = skuData.getNowprice();
            }

            index++;
        }

        List<PropInfo> props = new ArrayList<>();
        if (!StringUtils.isEmpty(styleKey)) {
            PropInfo<StylePropData> stylePropInfo = new PropInfo();
            stylePropInfo.setLabel(styleKey);
            stylePropInfo.setList(styles);
            //默认sku能加库存的需求
            stylePropInfo.setDefault(false); //isStyleDefault);
            props.add(stylePropInfo);
            if (StringUtils.isEmpty(sizeKey)) {
                skuDO.setProps(props);
            }
        }
        if (!StringUtils.isEmpty(sizeKey)) {
            PropInfo<SizePropData> sizePropInfo = new PropInfo();
            sizePropInfo.setLabel(sizeKey);
            sizePropInfo.setList(sizes);
            sizePropInfo.setDefault(false); //isSizeDefault);
            props.add(sizePropInfo);
            skuDO.setProps(props);
        }

        skuDO.setStyleKey(StringUtils.isEmpty(styleKey) ? null : styleKey);
        skuDO.setSizeKey(StringUtils.isEmpty(sizeKey) ? null : sizeKey);
        Collections.sort(skuDatas, new Comparator<SkuData>() {
            @Override
            public int compare(SkuData o1, SkuData o2) {
                if (o1.getStyleId() == o2.getStyleId()) {
                    return o1.getSizeId() == o2.getSizeId() ? 0 : (o1.getSizeId() > o2.getSizeId() ? 1 : -1);
                } else {
                    return o1.getStyleId() > o2.getStyleId() ? 1 : -1;
                }
            }
        });

        skuDO.setSkus(skuDatas);
        //没有价格区间


        if (highestNowPrice == lowestNowPrice) {
            skuDO.setPriceRange(formatPrice(lowestNowPrice, true));
        } else {
            skuDO.setPriceRange(formatPrice(lowestNowPrice, true) + '~' + formatPrice(highestNowPrice, true));
        }
        skuDO.setLowNowPrice(formatPrice(lowestNowPrice, false));
        skuDO.setHighNowPrice(formatPrice(highestNowPrice, false));
        skuDO.setDefaultPrice(skuDO.getPriceRange());
        return ret;
    }

    private boolean useLocalDiscount() {
        try {
            Boolean useLocal = metabaseClient.getBoolean("discount_useLocal");
            if (null != useLocal) {
                return useLocal;
            }
        } catch (Exception e) {
            ;
        }
        return true;
    }

    private Map<Long, Long> getPriceMap(DetailContext context, Long actId, Integer outType) {
        ItemDO item = context.getItemDO();
        Map<Long, Long> skuPriceMap = new HashMap<>();
        for (ItemSkuDO sku : item.getItemSkuDOList()) {
            skuPriceMap.put(sku.getSkuId(), sku.getPrice());
        }
        ItemDetailRequestV2 request = new ItemDetailRequestV2();
        Pbuyer pbuyer = new Pbuyer();
        pbuyer.setBuyerId(DetailContextHolder.get().getLoginUserId());
        Pseller pSeller = new Pseller();
        pSeller.setSellerId(item.getUserId());
        PitemDetail pitemDetail = new PitemDetail();
        pitemDetail.setExtra(item.getJsonExtra());
        pitemDetail.setItemId(item.getItemId());
        Map<Long, Long> originSkuMap = skuPriceMap;
        pitemDetail.setSkuPriceMap(originSkuMap);
        pitemDetail.setItemPrice(getHighestPrice(item.getItemSkuDOList()));
        InvokeInfo invokeInfo = new InvokeInfo();
        invokeInfo.setChannel(2012);
        short market = RequestConstants.Market.MOGUJIE;
        try {
            App app = DetailContextHolder.get().getRouteInfo().getApp();
            if (item.getVerticalMarket() == 12) {
                market = 12;
            } else {
                market = App.MLS.equals(app) ? RequestConstants.Market.MEILISHUO : market;
            }
        } catch (Throwable e) {
            LOGGER.error("get app info failed : {}", e);
        }
        invokeInfo.setMarket(market);
        invokeInfo.setSource(RequestConstants.Source.DETAIL);
        invokeInfo.setTerminal(RequestConstants.Terminal.APP);
        invokeInfo.setOutId(actId.toString());
        invokeInfo.setOutType(null != outType ? outType.shortValue() : (short) 4);
        request.setPitemDetail(pitemDetail);
        request.setSeller(pSeller);
        request.setPbuyer(pbuyer);
        request.setInvokeInfo(invokeInfo);

        Result<ItemDetailPromotion> ret = promotionReadService.calcForItemDetailPromotion(request);
        if (null != ret && ret.isSuccess() && null != ret.getData() && null != ret.getData().getItemRealPrice()) {
            return ret.getData().getSkuRealPriceMap();
        }
        return null;
    }

    private PriceMapParseResult getNormalPriceMap(DetailContext context) {
        DetailItemDO item = context.getItemDO();
        Map<Long, Long> skuPriceMap = new HashMap<>();
        for (ItemSkuDO sku : item.getItemSkuDOList()) {
            skuPriceMap.put(sku.getSkuId(), sku.getPrice());
        }
        ItemDetailRequestV2 request = new ItemDetailRequestV2();
        Pbuyer pbuyer = new Pbuyer();
        pbuyer.setBuyerId(DetailContextHolder.get().getLoginUserId());
        Pseller pSeller = new Pseller();
        pSeller.setSellerId(item.getUserId());
        PitemDetail pitemDetail = new PitemDetail();
        pitemDetail.setExtra(item.getJsonExtra());
        pitemDetail.setItemId(item.getItemId());
        pitemDetail.setNumber(1L);
        pitemDetail.setItemTagList(PromotionConvertUtils.convertItemTagToItemProTag(context.getItemDO().getItemTags()));
        pitemDetail.setBuyType(RequestConstants.BuyType.GROUP_SHOPPING);
        Map<Long, Long> originSkuMap = skuPriceMap;
        pitemDetail.setSkuPriceMap(originSkuMap);
        pitemDetail.setItemPrice(getHighestPrice(item.getItemSkuDOList()));
        InvokeInfo invokeInfo = new InvokeInfo();
        short market = RequestConstants.Market.MOGUJIE;
        try {
            App app = DetailContextHolder.get().getRouteInfo().getApp();
            if (item.getVerticalMarket() == 12) {
                market = 12;
            } else {
                market = App.MLS.equals(app) ? RequestConstants.Market.MEILISHUO : market;
            }
        } catch (Throwable e) {
            LOGGER.error("get app info failed : {}", e);
        }
        invokeInfo.setMarket(market);
        invokeInfo.setSource(RequestConstants.Source.DETAIL);
        invokeInfo.setTerminal(RequestConstants.Terminal.APP);
        request.setPitemDetail(pitemDetail);
        request.setSeller(pSeller);
        request.setPbuyer(pbuyer);
        request.setInvokeInfo(invokeInfo);

        Result<ItemDetailPromotion> ret = promotionReadService.calcForItemDetailPromotion(request);
        if (null != ret && ret.isSuccess() && null != ret.getData()) {
            CampaignInfo campaignInfo = ret.getData().getCampaigninfo();
            if (null != campaignInfo) {
                if (null != campaignInfo.getPromotionMark() && PromotionConstants.PromotionCode.GROUP_SHOPPING.equals(campaignInfo.getPromotionMark().getPromotionCode())) {
                    Long campaignId = campaignInfo.getPromotionMark().getPromotionId();
                    return new PriceMapParseResult(campaignId, ret.getData().getSkuRealPriceMap(), campaignInfo.isSystem());
                } else {
                    return null;
                }
            }
        }
        return null;
    }

    private Map<Long, ActivityInventoryV2> getInventoryMap(List<ItemSkuDO> skus, Long actId) {
        List<Long> skuIds = new ArrayList<>();
        for (ItemSkuDO sku : skus) {
            skuIds.add(sku.getSkuId());
        }

        BatchActivityInventoryQueryParamV2 param = new BatchActivityInventoryQueryParamV2();
        param.setSkuIds(skuIds);
        param.setChannelId(2012);
        param.setActivityId(actId);
        MapResult<Long, ActivityInventoryV2> inventoryMapResult = inventoryReadService.batchQueryActivityInventoryV3(param);
        if (null != inventoryMapResult && inventoryMapResult.isSuccess()) {
            return inventoryMapResult.getData();
        }
        return new HashMap<>();
    }


    protected boolean canBuy(DetailItemDO item, Integer totalStock) {
        if (item.getIsDeleted() == 1) {
            return false;
        }

        if (item.getIsShelf() == 1) {
            return false;
        }

        int time = (int) (System.currentTimeMillis() / 1000);

        if (item.getStatus() == 1 || item.getStatus() < 0) {
            return false;
        }

        if (totalStock <= 0) {
            return false;
        }

        return true;
    }

    protected static String formatPrice(double price, Boolean withDolar) {
        return (withDolar ? "¥" : "") + NumUtil.formatNum(price / 100D);
    }

    protected Map<String, String> parseProps(SkuData skuData, List<SkuAttributionDO> attributions) {
        int index = 0;
        Map<String, String> mapping = new HashMap<>();
        if (CollectionUtils.isEmpty(attributions)) {
            return mapping;
        }

        List<SkuAttributionDO> atts = new ArrayList<>(attributions);
        Collections.sort(atts, new Comparator<SkuAttributionDO>() {
            @Override
            public int compare(SkuAttributionDO o1, SkuAttributionDO o2) {
                int o1Pos = o1.getShowPosition() == null ? 0 : o1.getShowPosition();
                int o2Pos = o2.getShowPosition() == null ? 0 : o2.getShowPosition();
                return o1Pos < o2Pos ? -1 : (o1Pos > o2Pos ? 1 : 0);
            }
        });

        for (SkuAttributionDO attr : atts) {
            String attrName = attr.getName();
            if (attr.getShowPosition() == null || attr.getShowPosition() == 0) {
                skuData.setStyle(attr.getValue());
                mapping.put("style", attrName);
            } else {
                skuData.setSize(attr.getValue());
                mapping.put("size", attrName);
            }
            index++;
        }
        return mapping;
    }

    private Long getHighestPrice(final List<ItemSkuDO> skuList) {
        if (CollectionUtil.isEmpty(skuList)) {
            return 0L;
        }
        Long highestPrice = skuList.get(0).getPrice().longValue();
        for (ItemSkuDO sku : skuList) {
            if (sku.getPrice().longValue() > highestPrice) {
                highestPrice = sku.getPrice().longValue();
            }
        }
        return highestPrice;
    }

    public static class PriceMapParseResult {

        /**
         * 是否是招商优惠
         */
        private Boolean isSystem;

        private Long campaignId;

        private Map<Long, Long> priceMap;

        public PriceMapParseResult(Long campaignId, Map<Long, Long> priceMap, Boolean isSystem) {
            this.campaignId = campaignId;
            this.priceMap = priceMap;
            this.isSystem = isSystem;
        }

        public Long getCampaignId() {
            return campaignId;
        }

        public void setCampaignId(Long campaignId) {
            this.campaignId = campaignId;
        }

        public Map<Long, Long> getPriceMap() {
            return priceMap;
        }

        public void setPriceMap(Map<Long, Long> priceMap) {
            this.priceMap = priceMap;
        }

        public Boolean getSystem() {
            return isSystem;
        }

        public void setSystem(Boolean system) {
            isSystem = system;
        }
    }

    public static class ParseResult {
        private Boolean isSystem;

        private Long campaignId;

        public ParseResult(Long campaignId, Boolean isSystem) {
            this.campaignId = campaignId;
            this.isSystem = isSystem;
        }

        public Long getCampaignId() {
            return campaignId;
        }

        public void setCampaignId(Long campaignId) {
            this.campaignId = campaignId;
        }

        public Boolean getSystem() {
            return isSystem;
        }

        public void setSystem(Boolean system) {
            isSystem = system;
        }
    }

    /**
     * 设置sku对应的号型
     *
     * @param skuData
     * @param attrs
     */
    private void parseSizeType(SkuData skuData, List<SkuAttributionDO> attrs) {
        if (attrs == null) {
            return;
        }
        for (SkuAttributionDO skuAttributionDO : attrs) {
            String props = skuAttributionDO.getProperties();
            if (org.apache.commons.lang3.StringUtils.isBlank(props)) {
                continue;
            }
            String[] propsPair = org.apache.commons.lang3.StringUtils.split(props, ";");
            for (String property : propsPair) {
                String[] kvs = org.apache.commons.lang3.StringUtils.split(property, ":");
                if (kvs.length != 2) {
                    continue;
                }
                if ("号型".equals(kvs[0])) {
                    skuData.setSizeType(kvs[1]);
                    return;
                }
            }
        }
    }
}
