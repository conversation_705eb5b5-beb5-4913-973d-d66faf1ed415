package com.mogujie.detail.module.activity.provider;

import com.alibaba.fastjson.JSONObject;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.*;
import com.mogujie.detail.module.activity.domain.*;
import com.mogujie.detail.module.activity.util.ActivityUtil;
import com.mogujie.metabase.spring.client.MetabaseClient;
import com.mogujie.service.hummer.constains.PromotionConstants;
import com.mogujie.service.hummer.constains.RequestConstants;
import com.mogujie.service.hummer.utils.RedPacketChecker;
import com.mogujie.service.hummer.utils.SystemDiscountChecker;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.themis.camp.config.CampTagConfigClient;
import com.mogujie.themis.camp.dto.BaseCampConfigDTO;
import com.mogujie.themis.config.BaseInfo;
import com.mogujie.themis.config.CampMetabaseConfig;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.mogujie.cayenne.timeclient.TimeClient;
import org.mogujie.cayenne.timeclient.TimeClientFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by xiaoyao on 16/10/26.
 */
@Module(name = "activity")
public class ActivityDOProvider implements IModuleDOProvider<ActivityDO> {

    protected final int PRE_ACTIVITY_STATE = 1; // 预热期间

    protected final int DURING_ACTIVITY_SATE = 2; // 活动期间

    protected final int MGJ_PLATFORM_ID = 8;

    @Resource(name = "confMetabaseClient")
    protected MetabaseClient metabaseClient;

    @Autowired
    protected CommonSwitchUtil commonSwitchUtil;

    // 大促氛围场景
    protected static final String APP_KEY = "detail";

    private static final Logger LOGGER = LoggerFactory.getLogger(ActivityDOProvider.class);

    protected TimeClient timeClient = null;

    @Override
    public ActivityDO emit(DetailContext context) {

        DetailItemDO itemDO = context.getItemDO();
        // 活动的优先级最高
        // 获取尝新基金&红包的相关
        Integer redPacketFlag = RedPacketChecker.getFlag(itemDO.getJsonExtra());
        Gift redPacketsGift = ActivityUtil.getRedPacketsGift(itemDO.getJsonExtra(), redPacketFlag);
        List<Gift> giftList = new ArrayList<>();
        if (null != redPacketsGift) {
            giftList.add(redPacketsGift);
        }
        Gift modouGift = null;
        if (null != modouGift) {
            giftList.add(modouGift);
        }
        ActivityDO activityDO = this.getActivityInfo(context);

        //有活动或团购信息
        if (null != activityDO) {
            // 展示红包和尝新基金的相关信息
            if (!CollectionUtils.isEmpty(giftList)) {
                activityDO.setGiftList(giftList);
            }
            return activityDO;
        } else if (!CollectionUtils.isEmpty(giftList)) {
            activityDO = new ActivityDO();
            activityDO.setGiftList(giftList);
            return activityDO;
        }
        return null;
    }

    /**
     * 获取活动信息
     *
     * @return
     */
    private ActivityDO getActivityInfo(DetailContext context) {
        try {
            int activityState;
            long countdown;
            int startTime, endTime;
            List<String> tags = ContextUtil.getNumTags(context.getItemDO());
            String tagString = StringUtils.join(tags, ",");
            if (CampTagConfigClient.isWithinWarmUpCampByMarket(MGJ_PLATFORM_ID, tagString)) {
                activityState = PRE_ACTIVITY_STATE;
                BaseCampConfigDTO baseCampConfigDTO = CampTagConfigClient.getBaseCampConfigTagDTODetail(MGJ_PLATFORM_ID, tagString);
                startTime = baseCampConfigDTO.getStartTime();
                endTime = baseCampConfigDTO.getEndTime();
                countdown = startTime - (int) (timeClient.fake() / 1000);
            } else if (CampTagConfigClient.isWithinCampByMarket(MGJ_PLATFORM_ID, tagString)) {
                activityState = DURING_ACTIVITY_SATE;
                BaseCampConfigDTO baseCampConfigDTO = CampTagConfigClient.getBaseCampConfigTagDTODetail(MGJ_PLATFORM_ID, tagString);
                startTime = baseCampConfigDTO.getStartTime();
                endTime = baseCampConfigDTO.getEndTime();
                countdown = endTime - (int) (timeClient.fake() / 1000);
            } else {
                return null;
            }
            boolean isC1 = BaseInfo.CampLevel.C1.getCode().equalsIgnoreCase(CampTagConfigClient.getBaseCampConfigTagDTODetail(MGJ_PLATFORM_ID, tagString).getLevel());
            ActivityDO activityDO = null;
            switch (context.getRouteInfo().getPlatform()) {
                case PC:
                    activityDO = this.buildPcActivityInfo(context, isC1, activityState, countdown);
                    break;
                default:
                    activityDO = this.buildActivityInfo(context, isC1, activityState, countdown);
                    break;
            }
            if (activityDO == null) {
                return null;
            }
            activityDO.setActivityState(activityState);
            activityDO.setStartTime(startTime);
            activityDO.setEndTime(endTime);
            return activityDO;
        }catch (Exception ex){
            LOGGER.error("getActivityInfo Exception",ex);
            return null;
        }
    }

    private ActivityDO buildPcActivityInfo(DetailContext context, boolean isC1, int activityState, long countdown) {
        // 获取配置的活动资源信息
        DetailItemDO pcItem = context.getItemDO();
        List<String> tags = ContextUtil.getNumTags(pcItem);
        String tagString = StringUtils.join(tags, ",");
        JSONObject jsonObject = JSONObject.parseObject(metabaseClient.get("hide_c1_normal_item_countdown_pc"));
        boolean pcSwitch=jsonObject.getBoolean("pcCloseSwitch")==null?false:jsonObject.getBoolean("pcCloseSwitch");
        if(pcSwitch){
            return null;
        }
        if (isC1 && jsonObject.getBoolean("hide") && (StringUtils.isBlank(tagString) || !tags.contains(jsonObject.getString("tag")))) {
            return null;
        }
        Map<String, Object> map = CampTagConfigClient.getCampConfigDetail(MGJ_PLATFORM_ID, null, tagString, BaseInfo.Platform.PC.getCode(), CampMetabaseConfig.CampConfigMenu.CAMP_CONFIG_PAGE_DETAIL);
        if (MapUtils.isEmpty(map)) {
            return null;
        }
        ActivityDO pcEventInfo = new ActivityDO();
        pcEventInfo.setCountdown(countdown);
        pcEventInfo.setWarmUpTitle(CampTagConfigClient.objToString(map.get("warmUpTitle")));
        pcEventInfo.setType(0); // 0 表示显示倒计时  1 表示显示文本

        SystemDiscountChecker.SystemDiscountReq req = new SystemDiscountChecker.SystemDiscountReq();
        req.setExtra(pcItem.getJsonExtra());
        req.setOrgiPrice(pcItem.getReservePrice());
        req.setIsDisplay(true);
        req.setMarket(ContextUtil.getMarketByContext(context));
        Map<Long, Long> skuPriceMap = new HashMap<>();
        for (ItemSkuDO sku : pcItem.getItemSkuDOList()) {
            skuPriceMap.put(sku.getSkuId(), sku.getPrice().longValue());
        }
        req.setSkuPriceMap(skuPriceMap);
        SystemDiscountChecker.SystemDiscountRes ret = SystemDiscountChecker.calcSystemDiscount(req);

        //计算出区间价
        Long realLowPrice = null, realHighPrice = null; Long eventPricePence=0l;
        if (ret != null) {

            eventPricePence= ret.getRealPrice();

            Map<Long, Long> realPriceMap = ret.getSkuRalPrice();
            long max = 0;
            long min = Long.MAX_VALUE;
            if (MapUtils.isNotEmpty(realPriceMap)) {
                for (Long skuPrice : realPriceMap.values()) {
                    min = skuPrice < min ? skuPrice : min;
                    max = skuPrice > max ? skuPrice : max;
                }
                if (max != min) {
                    realLowPrice = min;
                    realHighPrice = max;
                }
            }
        }

        float price = eventPricePence / 100f;

        if (activityState == PRE_ACTIVITY_STATE) {
            pcEventInfo.setCountdownTitle(CampTagConfigClient.objToString(map.get("pre_countdown_text")));
            pcEventInfo.setPriceDesc(pcItem.getLimitNum() != null ? pcItem.getDiscountDesc() : CampTagConfigClient.objToString(map.get("pre_price_text")));
            pcEventInfo.setPriceColor("#333333");
            pcEventInfo.setCountdownBgImg(ImageUtil.img(CampTagConfigClient.objToString(map.get("pre_countdown_bg"))));

            if (price > 0) {
                // 活动价格存在时
                WarmUpPrice warmUpPrice = new WarmUpPrice();
                if (realLowPrice == null || realHighPrice == null) {
                    warmUpPrice.setPrice("¥" + NumUtil.formatNum(price, 2));
                } else {
                    warmUpPrice.setPrice("¥" + NumUtil.formatNum(realLowPrice / 100f, 2) + "~¥" + NumUtil.formatNum(realHighPrice / 100f, 2));
                }
                pcEventInfo.setWarmUpPrice(warmUpPrice);
            }else {
                pcEventInfo.setPriceColor("#ff0000");
            }

        }else if (activityState == DURING_ACTIVITY_SATE) {
            pcEventInfo.setCountdownTitle(CampTagConfigClient.objToString(map.get("countdown_text")));
            pcEventInfo.setPriceColor(CampTagConfigClient.objToString(map.get("price_color")));
            pcEventInfo.setCountdownBgImg(ImageUtil.img(CampTagConfigClient.objToString(map.get("countdown_bg"))));

            if (price > 0) {
                pcEventInfo.setPriceDesc(pcItem.getLimitNum() != null ? pcItem.getDiscountDesc() : CampTagConfigClient.objToString(map.get("price_tag_text")));
                pcEventInfo.setHideDiscount(true);
                pcEventInfo.setInActivityItem(true);
            }
        }else{
            return null;
        }

        // 价格保证 双11大促需要展示"30天最低价",
        if (isC1) {
            String priceGuarantee = CampTagConfigClient.objToString(map.get("priceGuarantee"));
            if (StringUtils.isNotBlank(priceGuarantee)) {
                pcEventInfo.setPriceGuarantee(priceGuarantee);
            }
        }


        return pcEventInfo;
    }

    /**
     * 设置活动资源信息
     *
     * @param activityState 活动状态, 1: 预热期  2: 活动期
     * @param countdown     倒计时s
     * @return
     */
    protected ActivityDO buildActivityInfo(DetailContext context, boolean isC1, int activityState, long countdown) {
        // 获取配置的活动资源信息
        DetailItemDO item = context.getItemDO();
        List<String> tags = ContextUtil.getNumTags(item);
        String tagString = StringUtils.join(tags, ",");
        JSONObject jsonObject = JSONObject.parseObject(metabaseClient.get("hide_c1_normal_item_countdown_default"));
        if (isC1 && jsonObject.getBoolean("hide") && (StringUtils.isBlank(tagString) || !tags.contains(jsonObject.getString("tag")))) {
            return null;
        }
        Map<String, Object> map = CampTagConfigClient.getCampConfigDetail(MGJ_PLATFORM_ID, null, tagString, BaseInfo.Platform.APP.getCode(), CampMetabaseConfig.CampConfigMenu.CAMP_CONFIG_PAGE_DETAIL);
        if (MapUtils.isEmpty(map)) {
            return null;
        }


        if (MetabaseTool.isOn("activity_sp_item_atmosphere", false)) {
            List<Map<String, Object>> spTags = MaitUtil.getMaitData(156699);
            if (spTags != null && !spTags.isEmpty()) {
                Map<String, Object> spTag = spTags.get(0);
                if (tags != null && tags.contains(spTag.get("itemmark") + "")) {
                    map.putAll(spTag);
                }
            }
        }


        ActivityDO eventInfo = new ActivityDO();
        eventInfo.setCountdown(countdown);
        eventInfo.setWarmUpTitle(CampTagConfigClient.objToString(map.get("warmUpTitle")));
        eventInfo.setType(0); // 0 表示显示倒计时  1 表示显示文本

        SystemDiscountChecker.SystemDiscountReq req = new SystemDiscountChecker.SystemDiscountReq();
        req.setExtra(item.getJsonExtra());
        req.setOrgiPrice(item.getReservePrice());
        req.setIsDisplay(true);
        req.setMarket(ContextUtil.getMarketByContext(context));
        Map<Long, Long> skuPriceMap = new HashMap<>();
        for (ItemSkuDO sku : item.getItemSkuDOList()) {
            skuPriceMap.put(sku.getSkuId(), sku.getPrice().longValue());
        }
        req.setSkuPriceMap(skuPriceMap);
        SystemDiscountChecker.SystemDiscountRes ret = SystemDiscountChecker.calcSystemDiscount(req);

        //计算出区间价
        Long realLowPrice = null, realHighPrice = null;
        if (ret != null) {
            Map<Long, Long> realPriceMap = ret.getSkuRalPrice();
            long max = 0;
            long min = Long.MAX_VALUE;
            if (MapUtils.isNotEmpty(realPriceMap)) {
                for (Long skuPrice : realPriceMap.values()) {
                    min = skuPrice < min ? skuPrice : min;
                    max = skuPrice > max ? skuPrice : max;
                }
                if (max != min) {
                    realLowPrice = min;
                    realHighPrice = max;
                }
            }
        }
        Long eventPricePence = ret == null ? 0 : ret.getRealPrice();
        float price = eventPricePence / 100f;

        Integer redPacketFlag = RedPacketChecker.getFlag(item.getJsonExtra());
        // 商品有红包抵扣且有大促价, 则透出红包抵扣价格
        if ((null == redPacketFlag || redPacketFlag != PromotionConstants.RedpacketFlag.COMMON) && price > 0) {
            Long redPacketPence = RedPacketChecker.getSupportMoney(item.getJsonExtra(), RequestConstants.Market.MOGUJIE);
            if (redPacketPence != null && redPacketPence > 0) {
                String redPacketPriceStr = "¥" + NumUtil.formatNum((eventPricePence - redPacketPence) / 100.0F, 2);
                RedPacketPrice redPacketPrice = new RedPacketPrice();
                redPacketPrice.setPrice(redPacketPriceStr);
                redPacketPrice.setColor("#ff2255");
                if (activityState == PRE_ACTIVITY_STATE) {
                    redPacketPrice.setPriceDesc(metabaseClient.get("redPacketPriceDescPre"));
                } else {
                    redPacketPrice.setPriceDesc(metabaseClient.get("redPacketPriceDescDuring"));
                }
                eventInfo.setRedPacketPrice(redPacketPrice);
            }
        }

        // 价格保证 双11大促需要展示"30天最低价",
        if (isC1) {
            String priceGuarantee = CampTagConfigClient.objToString(map.get("priceGuarantee"));
            if (StringUtils.isNotBlank(priceGuarantee)) {
                eventInfo.setPriceGuarantee(priceGuarantee);
            }
        }

        eventInfo.setActivitySphereImage(ImageUtil.img(CampTagConfigClient.objToString(map.get("activitySphere"))));
        eventInfo.setActivityTitleImage(ImageUtil.img(CampTagConfigClient.objToString(map.get("activityTitle"))));
        //1590 大促主力盘商品标签 https://mogu.feishu.cn/docs/doccnFAaZ4tCHOsLoWp6sQzq2Kb
        if (StringUtils.isNotEmpty(eventInfo.getActivityTitleImage())) {
            List<Map<String, Object>> spTags = MaitUtil.getMaitData(156207);
            if (spTags != null && !spTags.isEmpty()) {
                Map<String, Object> spTag = spTags.get(0);
                if (tags != null && tags.contains(spTag.get("itemmark") + "")) {
                    eventInfo.setActivityTitleImage(spTag.get("tagIconImg").toString());
                }
            }
        }
        eventInfo.setActivityIcon(ImageUtil.img(CampTagConfigClient.objToString(map.get("activityIcon"))));
        eventInfo.setEndTimeHintColor(CampTagConfigClient.objToString(map.get("endTimeHintColor")));
        if (activityState == PRE_ACTIVITY_STATE) {
            eventInfo.setCountdownBgImg(ImageUtil.img(CampTagConfigClient.objToString(map.get("pre_countdown_bg"))));
            eventInfo.setCountdownTitle(CampTagConfigClient.objToString(map.get("pre_countdown_text")));
            eventInfo.setPreActivityInImage1110(ImageUtil.img(CampTagConfigClient.objToString(map.get("preActivityInImage1110"))));
            eventInfo.setActivityPreImage(ImageUtil.img(CampTagConfigClient.objToString(map.get("activityPreImage"))));
            if (price > 0) {
                // 活动价格存在时
                List<Map<String, Object>> colResources = MaitUtil.getMaitData(ActivityUtil.EVENT_INFO);
                // 预热价格
                String priceTagText = null;
                Map<String, Object> tagMap = ActivityUtil.getEventTagsMap(colResources);
                if (tagMap != null && tagMap.get("priceTag") != null) {
                    EventTag priceTag = (EventTag) tagMap.get("priceTag");
                    priceTagText = ret.getIsGroupShopping() ? priceTag.getPintuanTagText() : priceTag.getTagText();
                }
                WarmUpPrice warmUpPrice = new WarmUpPrice();
                if (realLowPrice == null || realHighPrice == null) {
                    warmUpPrice.setPrice("¥" + NumUtil.formatNum(price, 2));
                } else {
                    warmUpPrice.setPrice("¥" + NumUtil.formatNum(realLowPrice / 100f, 2) + "~¥" + NumUtil.formatNum(realHighPrice / 100f, 2));
                }
                warmUpPrice.setColor("#ff2255");
                warmUpPrice.setPriceDesc(StringUtils.isBlank(priceTagText) ? "活动价" : priceTagText);
                eventInfo.setWarmUpPrice(warmUpPrice);
                // 活动标签
                if (tagMap != null && tagMap.get("eventTags") != null) {
                    eventInfo.setEventTags((List<EventTag>) tagMap.get("eventTags"));
                }
            }
            return eventInfo;
        } else if (activityState == DURING_ACTIVITY_SATE) {
            eventInfo.setCountdownBgImg(ImageUtil.img(CampTagConfigClient.objToString(map.get("countdown_bg"))));
            eventInfo.setCountdownTitle(CampTagConfigClient.objToString(map.get("countdown_text")));
            eventInfo.setActivityInImage(ImageUtil.img(CampTagConfigClient.objToString(map.get("activityInImage"))));
            eventInfo.setActivityInImage1110(ImageUtil.img(CampTagConfigClient.objToString(map.get("activityInImage1110"))));
            eventInfo.setActivityInTitleColor(CampTagConfigClient.objToString(map.get("activityInTitleColor")));
            eventInfo.setDiscountPriceColor(CampTagConfigClient.objToString(map.get("discountPriceColor")));
            eventInfo.setDiscountPriceBgColor(CampTagConfigClient.objToString(map.get("discountPriceBgColor")));
            if (price > 0) {
                // 活动期间内, 隐藏折扣，显示价格描述和价格颜色
                eventInfo.setPriceDesc(item.getLimitNum() != null ? item.getDiscountDesc() : CampTagConfigClient.objToString(map.get("price_tag_text")));
                eventInfo.setPriceColor(CampTagConfigClient.objToString(map.get("price_color")));
                eventInfo.setHideDiscount(true);
                eventInfo.setInActivityItem(true);
            }
            return eventInfo;
        } else {
            // 非法的活动状态
            return null;
        }
    }

    @Override
    public void init() throws DetailException {
        try {
            timeClient = TimeClientFactory.client("cayenne");
        } catch (Exception e) {
            LOGGER.error("init service failed : {}", e);
        }
    }
}
