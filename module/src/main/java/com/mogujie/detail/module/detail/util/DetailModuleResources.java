package com.mogujie.detail.module.detail.util;

import java.util.*;

/**
 * Created by <PERSON><PERSON><PERSON> on 16/1/7.
 */
public class DetailModuleResources {
    // 大类的商品模块配置
    protected static Map<String, List<String>> config;

    // 公共的模块库
    protected static Map<String, String> modules;

    static {
        config = initItemTypes();
        modules = initPublicModules();
    }

    private static Map<String, List<String>> initItemTypes() {
        config = new LinkedHashMap<>();
        config.put("衣服", Arrays.asList("size_info", "model_img", "baipai_img2", "detail_img", "product_info", "brand_info", "package_info", "service_info", "shop_info", "qualification_info"));
        config.put("鞋子", Arrays.asList("size_info", "model_img", "baipai_img3", "detail_img", "product_info", "brand_info", "package_info", "service_info", "shop_info", "qualification_info"));
        config.put("包包", Arrays.asList("size_info", "model_img", "baipai_img3", "detail_img", "product_info", "brand_info", "package_info", "service_info", "shop_info", "qualification_info"));
        config.put("配饰", Arrays.asList("size_info", "model_img", "baipai_img3", "detail_img", "product_info", "brand_info", "package_info", "service_info", "shop_info", "qualification_info"));
        config.put("家居", Arrays.asList("effect_img", "baipai_img3", "detail_img", "size_info", "product_info", "brand_info", "package_info", "service_info", "shop_info", "qualification_info"));
        config.put("数码", Arrays.asList("size_info", "baipai_img3", "detail_img", "product_show", "function_info", "product_detail", "service_info3", "brand_info", "package_info", "shop_info", "qualification_info"));
        config.put("家电", Arrays.asList("baipai_img3", "detail_img", "product_show", "function_info", "product_detail", "service_info3", "brand_info", "package_info", "shop_info", "qualification_info"));
        config.put("虚拟", Arrays.asList("product_detail", "shop_info", "qualification_info"));
        config.put("美妆", Arrays.asList("size_info", "beauty_info", "product_specifics", "product_use_func", "brand_introduction", "brand_authorization", "beauty_service_info", "shop_info", "qualification_info"));
        config.put("食品", Arrays.asList("effect_img", "baipai_img3", "detail_img", "size_info", "product_info", "brand_info", "package_info", "service_info", "smart_shop_info", "shop_info", "qualification_info"));
        config.put("文玩", Arrays.asList(new String[]{}));

        config.put("汽车", Arrays.asList("car_show", "car_details", "car_realshot", "car_service", "shop_info", "qualification_info"));
        config.put("旅游", Arrays.asList("travel_content", "shop_info", "qualification_info"));
        config.put("酒店", Arrays.asList("room_introduce", "food_device", "shop_info", "qualification_info"));
        return config;
    }

    private static Map<String, String> initPublicModules() {
        modules = new HashMap<String, String>() {
            private static final long serialVersionUID = -312157765169074636L;
            {
                put("size_info", "尺码说明");
                put("model_img", "穿着效果");
                put("baipai_img2", "整体款式");
                put("baipai_img3", "整体款式");
                put("detail_img", "细节做工");
                put("product_info", "产品介绍");
                put("brand_info", "品牌介绍");
                put("package_info", "包装展示");
                put("service_info", "服务说明");
                put("service_info3", "服务说明");
                put("effect_img", "效果图");
                put("product_show", "产品展示");
                put("function_info", "功能详情");
                put("product_detail","产品详情");

                // 为美妆添加
                put("beauty_info","商品信息");
                put("product_specifics","商品细节");
                put("product_use_func","使用说明");
                put("brand_introduction","品牌介绍");
                put("brand_authorization","品牌授权");
                put("beauty_service_info","服务说明");
                put("smart_shop_info","关注动态图");

                // 汽配摩托—--新车/二手车
                put("car_show","车型展示");
                put("car_details","细节展示");
                put("car_realshot","车型实拍");
                put("car_service","售后服务");

                // 文化玩乐—度假线路/旅游服务/签证
                put("travel_content","旅游内容");

                // 文化玩乐—特价酒店
                put("room_introduce","客房介绍");
                put("food_device","餐饮/设施");

                // 为美丽说添加
                put("shop_info", "店铺介绍");
                put("qualification_info", "资质认证");
            }
        };

        return modules;
    }
}
