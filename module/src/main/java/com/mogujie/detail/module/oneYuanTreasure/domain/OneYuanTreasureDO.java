package com.mogujie.detail.module.oneYuanTreasure.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by houan on 18/7/3.
 */
public class OneYuanTreasureDO implements ModuleDO {

    /**
     * 活动id
     */
    @Setter
    @Getter
    private String activityId;

    /**
     * 商品原价
     */
    @Setter
    @Getter
    private String originalPrice;


    /**
     * 用户购买获取夺宝码的次数
     */
    @Setter
    @Getter
    private Integer buyCodeNum;

    /**
     * 用户分享获取夺宝码的次数
     */
    @Setter
    @Getter
    private Integer shareCodeNum;

    /**
     * 是否限制购买
     */
    @Setter
    @Getter
    private Boolean isBuyLimit = Boolean.FALSE;

    /**
     * 是否限制分享
     */
    @Setter
    @Getter
    private Boolean isShareLimit = Boolean.FALSE;


    /**
     * 用户分享链接
     */
    @Setter
    @Getter
    private String shareUrl;

    /**
     * 已有夺宝码数
     */
    @Setter
    @Getter
    private Integer hasCodeNum;

    /**
     * 开奖总需夺宝码
     */
    @Setter
    @Getter
    private Integer totalCodeNum;

    /**
     * 夺宝状态 0未开始 1进行中 3已开奖-未中奖用户 4已开奖-中奖用户-未领取 5已开奖-中奖用户-已领取 6已过期
     */
    @Setter
    @Getter
    private Integer status;

    /**
     * 中奖者
     */
    @Setter
    @Getter
    private String winUserName;

    /**
     * 中奖用户的夺宝码数量
     */
    @Setter
    @Getter
    private Integer winUserCodeNum;

    /**
     * 中奖夺宝码
     */
    @Setter
    @Getter
    private Long winUserCode;

    /**
     * 抽检开始时间
     */
    @Setter
    @Getter
    private Long startTime;

    /**
     * 抽奖结束时间
     */
    @Setter
    @Getter
    private Long endTime;

    /**
     * 当前时间
     */
    @Setter
    @Getter
    private Long currentTime;

    /**
     * 开奖状态：开奖时间
     */
    @Setter
    @Getter
    private Long drawnTime;

    /**
     * 开奖状态：中奖者头像
     */
    @Setter
    @Getter
    private String winUserAvatar;

    /**
     * 进行中状态：用户的手机号 可能为空
     */
    @Setter
    @Getter
    private String phone;

    /**
     * 未开始状态：是否设置提醒
     */
    @Setter
    @Getter
    private Boolean isSetRemind;

    /**
     * 进行中状态：拥有免费夺宝资格数量
     */
    @Setter
    @Getter
    private Integer hasFreeQual;

    @Setter
    @Getter
    private Integer isVirtualPrize;

    /**
     * 该夺宝商品最近夺宝记录
     */
    @Setter
    @Getter
    private List<OneYuanTreatureRecord> recentRecordList;

    public OneYuanTreasureDO() {
    }


    /**
     * 用户一元夺宝记录
     */
    private class OneYuanTreatureRecord {
        /**
         * 用户名
         */
        @Setter
        @Getter
        private String userName;

        /**
         * 用户头像
         */
        @Setter
        @Getter
        private String avatar;

        /**
         * 获取夺宝码时间
         */
        @Setter
        @Getter
        private Long getCodeTime;

        /**
         * 获取夺宝码的渠道 0购买 1分享
         */
        @Setter
        @Getter
        private Integer channel;

        /**
         * 获取夺宝码
         */
        @Setter
        @Getter
        private Long code;


    }

}
