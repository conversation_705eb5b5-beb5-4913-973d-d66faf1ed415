package com.mogujie.detail.module.floatlayer.provider;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.MaitUtil;
import com.mogujie.detail.module.floatlayer.domain.FloatLayerDO;
import com.mogujie.tesla.common.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by xiaoyao on 16/11/3.
 */
@Module(name = "floatlayer")
public class FloatLayerDOProvider implements IModuleDOProvider<FloatLayerDO> {

    private static final Logger LOGGER = LoggerFactory.getLogger(FloatLayerDOProvider.class);

    private static final String GIRL_CLOTHES_CID = "683";

    private static final String UNDER_CLOTHES_CID = "1395";

    private static final String SEXY_UNDER_CLOTHES_CID = "1410";

    private static final String GIRL_SHOES_CID = "757";


    private static final long FLOAT_RESOURCE_CODE = 438L;


    Map<Integer, String> headers = new HashMap<Integer, String>() {

        {
            put(1, "detail_Stay");
            put(2, "detail_Ssize");
            put(3, "detail_Ccup");
        }
    };


    @Override
    public FloatLayerDO emit(DetailContext context) {
        List<String> cids = getCids(context.getItemDO().getCids());

        int type = 0;
        if (cids.contains(GIRL_CLOTHES_CID)) {
            type = 2;
        } else if (cids.contains(UNDER_CLOTHES_CID) && !cids.contains(SEXY_UNDER_CLOTHES_CID)) {
            type = 3;
        } else if (cids.contains(GIRL_SHOES_CID)) {
            type = 1;
        }

        FloatLayerDO floatLayer = this.getFloatLayerImgs(type, cids);
        return floatLayer;
    }

    @Override
    public void init() throws DetailException {
    }

    private List<String> getCids(String cids) {
        List<String> cidList = new ArrayList<>();
        if (null == cids) {
            return cidList;
        }

        String[] rawCids = cids.split(" ");
        if (rawCids.length < 1) {
            return cidList;
        }

        for (String rawCid : rawCids) {
            cidList.add(rawCid.replaceAll("#", ""));
        }
        return cidList;

    }

    private FloatLayerDO getFloatLayerImgs(int type, List<String> cids) {
        List<Map<String, Object>> resources = MaitUtil.getMaitData(FLOAT_RESOURCE_CODE);
        if (CollectionUtils.isEmpty(resources)) {
            // 没有获取到资源
            return null;
        }

        Map<String, Object> materials = new HashMap<>();
        if (type != 0) {
            for (Map<String, Object> map : resources) {
                if (!"2".equals(map.get("location"))
                        || !headers.get(type).equals(map.get("title"))) {
                    continue;
                }
                materials = map;
            }
        }

        if (CollectionUtils.isEmpty(materials)) {
            // 非三种特殊情况
            // 上面没有获得数据，则再扫描一遍，判断新的规则
            for (Map<String, Object> map : resources) {
                if (!"2".equals(map.get("location")))
                    continue;

                String title = map.get("title") + "";
                String[] tcids = StringUtils.split(title, ",");
                if (!containSameCell(cids, tcids)) { // 类目匹配失败
                    continue;
                }

                materials = map;
                type = 1; //这种情况设置为3s后自动消失
                break;
            }
        }


        if (CollectionUtils.isEmpty(materials)) {
            // 依然没有获得资源
            return null;
        }


        FloatLayerDO floatLayer = new FloatLayerDO();
        String duration = String.valueOf(materials.get("interval"));
        try {
            floatLayer.setDuration(Integer.valueOf(duration));
        } catch (Exception e) {
            floatLayer.setDuration(4);
        }

        List<String> imgs = new ArrayList<>(5);
        String img1 = (String) materials.get("floatImg1");
        String img2 = (String) materials.get("floatImg2");
        String img3 = (String) materials.get("floatImg3");
        String img4 = (String) materials.get("floatImg4");
        String img5 = (String) materials.get("floatImg5");
        if (StringUtils.isNotBlank(img1)) {
            imgs.add(img1);
        }

        if (StringUtils.isNotBlank(img2)) {
            imgs.add(img2);
        }

        if (StringUtils.isNotBlank(img3)) {
            imgs.add(img3);
        }

        if (StringUtils.isNotBlank(img4)) {
            imgs.add(img4);
        }

        if (StringUtils.isNotBlank(img5)) {
            imgs.add(img5);
        }

        floatLayer.setType(type);
        floatLayer.setIsShow(Boolean.valueOf((String)materials.get("isShow")));
        floatLayer.setMaterials(imgs);
        floatLayer.setLink((String) materials.get("link"));

        return floatLayer;
    }

    private boolean containSameCell(List<String> cids, String[] tcids) {
        for (String cid : tcids) {
            if (cids.contains(cid)) {
                return true;
            }
        }

        return false;
    }
}
