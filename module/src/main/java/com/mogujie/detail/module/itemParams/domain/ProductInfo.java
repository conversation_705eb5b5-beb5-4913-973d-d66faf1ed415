package com.mogujie.detail.module.itemParams.domain;

import com.mogujie.metabase.utils.CollectionUtils;
import com.mogujie.metabase.utils.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 产品参数
 * Created by <PERSON><PERSON><PERSON> on 15/12/7.
 */
public class ProductInfo implements Serializable {
    private static final long serialVersionUID = -8880299562816316950L;

    public static final String SET_KEY_FIRST = "key";
    public static final String SET_KEY_SECOND = "value";
    public static final String DETAIL_MODULE_NAME = "product_info";

    /**
     * 产品参数的图片列表
     */
    private List<String> images;
    /**
     * 产品参数的具体属性值
     */
    private Map<String, String> set;
    /**
     * 产品参数的描述
     */
    private String desc;
    /**
     * 产品参数模块显示名,一般为 产品参数
     */
    private String key;
    /**
     * 产品参数的锚点,用于pc详情页, 一般为: product_info
     */
    private String anchor;

    public ProductInfo() {
    }

    public List<String> getImages() {
        return images;
    }

    public void setImages(List<String> images) {
        this.images = images;
    }

    public Map<String, String> getSet() {
        return set;
    }

    public void setSet(Map<String, String> set) {
        this.set = set;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getAnchor() {
        return anchor;
    }

    public void setAnchor(String anchor) {
        this.anchor = anchor;
    }

    public boolean isEmpty() {
        if (!StringUtils.isBlank(desc) || !CollectionUtils.isEmpty(images) || !CollectionUtils.isEmpty(set)) {
            return false;
        } else {
            return true;
        }
    }

    @Override
    public String toString() {
        return "ProductInfo{" +
                "images=" + images +
                ", set=" + set +
                ", desc='" + desc + '\'' +
                ", key='" + key + '\'' +
                ", anchor='" + anchor + '\'' +
                '}';
    }
}
