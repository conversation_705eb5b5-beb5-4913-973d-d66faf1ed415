package com.mogujie.detail.module.extra.provider;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.meili.service.shopcenter.domain.entity.ShopInfo;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.darling.daren.service.MoguStarDataService;
import com.mogujie.darwin.application.client.DarwinClient;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.adt.RouteInfo;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.SwitchKey;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.core.util.ContextUtil;
import com.mogujie.detail.core.util.DetailWebMetabase;
import com.mogujie.detail.core.util.ImageUtil;
import com.mogujie.detail.core.util.MaitUtil;
import com.mogujie.detail.core.util.MetabaseTool;
import com.mogujie.detail.core.util.NumUtil;
import com.mogujie.detail.core.util.TagUtil;
import com.mogujie.detail.module.extra.domain.BuyerInfo;
import com.mogujie.detail.module.extra.domain.DarenInfo;
import com.mogujie.detail.module.extra.domain.ExplainInfo;
import com.mogujie.detail.module.extra.domain.ExtraDO;
import com.mogujie.detail.module.extra.domain.FlashBuyInfo;
import com.mogujie.detail.module.extra.domain.HotSaleRankInfo;
import com.mogujie.detail.module.extra.domain.SupplierShopInfo;
import com.mogujie.detail.module.extra.domain.UserRecvAddressInfo;
import com.mogujie.detail.module.wrapper.PaganiSearchWrapper;
import com.mogujie.dts.api.query.Query;
import com.mogujie.dts.api.query.column.OrderByColumn;
import com.mogujie.dts.api.query.condition.Conditions;
import com.mogujie.dts.api.query.constant.Operator;
import com.mogujie.dts.api.result.QueryResult;
import com.mogujie.dts.api.result.RowResult;
import com.mogujie.dts.api.service.DtsQueryService;
import com.mogujie.dts.utils.TokenUtil;
import com.mogujie.market.base.model.ResultBase;
import com.mogujie.mars.api.domain.MarsResult;
import com.mogujie.mars.api.domain.entity.seller.ContactInfoDTO;
import com.mogujie.mars.api.seller.SellerInfoService;
import com.mogujie.metabase.spring.client.MetabaseClient;
import com.mogujie.service.item.api.ItemInitialSaleService;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.basic.ItemInitialSaleDO;
import com.mogujie.service.item.domain.basic.ItemPreSaleDO;
import com.mogujie.service.item.domain.basic.ItemTagDO;
import com.mogujie.service.item.domain.result.BaseResultDO;
import com.mogujie.service.muser.Result;
import com.mogujie.service.muser.api.UserService;
import com.mogujie.service.muser.domain.entity.User;
import com.mogujie.service.trade.service.logistics.address.v1.api.BuyerQueryService;
import com.mogujie.service.trade.service.logistics.address.v1.api.QueryService;
import com.mogujie.service.trade.service.logistics.address.v1.domain.dto.query.GetDefaultReceiverAddressByUserIdReqDto;
import com.mogujie.service.trade.service.logistics.address.v1.domain.dto.query.GetReceiverAddressByAddressIdReqDto;
import com.mogujie.service.trade.service.logistics.address.v1.domain.dto.query.TradeAddressDto;
import com.mogujie.service.trade.service.logistics.address.v1.domain.dto.query.TradeUserAddressResDto;
import com.mogujie.service.trade.service.logistics.express.v1.api.CalculateService;
import com.mogujie.service.trade.service.logistics.express.v1.domain.dto.calculate.CalculateSellerExpressDto;
import com.mogujie.service.trade.service.logistics.express.v1.domain.dto.calculate.CalculateStockExpressDto;
import com.mogujie.service.trade.service.logistics.express.v1.domain.dto.calculate.RequestCalculateExpressDto;
import com.mogujie.service.trade.service.logistics.express.v1.domain.dto.calculate.ResponseCreateOrderCalculateExpressDto;
import com.mogujie.service.waitress.platform.api.PlatformShopServiceService;
import com.mogujie.service.waitress.platform.common.ServiceDetailEnum;
import com.mogujie.service.waitress.platform.domain.ResultSupport;
import com.mogujie.service.waitress.platform.domain.entity.ShopServiceIcon;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import com.mogujie.themis.camp.config.CampTagConfigClient;
import com.mogujie.trade.response.Response;
import com.mogujie.trade.sales.api.dto.ItemSalesQueryDto;
import com.mogujie.trade.sales.api.dto.PreItemSalesQueryDto;
import com.mogujie.trade.sales.query.client.ItemSalesQueryClient;
import com.mogujie.trade.sales.query.client.PreItemSalesQueryClient;
import com.mogujie.trade.sales.query.domain.App;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.util.TextUtils;
import org.mogujie.cayenne.timeclient.TimeClient;
import org.mogujie.cayenne.timeclient.TimeClientFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Created by xiaoyao on 16/8/26.
 */
@Module(name = "extra")
public class ExtraDOProvider implements IModuleDOProvider<ExtraDO> {

    private static final Logger LOGGER = LoggerFactory.getLogger(ExtraDOProvider.class);

    private PlatformShopServiceService platformShopServiceService;

    private SellerInfoService sellerInfoService;

    @Autowired
    private ItemSalesQueryClient itemSalesQueryClient;

    @Autowired
    private PreItemSalesQueryClient preItemSalesQueryClient;

    // 计算邮费
    private CalculateService calculateService;
    // 发货地
    private QueryService queryService;

    @Autowired
    private CommonSwitchUtil commonSwitchUtil;

    private UserService userService;

    private MoguStarDataService moguStarDataService;

    private DtsQueryService dtsQueryService;

    @Autowired
    private ItemInitialSaleService itemInitialSaleService;

    @Autowired
    private BuyerQueryService buyerQueryService;

    @Autowired
    private PaganiSearchWrapper paganiSearchWrapper;

    @Resource(name = "confMetabaseClient")
    private MetabaseClient metabaseClient;

    /**
     * 招商闪购kv标字段名
     */
    private static final String FLASH_KV_MARK_KEY = "shango";

    /**
     * 比价商品数字标
     */
    private static final String COMPARE_PRICE_ITEM_TAG = "1589";

    /**
     * 比价banner配置的资源位id
     */
    private static final Long COMPARE_PRICE_BANNER_DEFINITION_ID = 152369L;

    private static Gson gson = new Gson();

    private static final String CROSS_STORE_DISCOUNT_KEY = "camp_temp_before_hour_discount";

    private static final String[] ZHIXIA = new String[]{"北京市", "天津市", "上海市", "重庆市"};

    private static final String SUPPLIER_COMMISION_KEY = "commission";

    private static final String SUPPLIER_STOCK_NUM_KEY = "supplyStockNum";

    private static final String SWT_SELLER_CONTACT_INFO = "supplier_seller_contact_info";

    private static final String DEFAULT_ADDRESS = "北京市";

    private TimeClient timeClient;

    @Override
    public void init() throws DetailException {
        try {
            platformShopServiceService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(PlatformShopServiceService.class);
            calculateService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(CalculateService.class);
            queryService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(QueryService.class);
            sellerInfoService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(SellerInfoService.class);
            timeClient = TimeClientFactory.client("cayenne");
            userService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(UserService.class);
            moguStarDataService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(MoguStarDataService.class);
            dtsQueryService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(DtsQueryService.class);
        } catch (Exception e) {
            LOGGER.error("init shopServiceService failed, {}", e);
        }
    }

    @Override
    public ExtraDO emit(DetailContext context) {
        DetailItemDO item = context.getItemDO();
        ExtraDO extraDO = new ExtraDO();
        if (null != item.getStarts()) {
            extraDO.setOnSaleTime(item.getStarts().getTime() / 1000);
        }
        ShipService shipService = getShipService(context);
        extraDO.setExpress(shipService.getExpress());
        extraDO.setFreePost(shipService.isFreePost());
        extraDO.setCrossStoreDiscount(getCrossStoreDiscount(context));
        extraDO.setModouDiscount(getModouDiscount(context));
        this.buildUserRecvAddress(context, extraDO);
        if (!shipService.isFreePost()) {
            extraDO.setPostPrice(getPostagePrice(context, extraDO));
            //邮费低于0,则是部分地区包邮
            if (extraDO.getPostPrice() != null && extraDO.getPostPrice() < 0) {
                extraDO.setFreePost(true);
                extraDO.setPostPrice(null);
            }
        }
        //设置直播供应链模板商品所需的内容
        decorateSupplierInfo(extraDO, context);
        RouteInfo routeInfo = context.getRouteInfo();
        if (routeInfo.getBizType() == BizType.SKU && routeInfo.getVersion().startsWith("live.")/* 直播sku接口 */) {
            //直播sku选择器中不需要销量数据，减少销量接口压力
            extraDO.setSales(0L);
        } else {
            extraDO.setSales(getSales(item));
        }
        extraDO.setDarenInfo(getDarenInfo(item));

        // 视频讲解数据
        extraDO.setExplainInfos(getLiveSecKillInfo(item));
        // 当前是否有主播设置成直播秒杀中
        extraDO.setLiveSeckill(ContextUtil.isLiveSecKilling(item));
        // 闪购信息。解析商品的shango kv标
        extraDO.setFlashBuyInfo(this.buildFlashBuyInfo(item));
        // 比价banner信息 or 热卖榜单信息
        extraDO.setHotSaleRankInfo(this.buildHotSaleRankInfo(item));
        extraDO.setPlatformAllowanceMsg(getPlatformAllowanceMsg(item));

        if (context.isDyn()) {
            return extraDO;
        }
        extraDO.setAddress(getAddress(item));
        extraDO.setXiaodianAdress(getXiaodianAppAddress(item));
        extraDO.setBuyerInfoList(getBuyerInfos(item));

        return extraDO;
    }

    /**
     * 获取商品平台补贴文案
     */
    private String getPlatformAllowanceMsg(DetailItemDO item) {
        String maitId = metabaseClient.get(SwitchKey.PLATFORM_ALLOWANCE_MAITID);
        if (StringUtils.isBlank(maitId)) {
            return null;
        }

        List<Map<String, Object>> maitData = MaitUtil.getMaitData(Long.parseLong(maitId));
        if (maitData == null || maitData.isEmpty()) {
            return null;
        }

        String msg = "";
        for (Map<String, Object> map : maitData) {
            if (map.getOrDefault("itemId", "").equals(IdConvertor.idToUrl(item.getItemId()))) {
                msg = String.valueOf(map.getOrDefault("picMsg", ""));
                break;
            }
        }

        return msg;
    }

    /**
     * 获取用户的收获地址
     *
     * @return
     */
    private void buildUserRecvAddress(DetailContext context, ExtraDO extraDO) {
        try {
            String recvAddressId = context.getParam("recvAddressId");
            if (StringUtils.isNotBlank(recvAddressId) && StringUtils.isNumeric(recvAddressId)) {
                this.buildForSelectAddress(context,recvAddressId, extraDO);
            }
            if (extraDO.getUserRecvAddressInfo() == null) {
                this.buildForDefaultAddress(context, extraDO);
            }
        } catch (Exception e) {
            LOGGER.error("buildTackDeliveryAddress Exception!itemId:{}", context.getItemId(), e);
        }
    }

    /**
     * 用户没有选择收获地址时构造默认的地址信息
     *
     * @param context 详情页上下文
     * @param extraDO 最终的保存对象
     */
    private void buildForDefaultAddress(DetailContext context, ExtraDO extraDO) {
        Long loginUserId = context.getLoginUserId();
        extraDO.setRecvAddress(removeCityeQua(DEFAULT_ADDRESS));
        extraDO.setUserRecvAddressInfo(new UserRecvAddressInfo(DEFAULT_ADDRESS));
        if (loginUserId == null) {
            return;
        }
        GetDefaultReceiverAddressByUserIdReqDto reqDto = new GetDefaultReceiverAddressByUserIdReqDto();
        reqDto.setUserId(loginUserId);
        com.mogujie.service.trade.service.logistics.base.v1.domain.dto.Response<TradeUserAddressResDto> addressResult =
                buyerQueryService.getDefaultReceiverAddressByUserId(reqDto);
        if (addressResult == null || addressResult.getData() == null) {
            return;
        }
        TradeUserAddressResDto tradeUserAddress = addressResult.getData();
        String province = tradeUserAddress.getProvince();
        String city = tradeUserAddress.getCity();
        if (StringUtils.isBlank(province) || StringUtils.isBlank(city)) {
            return;
        }
        String recvAddress = Arrays.asList(ZHIXIA).contains(province)
                ? removeCityeQua(city)
                : removeProvinceQua(removeProvinceQua(province) + "" + removeCityeQua(city));
        extraDO.setRecvAddress(recvAddress);
        UserRecvAddressInfo userRecvAddressInfo = UserRecvAddressInfo.convert(tradeUserAddress);
        extraDO.setUserRecvAddressInfo(userRecvAddressInfo);
    }


    /**
     * 根据用户选择的收获地址id收获地址时构造地址信息
     *
     * @param recvAddressId 收获地址id
     * @param extraDO       最终的保存对象
     */
    private void buildForSelectAddress(DetailContext context,String recvAddressId, ExtraDO extraDO) {
        Long loginUserId = context.getLoginUserId();
        if(loginUserId==null){
            return;
        }
        GetReceiverAddressByAddressIdReqDto dto = new GetReceiverAddressByAddressIdReqDto();
        dto.setAddressId(Long.valueOf(recvAddressId));
        dto.setUserId(loginUserId);
        com.mogujie.service.trade.service.logistics.base.v1.domain.dto.Response<TradeUserAddressResDto> addressResult = buyerQueryService.getReceiverAddressByAddressId(dto);
        if (addressResult == null || addressResult.getData() == null) {
            return;
        }
        TradeUserAddressResDto tradeUserAddressDto = addressResult.getData();
        String province = tradeUserAddressDto.getProvince();
        String city = tradeUserAddressDto.getCity();
        if (StringUtils.isBlank(province) || StringUtils.isBlank(city)) {
            return;
        }
        String recvAddress = Arrays.asList(ZHIXIA).contains(province)
                ? removeCityeQua(city)
                : removeProvinceQua(removeProvinceQua(province) + "" + removeCityeQua(city));
        extraDO.setRecvAddress(recvAddress);
        UserRecvAddressInfo userRecvAddressInfo = UserRecvAddressInfo.convert(tradeUserAddressDto);
        extraDO.setUserRecvAddressInfo(userRecvAddressInfo);
    }

    /**
     * 构造比价banner OR 热卖榜单信息
     * docs：https://mogu.feishu.cn/docs/doccnnKEnPc41VRclnMBYZ06hff#
     *
     * @param item 商品信息
     * @return
     */
    private HotSaleRankInfo buildHotSaleRankInfo(DetailItemDO item) {
        //https://mogu.feishu.cn/docs/doccnvx7jEAEwZ0RmwL1yHcKYdf#  小红书项目_商详女装设计款入口
        if (commonSwitchUtil.isOn("swt_nvzhuang_shejikuan")) {
            List<Map<String, Object>> maitData = MaitUtil.getMaitData(156933L);
            if (maitData != null && !maitData.isEmpty()) {
                Map<String, Object> map = maitData.get(0);
                List<String> tags = ContextUtil.getNumTags(item);
                if (tags != null && tags.contains(map.get("itemmark") + "")) {
                    return HotSaleRankInfo.convert(JSON.parseObject(JSON.toJSONString(map)));
                }
            }
        }
        HotSaleRankInfo hotSaleRankInfo = this.buildComparePriceBanner(item);
        if (hotSaleRankInfo != null) {
            return hotSaleRankInfo;
        }
        long itemId = item.getItemId();
        if (!commonSwitchUtil.isOn(SwitchKey.SWITCH_HOT_SALE_RANK_LIST)) {
            return null;
        }
        JSONObject rankListBanner = paganiSearchWrapper.getRankListBanner(itemId);
        if (MapUtils.isEmpty(rankListBanner)) {
            return null;
        }
        return HotSaleRankInfo.convert(rankListBanner);
    }

    /**
     * 构造比价氛围，和热销榜单公用一个客户端字段。
     *
     * @param item 商品信息
     * @return
     */
    private HotSaleRankInfo buildComparePriceBanner(DetailItemDO item) {
        List<ItemTagDO> itemTags = item.getItemTags();
        if (CollectionUtils.isEmpty(itemTags)) {
            return null;
        }
        Optional<ItemTagDO> optionalTagDO = itemTags.stream()
                .filter(itemTagDO -> itemTagDO != null
                        && "tags".equals(itemTagDO.getTagKey())
                        && COMPARE_PRICE_ITEM_TAG.equals(itemTagDO.getTagValue()))
                .findFirst();
        if (!optionalTagDO.isPresent()) {
            return null;
        }
        ResultBase<List<Map<String, Object>>> data = DarwinClient.getData(COMPARE_PRICE_BANNER_DEFINITION_ID);
        if (data == null || !data.isSuccess() || CollectionUtils.isEmpty(data.getValue())) {
            return null;
        }
        Map<String, Object> bannerConfig = data.getValue().get(0);
        String bgImage = MapUtils.getString(bannerConfig, "bgImage");
        String link = MapUtils.getString(bannerConfig, "link");
        if (StringUtils.isBlank(bgImage) || StringUtils.isBlank(link)) {
            return null;
        }
        return new HotSaleRankInfo(bgImage, link);
    }

    /**
     * 构造闪购信息
     *
     * @param item 商品原始数据
     * @return
     */
    private FlashBuyInfo buildFlashBuyInfo(DetailItemDO item) {
        List<ItemTagDO> itemTags = item.getItemTags();
        if (CollectionUtils.isEmpty(itemTags)) {
            return null;
        }
        Optional<ItemTagDO> optionalTagDO = itemTags.stream().filter(itemTagDO -> itemTagDO != null && FLASH_KV_MARK_KEY.equals(itemTagDO.getTagKey())).findFirst();
        if (!optionalTagDO.isPresent()) {
            return null;
        }
        String tagValue = optionalTagDO.get().getTagValue();
        if (StringUtils.isBlank(tagValue)) {
            return null;
        }
        FlashBuyInfo flashBuyInfo = FlashBuyInfo.convert(tagValue);
        return flashBuyInfo;
    }


    // 视频讲解数据
    private List<ExplainInfo> getLiveSecKillInfo(DetailItemDO itemDO) {
        String explainStr = null;
        try {
            String explainKey = "explain";
            Map<String, String> features = itemDO.getFeatures();
            if (features == null) {
                return null;
            }
            explainStr = features.get(explainKey);
            if (!TextUtils.isEmpty(explainStr)) {
                List<ExplainInfo> explainInfos = gson.fromJson(explainStr, new TypeToken<List<ExplainInfo>>() {
                }.getType());
                if (explainInfos != null && !explainInfos.isEmpty()) {
                    return explainInfos;
                }
            }
        } catch (Throwable e) {
            String errorMsg = String.format("parse explain from features(%s) error! itemId: {}.", String.valueOf(explainStr));
            LOGGER.error(errorMsg, itemDO.getItemId(), e);
        }
        return null;
    }

    private DarenInfo getDarenInfo(DetailItemDO item) {
        try {
            Long maitId = 138701L;
            List<Map<String, Object>> list = MaitUtil.getMaitData(maitId);
            Map<String, Object> foundMait = null;
            List<Long> numTags = getNumTags(item.getItemTags());
            if (numTags != null && list != null) {
                for (Map<String, Object> maitData : list) {
                    long id = Long.parseLong(String.valueOf(maitData.get("tag")));
                    for (Long tag : numTags) {
                        if (tag == id) {
                            foundMait = maitData;
                            break;
                        }
                    }
                    if (foundMait != null) {
                        break;
                    }
                }
                if (foundMait != null) {
                    long darenId = Long.parseLong(String.valueOf(foundMait.get("userid")));
                    // 查询达人用户数据和种草力
                    DarenInfo darenInfo = new DarenInfo();
                    Result<User> userResult = userService.getUserById(darenId);
                    User user = userResult.getValue();
                    darenInfo.setId(darenId);
                    darenInfo.setAvatar(ImageUtil.img(user.getAvatar()));
                    darenInfo.setName(user.getUname());
                    ResultBase<Map<Long, Long>> resultBase = moguStarDataService.queryGrassPointByUserIds(Collections.singletonList(darenId));
                    darenInfo.setPower(resultBase.getValue().get(darenId));
                    return darenInfo;
                }
            }
        } catch (Throwable e) {
            LOGGER.error("get daren count error! itemId: {}.", item.getItemId(), e);
        }
        return null;
    }

    private List<Long> getNumTags(List<ItemTagDO> itemTagList) {
        if (itemTagList == null || itemTagList.size() == 0) {
            return null;
        }
        List<Long> numTags = new ArrayList<>();
        for (ItemTagDO tag : itemTagList) {
            if ("tags".equals(tag.getTagKey())) {
                try {
                    numTags.add(Long.parseLong(tag.getTagValue()));
                } catch (Throwable ignore) {
                }
            }
        }
        return numTags;
    }

    /**
     * 获取商品销量核心逻辑
     *
     * @param item
     * @return
     */
    private Long getSales(DetailItemDO item) {
        try {
            /******************
             * 普通商品销量逻辑 *
             ******************/
            App appInfo = new App("appdetail-mgj", "xiaoyao");
            // 预售商品，且在预售期间
            if (item.getItemPreSaleDO() != null &&
                    (int) (System.currentTimeMillis() / 1000) < item.getItemPreSaleDO().getEnd()) {
                PreItemSalesQueryDto preItemSalesQueryDto = new PreItemSalesQueryDto();
                preItemSalesQueryDto.setItemId(item.getXdItemId());
                Response<Long> ret = preItemSalesQueryClient.queryPreItemSalesById(preItemSalesQueryDto, appInfo);
                if (null != ret && ret.isSuccess()) {
                    return ret.getData();
                }
            } else {
                ItemSalesQueryDto itemSalesQueryDto = new ItemSalesQueryDto();
                itemSalesQueryDto.setItemId(item.getXdItemId());
                itemSalesQueryDto.setSellerUserId(item.getUserId());
                itemSalesQueryDto.setRecentCreateDays(-1);
                itemSalesQueryDto.setHasPaid(true);
                Response<Long> ret = itemSalesQueryClient.queryItemSalesById(itemSalesQueryDto, appInfo);
                if (null != ret && ret.isSuccess() && ret.getData() > 0) {
                    return ret.getData();
                }

                // 若销量为0，并且商品创建时间小于3天，查询初始销量
                Integer nowTime = (int) (timeClient.fake() / 1000);
                if ((item.getGmtCreate().getTime() / 1000) > (nowTime - 3 * 24 * 60 * 60)) {
                    BaseResultDO<ItemInitialSaleDO> result = itemInitialSaleService.queryItemInitialSaleByItemId(item.getItemId());
                    if (null != result && result.isSuccess() && result.getResult() != null) {
                        return result.getResult().getSale() == null ? 0L : result.getResult().getSale();
                    }
                }

            }
        } catch (Throwable e) {
            LOGGER.error("get item sales count error! itemId: {}.", item.getItemId(), e);
        }
        return 0L;
    }

    private String getXiaodianAppAddress(DetailItemDO item) {
        String address = item.getAddress();
        if (!StringUtils.isEmpty(address)) {
            for (String city : ZHIXIA) {
                if (address.contains(city)) {
                    address = city;
                    break;
                }
            }
            return address;
        }
        return null;
    }

    private String getAddress(DetailItemDO item) {
        if (!commonSwitchUtil.isOn(SwitchKey.SHOW_ADDRESS)) {
            return null;
        }
        String address = null;
        try {
            com.mogujie.service.trade.service.logistics.base.v1.domain.dto.Response<TradeAddressDto> response = queryService.getDefaultShipAddressByUserId(item.getUserId());
            if (response != null && response.isSuccess()) {
                TradeAddressDto tradeAddressDto = response.getData();
                if (tradeAddressDto != null) {
                    if (Arrays.asList(ZHIXIA).contains(tradeAddressDto.getCity()) || Arrays.asList("吉林市").contains(tradeAddressDto.getCity())) {
                        address = removeCityeQua(tradeAddressDto.getCity());
                    } else {
                        address = removeProvinceQua(tradeAddressDto.getProvince()) + "" + removeCityeQua(tradeAddressDto.getCity());
                    }
                }
            }
        } catch (Throwable e) {
            LOGGER.error("get send address error.", e);
        }
        return address;
    }

    private String removeCityeQua(String city) {
        if (StringUtils.isEmpty(city)) {
            return city;
        }
        if (!DetailWebMetabase.getBoolean("province_city_remove_qua_switch", false)) {
            return city;
        }
        if (city.endsWith("市")) {
            return city.substring(0, city.length() - 1);
        } else {
            return city;
        }
    }

    private String removeProvinceQua(String province) {
        if (StringUtils.isEmpty(province)) {
            return province;
        }
        if (!DetailWebMetabase.getBoolean("province_city_remove_qua_switch", false)) {
            return province;
        }
        if (province.endsWith("省")) {
            return province.substring(0, province.length() - 1);
        } else {
            return province;
        }
    }


    private ShipService getShipService(DetailContext context) {


        String fastbuyIdStr = context.getParam("fastbuyId");
        ItemDO item = context.getItemDO();
        if (TagUtil.isFlItem(context.getItemDO().getJsonExtra())) {
            ShipService shipService = new ShipService();
            shipService.setExpress("默认快递");
            return shipService;
        }

        if (!StringUtils.isEmpty(fastbuyIdStr) || context.getItemDO().getPostageId() == 100 || context.getRouteInfo().getBizType() == BizType.SECKILL) {
            ShipService shipService = new ShipService();
            shipService.setFreePost(true);
            shipService.setExpress("全国包邮");
            return shipService;
        }

        if (ContextUtil.isMedicalItem(context)) {
            ShipService shipService = new ShipService();
            shipService.setFreePost(true);
            shipService.setExpress("全国包邮");
            return shipService;
        }

        if (context.getRouteInfo().getBizType() == BizType.TREASURE) {
            ShipService shipService = new ShipService();
            shipService.setFreePost(true);
            shipService.setExpress("全国包邮");
            return shipService;
        }

        if (context.getItemDO().getPostageId() == 110) {
            ShipService shipService = new ShipService();
            shipService.setFreePost(true);
            shipService.setExpress("包邮包税");
            return shipService;
        }

        if (!commonSwitchUtil.isOn(SwitchKey.WAITRESS_SHIP_SERVICE)) {
            ShipService shipService = new ShipService();
            shipService.setExpress("默认快递");
            return shipService;
        }

        try {
            ResultSupport<List<ShopServiceIcon>> services = platformShopServiceService.getShopSubscribedService(item.getShopId(), 0);
            if (null != services && services.isSuccess() && !CollectionUtils.isEmpty(services.getModule())) {
                List<ShopServiceIcon> serviceIcons = services.getModule();
                for (ShopServiceIcon serviceIcon : serviceIcons) {
                    if (ServiceDetailEnum.BY_310.getDetailId() == serviceIcon.getServiceDetailId()) {
                        ShipService shipService = new ShipService();
                        shipService.setFreePost(true);
                        shipService.setExpress("全国包邮");
                        return shipService;
                    } else if (ServiceDetailEnum.BY_320.getDetailId() == serviceIcon.getServiceDetailId()) {
                        ShipService shipService = new ShipService();
                        shipService.setFreePost(true);
                        shipService.setExpress("包邮包税");
                        return shipService;
                    }
                }
            }
        } catch (Throwable e) {
            LOGGER.error("get service from waitress failed : {}, {}", item.getShopId(), e);
        }

        ShipService shipService = new ShipService();
        shipService.setExpress("默认快递");
        return shipService;
    }

    /**
     * 根据用户ip所在地,实时估算邮费
     *
     * @param context
     * @return
     */
    public Integer getPostagePrice(DetailContext context, ExtraDO extraDO) {
        DetailItemDO item = context.getItemDO();
        if (TagUtil.isFlItem(context.getItemDO().getJsonExtra())) {
            return null;
        }
        UserRecvAddressInfo userRecvAddressInfo = extraDO.getUserRecvAddressInfo();
        if (userRecvAddressInfo == null) {
            return null;
        }
        String province = userRecvAddressInfo.getProvince();
        String city = userRecvAddressInfo.getCity();
        String area = userRecvAddressInfo.getArea();
        //计算运费
        if (commonSwitchUtil.isOn(SwitchKey.CAL_POSTAGE_FEE) && StringUtils.isNotBlank(context.getClientIp()) && item.getPostageId() != 0 && item.getPostageId() != 100 && item.getPostageId() != 110) {
            try {
                // 获取用户ip所在地 0.国家 1.省份 2.城市
                // IPDescriptionDO ipDescription = IPLibraryUtil.getIpDescription(context.getClientIp());
                if (StringUtils.isNotEmpty(province)) {
                    // 设置计价的商品、sku信息
                    CalculateStockExpressDto calculateStockExpressDto = new CalculateStockExpressDto();
                    calculateStockExpressDto.setTradeItemId(item.getItemId());
                    calculateStockExpressDto.setStockId(item.getItemSkuDOList().get(0).getSkuId());
                    calculateStockExpressDto.setTplId(item.getPostageId());
                    calculateStockExpressDto.setNumber(1);
                    calculateStockExpressDto.setVolume(item.getItemSkuDOList().get(0).getVolume());
                    calculateStockExpressDto.setWeight(item.getItemSkuDOList().get(0).getWeight());
                    List<CalculateStockExpressDto> calculateStockExpressDtoList = new ArrayList<>();
                    calculateStockExpressDtoList.add(calculateStockExpressDto);
                    // 设置店铺信息
                    CalculateSellerExpressDto calculateSellerExpressDto = new CalculateSellerExpressDto();
                    calculateSellerExpressDto.setSellerUserId(item.getUserId());
                    calculateSellerExpressDto.setSplitGroupKey("googs-detail-" + item.getUserId());
                    calculateSellerExpressDto.setExpressCode("defaultlogistics");
                    calculateSellerExpressDto.setCalculateStockExpressDtoList(calculateStockExpressDtoList);
                    List<CalculateSellerExpressDto> calculateSellerExpressDtoList = new ArrayList<>();
                    calculateSellerExpressDtoList.add(calculateSellerExpressDto);
                    // 设置买家地址
                    RequestCalculateExpressDto expressDto = new RequestCalculateExpressDto();
                    expressDto.setProvince(province);
                    expressDto.setCity(StringUtils.isBlank(city) ? province : city);
                    expressDto.setArea(StringUtils.isBlank(area) ? province : area);
                    expressDto.setCalculateSellerExpressDtoList(calculateSellerExpressDtoList);

                    com.mogujie.service.trade.service.logistics.base.v1.domain.dto.Response<Map<String, ResponseCreateOrderCalculateExpressDto>> expressResponse = calculateService.calculateOrderFreightByStocksAndAddressInfo(expressDto);
                    if (expressResponse != null && expressResponse.isSuccess() && expressResponse.getData() != null) {
                        ResponseCreateOrderCalculateExpressDto expressPriceDto = expressResponse.getData().get("googs-detail-" + item.getUserId());
                        if (expressPriceDto == null) {
                            return null;
                        }
                        //部分地区包邮
                        if (expressPriceDto.getIsFreePostage() != null && expressPriceDto.getIsFreePostage() == 1) {
                            return -1;
                        }
                        if (expressPriceDto.getPrice() != null) {
                            return expressPriceDto.getPrice();
                        }
                    }
                }
            } catch (Throwable e) {
                LOGGER.error("get post fee error.", e);
            }
        }
        return null;
    }

    private String getCrossStoreDiscount(DetailContext context) {
        try {
            String discountValue = getCrossStoreDiscountValue(context.getItemDO());
            if (!StringUtils.isEmpty(discountValue)) {
                Map<String, Object> data = CampTagConfigClient.getCampConfig(ContextUtil.getNumTagString(context.getItemDO()), null, CROSS_STORE_DISCOUNT_KEY);
                if (null != data && !data.isEmpty()) {
                    Integer startTime = (Integer) data.get("startTime");
                    Integer endTime = (Integer) data.get("endTime");
                    Integer nowTime = (int) (timeClient.fake() / 1000);
                    if (nowTime < startTime || nowTime > endTime) {
                        return null;
                    }
                    String bannerText = CampTagConfigClient.objToString(data.get("detailBannerText"));
                    if (!StringUtils.isEmpty(bannerText)) {
                        return bannerText + discountValue + "元";
                    }
                }
            }
        } catch (Throwable e) {
            LOGGER.error("get cross store discount failed : {}", e);
        }
        return null;
    }

    protected String getCrossStoreDiscountValue(DetailItemDO itemDO) {
        List<ItemTagDO> itemTags = itemDO.getItemTags();
        if (CollectionUtils.isEmpty(itemTags)) {
            return null;
        }
        for (ItemTagDO tag : itemTags) {
            if (tag.getTagKey().equals("xsyh")) {
                String[] tagPairs = tag.getTagValue().split("\\|");
                for (String tagPair : tagPairs) {
                    String[] pair = tagPair.split(":");
                    if (pair.length == 2 && "cp".equals(pair[0]) && StringUtils.isNumeric(pair[1])) {
                        return NumUtil.formatNum(Integer.parseInt(pair[1]) / 100, 0);
                    }
                }
            }
        }
        return null;
    }

    protected String getModouDiscount(DetailContext context) {
        if (!context.getRouteInfo().getBizType().equals(BizType.NORMAL) || isPresale(context.getItemDO()) || isVirtualItem(context.getItemDO().getCids())) {
            return null;
        }
        if (commonSwitchUtil.isOn(SwitchKey.SWT_SHOW_MODOU_DISCOUNT)) {
            DetailItemDO itemDO = context.getItemDO();
            List<ItemTagDO> itemTags = itemDO.getItemTags();
            if (CollectionUtils.isEmpty(itemTags)) {
                return null;
            }
            for (ItemTagDO tag : itemTags) {
                if (tag.getTagKey().equals("mgd")) {
                    String[] tagPairs = tag.getTagValue().split(";");
                    for (String tagPair : tagPairs) {
                        String[] pair = tagPair.split(":");
                        if (pair.length == 2 && "pc".equals(pair[0])) {
                            return pair[1];
                        }
                    }
                }
            }
        }
        return null;
    }

    private Boolean isPresale(ItemDO item) {
        int nowTime = (int) (System.currentTimeMillis() / 1000);
        if (null != item.getItemPreSaleDO()) {
            ItemPreSaleDO preSale = item.getItemPreSaleDO();
            if (preSale.getStart() < nowTime && nowTime < preSale.getEnd()) {
                return true;
            }
        }
        return false;
    }

    private Boolean isVirtualItem(String cids) {
        if (StringUtils.isEmpty(cids)) {
            return false;
        }
        String virtualCateRoot = "#" + metabaseClient.get("virtual_category_root") + "#";
        return cids.contains(virtualCateRoot);
    }

    /**
     * 填入直播供应链模板商品相关的数据：
     * 供货量、佣金、供货商联系方式等
     *
     * @param context
     */
    private void decorateSupplierInfo(ExtraDO extraDO, DetailContext context) {
        DetailItemDO item = context.getItemDO();
        try {
            if (!ContextUtil.isLiveSupplyTemplateItem(item)) {
                return;
            }
            Map<String, String> features = item.getFeatures();
            if (item.getFeatures() != null && features.get(SUPPLIER_COMMISION_KEY) != null) {
                extraDO.setSupplierCommission(NumUtil.formatPriceDrawer((int) (Double.parseDouble(features.get(SUPPLIER_COMMISION_KEY)) * 100)));
            }
            if (item.getFeatures() != null && features.get(SUPPLIER_STOCK_NUM_KEY) != null) {
                extraDO.setSupplyStockNum(Long.parseLong(features.get(SUPPLIER_STOCK_NUM_KEY)));
            }

            ShopInfo shopInfo = item.getShopInfo();
            if (MetabaseTool.isOn(SWT_SELLER_CONTACT_INFO, true)
                    && shopInfo != null) {
                MarsResult<ContactInfoDTO> marsResult = sellerInfoService.getSellerContactInfo(shopInfo.getUserId());
                if (marsResult != null && marsResult.isSuccess() && marsResult.getResult() != null) {
                    ContactInfoDTO contactInfo = marsResult.getResult();
                    SupplierShopInfo supplierShopInfo = new SupplierShopInfo(shopInfo.getName(), contactInfo.getContactQQ(), contactInfo.getContactWeixin(), contactInfo.getContactCellphone());
                    extraDO.setSupplierShopInfo(supplierShopInfo);
                }
            }
        } catch (Throwable e) {
            LOGGER.error("set supplier contact info error!", e);
        }
    }


    private List<BuyerInfo> getBuyerInfos(DetailItemDO detailItemDO) {
        List<BuyerInfo> buyerInfos = new LinkedList<>();
        String cids = metabaseClient.get(SwitchKey.SWT_HIDE_BUY_USE_CIDS);
        if (StringUtils.isNotBlank(cids)) {
            Optional<String> optional = Arrays.stream(cids.split(","))
                    .filter(cid -> detailItemDO.getCids().contains(cid)).findAny();
            if (optional.isPresent()) {
                return buyerInfos;
            }
        }

        try {
            String visit_date_begin = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now().minusDays(3));
            String visit_date_end = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now().minusDays(1));

            Query query = new Query();
            query.addColumn("0197_tradeitemid");
            query.addColumn("0197_visit_date");
            query.addColumn("0197_userids");
            query.addColumn("0197_shopid");
            query.addColumn("0197_sellerid");
            query.setCondition(
                    Conditions.and(
                            Conditions.condition("visit_date", Operator.GE, visit_date_begin),
                            Conditions.condition("visit_date", Operator.LE, visit_date_end),
                            Conditions.condition("tradeitemid", Operator.EQ, detailItemDO.getItemId())
                    )
            );

            query.setOrderBy(OrderByColumn.desc("0197_visit_date"));
            String token = TokenUtil.produce("detail", "detail_dts");
            QueryResult queryResult = dtsQueryService.query(query, "detail", token);
            if (queryResult != null && !CollectionUtils.isEmpty(queryResult.getRowResults())) {
                for (RowResult rowResult : queryResult.getRowResults()) {
                    String userResult = rowResult.get("0197_userids").toString();
                    JSONArray userArrays = JSON.parseArray(userResult);
                    if (userArrays != null && userArrays.size() >= 3) {
                        for (int i = 0; i < userArrays.size(); ++i) {
                            JSONObject jsonObject = userArrays.getJSONObject(i);
                            String desc = jsonObject.getOrDefault("uname", "") + "下单成功";
                            String logo = ImageUtil.img(jsonObject.getOrDefault("avatar", "").toString());
                            buyerInfos.add(new BuyerInfo(logo, desc));
                        }
                        break;
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("获取下单信息失败, {}", e.getMessage(), e);
        }
        return buyerInfos;
    }

    class ShipService {

        /**
         * 是否包邮
         */
        private boolean isFreePost;

        /**
         * express
         *
         * @return
         */
        private String express;

        public String getExpress() {
            return express;
        }

        public void setExpress(String express) {
            this.express = express;
        }

        public boolean isFreePost() {
            return isFreePost;
        }

        public void setFreePost(boolean freePost) {
            isFreePost = freePost;
        }
    }
}
