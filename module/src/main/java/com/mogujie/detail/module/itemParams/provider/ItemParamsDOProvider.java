package com.mogujie.detail.module.itemParams.provider;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.MetabaseTool;
import com.mogujie.detail.core.util.TagUtil;
import com.mogujie.detail.module.itemParams.domain.ItemParamsDO;
import com.mogujie.detail.module.itemParams.domain.ProductInfo;
import com.mogujie.detail.module.itemParams.domain.Rule;
import com.mogujie.detail.module.itemParams.util.CommonUtil;
import com.mogujie.detail.module.itemParams.util.DetailUtil;
import com.mogujie.detail.module.itemParams.util.SizeUtil;
import com.mogujie.service.item.api.basic.ItemReadService;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.basic.ItemDetailDO;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.service.item.domain.external.IcException;
import com.mogujie.service.item.domain.query.QueryItemOptions;
import com.mogujie.service.item.domain.result.BaseResultDO;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.util.*;

/**
 * Created by xiaoyao on 16/8/16.
 */
@Module(name = "itemParams")
public class ItemParamsDOProvider implements IModuleDOProvider<ItemParamsDO> {

    @Autowired
    ItemReadService itemReadService;
    private static final Logger logger = LoggerFactory.getLogger(ItemParamsDOProvider.class);


    @Override
    public void init() throws DetailException {

    }

    @Override
    public ItemParamsDO emit(DetailContext context) {
        if (context.isDyn()) {
            return null;
        }
        ItemParamsDO itemParamsDO = new ItemParamsDO();
        DetailItemDO item = context.getItemDO();
        ProductInfo productInfo = this.buildProductInfo(item);
        Rule rule = this.buildSizeInfo(item);

        if (productInfo == null && rule == null) {
            return null;
        }

        itemParamsDO.setInfo(productInfo);
        itemParamsDO.setRule(rule);
        return itemParamsDO;
    }

    /**
     * 产品参数
     *
     * @param item
     * @return
     */
    ProductInfo buildProductInfo(DetailItemDO item) {
        ItemDetailDO itemDetail = item.getItemDetailDO();
        String detail = itemDetail == null ? null : itemDetail.getDetail();
        ProductInfo productInfo = DetailUtil.getBaseProductInfo(detail);

        Map<String, String> propertyMap = new HashMap<>(item.getProperties());
        //region 影子商品资质属性取主商品的
        try {
            if (MetabaseTool.isOn("shadow_cert_property", false) && TagUtil.isContainsTag(item.getItemTags(), 800)) {
                dealWithShadowCertProperty(item, propertyMap);
            }
        } catch (Exception ex) {
            logger.error("影子商品获取主商品资质属性失败", ex);
        }
        //endregion
        if (!Boolean.parseBoolean(MetabaseTool.getValue("detail_factory_is_can_show")) && !canShowFactoryInfo(item)) {
            for (String propertyKey : item.getProperties().keySet()) {
                if (propertyKey.equals("厂名") || propertyKey.equals("厂址")) {
                    propertyMap.remove(propertyKey);
                }
            }
        }

        //对时间做处理
        String produceDate = propertyMap.get("生产日期");
        if(StringUtils.isNotBlank(produceDate)){
            try {
                produceDate = CommonUtil.formatDate(CommonUtil.parse(produceDate), "yyyy年MM月dd日");
                propertyMap.put("生产日期", produceDate);
            } catch (ParseException e) {

            }

        }
        //商品发布改造把品牌id放到properties,需要删除掉,pc详情页会根据item中的brandId自己去查询（只有pc会用到品牌）
        propertyMap.remove("brandId");
        //region 根据类目隐藏属性
        if (MetabaseTool.isOn("detail_hide_property_enable", false)) {
            JSONObject hideConfig = JSON.parseObject(MetabaseTool.getValue("detail_hide_property"));
            for (String cid : hideConfig.keySet()) {
                if (item.getCids().contains("#" + cid + "#")) {
                    JSONArray propersHide = hideConfig.getJSONArray(cid);
                    for (Object o : propersHide) {
                        propertyMap.remove(o.toString());
                    }
                }
            }
        }
        //endregion

        productInfo.setSet(propertyMap);

        if (productInfo.isEmpty()) {
            return null;
        }

        return productInfo;
    }

    private void dealWithShadowCertProperty(DetailItemDO item,Map<String,String> propertyMap) throws IcException {
        if (item.getFeatures() == null) {
            return;
        }
        String path = item.getCids();
        List<JSONObject> configs = JSON.parseArray(MetabaseTool.getValue("cert_cid_config"), JSONObject.class);
        JSONObject match = null;
        outer:
        for (JSONObject config : configs) {
            JSONArray cids = config.getJSONArray("cids");
            for (Object cid : cids) {
                if (path.contains("#" + cid + "#")) {
                    match = config;
                    break outer;
                }
            }
        }
        if (match != null) {
            String templateItemId = item.getFeatures().get("templateItemId");
            if (templateItemId != null && !templateItemId.isEmpty()) {
                long mainItemId = Long.parseLong(templateItemId);
                QueryItemOptions queryItemOptions = new QueryItemOptions();
                queryItemOptions.setQueryItemDetail(false);
                queryItemOptions.setQueryItemTag(false);
                queryItemOptions.setQuerySkuInfo(false);
                BaseResultDO<ItemDO> itemDOBaseResultDO = itemReadService.queryItemById(mainItemId, queryItemOptions);
                if (itemDOBaseResultDO.isSuccess()) {
                    ItemDO mainItemDO = itemDOBaseResultDO.getResult();
                    if (mainItemDO != null) {
                        Map<String, String> mainItemDOProperties = mainItemDO.getProperties();
                        JSONArray properFields = match.getJSONArray("properFields");
                        for (Object properField : properFields) {
                            String field = mainItemDOProperties.get(properField.toString());
                            if (field != null) {
                                propertyMap.put(properField.toString(), field);
                            }
                        }
                    }
                }
            }
        }

    }

    private boolean canShowFactoryInfo(ItemDO item) {
        String cids = MetabaseTool.getValue("detail_factory_is_can_show_cid");
        String [] cidArr = cids.split(",");
        for (String cid : cidArr) {
            if (item.getCids().contains("#" + cid + "#")) {
                return true;
            }
        }
        return false;

    }

    /**
     * 尺码说明
     *
     * @param item
     * @return
     */
    Rule buildSizeInfo(ItemDO item) {
        ItemDetailDO itemDetail = item.getItemDetailDO();
        String detail = itemDetail == null ? null : itemDetail.getDetail();
        Rule rule = DetailUtil.getBaseRule(detail, item.getCids());

        List<List<List<String>>> sizeTable = this.getPcSizeTable(item);
        rule.setTables(sizeTable);

        if (rule.isEmpty()) {
            return null;
        }

        return rule;
    }

    /**
     * 尺码说明
     *
     * @param item
     * @return
     */
    private List<List<List<String>>> getAppSizeTable(ItemDO item) {
        List<ItemSkuDO> skuList = item.getItemSkuDOList();
        if (CollectionUtils.isEmpty(skuList)) {
            return Collections.emptyList();
        }

        // sku属性的key值  如： {'脚长', '脚宽', '筒高'}
        Set<String> attrKeys = new HashSet<>();
        Map<String, Map<String, String>> sizeTable = SizeUtil.buildStockAttrs(item, attrKeys);


        if (CollectionUtils.isEmpty(sizeTable)) {
            return Collections.emptyList();
        }

        return getAppSizeInfo(sizeTable, attrKeys);
    }

    /**
     * 将获取的尺码说明信息封装为前端需要的结构
     * @param sizeTable  { 35: {脚长: 245, 脚宽: 95}, 36: {脚长: 248, 脚宽: 99} }
     * @param attrKeys  {脚长, 脚宽}
     * @return  [['尺码', '35', '36'], ['脚长', '245', '248'], ['脚宽', '95', '99']]
     */
    private List<List<List<String>>> getAppSizeInfo(Map<String, Map<String, String>> sizeTable,
                                                    Set<String> attrKeys) {
        List<List<List<String>>> result = new ArrayList<>();

        Set<String> keys = sizeTable.keySet();
        // 将keys按每4个进行分割
        List<List<String>> sizeValues = CommonUtil.splitValueIntoCells(keys);

        for (List<String> titles : sizeValues) {
            List<List<String>> sizeInfo = new ArrayList<>();
            // get  ['尺码', 's', 'l']
            List<String> sizeTitle = new ArrayList<>(5);
            sizeTitle.add(Rule.SIZE_TABLE_NAME);
            sizeTitle.addAll(titles);
            sizeInfo.add(sizeTitle);


            for (String skuAttrName : attrKeys) {
                List<String> sizeCell = new ArrayList<>(5);
                sizeCell.add(skuAttrName);
                for (String title : titles) {
                    String skuAttrValue = sizeTable.get(title).get(skuAttrName);
                    if (StringUtils.isBlank(skuAttrValue)) {
                        skuAttrValue = "";
                    }
                    sizeCell.add(skuAttrValue);
                }
                sizeInfo.add(sizeCell);
            }

            result.add(sizeInfo);
        }

        return result;
    }

    private List<List<List<String>>> getPcSizeTable(ItemDO item) {
        List<ItemSkuDO> skuList = item.getItemSkuDOList();
        if (CollectionUtils.isEmpty(skuList)) {
            return Collections.emptyList();
        }

        // sku属性的key值  如： {'脚长', '脚宽', '筒高'}
        Set<String> attrKeys = new LinkedHashSet<>();
        Map<String, Map<String, String>> sizeTable = SizeUtil.buildStockAttrs(item, attrKeys);
        if (CollectionUtils.isEmpty(sizeTable)) {
            return Collections.emptyList();
        }

        return getPcSizeInfo(sizeTable, attrKeys);
    }




    /**
     * 将获取的尺码说明信息封装为前端需要的结构
     * @param sizeTable  { 35: {脚长: 245, 脚宽: 95}, 36: {脚长: 248, 脚宽: 99} }
     * @param attrKeys  {脚长, 脚宽}
     * @return  [['尺码', '脚长', '脚宽'], ['35', '245', '95'], ['248', '24', '98']]
     */
    private List<List<List<String>>> getPcSizeInfo(Map<String, Map<String, String>> sizeTable,
                                                   Set<String> attrKeys) {
        List<List<String>> result = new ArrayList<>();

        // 获取尺码说明第一列,即各属性的value
        int size = attrKeys.size() + 1;
        List<String> keys = new ArrayList<>(size);
        keys.addAll(attrKeys);

        for (Map.Entry<String, Map<String, String>> entry: sizeTable.entrySet()) {
            List<String> cell = new ArrayList<>();
            cell.add(entry.getKey());
            for (String key: keys) {
                String value = entry.getValue().get(key);
                cell.add(StringUtils.isBlank(value) ? "" : value); // 为空的时候用空字符串代替
            }
            result.add(cell);
        }

        keys.add(0, Rule.SIZE_TABLE_NAME);
        result.add(0, keys);
        return Collections.singletonList(result);
    }
}
