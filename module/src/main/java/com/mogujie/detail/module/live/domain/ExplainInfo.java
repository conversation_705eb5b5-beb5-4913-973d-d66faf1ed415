package com.mogujie.detail.module.live.domain;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @AUTUOR: lengshan
 * @DESCRIPTION: 视频讲解
 * @DATE: 2019/9/24 下午6:21
 */
@Getter
@Setter
public class ExplainInfo {

    /**
     * 主播userId
     */
    private String actUserId;

    /**
     * 主播名
     */
    private String actUserName;

    /**
     * 主播头像
     */
    private String avatar;

    /**
     * 主播身高（cm）
     */
    private int actHeight;

    /**
     * 主播体重（kg）
     */
    private int actWeight;

    /**
     * 主播所在地
     */
    private String city;

    /**
     * 主播标签
     */
    private List<TagInfo> tags;

    /**
     * 视频讲解ID
     */
    private long videoId;

    /**
     * 切片信息
     */
    private String slice;

    /**
     * 主播粉丝数
     */
    private Integer fansNum;

    /**
     * 影子商品ID(url形式)
     */
    private String itemId;
}
