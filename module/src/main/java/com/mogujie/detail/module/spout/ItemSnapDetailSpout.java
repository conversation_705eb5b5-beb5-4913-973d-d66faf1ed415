package com.mogujie.detail.module.spout;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Charsets;
import com.meili.service.shopcenter.domain.entity.ShopInfo;
import com.meili.service.shopcenter.result.Result;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.SnapDetailSpout;
import com.mogujie.detail.core.util.NumUtil;
import com.mogujie.item.mapper.ModelMapperHelper;
import com.mogujie.item.mapper.ObjectConverter;
import com.mogujie.metabase.utils.CollectionUtils;
import com.mogujie.mht.slardar.SmallFile;
import com.mogujie.service.item.api.basic.ItemEditHistoryService;
import com.mogujie.service.item.domain.CompleteItem;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.basic.ItemEditHistoryDO;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.service.shopcenter.client.ShopReadServiceClient;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * Created by anshi on 17/3/8.
 */
@Component
public class ItemSnapDetailSpout implements SnapDetailSpout {

    private static final Logger LOGGER = LoggerFactory.getLogger(ItemSnapDetailSpout.class);

    private ItemEditHistoryService itemEditHistoryService;

    private SmallFile smallFile;

    @Autowired
    private ObjectConverter convertMapper;

    @PostConstruct
    public void init(){
        try {
            itemEditHistoryService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ItemEditHistoryService.class);
            smallFile = new SmallFile("27670d1305e16e00483fc6a3f5ae7401", "eac060028a4327fc3f988c7b8a152fed");
        } catch (Exception e) {
            LOGGER.error("init service failed : ", e);
        }
    }

    /**
     * 将ItemDO塞入context
     *
     * @param context 详情上下文
     * @param snapId  商品编辑快照id
     */
    @Override
    public void decorateSnapItemDO(DetailContext context, String snapId){
        if (StringUtils.isBlank(snapId)) {
            return;
        }
        ItemDO itemDO = this.getHistoryItem(snapId);
        if (null == itemDO) {
            return;
        }
        DetailItemDO detailItemDO = new DetailItemDO(itemDO);
        context.setItemId(detailItemDO.getItemId());
        context.setItemDO(detailItemDO);
        this.appendShopInfo(detailItemDO);
        this.decorateSkuAndPrice(detailItemDO);
    }


    /**
     * 追加店铺信息
     *
     * @param detailItemDO 商品信息
     */
    private void appendShopInfo(DetailItemDO detailItemDO) {
        long shopId = detailItemDO.getShopId();
        if (shopId <= 0) {
            return;
        }
        try {
            Result<ShopInfo> ret = ShopReadServiceClient.getShopByShopId(shopId);
            if (null != ret && ret.isSuccess()) {
                detailItemDO.setShopInfo(ret.getData());
            }
        } catch (Exception e) {
            LOGGER.error("get shopInfo Exception!shopId:" + shopId);
        }
    }

    /**
     * 根据快照id查询商品信息
     *
     * @param snapId
     * @return
     */
    private ItemDO getHistoryItem(String snapId) {
        Long snapshotId = IdConvertor.urlToId(snapId);
        ItemEditHistoryDO itemEditHistoryDO = itemEditHistoryService.queryById(snapshotId);
        if (null == itemEditHistoryDO || StringUtils.isBlank(itemEditHistoryDO.getFpath())) {
            return null;
        }
        byte[] content = smallFile.get(itemEditHistoryDO.getFpath());
        if (content.length <= 0) {
            return null;
        }
        CompleteItem completeItem = JSONObject.parseObject(new String(content, Charsets.UTF_8), CompleteItem.class);
        ModelMapperHelper.getItemDO(completeItem, convertMapper);
        return ModelMapperHelper.getItemDO(completeItem, convertMapper);
    }

    /**
     * 装修sku及价格
     *
     * @param detailItemDO 原始商品对象
     */
    public void decorateSkuAndPrice(DetailItemDO detailItemDO) {
        try {
            List<ItemSkuDO> itemSkuDOList = detailItemDO.getItemSkuDOList();
            if (CollectionUtils.isEmpty(itemSkuDOList)) {
                return;
            }
            Long totalStock = 0L;
            for (ItemSkuDO sku : detailItemDO.getItemSkuDOList()) {
                sku.setQuantity(1);
                sku.setNowPrice(sku.getPrice().intValue());
                totalStock += 1;
            }
            Long highestPrice = getMostPrice(detailItemDO.getItemSkuDOList(), true);
            Long lowestPrice = getMostPrice(detailItemDO.getItemSkuDOList(), false);
            detailItemDO.setLowNowPrice(NumUtil.formatNum(lowestPrice / 100));
            detailItemDO.setHighNowPrice(NumUtil.formatNum(highestPrice / 100));
            detailItemDO.setLowPrice(NumUtil.formatNum(lowestPrice / 100));
            detailItemDO.setHighPrice(NumUtil.formatNum(highestPrice / 100));
            detailItemDO.setTotalStock(totalStock);
            detailItemDO.setPromotionPrice(lowestPrice);
        } catch (Throwable e) {
            LOGGER.error("decorate snapshot price failed : {}", e);
        }
    }

    /**
     * 获取sku的最低最高售价
     *
     * @param skus      商品sku列表
     * @param isHighest 是否最高
     * @return
     */
    private Long getMostPrice(List<ItemSkuDO> skus, boolean isHighest) {
        if (CollectionUtils.isEmpty(skus)) {
            return 0L;
        }
        Long mostPrice = skus.get(0).getPrice();
        for (ItemSkuDO sku : skus) {
            if (isHighest) {
                if (sku.getPrice() > mostPrice) {
                    mostPrice = sku.getPrice();
                }
            } else {
                if (sku.getPrice() < mostPrice) {
                    mostPrice = sku.getPrice();
                }
            }
        }
        return mostPrice;
    }

}
