package com.mogujie.detail.module.qzonevip.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import com.mogujie.detail.module.sku.domain.SkuDO;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by anshi on 17/7/25.
 */
public class QZoneDO implements ModuleDO {

    /**
     * 当前商品是否为黄钻商品
     */
    @Getter
    @Setter
    private Boolean isVipItem;

    /**
     * 黄钻价
     */
    @Getter
    @Setter
    private Long vipPrice;

    /**
     * 黄钻活动id
     */
    @Getter
    @Setter
    private String activityId;

    /**
     * 黄钻sku（价格、库存）
     */
    @Getter
    @Setter
    private SkuDO skuInfo;

    @Getter
    @Setter
    private Integer startTime;

    @Getter
    @Setter
    private Integer endTime;
}
