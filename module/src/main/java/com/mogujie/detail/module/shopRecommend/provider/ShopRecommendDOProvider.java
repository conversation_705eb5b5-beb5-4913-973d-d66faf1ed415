package com.mogujie.detail.module.shopRecommend.provider;

import com.alibaba.fastjson.JSONObject;
import com.meili.service.shopcenter.util.ShopInfoTagsUtil;
import com.mogujie.algo.prism.client.PrismClient;
import com.mogujie.algo.prism.client.PrismClientFactory;
import com.mogujie.algo.prism.client.domain.PrismParam;
import com.mogujie.algo.prism.client.enums.PlatFormEnum;
import com.mogujie.algo.prism.common.domain.PrismResult;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.constant.SwitchKey;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.ContextUtil;
import com.mogujie.detail.core.util.ImageUtil;
import com.mogujie.detail.core.util.MetabaseTool;
import com.mogujie.detail.core.util.NumUtil;
import com.mogujie.detail.module.itemParams.util.CommonUtil;
import com.mogujie.detail.module.shopRecommend.domain.RealTimeRecommendItem;
import com.mogujie.detail.module.shopRecommend.domain.ShopRecommendDO;
import com.mogujie.detail.module.shopRecommend.domain.ShopRecommendItem;
import com.mogujie.metabase.spring.client.MetabaseClient;
import com.mogujie.search.ara.abtest.common.utils.MoguUuidUtil;
import com.mogujie.service.hummer.api.client.SearchPriceClient;
import com.mogujie.service.item.api.basic.ItemRelevanceService;
import com.mogujie.service.item.domain.basic.ItemRelevanceDO;
import com.mogujie.service.item.search.api.ItemSearchService;
import com.mogujie.service.item.search.domain.ItemSearchDoc;
import com.mogujie.service.item.search.domain.ItemSearchResult;
import com.mogujie.service.item.search.domain.query.ItemSearchQuery;
import com.mogujie.session.SessionContextHolder;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import com.mogujie.themis.camp.config.CampTagConfigClient;
import com.mogujie.themis.config.BaseInfo;
import com.mogujie.themis.config.CampMetabaseConfig;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.*;

/**
 * Created by anshi on 2017/4/25.
 */
@Module(name = "shopRecommend")
public class ShopRecommendDOProvider implements IModuleDOProvider<ShopRecommendDO> {

    private static final Logger logger = LoggerFactory.getLogger(ShopRecommendDOProvider.class);

    protected final int MGJ_PLATFORM_ID = 8;

    @Autowired
    private ItemRelevanceService itemRelevanceService;

    private ItemSearchService itemSearchService;

    private PrismClient prismClient;

    @Resource(name = "confMetabaseClient")
    protected MetabaseClient metabaseClient;

    @Override
    public void init() throws DetailException {
        try {
            //自动读取prism.properties配置
            prismClient = PrismClientFactory.getInstance().getClient();
            itemSearchService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ItemSearchService.class);
        } catch (Exception e) {
            throw new DetailException(e);
        }
    }

    @Override
    public ShopRecommendDO emit(DetailContext context) {
        try {
            if (ContextUtil.isLiveSupplyShadowItem(context.getItemDO())){
                return null;
            }
            ShopRecommendDO shopRecommendDO = new ShopRecommendDO();
            shopRecommendDO.setRealTimeRecommendItemList(getRealtimeRecommendItems(context));
            if (!context.isDyn()) {
                shopRecommendDO.setRecommendItemList(getShopRecommendItems(context));
            }
            return shopRecommendDO;
        } catch (Throwable e) {
            logger.error("get recommend items error.", e);
        }
        return null;
    }

    private List<RealTimeRecommendItem> getRealtimeRecommendItems(DetailContext context) {
        try {
            // 查询店铺实时推荐开关，设置true查询，false直接返回
            if (!MetabaseTool.isOn(SwitchKey.SWT_SHOP_REAL_TIME_RECOMMEND, false)){
                return null;
            }
            if (context.getItemDO().getShopInfo() == null) {
                return null;
            }
            Set<Integer> shopTags = ShopInfoTagsUtil.stringToSet(context.getItemDO().getShopInfo().getTags());
            // 店铺已经打开实时推荐开关（940标）
            if (CollectionUtils.isEmpty(shopTags) || !shopTags.contains(940)) {
                return null;
            }
            PrismParam prismParam = new PrismParam();
            prismParam.setPid(62806L);
            prismParam.setCode("62806");
            prismParam.setDid(context.getParam("_did"));
            Long uid = context.getLoginUserId() == null || context.getLoginUserId() <= 0 ? null : context.getLoginUserId();
            prismParam.setUid(uid);
            prismParam.setPlatform(PlatFormEnum.APP);
            prismParam.setStart(0);
            prismParam.setRow(3);
            String uuid=SessionContextHolder.getUUID();
            if(StringUtils.isBlank(uuid)){
                String mwTTid=context.getParam("mwTTid");
                String mwDid=context.getParam("mwDid");
                uuid= MoguUuidUtil.getMobileMgjuuid(mwTTid,mwDid);
            }

            if(StringUtils.isNotBlank(uuid)){
                prismParam.setUuid(uuid);
            }

            //扩展参数
            Map<String, Object> extParam = new HashMap<>();
            extParam.put("iid", context.getItemId());
            //消息头
            Map<String, String> headers = new HashMap<>();
            headers.put("ip", context.getClientIp());
            prismParam.setParams(extParam);
            prismParam.setHeaders(headers);

            PrismResult prismResult = prismClient.getRecommendations(prismParam);
            if (prismResult != null && prismResult.isSuccess() && CollectionUtils.isNotEmpty(prismResult.getData())) {
                List<RealTimeRecommendItem> realTimeRecommendItems = new ArrayList<>();
                List<Map<String, Object>> itemList = prismResult.getData();
                for (Map<String, Object> itemMap : itemList) {
                    RealTimeRecommendItem realTimeRecommendItem = new RealTimeRecommendItem();
                    realTimeRecommendItem.setIid(itemMap.get("item_id").toString());
                    realTimeRecommendItem.setAcm(itemMap.get("acm").toString());
                    realTimeRecommendItem.setImg(ImageUtil.img(itemMap.get("image").toString()));
                    realTimeRecommendItem.setTitle(itemMap.get("title").toString());
                    realTimeRecommendItem.setPrice("¥" + itemMap.get("discountPrice").toString());
                    realTimeRecommendItem.setIsPintuan(itemMap.get("price_taglist") != null);
                    realTimeRecommendItem.setSale(itemMap.get("historySale") == null ? "" :  CommonUtil.formatSale(itemMap.get("historySale").toString()));
                    realTimeRecommendItems.add(realTimeRecommendItem);
                }
                return realTimeRecommendItems;
            }
        } catch (Throwable e) {
            logger.error("get realtime recommend item error.", e);
        }
        return null;
    }

    private List<ShopRecommendItem> getShopRecommendItems(DetailContext context) {
        try {
            List<ItemRelevanceDO> result = itemRelevanceService.getItemRelevanceList(context.getItemId(), Arrays.asList(2));
            if (result != null && result.size() > 0 && StringUtils.isNotBlank(result.get(0).getSellerRelevance())) {
                String[] ids = result.get(0).getSellerRelevance().split(",");
                List<Long> itemIds = new ArrayList<>();
                for (String id : ids) {
                    itemIds.add(Long.valueOf(id));
                }
                ItemSearchQuery itemSearchQuery = new ItemSearchQuery();
                itemSearchQuery.setTradeItemIds(itemIds);
                itemSearchQuery.setStatusPass(true);
                ItemSearchResult response = itemSearchService.queryItems(itemSearchQuery);

                List<ShopRecommendItem> recommendItems = new ArrayList<>();
                for (ItemSearchDoc item : response.getDocs()) {
                    recommendItems.add(getRecommendItem(context, item));
                }
                return recommendItems;
            }
        } catch (Throwable e) {
            logger.error("get shop recommend items error.", e);
        }
        return null;
    }

    private ShopRecommendItem getRecommendItem(DetailContext context, ItemSearchDoc item) {
        ShopRecommendItem recommendItem = new ShopRecommendItem();
        String iid = IdConvertor.idToUrl(item.getTradeItemId());
        recommendItem.setIid(iid);
        recommendItem.setTitle(item.getTitle());
        HashSet<String> tags = new HashSet(Arrays.asList(item.getSearchTag_ik().split(" ")));
        if (tags.contains("108")
                || (tags.contains("109") && SearchPriceClient.isActivity((short) item.getType()))) {
            recommendItem.setIsPintuan(true);
        } else {
            recommendItem.setIsPintuan(false);
        }
        int disPrice = item.getDailyPriceForPicWall_it() == null ? 0 : item.getDailyPriceForPicWall_it().intValue();
        if (isActivityDuring(context)){
            disPrice = item.getPromotionPriceForPicWall_it() == null ? 0 : item.getPromotionPriceForPicWall_it().intValue();
        }
        recommendItem.setPrice("¥" + NumUtil.formatPriceDrawer(disPrice));
        recommendItem.setImg(ImageUtil.img(item.getImage()));
        recommendItem.setSale(item.getHistorySale() == 0 ? "" : "销量 " + item.getHistorySale());
        return recommendItem;
    }

    private boolean isActivityDuring(DetailContext context) {
        try {
            List<String> tags = ContextUtil.getNumTags(context.getItemDO());
            String tagString = StringUtils.join(tags, ",");
            if (CampTagConfigClient.isWithinCampByMarket(MGJ_PLATFORM_ID, tagString)){
                boolean isC1 = BaseInfo.CampLevel.C1.getCode().equalsIgnoreCase(CampTagConfigClient.getBaseCampConfigTagDTODetail(MGJ_PLATFORM_ID, tagString).getLevel());
                JSONObject jsonObject = JSONObject.parseObject(metabaseClient.get("hide_c1_normal_item_countdown_default"));
                if (isC1 && jsonObject.getBoolean("hide") && (StringUtils.isBlank(tagString) || !tags.contains(jsonObject.getString("tag")))) {
                    return false;
                }
                Map<String, Object> map = new HashMap<>();
                switch (context.getRouteInfo().getPlatform()) {
                    case PC:
                        map = CampTagConfigClient.getCampConfigDetail(MGJ_PLATFORM_ID, null, tagString, BaseInfo.Platform.PC.getCode(), CampMetabaseConfig.CampConfigMenu.CAMP_CONFIG_PAGE_DETAIL);
                        break;
                    default:
                        map = CampTagConfigClient.getCampConfigDetail(MGJ_PLATFORM_ID, null, tagString, BaseInfo.Platform.APP.getCode(), CampMetabaseConfig.CampConfigMenu.CAMP_CONFIG_PAGE_DETAIL);
                        break;
                }
                if (MapUtils.isEmpty(map)) {
                    return false;
                }
                return true;
            }
        } catch (Throwable e) {
            logger.error("isActivityDuring fault!", e);
        }
        return false;
    }


}
