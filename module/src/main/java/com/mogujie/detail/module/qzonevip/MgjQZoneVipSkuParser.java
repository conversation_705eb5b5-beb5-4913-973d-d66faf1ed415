package com.mogujie.detail.module.qzonevip;

import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.util.ContextUtil;
import com.mogujie.detail.core.util.ImageUtil;
import com.mogujie.detail.core.util.NumUtil;
import com.mogujie.detail.core.util.SkuUtil;
import com.mogujie.detail.module.sku.domain.*;
import com.mogujie.detail.module.sku.spi.ISkuParser;
import com.mogujie.metabase.spring.client.MetabaseClient;
import com.mogujie.service.hummer.constains.RequestConstants;
import com.mogujie.service.hummer.utils.QZoneVipPriceChecker;
import com.mogujie.service.inventory.api.InventoryReadService;
import com.mogujie.service.inventory.domain.ActivityInventoryV2;
import com.mogujie.service.inventory.domain.param.BatchActivityInventoryQueryParamV2;
import com.mogujie.service.inventory.domain.result.MapResult;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.service.item.domain.basic.SkuAttributionDO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * Created by xiaoyao on 16/4/12.
 */

public class MgjQZoneVipSkuParser implements ISkuParser {

    private static final Logger LOGGER = LoggerFactory.getLogger(MgjQZoneVipSkuParser.class);

    private MetabaseClient metabaseClient;

    private InventoryReadService inventoryReadService;

    public MgjQZoneVipSkuParser(MetabaseClient metabaseClient) {
        try {
            this.metabaseClient = metabaseClient;
            inventoryReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(InventoryReadService.class);
        } catch (Exception e) {
            LOGGER.error("init service failed : ", e);
        }
    }

    @Override
    public void parseSku(DetailContext context, SkuDO skuDO) {
        DetailItemDO item = context.getItemDO();
        QZoneVipPriceChecker.QZoneVipPriceRes priceRes = QZoneVipPriceChecker.calcQZoneVipPrice(item.getReservePrice(), item.getItemTags(), RequestConstants.Market.MOGUJIE);
        Long activityId = Long.parseLong(priceRes.getOutId());
        Long realPrice = priceRes.getRealPrice();
        List<ItemSkuDO> oldSkus = item.getItemSkuDOList();
        Map<Long, ActivityInventoryV2> invertoryMap = getInventoryMap(oldSkus, activityId);
        List<ItemSkuDO> skus = new ArrayList<>();
        Integer totalStock = 0;
        for (ItemSkuDO sku : oldSkus) {
            ItemSkuDO newSku = new ItemSkuDO();
            newSku.setSkuId(sku.getSkuId());
            newSku.setXdSkuId(sku.getXdSkuId());
            newSku.setAttributions(sku.getAttributions());
            newSku.setItemId(sku.getItemId());
            newSku.setImage(sku.getImage());
            newSku.setCode(sku.getCode());
            newSku.setPrice(sku.getPrice());
            newSku.setNowPrice(realPrice == null ? sku.getPrice().intValue() : realPrice.intValue());
            newSku.setIsDefault(sku.getIsDefault());
            newSku.setQuantity(invertoryMap.get(sku.getSkuId()) == null ? 0 : invertoryMap.get(sku.getSkuId()).getStock());
            totalStock += newSku.getQuantity();
            skus.add(newSku);
        }

        List<SkuData> skuDatas = new ArrayList<>();
        if (CollectionUtils.isEmpty(skus)) {
            return;
        }
        boolean canBuy = this.canBuy(item, totalStock);
        String styleKey = "";
        String sizeKey = "";
        boolean isSizeDefault = false;
        boolean isStyleDefault = false;
        List<String> tmpStyle = new ArrayList<>();
        List<String> tmpSize = new ArrayList<>();
        List<StylePropData> styles = new ArrayList<>();
        List<SizePropData> sizes = new ArrayList<>();
        int styleId = 1;
        int sizeId = 100;
        Map<String, Integer> styleMap = new HashMap<>();
        Map<String, Integer> sizeMap = new HashMap<>();

        int index = 0;

        int lowestPrice = 0;
        int highestPrice = 0;
        int lowestNowPrice = 0;
        int highestNowPrice = 0;
        Map<String, Map<String, String>> attrsMap = new HashMap<>();
        for (ItemSkuDO sku : skus) {
            SkuData skuData = new SkuData();
            skuData.setCurrency("¥");
            skuData.setImg(null == sku.getImage() ? ImageUtil.img(item.getMainImage()) : ImageUtil.img(sku.getImage()));
            skuData.setNowprice(sku.getNowPrice());
            skuData.setPrice(sku.getPrice().intValue());
            skuData.setStock(canBuy ? sku.getQuantity() : 0);
            skuData.setXdSkuId(IdConvertor.idToUrl(sku.getXdSkuId()));
            skuData.setStockId(IdConvertor.idToUrl(sku.getSkuId()));
            skuData.setDelayTime(ContextUtil.contain30dayDeliveryService(context) ? SkuUtil.getDelayedDeliveryTimeWithoutNewYear(sku) : SkuUtil.getDelayedDeliveryTimeWithNewYear(item, sku));            skuData.setDelayHours(SkuUtil.getDelayDeliveryPeriod(sku.getExtra()));
            attrsMap.put(skuData.getStockId(), parseProps(skuData, sku.getAttributions()));
            skuDatas.add(skuData);
        }
        skuDO.setTotalStock(totalStock);

        for (SkuData skuData : skuDatas) {
            Map<String, String> attrMap = attrsMap.get(skuData.getStockId());
            styleKey = attrMap.get("style");
            sizeKey = attrMap.get("size");

            String realStyle = attrMap.get("style") == null ? "" : (attrMap.get("style").equals(styleKey)) ? skuData.getStyle() : skuData.getSize();
            String realSize = attrMap.get("size") == null ? "" : (attrMap.get("size").equals(sizeKey)) ? skuData.getSize() : skuData.getStyle();

            skuData.setStyle(StringUtils.isEmpty(realStyle) ? null : realStyle);
            skuData.setSize(StringUtils.isEmpty(realSize) ? null : realSize);

            if (!StringUtils.isEmpty(skuData.getStyle()) && !tmpStyle.contains(skuData.getStyle())) {
                tmpStyle.add(skuData.getStyle());
                if (StringUtils.isEmpty(realStyle) && false == isStyleDefault) {
                    isStyleDefault = true;
                }
                StylePropData styleProp = new StylePropData();
                styleProp.setDefault(false);//StringUtils.isEmpty(realStyle));
                styleProp.setType("style");
                styleProp.setName(skuData.getStyle());
                styleProp.setIndex(styleId);
                styleProp.setStyleId(styleId);
                styles.add(styleProp);
                styleMap.put(skuData.getStyle(), styleId);
                styleId++;
            }

            if (!StringUtils.isEmpty(skuData.getSize()) && !tmpSize.contains(skuData.getSize())) {
                tmpSize.add(skuData.getSize());
                if (StringUtils.isEmpty(realSize) && false == isSizeDefault) {
                    isSizeDefault = true;
                }

                SizePropData sizeProp = new SizePropData();
                sizeProp.setDefault(false);//StringUtils.isEmpty(realSize));
                sizeProp.setType("size");
                sizeProp.setName(skuData.getSize());
                sizeProp.setIndex(sizeId);
                sizeProp.setSizeId(sizeId);
                sizes.add(sizeProp);
                sizeMap.put(skuData.getSize(), sizeId);
                sizeId++;
            }

            // 给sku添加index
            if (!StringUtils.isEmpty(skuData.getStyle()) && null != styleMap.get(skuData.getStyle())) {
                skuData.setStyleId(styleMap.get(skuData.getStyle()));
            }

            if (!StringUtils.isEmpty(skuData.getSize()) && null != sizeMap.get(skuData.getSize())) {
                skuData.setSizeId(sizeMap.get(skuData.getSize()));
            }

            if (index == 0) {
                highestPrice = lowestPrice = skuData.getPrice();
                highestNowPrice = lowestNowPrice = skuData.getNowprice();
            }

            if (skuData.getPrice() > highestPrice) {
                highestPrice = skuData.getPrice();
                highestNowPrice = skuData.getNowprice();
            }
            if (skuData.getPrice() < lowestPrice) {
                lowestPrice = skuData.getPrice();
                lowestNowPrice = skuData.getNowprice();
            }
            index++;
        }

        List<PropInfo> props = new ArrayList<>();
        if (!StringUtils.isEmpty(styleKey)) {
            PropInfo<StylePropData> stylePropInfo = new PropInfo();
            stylePropInfo.setLabel(styleKey);
            stylePropInfo.setList(styles);
            //默认sku能加库存的需求
            stylePropInfo.setDefault(false); //isStyleDefault);
            props.add(stylePropInfo);
            if (StringUtils.isEmpty(sizeKey)) {
                skuDO.setProps(props);
            }
        }
        if (!StringUtils.isEmpty(sizeKey)) {
            PropInfo<SizePropData> sizePropInfo = new PropInfo();
            sizePropInfo.setLabel(sizeKey);
            sizePropInfo.setList(sizes);
            sizePropInfo.setDefault(false); //isSizeDefault);
            props.add(sizePropInfo);
            skuDO.setProps(props);
        }

        skuDO.setStyleKey(StringUtils.isEmpty(styleKey) ? null : styleKey);
        skuDO.setSizeKey(StringUtils.isEmpty(sizeKey) ? null : sizeKey);
        Collections.sort(skuDatas, new Comparator<SkuData>() {
            @Override
            public int compare(SkuData o1, SkuData o2) {
                if (o1.getStyleId() == o2.getStyleId()) {
                    return o1.getSizeId() == o2.getSizeId() ? 0 : (o1.getSizeId() > o2.getSizeId() ? 1 : -1);
                } else {
                    return o1.getStyleId() > o2.getStyleId() ? 1 : -1;
                }
            }
        });

        skuDO.setSkus(skuDatas);
        //没有价格区间
        if (highestNowPrice == lowestNowPrice) {
            skuDO.setPriceRange(formatPrice(lowestNowPrice));
        } else {
            skuDO.setPriceRange(formatPrice(lowestNowPrice) + '~' + formatPrice(highestNowPrice));
        }
        skuDO.setDefaultPrice(skuDO.getPriceRange());
    }

    private Map<Long, ActivityInventoryV2> getInventoryMap(List<ItemSkuDO> skus, Long actId) {
        List<Long> skuIds = new ArrayList<>();
        for (ItemSkuDO sku : skus) {
            skuIds.add(sku.getSkuId());
        }

        BatchActivityInventoryQueryParamV2 param = new BatchActivityInventoryQueryParamV2();
        param.setSkuIds(skuIds);
        param.setChannelId(2015);
        param.setActivityId(actId);
        MapResult<Long, ActivityInventoryV2> inventoryMapResult = inventoryReadService.batchQueryActivityInventoryV3(param);
        if (null != inventoryMapResult && inventoryMapResult.isSuccess()) {
            return inventoryMapResult.getData();
        }
        return new HashMap<>();
    }

    protected boolean canBuy(DetailItemDO item, Integer totalStock) {
        if (item.getIsDeleted() == 1) {
            return false;
        }

        if (item.getIsShelf() == 1) {
            return false;
        }

        int time = (int) (System.currentTimeMillis() / 1000);

        if (item.getStatus() == 1 || item.getStatus() < 0) {
            return false;
        }

        if (totalStock <= 0) {
            return false;
        }

        return true;
    }

    protected static String formatPrice(double price) {
        return "¥" + NumUtil.formatNum(price / 100D);
    }

    protected Map<String, String> parseProps(SkuData skuData, List<SkuAttributionDO> attributions) {
        int index = 0;
        Map<String, String> mapping = new HashMap<>();
        if (CollectionUtils.isEmpty(attributions)) {
            return mapping;
        }

        List<SkuAttributionDO> atts = new ArrayList<>(attributions);
        Collections.sort(atts, new Comparator<SkuAttributionDO>() {
            @Override
            public int compare(SkuAttributionDO o1, SkuAttributionDO o2) {
                int o1Pos = o1.getShowPosition() == null ? 0 : o1.getShowPosition();
                int o2Pos = o2.getShowPosition() == null ? 0 : o2.getShowPosition();
                return o1Pos < o2Pos ? -1 : (o1Pos > o2Pos ? 1 : 0);
            }
        });

        for (SkuAttributionDO attr : atts) {
            String attrName = attr.getName();
            if (attr.getShowPosition() == null || attr.getShowPosition() == 0) {
                skuData.setStyle(attr.getValue());
                mapping.put("style", attrName);
            } else {
                skuData.setSize(attr.getValue());
                mapping.put("size", attrName);
            }
            index++;
        }
        return mapping;
    }
}
