package com.mogujie.detail.module.itemBase.domain;

import lombok.Getter;
import lombok.Setter;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/3/15.
 */
public class GoodsBanner {

    /**
     * banner 图片
     */
    @Getter
    @Setter
    private String img;

    /**
     * banner 链接地址
     */
    @Getter
    @Setter
    private String link;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof GoodsBanner)) return false;

        GoodsBanner that = (GoodsBanner) o;

        if (img != null ? !img.equals(that.img) : that.img != null) return false;
        if (link != null ? !link.equals(that.link) : that.link != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = img != null ? img.hashCode() : 0;
        result = 31 * result + (link != null ? link.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "GoodsBanner{" +
                "img='" + img + '\'' +
                ", link='" + link + '\'' +
                '}';
    }
}
