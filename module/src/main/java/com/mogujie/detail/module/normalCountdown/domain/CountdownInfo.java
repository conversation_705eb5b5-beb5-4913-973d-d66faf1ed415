package com.mogujie.detail.module.normalCountdown.domain;

import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * Created by anshi on 18/2/6.
 */
public class CountdownInfo {

    /**
     * 商品标的key
     */
    @Getter
    @Setter
    private String tagKey;

    /**
     * 预热开始时间
     */
    @Getter
    @Setter
    private Long warmUpTime;

    /**
     * 正式开始时间
     */
    @Getter
    @Setter
    private Long startTime;

    /**
     * 结束时间
     */
    @Getter
    @Setter
    private Long endTime;

    /**
     * 倒计时处于什么阶段(预热/正式)
     */
    @Getter
    @Setter
    private CountdownState state;

    /**
     * 倒计时时间(秒)
     */
    @Getter
    @Setter
    private Long countdown;

    /**
     * sku最低价格 x 报名的折扣
     */
    @Getter
    @Setter
    private Long price;

    /**
     * 每个sku的价格 x 报名的折扣
     */
    @Getter
    @Setter
    private Map<Long, Long> priceMap;

    /**
     * 会员价 sku最低价格 x 报名的折扣
     */
    @Getter
    @Setter
    private Long memberPrice;

    /**
     * 每个sku的价格 x 报名的会员折扣
     */
    @Getter
    @Setter
    private Map<Long, Long> memberPriceMap;

    /**
     * 招商活动id
     */
    @Getter
    @Setter
    private Long activityId;

    @Getter
    @Setter
    private Long maitId;

    /**
     * 1110版本新版麦田配置id
     */
    @Getter
    @Setter
    private Long maitId1110;

    /**
     * 闪购价格曲线图
     */
    @Getter
    @Setter
    private String priceHistoryImg;

    /**
     * 闪购招商的招商类型id 117标识爆款比价
     */
    @Getter
    @Setter
    private String nbt;

    /**
     * 爆款比价的外网价 如：98.5
     */
    @Getter
    @Setter
    private String outNetPrice;

    /**
     * 外网图片 用于切片爆款比价
     */
    @Getter
    @Setter
    private String outNetImage;
}
