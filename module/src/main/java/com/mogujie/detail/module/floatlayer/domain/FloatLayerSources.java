package com.mogujie.detail.module.floatlayer.domain;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * Created by xiaoyao on 15/12/4.
 */
public class FloatLayerSources implements Serializable{

    private static final long serialVersionUID = -2364154880095607212L;

    public FloatLayerSources() {
    }

    @Setter
    @Getter
    private int duration;

    @Setter
    @Getter
    private List<String> images;

    @Override
    public String toString() {
        return "FloatLayerSources{" +
                "duration=" + duration +
                ", images=" + images +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        FloatLayerSources that = (FloatLayerSources) o;

        if (duration != that.duration) return false;
        if (images != null ? !images.equals(that.images) : that.images != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = duration;
        result = 31 * result + (images != null ? images.hashCode() : 0);
        return result;
    }
}
