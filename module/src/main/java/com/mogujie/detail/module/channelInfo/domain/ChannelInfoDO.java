package com.mogujie.detail.module.channelInfo.domain;

import com.mogujie.detail.core.adt.ChannelMeta;
import com.mogujie.detail.core.adt.ModuleDO;
import com.mogujie.detail.core.annotation.Module;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * 通用渠道信息
 * Created by anshi on 18/1/11.
 */
public class ChannelInfoDO implements ModuleDO {

    /**
     * 渠道id
     */
    @Getter
    @Setter
    private ChannelMeta channelMetaInfo;

    /**
     * 商品标中的其他字段
     * 比如在分享福利价的fxfl标中，extraMap中有gsc表示分享X群解锁福利价
     */
    @Getter
    @Setter
    private Map<String, String> extraTagMap;

    /**
     * 商品其他动态数据
     * 比如在分享福利价渠道中，extraDynMap中有"remainNum"表示"剩余需要分享的群数量"，"total"表示总共所需的分享次数
     */
    @Getter
    @Setter
    private Map<String, Object> extraDynMap;

    /**
     * 活动预热开始时间
     */
    @Getter
    @Setter
    private Integer warmUpTime;

    /**
     * 活动正式开始时间
     */
    @Getter
    @Setter
    private Integer startTime;

    /**
     * 活动结束时间
     */
    @Getter
    @Setter
    private Integer endTime;

    /**
     * 预热期间用，渠道价(一口价场景)
     */
    @Getter
    @Setter
    private Long channelPrice;

    /**
     * 商品日常普通售卖价（所有sku最低价）
     */
    @Getter
    @Setter
    private Long lowNormalPrice;

    /**
     * 商品日常普通售卖价（所有sku最高价）
     */
    @Getter
    @Setter
    private Long highNormalPrice;

    /**
     * 预热期间用，渠道价(最低，区间价场景)
     */
    @Getter
    @Setter
    private Long lowChannelPrice;

    /**
     * 预热期间用，渠道价(最高，区间价场景)
     */
    @Getter
    @Setter
    private Long highChannelPrice;

    /**
     * 渠道报名总库存
     */
    @Getter
    @Setter
    private Long originTotalStock;

    /**
     * 商品现售价格（ItemBaseDO和SkuDO中的价格）是否是本渠道正式期的价格
     */
    @Getter
    @Setter
    private boolean currentPriceIsChannelPrice;
}
