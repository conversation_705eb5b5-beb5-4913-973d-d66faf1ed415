package com.mogujie.detail.module.myCouponBubble.provider;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.mogujie.darwin.application.client.DarwinClient;
import com.mogujie.darwin.application.query.ApplicationLaunchQuery;
import com.mogujie.darwin.application.service.client.ApplicationLaunchClient;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.DetailWebMetabase;
import com.mogujie.detail.module.myCouponBubble.domain.MyCouponBubbleDO;
import com.mogujie.detail.module.mycoupon.provider.MyCouponDOProvider;
import com.mogujie.market.base.model.ResultBase;
import com.mogujie.search.ara.abtest.common.utils.MoguUuidUtil;
import com.mogujie.service.diana.DianaLaunchService;
import com.mogujie.service.diana.query.QueryContext;
import com.mogujie.service.diana.result.NatashaResult;
import com.mogujie.service.hummer.api.BuyerResourceReadService;
import com.mogujie.service.hummer.constains.PromotionConstants;
import com.mogujie.service.hummer.constains.RequestConstants;
import com.mogujie.service.hummer.domain.dto.BuyerResourceDTO;
import com.mogujie.service.hummer.domain.dto.BuyerResourceQuery;
import com.mogujie.service.hummer.domain.dto.PlatformCouponDTO;
import com.mogujie.service.hummer.domain.dto.result.Result;
import com.mogujie.session.SessionContextHolder;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by nanyang on 18/10/24.
 * 配置的麦田资源位都已经失效掉了，所以线上对该模块做关闭处理 2020.09.29 桌子
 */
@Module(name = "myCouponBubble")
public class MyCouponBubbleDOProvider implements IModuleDOProvider<MyCouponBubbleDO> {

    private static final Logger LOGGER = LoggerFactory.getLogger(MyCouponBubbleDOProvider.class);

    /**
     * 跨境商品店铺标
     */
    private static final Integer CROSS_BORDER_SHOP_TAG = 1954;

    private BuyerResourceReadService buyerResourceReadService;

    /**
     * 第一轮麦田数据获取资源位
     */
    private static final Long FIRST_COUPON_BUBBLE_MAIT_ID = 139585L;


    /**
     * 第二轮麦田数据获取资源位
     */
    private static final Long SECOND_COUPON_BUBBLE_MAIT_ID = 139339L;


    /**
     * 虚拟商品类目ID
     */
    private static final Long VIRTUAL_ITEM_CID = 1264L;


    /**
     * 邮费商品类目ID
     */
    private static final Long POSTAGE_ITEM_CID = 1266L;


    /**
     * metabase开关判断是否需要第二次获取麦田数据
     */
    private static final String SWITCH_QUERY_MAIT_SECOND = "switch_query_mait_second";


    private DianaLaunchService dianaLaunchService;

    @Override
    public void init() throws DetailException {
        try {
            buyerResourceReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(BuyerResourceReadService.class);
            dianaLaunchService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(DianaLaunchService.class);
        } catch (Exception e) {
            LOGGER.error("init service failed : {}", e);
        }
    }

    @Override
    public MyCouponBubbleDO emit(DetailContext context) {

        if (context.getLoginUserId() == null)
            return new MyCouponBubbleDO();

        MyCouponBubbleDO myCouponBubbleDO = null;
        List<Long> couponIds = Lists.newArrayList();
        boolean isFilter = filterItem(context);
        try {
            List<Map<String, Object>> maitData = getMaitData(context, FIRST_COUPON_BUBBLE_MAIT_ID);
            if (!CollectionUtils.isEmpty(maitData)) {
                //查看是否可以领取
                boolean canGet = canGetCoupon(maitData, context.getLoginUserId(), couponIds);
                //查看是否可以领取
                if (canGet && !isFilter) {
                    myCouponBubbleDO = getGoToInfo(maitData);
                    return myCouponBubbleDO;
                }
                myCouponBubbleDO = getUnusedAppPlatformCoupon(maitData, context.getLoginUserId(), couponIds, context);
            }
            if (isFilter) {
                myCouponBubbleDO = null;
            }
            if (myCouponBubbleDO != null && !CollectionUtils.isEmpty(maitData) && !isFilter) {

                if (DetailWebMetabase.getBoolean(SWITCH_QUERY_MAIT_SECOND, false)) {
                    maitData = getMaitData(context, SECOND_COUPON_BUBBLE_MAIT_ID);
                }
                if (CollectionUtils.isEmpty(maitData)) {
                    myCouponBubbleDO = null;
                }
            }
        } catch (Throwable e) {
            LOGGER.error("get myCouponBubble error!", e);
        }

        /**
         * 避免走缓存
         */
        if (myCouponBubbleDO == null)
            return new MyCouponBubbleDO();
        return myCouponBubbleDO;
    }


    /**
     * 判断改商品能不能透出优惠劵气泡
     *
     * @param context
     * @return
     */
    private boolean filterItem(DetailContext context) {

        boolean flag = false;
        DetailItemDO detailItemDO = context.getItemDO();
        String itemCids = detailItemDO.getCids();

        //过滤虚拟类目商品
        if (itemCids != null && itemCids.contains("#" + VIRTUAL_ITEM_CID + "#")) {
            flag = true;
            return flag;
        }

        //过滤跨境商品
        if (isCrossBorderItem(context)) {
            flag = true;
            return flag;
        }

        //过滤邮费类目商品
        if (itemCids != null && itemCids.contains("#" + POSTAGE_ITEM_CID + "#")) {
            flag = true;
            return flag;
        }


        //过滤库存为0
        if (detailItemDO.getTotalStock() == 0) {
            flag = true;
            return flag;
        }

        //过滤商品下架状态
        if (detailItemDO.getIsShelf() == 1) {
            flag = true;
            return flag;
        }
        return flag;
    }


    /**
     * 通过用户获取麦田数据
     *
     * @param context
     * @return
     */
    private List<Map<String, Object>> getMaitData(DetailContext context, Long definitionId) {
        try {

            ApplicationLaunchClient launchClient = DarwinClient.getLaunchClient();
            ApplicationLaunchQuery query = new ApplicationLaunchQuery();
            query.setDefinitionId(definitionId);
            query.setDid(context.getParam("_did"));
            query.setPageSize(30);

            String uuid = SessionContextHolder.getUUID();
            if (StringUtils.isBlank(uuid)) {
                String mwTTid = context.getParam("mwTTid");
                String mwDid = context.getParam("mwDid");
                uuid = MoguUuidUtil.getMobileMgjuuid(mwTTid, mwDid);
            }
            query.setUuid(uuid);

            query.setUid(context.getLoginUserId()); //当前用户UID
            ResultBase<List<Map<String, Object>>> rstBase = launchClient.applicationLaunch(query);
            if (rstBase != null && rstBase.hasSuccessValue() && rstBase.getValue().size() > 0) {
                List<Map<String, Object>> maitData = rstBase.getValue();
                return maitData;
            }
        } catch (Throwable e) {
            LOGGER.error("get maitData  error!", e);
        }
        return null;
    }


    /**
     * 促销接口获取数据拼接气泡对象
     *
     * @param maitData
     * @param userId
     * @param couponIds
     * @param context
     * @return
     */
    private MyCouponBubbleDO getUnusedAppPlatformCoupon(List<Map<String, Object>> maitData, Long userId, List<Long> couponIds, DetailContext context) {
        try {
            //获取couponIds的方法不一样了
            if (CollectionUtils.isEmpty(couponIds)) {
                return null;
            }

            BuyerResourceQuery query = new BuyerResourceQuery();
            query.setBuyerId(userId);
            query.setStatus(PromotionConstants.BuyerResourceStatus.UNUSED);
            query.setValid(RequestConstants.BuyerResourceTimeValid.IN);
            query.setNeedCampaignInfo(true);
            query.setNeedResourcePoolInfo(true);
            query.setCampaignIds(couponIds);


            query.setLimit(1024);
            query.setOrderBy(RequestConstants.BuyerResourceOrderBy.ID_DESC);

            Result<List<BuyerResourceDTO>> buyerSources = buyerResourceReadService.getResourcesOfUser(query);
            if (buyerSources == null || !buyerSources.isSuccess() || CollectionUtils.isEmpty(buyerSources.getData())) {
                return null;
            }
            List<BuyerResourceDTO> buyerSourcesData = buyerSources.getData();

            BuyerResourceDTO buyerSource = getBestCouponDto(context, buyerSourcesData);
            if (buyerSource == null) {
                return null;
            }
            PlatformCouponDTO couponDTO = MyCouponDOProvider.convertCampaignToPlatformCoupon(buyerSource.getCampaignDTO(), buyerSource.getResourcePoolDTO());

            if (couponDTO == null) {
                return null;
            }
            MyCouponBubbleDO couponBubble = new MyCouponBubbleDO();
            couponBubble.setCouponId(couponDTO.getId());
            couponBubble.setCutPrice(couponDTO.getCutPrice());
            couponBubble.setLimitPrice(couponDTO.getLimitPrice());
            couponBubble.setName(couponDTO.getName());
            couponBubble.setDecorate(couponDTO.getDecorate());
            couponBubble.setDiscount(couponDTO.getDiscount() != null ? couponDTO.getDiscount().intValue() : null);
            couponBubble.setMaxDecrease(couponDTO.getMaxDecrease());
            String terminal = "";
            if (couponDTO.getTerminalType() != null) {
                switch (couponDTO.getTerminalType()) {
                    case 1:
                        terminal = "App专享";
                        break;
                    case 2:
                        terminal = "PC专享";
                        break;
                    case 3:
                        terminal = "女装小程序专享";
                        break;
                    default:
                        break;
                }
            }
            couponBubble.setTerminal(terminal);

            /**
             * 这里的时间是领卷的时间和领的卷的结束时间
             */
            couponBubble.setStartTime(buyerSource.getStartTime() != null ? buyerSource.getStartTime().intValue() : null);
            couponBubble.setEndTime(buyerSource.getEndTime() != null ? buyerSource.getEndTime().intValue() : null);


            /**
             * 用卷ID和麦田比较获取背景图，优惠文案
             */
            for (Map<String, Object> map : maitData) {
                Long couponId = map.get("couponId") == null ? null : Long.parseLong(map.get("couponId").toString());
                if (couponId == null || couponBubble.getCouponId() == null || map.get("backgroundImage")==null ||  map.get("couponText")==null){
                    continue;
                }
                String backgroundImage = map.get("backgroundImage").toString();
                String couponText = map.get("couponText").toString();
                String newText = "";
                //整数元和带小数的元
                if ((couponBubble.getCutPrice() % 100) == 0) {
                    Integer cutPrice = couponBubble.getCutPrice() / 100;
                    newText = couponText.replaceAll("##", cutPrice.toString());
                } else {
                    Double cutPrice = couponBubble.getCutPrice() / 100d;
                    newText = couponText.replaceAll("##", cutPrice.toString());
                }
                couponBubble.setDubbleBgImg(backgroundImage);
                couponBubble.setCouponDesc(newText);
                couponBubble.setJumpUrl("");
                couponBubble.setType(0);
                break;

            }
            return couponBubble;
        } catch (Throwable e) {
            LOGGER.error("get unused coupons error!", e);
        }
        return null;
    }

    /**
     * 获取优惠的优惠券气泡透出
     *
     * @param context
     * @param buyerSourcesData
     * @return
     */
    private BuyerResourceDTO getBestCouponDto(DetailContext context, List<BuyerResourceDTO> buyerSourcesData) {
        PlatformCouponDTO bestcouponDto = null;
        BuyerResourceDTO bestBuyerResouceDto = null;
        Long highNowPriceVal = context.getItemDO().getHighNowPriceVal();
        Long lowNowPriceVal = context.getItemDO().getLowNowPriceVal();
        for (BuyerResourceDTO buyerResourceDTO : buyerSourcesData) {
            PlatformCouponDTO couponDto = MyCouponDOProvider.convertCampaignToPlatformCoupon(buyerResourceDTO.getCampaignDTO(), buyerResourceDTO.getResourcePoolDTO());
            if (couponDto == null) {
                continue;
            }
            //商品最高价格都不到满减要求
            if (couponDto.getLimitPrice() > highNowPriceVal) {
                continue;
            }
            //找到第一张商品最低价大于的满减券
            if (couponDto.getLimitPrice() <= lowNowPriceVal) {
                if (bestcouponDto == null && bestBuyerResouceDto == null) {
                    bestcouponDto = couponDto;
                    bestBuyerResouceDto = buyerResourceDTO;
                } else {
                    //透出减得多的
                    if (bestcouponDto.getCutPrice() < couponDto.getCutPrice()) {
                        bestcouponDto = couponDto;
                        bestBuyerResouceDto = buyerResourceDTO;
                    } else if (bestcouponDto.getCutPrice() == couponDto.getCutPrice()) {
                        //达到满减要求下同样透出满减门槛低
                        if (bestcouponDto.getLimitPrice() > couponDto.getLimitPrice()) {
                            bestcouponDto = couponDto;
                            bestBuyerResouceDto = buyerResourceDTO;
                        }
                    }
                }
            }
        }

        return bestBuyerResouceDto;

    }


    /**
     * 是否为海外跨境商品
     *
     * @return
     */
    private boolean isCrossBorderItem(DetailContext context) {
        try {
            return parseTags(context.getItemDO().getShopInfo().getTags(), CROSS_BORDER_SHOP_TAG);
        } catch (Throwable e) {
        }
        return false;
    }

    /**
     * 解析店铺标判断店铺标中是否含有目标标签
     *
     * @param tags
     * @param tagId
     * @return
     */
    private boolean parseTags(String tags, Integer tagId) {
        if (StringUtils.isEmpty(tags) || tagId <= 0) {
            return false;
        }
        String[] tagArr = tags.split(",");
        for (String tag : tagArr) {
            if (null == tag) continue;
            if (tagId.equals(Integer.parseInt(tag.trim()))) {
                return true;
            }
        }
        return false;
    }

    private MyCouponBubbleDO getGoToInfo(List<Map<String, Object>> maitData) {
        MyCouponBubbleDO couponBubble = new MyCouponBubbleDO();
        /**
         * 用卷ID和麦田比较获取背景图，优惠文案
         */
        for (Map<String, Object> map : maitData) {
            Long couponId = map.get("couponId") == null ? null : Long.parseLong(map.get("couponId").toString());
            if (couponId == null || map.get("receiveText") == null || map.get("jumpLink") == null || map.get("backgroundImage")== null) {

                continue;
            }

            String backgroundImage = map.get("backgroundImage").toString();
            String jumpLink = map.get("jumpLink").toString();
            String inductionText = map.get("receiveText").toString();
            couponBubble.setDubbleBgImg(backgroundImage);
            couponBubble.setCouponDesc(inductionText);
            couponBubble.setJumpUrl(jumpLink);
            couponBubble.setCouponId(0l);
            couponBubble.setCutPrice(0);
            couponBubble.setLimitPrice(0);
            couponBubble.setName("");
            couponBubble.setDecorate("");
            couponBubble.setDiscount(0);
            couponBubble.setMaxDecrease(0l);
            couponBubble.setType(1);
            break;
        }
        return couponBubble;

    }


    /**
     * 调用玩法接口查看用户是否可以领取该券 不能领有很多种情况（每日限领啊，是否在人群中啊、券是否过期啊等因素）
     *
     * @param maitData
     * @param userId
     * @param couponIds
     * @return
     */
    private boolean canGetCoupon(List<Map<String, Object>> maitData, Long userId, List<Long> couponIds) {
        boolean ans = false;  //默认不透出
        for (Map<String, Object> skteches : maitData) {
            Object relationKey = skteches.get("relationKey");
            if (relationKey == null) {
                continue;
            }
            QueryContext context = new QueryContext();
            context.setCode("couponGetStatus");
            context.setUserId(userId);
            Map<String, Object> request = new HashMap<>();
            request.put("relationKey", relationKey.toString());
            context.setRequest(request);
            NatashaResult result = dianaLaunchService.execute(context);
            if (result != null && result.getData() != null) {
                try {
                    //解析couponIds
                    HashMap data = (HashMap) result.getData();
                    List<JSONObject> couponList = (List<JSONObject>) data.get("couponList");
                    if (couponList != null && couponList.size() > 0) {
                        for (JSONObject couponMap : couponList) {
                            if (couponMap.get("couponId") != null) {
                                couponIds.add((Long) couponMap.get("couponId"));

                            }
                        }
                    }
                } catch (Exception e) {
                    LOGGER.warn("canGetCoupon解析优惠券列表数据失败.{}", result, e);
                }

            }
            //如果为空证明没有领过
            if (result == null || !result.isSuccess()) {
                continue;
            }
            if (result.isSuccess()) {
                ans = true;
                return ans;  //可以领取
            }
        }

        return ans;
    }

}
