package com.mogujie.detail.module.dailyconfig.provider;

import com.meili.service.shopcenter.domain.entity.ShopInfo;
import com.meili.service.shopcenter.util.ShopInfoTagsUtil;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.*;
import com.mogujie.detail.module.activity.domain.ActivityDO;
import com.mogujie.detail.module.dailyconfig.domain.*;
import com.mogujie.detail.module.itemTags.domain.ItemTagsDO;
import com.mogujie.metabase.spring.client.MetabaseClient;
import com.mogujie.service.item.domain.basic.ItemTagDO;
import com.mogujie.themis.daily.config.DailyConfigClient;
import com.mogujie.themis.daily.dto.DailyConfigInfoResultDTO;
import com.mogujie.themis.daily.dto.DailyConfigTagInfo;
import com.mogujie.themis.daily.query.DailyConfigQuery;
import org.apache.commons.lang3.StringUtils;
import org.mogujie.cayenne.timeclient.TimeClient;
import org.mogujie.cayenne.timeclient.TimeClientFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Set;

/**
 * Created by ansheng on 22/01/03.
 */
@Module(name = "newactivity")
public class NewActivityDOProvider implements IModuleDOProvider<NewActivityDO> {

    private static final Logger LOGGER = LoggerFactory.getLogger(NewActivityDOProvider.class);

    @Override
    public NewActivityDO emit(DetailContext context) {
        List<ItemTagDO> tagDOS = context.getItemDO().getItemTags();
        ShopInfo shopInfo = context.getItemDO().getShopInfo();
        String shopTags = shopInfo.getTags();
        String itemMarks = getNumTags(tagDOS);

        DailyConfigQuery query = DailyConfigQuery.builder()
                .bizTag("detail_activity_title_image")
                .seatId(1028)
                .itemMarks(itemMarks)
                .shopMarks(shopTags.replace(',',' ')) //以全链路氛围需要的格式进行输入
                .cids(context.getItemDO().getCids())
                .build();

        query.setExtra(new HashMap<>());
        DailyConfigInfoResultDTO resultDTO = DailyConfigClient.getDailyConfig(query);
        NewActivityDO newActivityDO = new NewActivityDO();


        if (resultDTO == null || resultDTO.getDailyConfigTagInfoList() == null || resultDTO.getDailyConfigTagInfoList().isEmpty()) {
            return null;
        }
        DailyConfigTagInfo dailyConfigTagInfo = resultDTO.getDailyConfigTagInfoList().get(0);

        if (dailyConfigTagInfo != null) {
            newActivityDO.setImg(dailyConfigTagInfo.getImage());
            newActivityDO.setH(Long.valueOf(String.valueOf(dailyConfigTagInfo.getExtraMap().get("h"))));
            newActivityDO.setW(Long.valueOf(String.valueOf(dailyConfigTagInfo.getExtraMap().get("w"))));
            newActivityDO.setSort(Long.valueOf(String.valueOf(dailyConfigTagInfo.getExtraMap().get("sort"))));
            newActivityDO.setStyleType(String.valueOf(dailyConfigTagInfo.getExtraMap().get("styleType")));
            return newActivityDO;
        }
        return null;
    }


    @Override
    public void init() throws DetailException {
        try {
        } catch (Exception e) {
            LOGGER.error("init service failed : {}", e);
        }
    }


    private String getNumTags(List<ItemTagDO> itemTagList) {
        if (itemTagList == null || itemTagList.size() == 0) {
            return null;
        }
        List<String> numTags = new ArrayList<>();
        for (ItemTagDO tag : itemTagList) {
            if ("tags".equals(tag.getTagKey())) {
                try {
                    numTags.add(tag.getTagValue());
                } catch (Throwable ignore) {
                }
            }
        }
        return StringUtils.join(numTags, ' ');
    }
}
