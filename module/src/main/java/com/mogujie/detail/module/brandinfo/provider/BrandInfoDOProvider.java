package com.mogujie.detail.module.brandinfo.provider;

import com.mogujie.brandcenter.api.BrandInfoApiService;
import com.mogujie.brandcenter.api.domain.BrandInfoDto;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.module.brandinfo.domain.BrandInfoDO;
import com.mogujie.detail.module.buyuser.domain.BuyuserInfoDO;
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO;
import com.mogujie.market.base.model.ResultBase;
import com.mogujie.service.muser.api.v1.UserService;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @auther huasheng
 * @time 19/4/10 11:55
 */
@Module(name = "brandInfo")
public class BrandInfoDOProvider implements IModuleDOProvider<BrandInfoDO> {


    private BrandInfoApiService brandInfoApiService;

    private static final Logger LOGGER = LoggerFactory.getLogger(BrandInfoDOProvider.class);

    @Override
    public BrandInfoDO emit(DetailContext context) {
        if (context.isDyn()) {
            return null;
        }
        DetailItemDO detailItemDO = context.getItemDO();
        BrandInfoDO brandInfoDO = getBrandInfo(detailItemDO.getBrandId());
        if (brandInfoDO != null) {
            String showName = StringUtils.isBlank(brandInfoDO.getCname()) ? brandInfoDO.getEname() : brandInfoDO.getCname();
            brandInfoDO.setShowName(showName);
        }
        return brandInfoDO;
    }

    @Override
    public void init() throws DetailException {
        try {
            brandInfoApiService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(BrandInfoApiService.class);
        } catch (Exception ex) {
            LOGGER.error("init module failed, {}", ex);
            throw new DetailException(ex);
        }
    }

    /**
     * 品牌信息
     *
     * @param brandId
     * @return
     */
    private BrandInfoDO getBrandInfo(Long brandId) {
        if (brandId == null || brandId <= 0) {
            return null;
        }
        ResultBase<BrandInfoDto> brandInfoDTOResult = brandInfoApiService.getBrandBaseInfoById(brandId);
        if (!brandInfoDTOResult.hasSuccessValue()) {
            return null;
        }
        BrandInfoDO brandInfoDO = new BrandInfoDO();

        BrandInfoDto brandInfoDto = brandInfoDTOResult.getValue();
        brandInfoDO.setId(brandInfoDto.getId());
        brandInfoDO.setEname(brandInfoDto.getEname());
        brandInfoDO.setCname(brandInfoDto.getCname());
        return brandInfoDO;
    }

}
