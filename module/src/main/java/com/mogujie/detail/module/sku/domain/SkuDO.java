package com.mogujie.detail.module.sku.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import com.mogujie.detail.core.adt.OtherPrice;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON>iaoyao on 16/8/17.
 */
public class SkuDO implements ModuleDO {

    @Setter
    @Getter
    private String title;

    /**
     * sku列表
     */
    @Setter
    @Getter
    private List<SkuData> skus;

    /**
     * sku属性表
     */
    @Setter
    @Getter
    private List<PropInfo> props;

    /**
     * sku属性名: "款式"、"颜色"、"样式"
     */
    @Setter
    @Getter
    private String styleKey;

    /**
     * sku属性名: "尺码"、"大小"
     */
    @Setter
    @Getter
    private String sizeKey;

    /**
     * 价格区间
     */
    @Setter
    @Getter
    private String priceRange;

    @Getter
    @Setter
    private String defaultPrice;

    /**
     * 总剩余库存(所有sku当前库存之和)
     */
    @Setter
    @Getter
    private int totalStock;

    /**
     * 预售定金
     */
    @Setter
    @Getter
    private String mainPriceStr;

    /**
     * 预售总价
     */
    @Setter
    @Getter
    private String subPriceStr;

    /**
     * 是否可分期
     */
    @Getter
    @Setter
    private boolean canInstallment;

    /**
     * 限购总库存
     */
    @Getter
    @Setter
    private Integer limitTotalStock;

    /**
     * 限购数量
     */
    @Getter
    @Setter
    private Integer limitNum;

    /**
     * 限购描述
     */
    @Getter
    @Setter
    private String limitDesc;

    @Getter
    @Setter
    private String highNowPrice;


    @Getter
    @Setter
    private String lowNowPrice;

    /**
     * 最高免息期数
     */
    @Getter
    @Setter
    private Integer freePhases;

    /**
     * App分期、先试后买的用户综合状态
     */
    @Getter
    @Setter
    private InstallmentState installmentState;

    /**
     * 京东sku库存剩余
     */
    @Getter
    @Setter
    private Map<String, Integer> jdSkuStock;

    /**
     * 收获地址信息
     */
    @Getter
    @Setter
    private AddressInfo addressInfo;

    /**
     * 其他价格
     */
    @Getter
    @Setter
    private List<OtherPrice> otherPrices;

    /**
     * 白付美sku文案动态
     */
    @Getter
    @Setter
    private Integer installmentTip;

    /**
     * 白付美剩余额度，包含可用固定额度和可用分期额度
     */
    @Getter
    @Setter
    private Long availableQuota;

    /**
     * 优惠后价格：
     */
    @Getter
    @Setter
    private Long promotionPrice;
}

