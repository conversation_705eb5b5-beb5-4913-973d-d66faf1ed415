package com.mogujie.detail.module.sku.util;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.module.sku.domain.SkuDO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * Created by anshi on 2018/5/29.
 */
@Component
public class JdSkuTool {
    private static final Logger LOGGER = LoggerFactory.getLogger(JdSkuTool.class);

    @PostConstruct
    public void init() throws Exception {
        try {
        } catch (Exception e) {
            LOGGER.error("init jd service failed : {}", e);
            throw e;
        }
    }

    /**
     * 如果是京东商品，则设置京东库存
     *
     * @param context
     * @param skuDO
     */
    public void decorateJDStockInfo(DetailContext context, SkuDO skuDO) {
        return;
    }
}
