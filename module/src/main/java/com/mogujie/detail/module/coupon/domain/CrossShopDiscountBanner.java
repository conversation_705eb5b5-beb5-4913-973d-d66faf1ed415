package com.mogujie.detail.module.coupon.domain;

import lombok.Data;

/**
 * Created by eryi
 * Date: 2020/9/21
 * Time: 11:41 AM
 * Introduction: 新版跨店满减banner
 * Document:
 * 新版跨店满减：https://mogu.feishu.cn/docs/doccnmVTUUPqZNjPzkFVGeCGwXg
 * 不是在优惠券信息的那快，而是在商品标题的上方有个类似banner的条。
 * 最大区别：之前的跨店满减（也叫购物金，平台出钱）。新版跨店满减（商家出钱）。
 * Actions:
 */
@Data
public class CrossShopDiscountBanner {

    /**
     * 新版跨店满减banner链接
     */
    private String imageUrl;

    /**
     * app端平台级凑单图墙跳转链接
     */
    private String appUrl;

    /**
     * 小程序端平台级凑单图墙跳转链接
     */
    private String xcxUrl;

}
