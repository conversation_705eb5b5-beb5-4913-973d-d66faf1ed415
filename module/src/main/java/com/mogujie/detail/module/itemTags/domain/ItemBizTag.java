package com.mogujie.detail.module.itemTags.domain;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * Created by xiaoyao on 16/8/16.
 */
public class ItemBizTag implements Serializable {

    @Getter
    @Setter
    private String icon;

    @Getter
    @Setter
    private String name;

    @Getter
    @Setter
    private String content;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ItemBizTag)) return false;

        ItemBizTag that = (ItemBizTag) o;

        if (getIcon() != null ? !getIcon().equals(that.getIcon()) : that.getIcon() != null) return false;
        if (getName() != null ? !getName().equals(that.getName()) : that.getName() != null) return false;
        return getContent() != null ? getContent().equals(that.getContent()) : that.getContent() == null;

    }

    @Override
    public int hashCode() {
        int result = getIcon() != null ? getIcon().hashCode() : 0;
        result = 31 * result + (getName() != null ? getName().hashCode() : 0);
        result = 31 * result + (getContent() != null ? getContent().hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "ItemBizTag{" +
                "icon='" + icon + '\'' +
                ", name='" + name + '\'' +
                ", content='" + content + '\'' +
                '}';
    }
}
