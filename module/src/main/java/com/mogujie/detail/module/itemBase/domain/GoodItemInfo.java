package com.mogujie.detail.module.itemBase.domain;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/3/28.
 */
public class GoodItemInfo {
    private String subtitle;
    private String image;
    private List<ImageDesc> imageDescs;
    private GoodItemInfo.LeVideo leVideo;

    public GoodItemInfo() {
    }

    public String getSubtitle() {
        return this.subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public String getImage() {
        return this.image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public List<ImageDesc> getImageDescs() {
        return this.imageDescs;
    }

    public void setImageDescs(List<ImageDesc> imageDescs) {
        this.imageDescs = imageDescs;
    }

    public GoodItemInfo.LeVideo getLeVideo() {
        return this.leVideo;
    }

    public void setLeVideo(GoodItemInfo.LeVideo leVideo) {
        this.leVideo = leVideo;
    }

    public String toString() {
        return "GoodItem{subtitle=\'" + this.subtitle + '\'' + ", image=\'" + this.image + '\'' + ", imageDescs=" + this.imageDescs + ", leVideo=" + this.leVideo + '}';
    }

    public static final class LeVideo {
        private Long videoId;
        private String videoUnique;
        private String image;
        private String tencentVideoId;

        public LeVideo() {
        }

        public Long getVideoId() {
            return this.videoId;
        }

        public void setVideoId(Long videoId) {
            this.videoId = videoId;
        }

        public String getVideoUnique() {
            return this.videoUnique;
        }

        public void setVideoUnique(String videoUnique) {
            this.videoUnique = videoUnique;
        }

        public String getImage() {
            return this.image;
        }

        public void setImage(String image) {
            this.image = image;
        }

        public String getTencentVideoId() {
            return tencentVideoId;
        }

        public void setTencentVideoId(String tencentVideoId) {
            this.tencentVideoId = tencentVideoId;
        }

        public String toString() {
            return "Video{videoId=\'" + this.videoId + '\'' + ", image=\'" + this.image + '\'' + '}';
        }
    }

    public static final class ImageDesc {
        private String image;
        private String desc;

        public ImageDesc() {
        }

        public String getImage() {
            return this.image;
        }

        public void setImage(String image) {
            this.image = image;
        }

        public String getDesc() {
            return this.desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        public String toString() {
            return "ImageDesc{image=\'" + this.image + '\'' + ", desc=\'" + this.desc + '\'' + '}';
        }
    }
}
