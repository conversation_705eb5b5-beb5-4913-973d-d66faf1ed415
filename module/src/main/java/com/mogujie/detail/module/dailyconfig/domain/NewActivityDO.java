package com.mogujie.detail.module.dailyconfig.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import lombok.Getter;
import lombok.Setter;

public class NewActivityDO implements ModuleDO {

    /**
     * 氛围标签类型 1 表示图片,目前只支持1就可以
     */
    @Getter
    @Setter
    private String styleType;

    /**
     * 图片
     */
    @Getter
    @Setter
    private String img;

    /**
     * 排序值,客户端可以忽略
     */
    @Getter
    @Setter
    private Long sort;


    /**
     * 图片宽
     */
    @Getter
    @Setter
    private Long w;

    /**
     * 图片高
     */
    @Getter
    @Setter
    private Long h;
}
