package com.mogujie.detail.module.buyershop.provider;

import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.constant.Platform;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.ImageUtil;
import com.mogujie.detail.core.util.TagUtil;
import com.mogujie.detail.module.buyershop.domain.BuyershopDO;
import com.mogujie.detail.module.buyershop.domain.ShareUserInfo;
import com.mogujie.msd.api.MSDCommissionService;
import com.mogujie.msd.api.MSDItemService;
import com.mogujie.msd.api.MSDResellerService;
import com.mogujie.msd.api.MSDUserService;
import com.mogujie.msd.domain.dto.ItemCommissionRateDTO;
import com.mogujie.msd.domain.dto.MSDResellerInfoDTO;
import com.mogujie.msd.domain.dto.MSDResponse;
import com.mogujie.msd.domain.pojo.ItemCommissionRateQuery;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.service.item.domain.basic.ItemTagDO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.OptionalInt;

/**
 * 美丽买手店专用数据 @达林 @西风
 * Created by anshi on 2018/5/23.
 */
@Module(name = "buyershop")
public class BuyershopDOProvider implements IModuleDOProvider<BuyershopDO> {

    private static final Logger logger = LoggerFactory.getLogger(BuyershopDOProvider.class);

    // 美丽买手店礼包商品数字标
    private static final Integer MAISHOU_GIFT_TAG = 1897;

    private static final Integer DAY_SECONDS = 86400;

    // 店主信息查询接口
    private MSDResellerService msdResellerService;

    // 佣金计算接口
    private MSDCommissionService msdCommissionService;

    // 商品精选
    private MSDItemService msdItemService;

    // 用户vip信息接口
    private MSDUserService msdUserService;

    @Override
    public BuyershopDO emit(DetailContext context) {
        return null;
//        //初始化设置分享者id
//        Long shareId = null;
//        MSDResellerInfoDTO currentUserInfo = null;
//        MSDResellerInfoDTO sharedUserInfo = null;
//        if (StringUtils.isNotBlank(context.getParam("shareId"))) {
//            //H5的场景是由前端传过来的
//            shareId = IdConvertor.urlToId(context.getParam("shareId"));
//        } else if (context.getLoginUserId() != null && context.getRouteInfo().getPlatform() == Platform.APP) {
//            //App等场景是根据当前登录用户获取其绑定的分享者（如果当前用户是店主，这里会返回自己的信息）
//            MSDResponse<MSDResellerInfoDTO> sharerInfo = msdResellerService.getBossInfo(context.getLoginUserId());
//            if (sharerInfo != null && sharerInfo.isSuccess() && sharerInfo.getData() != null) {
//                shareId = sharerInfo.getData().getUserId();
//            }
//        }
//        //当没有分享者的时候，这里默认用官方号
//        if (shareId == null) {
//            MSDResponse<MSDResellerInfoDTO> officialAccount = msdResellerService.getOfficialResellerInfo();
//            if (officialAccount != null && officialAccount.isSuccess() && officialAccount.getData() != null) {
//                shareId = officialAccount.getData().getUserId();
//            }
//        }
//        if (shareId == null) {
//            return null;
//        }
//        try {
//            BuyershopDO buyershopDO = new BuyershopDO();
//            //设置买手店会员价相关逻辑
//            decoratePriceRange(buyershopDO, context);
//            if (context.getLoginUserId() != null) {
//                MSDResponse<Boolean> userVipResponse = msdUserService.hasBecomeVip(context.getLoginUserId());
//                if (userVipResponse != null && userVipResponse.isSuccess()) {
//                    buyershopDO.setVipUser(userVipResponse.getData());
//                }
//            }
//            //设置分享者信息
//            MSDResponse<MSDResellerInfoDTO> resellerInfoDTO = msdResellerService.getMyselfInfo(shareId);
//            if (resellerInfoDTO != null && resellerInfoDTO.isSuccess() && resellerInfoDTO.getData() != null) {
//                MSDResellerInfoDTO sellerInfoDTO = resellerInfoDTO.getData();
//                sharedUserInfo = sellerInfoDTO;
//                ShareUserInfo shareUserInfo = new ShareUserInfo();
//                shareUserInfo.setUserId(sellerInfoDTO.getUserId());
//                shareUserInfo.setUserIdUrl(IdConvertor.idToUrl(sellerInfoDTO.getUserId()));
//                shareUserInfo.setAvatar(ImageUtil.img(sellerInfoDTO.getAvatar()));
//                shareUserInfo.setName(sellerInfoDTO.getName());
//                shareUserInfo.setDescription(sellerInfoDTO.getDescription());
//                shareUserInfo.setInviteCode(sellerInfoDTO.getInviteCode());
//                buyershopDO.setShareUserInfo(shareUserInfo);
//            }
//
//            //设置当前用户信息
//            if (context.getLoginUserId() != null
//                    && sharedUserInfo != null
//                    && context.getLoginUserId().intValue() == sharedUserInfo.getUserId().intValue()) {
//                // 当前登录用户和分享者是同一个人时
//                currentUserInfo = sharedUserInfo;
//            }
//            if (currentUserInfo != null) {
//                ShareUserInfo currentSharerInfo = new ShareUserInfo();
//                currentSharerInfo.setUserId(currentUserInfo.getUserId());
//                currentSharerInfo.setAvatar(ImageUtil.img(currentUserInfo.getAvatar()));
//                currentSharerInfo.setName(currentUserInfo.getName());
//                currentSharerInfo.setDescription(currentUserInfo.getDescription());
//                currentSharerInfo.setInviteCode(currentUserInfo.getInviteCode());
//                buyershopDO.setCurrentUserInfo(currentSharerInfo);
//            }
//
//            if (context.getLoginUserId() != null) {
//                try {
//                    //设置当前用户是否为店主
//                    MSDResponse<Boolean> msdResponse = msdResellerService.hasBecomeReseller(context.getLoginUserId());
//                    if (msdResponse != null && msdResponse.isSuccess() && msdResponse.getData() != null) {
//                        buyershopDO.setSeller(msdResponse.getData());
//                        //如果当前用户是店主，则设置其邀请码
//                        if (msdResponse.getData() && currentUserInfo != null) {
//                            buyershopDO.setInviteCode(currentUserInfo.getInviteCode());
//                        }
//                    }
//
//                    //当前商品是否已被加入精选
//                    MSDResponse<Boolean> isCollectedRet = msdItemService.hasRecommendThisItem(context.getLoginUserId(), context.getItemId());
//                    if (isCollectedRet != null && isCollectedRet.isSuccess() && isCollectedRet.getData() != null) {
//                        buyershopDO.setHasRecommendThisItem(isCollectedRet.getData());
//                    }
//                } catch (Throwable e) {
//                    logger.error("set buyershop 'isSeller' error!", e);
//                }
//            }
//
//            //设置商品的佣金
//            DetailItemDO itemDO = context.getItemDO();
//            if (buyershopDO.isSeller()) {
//                ItemCommissionRateQuery query = new ItemCommissionRateQuery();
//                query.setItemId(itemDO.getItemId());
//                MSDResponse<ItemCommissionRateDTO> commissionRateDTOMSDResponse = msdCommissionService.getCommissionByItemId(query);
//                if (commissionRateDTOMSDResponse != null && commissionRateDTOMSDResponse.isSuccess() && commissionRateDTOMSDResponse.getData() != null) {
//                    ItemCommissionRateDTO itemCommissionRateDTO = commissionRateDTOMSDResponse.getData();
//                    //佣金比例(15.11% -> 1511)
//                    int rate = itemCommissionRateDTO.getCommissionRate1();
//                    int minPrice = Integer.MAX_VALUE;
//                    int maxPrice = Integer.MIN_VALUE;
//                    //设置当前商品的佣金
//                    try {
//                        for (Long skuPrice : itemDO.getNormalUserPrice().values()) {
//                            int sp = skuPrice.intValue();
//                            minPrice = sp < minPrice ? sp : minPrice;
//                            maxPrice = sp > maxPrice ? sp : maxPrice;
//                        }
//                    } catch (Throwable e) {
//                        for (ItemSkuDO skuDO : itemDO.getItemSkuDOList()) {
//                            minPrice = skuDO.getNowPrice() < minPrice ? skuDO.getNowPrice() : minPrice;
//                            maxPrice = skuDO.getNowPrice() > maxPrice ? skuDO.getNowPrice() : maxPrice;
//                        }
//                    }
//                    buyershopDO.setMaxCommission(maxPrice * rate / 10000);
//                    buyershopDO.setMinCommission(minPrice * rate / 10000);
//                }
//            }
//            //如果是限时快抢开始前48小时内，则需要"是否已经设置过提醒"字段
//            //根据商品上的限时快抢标来判断是否处于预热中
//            int startTime = Integer.MAX_VALUE;
//            if (itemDO.getItemTags() != null) {
//                Optional<ItemTagDO> optional = itemDO.getItemTags().stream()
//                        .filter(tag -> "msdFlashSale".equalsIgnoreCase(tag.getTagKey()))
//                        .findAny();
//                if (optional.isPresent()) {
//                    OptionalInt st = Arrays.stream(optional.get().getTagValue().split("\\|"))
//                            .filter(pair -> pair.startsWith("st:"))
//                            .mapToInt(pair -> Integer.parseInt(pair.split(":")[1])).findAny();
//                    if (st.isPresent()) {
//                        startTime = st.getAsInt();
//                    }
//                }
//            }
//            if (context.getLoginUserId() != null
//                    && ((int) (System.currentTimeMillis() / 1000) + (2 * DAY_SECONDS)) > startTime) {
//                MSDResponse<Boolean> setRemindResult = msdItemService.hasSetRemind(context.getLoginUserId(), context.getItemId(), null);
//                if (setRemindResult != null && setRemindResult.isSuccess() && setRemindResult.getData() != null) {
//                    buyershopDO.setHasSetRemind(setRemindResult.getData());
//                }
//            }
//            buyershopDO.setGiftItem(TagUtil.isContainsTag(itemDO, MAISHOU_GIFT_TAG));
//            return buyershopDO;
//        } catch (Throwable e) {
//            logger.error("get buyershop info error!", e);
//            return null;
//        }
    }

//    public void decoratePriceRange(BuyershopDO buyershopDO, DetailContext context) {
//        DetailItemDO itemDO = context.getItemDO();
//        Map<Long, Long> memberPriceMap = itemDO.getMemberPrice();
//        if (MapUtils.isNotEmpty(memberPriceMap)) {
//            long max = Long.MIN_VALUE;
//            long min = Long.MAX_VALUE;
//            for (Long price : memberPriceMap.values()) {
//                max = max > price ? max : price;
//                min = min < price ? min : price;
//            }
//            buyershopDO.setHighVipUserPrice(max);
//            buyershopDO.setLowVipUserPrice(min);
//        }
//        Map<Long, Long> normalUserPriceMap = itemDO.getNormalUserPrice();
//        if (MapUtils.isNotEmpty(normalUserPriceMap)) {
//            long max = Long.MIN_VALUE;
//            long min = Long.MAX_VALUE;
//            for (Long price : normalUserPriceMap.values()) {
//                max = max > price ? max : price;
//                min = min < price ? min : price;
//            }
//            buyershopDO.setHighNormalUserPrice(max);
//            buyershopDO.setLowNormalUserPrice(min);
//        }
//    }

    @Override
    public void init() throws DetailException {
    }
}
