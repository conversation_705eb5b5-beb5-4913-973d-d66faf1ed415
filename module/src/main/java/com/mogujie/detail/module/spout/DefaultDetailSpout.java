package com.mogujie.detail.module.spout;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.meili.service.shopcenter.domain.entity.ShopInfo;
import com.meili.service.shopcenter.enums.ShopStatus;
import com.meili.service.shopcenter.util.ShopInfoTagsUtil;
import com.meili.service.shopcenter.util.ShopUtils;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.ChannelMeta;
import com.mogujie.detail.core.adt.ChannelTag;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IDetailSpout;
import com.mogujie.detail.core.adt.RouteInfo;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.ItemTag;
import com.mogujie.detail.core.constant.Platform;
import com.mogujie.detail.core.constant.SwitchKey;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.manager.ThreadPoolExecutors;
import com.mogujie.detail.core.spi.SpiAutowired;
import com.mogujie.detail.core.task.AbstractCollectDataTask;
import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.core.util.ContextUtil;
import com.mogujie.detail.module.shop.util.WaitressUtil;
import com.mogujie.detail.module.spout.spi.ICommonDataProvider;
import com.mogujie.metabase.spring.client.MetabaseClient;
import com.mogujie.service.inventory.api.InventoryReadService;
import com.mogujie.service.item.api.basic.ItemReadService;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.basic.ItemExtraDO;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import com.mogujie.service.item.domain.basic.SkuAttributionDO;
import com.mogujie.service.item.domain.query.QueryItemOptions;
import com.mogujie.service.item.domain.result.BaseResultDO;
import com.mogujie.service.tagcenter.api.read.ItemTagReadService;
import com.mogujie.service.tagcenter.domain.entity.query.ItemTagQueryOption;
import com.mogujie.service.tagcenter.domain.entity.result.ItemTagDO;
import com.mogujie.service.tangram.domain.entity.BaseValue;
import com.mogujie.service.tangram.property.domain.CategorySkuDTO;
import com.mogujie.service.tangram.property.service.CategoryPropertyReadClient;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import com.mogujie.themis.camp.config.CampTagConfigClient;
import com.mogujie.themis.config.BaseInfo;
import com.mogujie.trace.threadable.LurkerRunnableAdaptor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * Created by anshi on 17/3/8.
 */
@Component
public class DefaultDetailSpout implements IDetailSpout {

    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultDetailSpout.class);

    private static final int COMMON_MAX_THREAD_NUM = 256;

    private static final int COMMON_TASK_QUEUE_LENGTH = 128;

    private static final int COMMON_CORE_THREAD_NUM = 128;

    private static final String CHANNEL_META_PREFFIX = "channelMetaInfo_";

    private static final String ACTIVITYID_PARAM_KEY = "activityId";

    private static final String PROMOTION_IMAGE_TAG_KEY = "itemPromotionImageTag";

    @Autowired
    private ItemReadService itemReadService;

    private ItemTagReadService itemTagReadService;

    private InventoryReadService inventoryReadService;

    @Resource(name = "confMetabaseClient")
    private MetabaseClient metabaseClient;

    @Resource(name = "detailTemplate")
    private MetabaseClient detailTemplate;

    @Autowired
    private CommonSwitchUtil commonSwitchUtil;

    @SpiAutowired
    private ICommonDataProvider commonDataProvider;

    @Autowired
    private CategoryPropertyReadClient categoryPropertyReadClient;

    private static Gson gson = new Gson();

    @PostConstruct
    public void init() throws Exception {
        inventoryReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(InventoryReadService.class);
        itemTagReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ItemTagReadService.class);
    }


    /**
     * 将ItemDO塞入context
     *
     * @param context
     * @throws DetailException
     */

    @Override
    public void decorateItemDO(DetailContext context) throws DetailException {
        QueryItemOptions queryItemOptions = new QueryItemOptions();
        queryItemOptions.setIncludeItemExtraInfo(true);
        queryItemOptions.setQueryBasicItem(true);
        queryItemOptions.setQueryInventory(true);
        queryItemOptions.setQuerySkuInfo(true);
        queryItemOptions.setIncludeDeleted(false);
        queryItemOptions.setQueryItemDetail(true);
        queryItemOptions.setQueryPresaleDO(true);
        queryItemOptions.setQuerySkuAttribute(true);
        queryItemOptions.setQueryTgrcStock(true);
        queryItemOptions.setQueryExpressTmpl(true);
        queryItemOptions.setQueryItemIdPropertyMap(true);
        queryItemOptions.setQueryTopImage(true);
        queryItemOptions.setQueryItemTag(true);
//        if (context.getRouteInfo().getApp()==App.MSD){
//            queryItemOptions.setMarketplaceId(14);
//        }
        try {
            BaseResultDO<ItemDO> ret = itemReadService.queryItemById(context.getItemId(), queryItemOptions);
            if (null == ret || !ret.isSuccess() || null == ret.getResult()) {
                return;
            }
            ItemDO item = ret.getResult();
            item.setItemSkuDOList(reorderSkus(item));
            // 一分钱商品,在普通详情页展示原商品数据
            if (isOneCentItem(item.getJsonExtra()) && context.getRouteInfo().getBizType() != BizType.ONECENT) {
                Long itemId = getOneCentOrigiItemId(item.getJsonExtra());
                if (null == itemId) {
                    return;
                }
                context.setItemId(itemId);
                ret = itemReadService.queryItemById(itemId, queryItemOptions);
                if (null == ret || !ret.isSuccess() || null == ret.getResult()) {
                    LOGGER.warn("onecent original item not found : {}", context.getItemId());
                    return;
                }
                item = ret.getResult();
            }
            DetailItemDO detailItemDO = new DetailItemDO(item);
            context.setItemDO(detailItemDO);

            ItemExtraDO itemExtraDO = item.getItemExtraDO();
            if (itemExtraDO != null && itemExtraDO.getFeatures() != null) {
                detailItemDO.setFeatures(itemExtraDO.getFeatures());
            }

            decorateChannelMetaAndTag(context);
            resetPromotionImage(context);
            List<AbstractCollectDataTask> collectDataTasks = commonDataProvider.listStaticCollectDataTask(context);
            CountDownLatch latch = new CountDownLatch(collectDataTasks.size());
            for (AbstractCollectDataTask collectDataTask : collectDataTasks) {
                collectDataTask.setCountDownLatch(latch);
                ThreadPoolExecutors.commonExecutorService.submit(new LurkerRunnableAdaptor(collectDataTask));
            }
            try {
                latch.await(1000, TimeUnit.MILLISECONDS);
            } catch (InterruptedException e) {
                LOGGER.error("wait for async emit failed {}", e);
            }

            ShopInfo shopInfo = detailItemDO.getShopInfo();
            if (null != shopInfo) {
                if (ShopStatus.isOffline(shopInfo.getStatus())) {
                    context.setItemDO(null);
                    return;
                }
                Set<Integer> shopTags = ShopInfoTagsUtil.stringToSet(shopInfo.getTags());
                if (!CollectionUtils.isEmpty(shopTags) && shopTags.contains(ShopUtils.EXCELLENT_SHOP_TAG)) {
                    if (null == detailItemDO.getItemBizTags()) {
                        List<ItemTag> itemTags = new ArrayList<>();
                        detailItemDO.setItemBizTags(itemTags);
                    }
                    detailItemDO.getItemBizTags().add(ItemTag.MOGU_SELECTION);
                }
                if (!CollectionUtils.isEmpty(shopTags) && shopTags.contains(ShopUtils.QUALITY_TAG)) {
                    if (null == detailItemDO.getItemBizTags()) {
                        List<ItemTag> itemTags = new ArrayList<>();
                        detailItemDO.setItemBizTags(itemTags);
                    }
                    detailItemDO.getItemBizTags().add(ItemTag.GOOD);
                }
            }
            //仓库标
            ItemTag tag = getItemStoreType(detailItemDO.getJsonExtra());
            if (tag != null) {
                if (null == detailItemDO.getItemBizTags()) {
                    List<ItemTag> itemTags = new ArrayList<>();
                    detailItemDO.setItemBizTags(itemTags);
                }
                detailItemDO.getItemBizTags().add(tag);
            }

            RouteInfo routeInfo = context.getRouteInfo();
            //这里设置的上下文参数`contain30dayDeliveryService`，是为了春节打烊的需求。带有30天发货服务的商品，不走打烊需求逻辑
            //非直播sku选择器场景不用走这段逻辑，纯粹为了减少waitress服务的压力
            if (!(routeInfo.getBizType() == BizType.SKU && routeInfo.getVersion().startsWith("live."))) {
                context.addContext(ContextUtil.IS_30_DAY_DELIVERY_ITEM, WaitressUtil.contains30dayDelivery(context));
            }
        } catch (Exception e) {
            LOGGER.error("unknown exception!", e);
            throw new DetailException(e);
        }
    }

    private static Boolean isOneCentItem(String extra) {
        if (StringUtils.isEmpty(extra)) {
            return false;
        }
        Map<String, String> extraInfo = gson.fromJson(extra, HashMap.class);
        if (null == extraInfo || extraInfo.isEmpty()) {
            return false;
        }
        String oneCentStr = extraInfo.get("yf");

        if (!StringUtils.isEmpty(oneCentStr)) {
            return true;
        }

        return false;
    }

    private Boolean hideBaihuoItem(DetailContext context, ItemDO itemDO) {
        if (null != itemDO && itemDO.getVerticalMarket() == 12L) {
            RouteInfo routeInfo = context.getRouteInfo();
            if (App.BH.equals(routeInfo.getApp())) {
                return false;
            }
            return true;
        }
        return false;
    }

    /**
     * 获取仓库类型
     *
     * @param extra
     * @return
     */
    private ItemTag getItemStoreType(String extra) {
        if (StringUtils.isEmpty(extra)) {
            return null;
        }
        Map<String, String> extraInfo = gson.fromJson(extra, HashMap.class);
        if (null == extraInfo || extraInfo.isEmpty()) {
            return null;
        }
        // wh标
        String instoreStr = extraInfo.get("wh");
        if (!StringUtils.isEmpty(instoreStr)) {
            JSONObject instoreObj = JSON.parseObject(instoreStr);
            if ("INBOUND".equals(instoreObj.getString("turnoverType"))) {
                return ItemTag.INSTORE;
            }
            if ("JIT".equals(instoreObj.getString("turnoverType"))) {
                return ItemTag.TRANSFER;
            }
        }

        // utg标
        instoreStr = extraInfo.get("utg");
        ItemTag tag = getStoreType(instoreStr);
        if (tag != null) {
            return tag;
        }

        // ptg标
        instoreStr = extraInfo.get("ptg");
        tag = getStoreType(instoreStr);
        if (tag != null) {
            return tag;
        }

        // tg标
        instoreStr = extraInfo.get("tg");
        tag = getStoreType(instoreStr);
        if (tag != null) {
            return tag;
        }

        return null;
    }

    private static ItemTag getStoreType(String tag) {
        int type = -1;
        Long start = null, end = null;
        if (StringUtils.isEmpty(tag)) {
            return null;
        }
        String[] hdInfoPairs = tag.split("\\|");
        for (String hdInfoPair : hdInfoPairs) {
            String[] hd = hdInfoPair.split(":");
            if (hd.length != 2) {
                continue;
            }
            if ("st".equals(hd[0])) {
                start = Long.parseLong(hd[1]);
            } else if ("et".equals(hd[0])) {
                end = Long.parseLong(hd[1]);
            } else if ("at".equals(hd[0])) {
                type = Integer.parseInt(hd[1]);
            }
        }

        int now = (int) (System.currentTimeMillis() / 1000);
        // 不在活动中
        if (start == null || end == null || start > now || now > end) {
            return null;
        }
        if (type == 1) {
            return ItemTag.INSTORE;
        } else if (type == 2) {
            return ItemTag.TRANSFER;
        }
        return null;
    }

    private static Long getOneCentOrigiItemId(String extra) {
        if (StringUtils.isEmpty(extra)) {
            return null;
        }
        Gson gson = new Gson();
        Map<String, String> extraInfo = gson.fromJson(extra, HashMap.class);
        if (null == extraInfo || extraInfo.isEmpty()) {
            return null;
        }
        String oneCentStr = extraInfo.get("yf");

        if (!StringUtils.isEmpty(oneCentStr)) {
            String[] oneCentInfoPairs = oneCentStr.split("\\|");
            if (oneCentInfoPairs.length < 1) {
                return null;
            }
            for (String hdInfoPair : oneCentInfoPairs) {
                String[] hd = hdInfoPair.split(":");
                if (hd.length != 2) {
                    continue;
                }
                if ("it".equals(hd[0])) {
                    return Long.parseLong(hd[1]);
                }
            }
        }
        return null;
    }

    /**
     * 重新排序sku,保证显示顺序与小店后台相同
     *
     * @param itemDO
     * @return
     */
    public List<ItemSkuDO> reorderSkus(ItemDO itemDO) {
        List<ItemSkuDO> skuList = itemDO.getItemSkuDOList();
        if (skuList == null) {
            return null;
        }
        if (!commonSwitchUtil.isOn(SwitchKey.ORDERED_SKU_ATTRS) || skuList.isEmpty()) {
            return skuList;
        }
        try {
            if (itemDO.getCategoryId() == null) {
                return skuList;
            }
            CategorySkuDTO categorySkuDTO = categoryPropertyReadClient.getCategorySku(itemDO.getCategoryId().longValue(), true);
            if (categorySkuDTO == null) {
                return skuList;
            }

            List<ItemSkuDO> orderedSkus = new ArrayList<>();
            if (skuList.get(0).getAttributions() == null || skuList.get(0).getAttributions().size() == 0) {
                return skuList;
            }
            //商品是一维sku
            if (skuList.get(0).getAttributions().size() == 1) {
                // 该维sku的属性名("款式"、"尺码"、"颜色"等)
                String attrKey = skuList.get(0).getAttributions().get(0).getName();
                // 将商品sku以属性值为key,存入map中
                Map<String, ItemSkuDO> skuMap = new LinkedHashMap<>();
                for (ItemSkuDO skuDO : skuList) {
                    SkuAttributionDO attr = skuDO.getAttributions().get(0);
                    skuMap.put(attr.getValue(), skuDO);
                }
                // 获取该维sku所对应的标准属性表
                List<BaseValue> standardValues = null;
                if (categorySkuDTO.getSize() != null && categorySkuDTO.getSize().equals(attrKey)) {
                    standardValues = categorySkuDTO.getSizeValueList();
                } else {
                    standardValues = categorySkuDTO.getStyleValueList();
                }
                // 按照标准属性表的顺序,重新将sku存入list中
                for (BaseValue baseValue : standardValues) {
                    if (skuMap.containsKey(baseValue.getValue())) {
                        orderedSkus.add(skuMap.get(baseValue.getValue()));
                        skuMap.remove(baseValue.getValue());
                    }
                }
                // 剩下的是自定义属性值的sku,一起存入list尾部
                if (!CollectionUtils.isEmpty(skuMap.values())) {
                    orderedSkus.addAll(skuMap.values());
                }
                return orderedSkus;
            } else if (skuList.get(0).getAttributions().size() == 2) {
                //商品是二维sku
                //将sku存入三元组map,key1为style(款式)、key2为size(尺码)
                Map<String, Map<String, ItemSkuDO>> skuMap = new LinkedHashMap<>();
                for (ItemSkuDO skuDO : skuList) {
                    String styleKey = null, sizeKey = null;
                    for (SkuAttributionDO attr : skuDO.getAttributions()) {
                        if (attr.getShowPosition() == 0) {
                            styleKey = attr.getValue();
                        } else {
                            sizeKey = attr.getValue();
                        }
                    }
                    Map<String, ItemSkuDO> sizeSkuMap = skuMap.get(styleKey);
                    if (sizeSkuMap == null) {
                        sizeSkuMap = new LinkedHashMap<>();
                        skuMap.put(styleKey, sizeSkuMap);
                    }
                    sizeSkuMap.put(sizeKey, skuDO);
                }

                //对照标准属性表,将sku重新塞入list
                for (BaseValue styleValue : categorySkuDTO.getStyleValueList()) {
                    if (!skuMap.containsKey(styleValue.getValue())) {
                        continue;
                    }
                    for (BaseValue sizeValue : categorySkuDTO.getSizeValueList()) {
                        if (skuMap.get(styleValue.getValue()) != null
                                && skuMap.get(styleValue.getValue()).get(sizeValue.getValue()) != null) {
                            Map<String, ItemSkuDO> sizeSkuMap = skuMap.get(styleValue.getValue());
                            orderedSkus.add(sizeSkuMap.get(sizeValue.getValue()));
                            sizeSkuMap.remove(sizeValue.getValue());
                            if (sizeSkuMap.isEmpty()) {
                                skuMap.remove(styleValue.getValue());
                            }
                        }
                    }
                }
                // 自定义style属性值的sku,也存入list
                for (Map<String, ItemSkuDO> map : skuMap.values()) {
                    for (BaseValue sizeValue : categorySkuDTO.getSizeValueList()) {
                        if (map.get(sizeValue.getValue()) != null) {
                            orderedSkus.add(map.get(sizeValue.getValue()));
                            map.remove(sizeValue.getValue());
                        }
                    }
                }
                // 剩下的是自定义size属性值的sku,一起存入list尾部
                for (Map<String, ItemSkuDO> map : skuMap.values()) {
                    if (!CollectionUtils.isEmpty(map.values())) {
                        orderedSkus.addAll(map.values());
                    }
                }
                return orderedSkus;
            }

        } catch (Throwable e) {
            LOGGER.error("order sku attrs error", e);
        }
        return skuList;
    }

    public void decorateChannelMetaAndTag(DetailContext context) {
        decorateChannelMeta(context);
        decorateChannelTag(context);
    }

    /**
     * 修饰当前渠道的元数据
     *
     * @param context
     */
    private void decorateChannelMeta(DetailContext context) {
        try {
            if (context.getRouteInfo().getBizType() == BizType.CHANNEL
                    || (context.getRouteInfo().getBizType() == BizType.SKU
                    && context.getRouteInfo().getVersion().startsWith("live.channel"))) {
                String channelMetaKey = CHANNEL_META_PREFFIX + context.getRouteInfo().getChannelType();
                String channelMetaStr = detailTemplate.get(channelMetaKey);
                ChannelMeta channelMeta = gson.fromJson(channelMetaStr, ChannelMeta.class);
                context.setChannelMeta(channelMeta);
            }
        } catch (Throwable e) {
            LOGGER.error("decorate channel meta error.", e);
        }
    }

    /**
     * 刷主图，将主图替换成大促促销条
     *
     * @param context
     */
    private void resetPromotionImage(DetailContext context) {
        try {
            if ((!ContextUtil.isNormalPageDetail(context)
                    || (context.getRouteInfo().getApp() == App.MGJ
                    && context.getRouteInfo().getPlatform() == Platform.H5
                    && !commonSwitchUtil.isOn(SwitchKey.SWT_SHOW_H5_PROMOTION_IMG)))
                    || (context.getRouteInfo().getBizType() == BizType.TTNORMAL)
                    || (isInShangGo(context) && !isDaPeiGou(context))){
                return;
            }
            DetailItemDO itemDO = context.getItemDO();
            if (itemDO.getFeatures() == null || itemDO.getFeatures().get(PROMOTION_IMAGE_TAG_KEY) == null) {
                return;
            }
            String mainImage = CampTagConfigClient.getItemImageUrl(BaseInfo.Channel.DETAIL,
                    ContextUtil.getNumTagString(context.getItemDO()),
                    itemDO.getFeatures().get(PROMOTION_IMAGE_TAG_KEY));
            if (StringUtils.isBlank(mainImage)) {
                return;
            }
            if (!StringUtils.isEmpty(mainImage)) {
                itemDO.setMainImage(mainImage);
                if (CollectionUtils.isNotEmpty(itemDO.getImages())) {
                    itemDO.getImages().get(0).setPath(mainImage);
                }
            }
        } catch (Throwable e) {
            LOGGER.error("get promotion image error.", e);
        }
    }

    /**
     * 是否是搭配购
     * @param context context
     */
    public boolean isDaPeiGou(DetailContext context) {
        return ContextUtil.isDaPeiGou(context);
    }

    /**
     * 是否在闪购正式期
     *
     * @param context
     */
    private boolean isInShangGo(DetailContext context) {
        try {
            long st = 0;
            long et = 0;
            List<com.mogujie.service.item.domain.basic.ItemTagDO> tags = context.getItemDO().getItemTags();
            if (CollectionUtils.isEmpty(tags)){
                return false;
            }
            for (com.mogujie.service.item.domain.basic.ItemTagDO tag : tags) {
                if (tag.getTagKey().equals("shango")) {
                    String[] tagValuePairs = tag.getTagValue().split("\\|");
                    for (String tagValuePair : tagValuePairs) {
                        String[] pair = tagValuePair.split(":");
                        if (pair.length != 2) {
                            continue;
                        }
                        if ("st".equals(pair[0])) {
                            st = Long.parseLong(pair[1]);
                        } else if ("et".equals(pair[0])) {
                            et = Long.parseLong(pair[1]);
                        }
                    }
                }
            }

            Long nowTime = CampTagConfigClient.fakeCurrentTime();
            return st <= nowTime && nowTime <= et;

        } catch (Throwable e) {
            LOGGER.error("verify in shango fail.", e);
        }
        return false;
    }

    /**
     * 解析并修饰当前商品的渠道标
     *
     * @param context
     */
    private void decorateChannelTag(DetailContext context) {
        try {
            if (context.getRouteInfo().getBizType() != BizType.CHANNEL
                    && !(context.getRouteInfo().getBizType() == BizType.SKU && context.getRouteInfo().getVersion().startsWith("live.channel"))) {
                return;
            }
            if (context.getChannelMeta() == null) {
                return;
            }
            //////////////
            ItemTagQueryOption option = new ItemTagQueryOption();
            option.setItemId(context.getItemId());
            option.setIncludedFutureTags(context.getChannelMeta().isQueryFutureTag());
            com.mogujie.service.tagcenter.domain.entity.result.BaseResultDO<List<ItemTagDO>> ret = itemTagReadService.queryItemTag(option);
            if (ret == null || !ret.isSuccess() || CollectionUtils.isEmpty(ret.getResult())) {
                return;
            }
            List<ItemTagDO> tagList = ret.getResult();
            Long activityId = IdConvertor.urlToId(context.getParam(ACTIVITYID_PARAM_KEY));
            ChannelMeta channelMeta = context.getChannelMeta();
            String tagKey = channelMeta.getTagKey();
            for (ItemTagDO tag : tagList) {
                if (tagKey.equals(tag.getTagKey())) {
                    ChannelTag tmpTag = new ChannelTag();
                    String[] pairs = tag.getTagValue().split("\\|");
                    Map<String, String> extraMap = new HashMap<>();
                    for (String pair : pairs) {
                        String[] p = pair.split(":");
                        switch (p[0]) {
                            case "ai":
                                tmpTag.setActivityId(Long.parseLong(p[1]));
                                break;
                            case "ws":
                                tmpTag.setWarmUpTime(Integer.parseInt(p[1]));
                                break;
                            case "st":
                                tmpTag.setStartTime(Integer.parseInt(p[1]));
                                break;
                            case "et":
                                tmpTag.setEndTime(Integer.parseInt(p[1]));
                                break;
                            case "pp":
                                tmpTag.setPrice(Long.parseLong(p[1]));
                                break;
                            case "dc"://目前不会有dc字段
                                tmpTag.setDiscount(Integer.parseInt(p[1]));
                                break;
                            default:
                                extraMap.put(p[0], p[1]);
                                break;
                        }
                    }
                    tmpTag.setExtraMap(extraMap);
                    if (activityId.equals(tmpTag.getActivityId())) {
                        context.setChannelTag(tmpTag);
                        return;
                    }
                }
            }
        } catch (Throwable e) {
            LOGGER.error("decorate channel tag error.", e);
        }
    }
}
