package com.mogujie.detail.module.seckill.provider;

import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.module.seckill.domain.SeckillDO;
import com.mogujie.marketing.ares.domain.entity.SecKillInfo;
import com.mogujie.marketing.remind.RushNocticeServiceApi;
import com.mogujie.marketing.remind.dto.IncrexItemFocusedParam;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by anshi on 17/3/8.
 */
@Module(name = "seckill")
public class SeckillDOProvider implements IModuleDOProvider<SeckillDO> {

    //预热时间300秒
    private static final int WARM_UP_SECONDS = 300;

    @Autowired
    RushNocticeServiceApi rushNocticeServiceApi;

    @Override
    public SeckillDO emit(DetailContext context) {
        SecKillInfo secKillInfo = (SecKillInfo) context.getContext("seckillInfo");
        //platform=0 -> 蘑菇街
        if (secKillInfo == null) {
            return null;
        }

        SeckillDO seckillDO = new SeckillDO();
        seckillDO.setSecKillId(IdConvertor.idToUrl(secKillInfo.getId()));
        seckillDO.setPrice((int) secKillInfo.getPrice());
        seckillDO.setEnrollStock(secKillInfo.getKillStock());
        seckillDO.setStartTime((int) secKillInfo.getKillStartTime());
        seckillDO.setEndTime((int) secKillInfo.getKillEndTime());
        seckillDO.setOuterId(secKillInfo.getOuterId());

        int now = (int) (System.currentTimeMillis() / 1000);
        int startTime = (int) secKillInfo.getKillStartTime();
        int warmUpTime = startTime - WARM_UP_SECONDS;
        int endTime = (int) secKillInfo.getKillEndTime();
        if (now < warmUpTime) {
            seckillDO.setStatus(0);
            seckillDO.setCountdown(startTime - WARM_UP_SECONDS - now);
        } else if (warmUpTime <= now && now < startTime) {
            seckillDO.setStatus(1);
            seckillDO.setCountdown(startTime - now);
        } else if (startTime <= now && now < endTime) {
            seckillDO.setStatus(2);
            seckillDO.setCountdown(endTime - now);
        } else {
            seckillDO.setStatus(3);
        }

        //活动开始前展示关注人数
        int nowTime = (int) (System.currentTimeMillis() / 1000);
        if (nowTime < seckillDO.getStartTime() ) {
            Long secKillId = secKillInfo.getId();
            IncrexItemFocusedParam increxItemFocusedParam = new IncrexItemFocusedParam();
            increxItemFocusedParam.setActivityKey("normal");//仅做业务标识
            increxItemFocusedParam.setActivityId(secKillId);
            rushNocticeServiceApi.increxItemFocusedNum(increxItemFocusedParam);
        }
        return seckillDO;
    }

    @Override
    public void init() throws DetailException {

    }
}
