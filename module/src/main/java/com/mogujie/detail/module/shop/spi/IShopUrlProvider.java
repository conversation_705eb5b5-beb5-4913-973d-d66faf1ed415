package com.mogujie.detail.module.shop.spi;


import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.spi.Exposed;

/**
 * Created by xiaoyao on 16/4/10.
 */
@Exposed
public interface IShopUrlProvider {

    /**
     * 获取店铺链接地址
     * @param context
     * @return
     */
    String getShopUrl(DetailContext context);


    /**
     * 获取店铺所有商品的链接地址
     * @param context
     * @return
     */
    String getShopAllGoodsUrl(DetailContext context);
}
