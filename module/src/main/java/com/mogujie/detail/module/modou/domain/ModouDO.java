package com.mogujie.detail.module.modou.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by anshi on 17/3/20.
 */
public class ModouDO implements ModuleDO {
    /**
     * 类型
     * 1. 超值购
     */
    @Getter
    @Setter
    private int type;

    /**
     * 抵扣金额(元)
     */
    @Getter
    @Setter
    private String offsetPrice;

    /**
     * 0. 未开始
     * 1. 活动中
     * 2. 活动结束
     * 3. 非蘑豆商品
     */
    @Getter
    @Setter
    private Integer state;

    /**
     * 开始时间
     */
    @Getter
    @Setter
    private Integer startTime;

    /**
     * 结束时间
     */
    @Getter
    @Setter
    private Integer endTime;

    /**
     * 蘑豆数量
     */
    @Getter
    @Setter
    private Integer modouAmount;

    /**
     * 总剩余库存
     */
    @Getter
    @Setter
    private Long totalStock;

}
