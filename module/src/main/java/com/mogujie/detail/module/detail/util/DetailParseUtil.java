package com.mogujie.detail.module.detail.util;


import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.util.DetailWebMetabase;
import com.mogujie.detail.core.util.ImageUtil;
import com.mogujie.detail.module.itemBase.domain.ImageInfo;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.basic.ItemExtraDO;
import com.mogujie.service.item.domain.basic.ItemImageDO;
import com.mogujie.service.tangram.domain.entity.DetailModule;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 图文详情解析的工具类
 * Created by y<PERSON><PERSON> on 16/4/19.
 */
public class DetailParseUtil {
    private static final org.slf4j.Logger LOGGER = org.slf4j.LoggerFactory.getLogger(DetailParseUtil.class);

    /**
     * 从获取的字符串形式的图文详情中，解析并返回
     * <p/>
     * 模块名1:::描述+++图1###&&&图2###&&&图n###^^^模块2:::+++图1###^^^自定义_自定义显示名:::描述+++图片###
     * moduleName:::desc+++imgPath###&&&imgPath###^^^custom_module_1_自定义模块:::append info+++imgPath###
     *
     * @param detail
     */
    public static List<DetailModule> parseItemDetail(String detail) {
        List<DetailModule> detailModuleList = new ArrayList<>();

        String[] modules = StringUtils.splitByWholeSeparatorPreserveAllTokens(detail, "^^^");

        // 将字符串格式的图文详情解析图文详情模块
        for (String module : modules) {
            DetailModule detailModule = parseItemModule(module);
            if (detailModule != null) {
                detailModuleList.add(detailModule);
            }
        }

        return detailModuleList;
    }


    /**
     * 只有描述和图片至少有一个存在的时候，才会返回该详情模块
     * 单个模块的解析 moduleName:::desc+++imgPath###&&&imgPath###
     *
     * @param module
     * @return
     */
    private static DetailModule parseItemModule(String module) {
        DetailModule detailModule = new DetailModule();
        if (!module.contains(":::")) {
            return null;
        }

        String moduleValue = setModuleName(detailModule, module);
        if(moduleValue == null) {
            return null;
        }

        String imgValues = setModuleDesc(detailModule, moduleValue);
        if (StringUtils.isBlank(imgValues)) {
            if (StringUtils.isBlank(detailModule.getDesc())) { // 描述&图片都没有,则不展示该模块
                return null;
            } else { // 有描述无图片,展示该模块
                return detailModule;
            }
        }

        setModuleImgs(detailModule, imgValues);
        return detailModule;
    }


    /**
     * 设置模块名,并返回模块内容
     * @param detailModule
     * @param module  : moduleName:::desc+++imgPath###&&&imgPath###
     * @return 返回模块的value:  desc+++imgPath###&&&imgPath###
     */
    private static String setModuleName(DetailModule detailModule, String module) {
        String[] values = StringUtils.splitByWholeSeparatorPreserveAllTokens(module, ":::");
        if (values == null || values.length != 2) {
            return null;
        }

        String anchor = values[0];
        // 图文详情中，不展示 size_info, product_info
        if ("size_info".equals(anchor) || "product_info".equals(anchor) || "smart_shop_info".equals(anchor)) {
            return null;
        }

        detailModule.setAnchor(anchor);
        return values[1];
    }

    /**
     * 设置模块文字描述, 并返回图片信息
     * @param detailModule
     * @param module : desc+++imgPath###&&&imgPath###
     * @return imgPath###imgdesc&&&imgPath###
     */
    private static String setModuleDesc(DetailModule detailModule, String module) {
        String[] values = StringUtils.splitByWholeSeparatorPreserveAllTokens(module, "+++");
        if (values == null || values.length == 0) {
            return null;
        }

        if (values.length == 1) {
            if (module.startsWith("+++")) {  // 只有图片, 没有文字描述
                detailModule.setDesc(null);
                return values[0];
            } else { // 只有文字描述,没有图片
                detailModule.setDesc(values[0]);
                return null;
            }
        }

        detailModule.setDesc(values[0]);
        return values[1];
    }


    /**
     * 设置模块的图片
     * @param detailModule
     * @param imgs imgPath###imgDesc&&&imgPath###imgDesc ;   imgPath###imgDesc ;   ###
     * @return
     */
    private static void setModuleImgs(DetailModule detailModule, String imgs) {
        String[] imgList = StringUtils.splitByWholeSeparatorPreserveAllTokens(imgs, "&&&");

        if (imgList.length <= 0) {
            return;
        }

        List<String> list = new ArrayList<>(imgList.length);
        for (String img : imgList) {
            list.add(getImgUrl(img));
        }

        detailModule.setList(list);
    }

    private static String getImgUrl(String strImg) {
        String[] values = StringUtils.splitByWholeSeparatorPreserveAllTokens(strImg, "###");
        if (values == null || values.length == 0) {
            return null;
        }

        try {
            String img = ImageUtil.img(values[0]);
            return img;
        } catch (Exception e) {
            LOGGER.error("get img http url error! img: {} exception: {}", values[0], e);
            return null;
        }
    }


    /**
     * 当图文详情没有数据时
     * 将topImages的图片塞入图文详情模块
     *
     * @return
     */
    public static DetailModule fillTopImages(ItemDO item) {
        DetailModule detailModule = new DetailModule();
        try {
            List<String> imageInfo = new ArrayList<>();
            // 如果图文详情为空，则将topImages填充进去
            List<ItemImageDO> topImages = item.getImages();
            for (ItemImageDO topImage : topImages) {
                imageInfo.add(ImageUtil.img(topImage.getPath()));
            }

            detailModule.setList(imageInfo);
            return detailModule;
        } catch (Exception e) {
            LOGGER.error("fill topImgs into detailInfo module error! exception: {}", e);
            return null;
        }
    }

    /**
     * 当ItemExtra有扩展信息时
     * 将ItemExtra的图片塞入图文详情模块
     *
     * @return
     */
    public static DetailModule fillItemExtraImages(List<String> extraImages) {
            DetailModule detailModule = new DetailModule();
            detailModule.setList(extraImages);
            return detailModule;

    }

    /**
     * 药品类目图片放入图片列表中
     * 目前放入主图后面、详情图后面
     */
    public static void addDrugImages(DetailItemDO item, List<String> images) {
        if (item == null) {
            return;
        }

        // 判断是否是药品类目
        String cids = item.getCids();
        if (StringUtils.isEmpty(cids)) {
            return;
        }

        // 规定展示图片的类目范围
        String drugCid = DetailWebMetabase.getString("drug_cid", "#10006268#");
        if (!cids.contains(drugCid)) {
            return;
        }

        if (CollectionUtils.isEmpty(images)) {
            images = new ArrayList<>();
        }

        List<String> frontalImageList = getDrugImages(item, "frontalImage");
        List<String> lateralImageList = getDrugImages(item, "lateralImage");
        List<String> instructionalImageList = getDrugImages(item, "instructionalImage");

        if (CollectionUtils.isNotEmpty(frontalImageList)) {
            images.addAll(frontalImageList);
        }
        if (CollectionUtils.isNotEmpty(lateralImageList)) {
            images.addAll(lateralImageList);
        }
        if (CollectionUtils.isNotEmpty(instructionalImageList)) {
            images.addAll(instructionalImageList);
        }
    }

    /**
     * 从ItemExtraDO中获取药品图片
     */
    private static List<String> getDrugImages(DetailItemDO itemDO, String key) {
        if (itemDO == null) {
            return null;
        }
        List<String> imageUrlList = Lists.newArrayList();

        ItemExtraDO itemExtraDO = itemDO.getItemExtraDO();
        if (itemExtraDO != null && MapUtils.isNotEmpty(itemExtraDO.getFeatures()) && itemExtraDO.getFeatures().get(key) != null) {
            List<ImageInfo> imageInfoList = JSONObject.parseArray(itemExtraDO.getFeatures().get(key), ImageInfo.class);
            imageUrlList = imageInfoList.stream().map(ImageInfo::getPath).map(ImageUtil::img).collect(Collectors.toList());
        }

        return CollectionUtils.isNotEmpty(imageUrlList) ? imageUrlList : new ArrayList<>();
    }

}
