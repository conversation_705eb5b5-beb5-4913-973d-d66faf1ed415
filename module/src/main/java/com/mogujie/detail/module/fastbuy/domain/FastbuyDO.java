package com.mogujie.detail.module.fastbuy.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * Created by <PERSON>iaoyao on 16/8/16.
 */
public class FastbuyDO implements ModuleDO {

    /**
     * 0. 未开始
     * 1. 活动中
     * 2. 活动中, 但是库存为0，并且没有未付款人数
     * 3. 活动结束 （这个状态不会使用）
     * 4. 活动中, 但是库存为0，并且有未付款人数
     */
    @Getter
    @Setter
    private int state;

    /**
     * 开始时间
     */
    @Getter
    @Setter
    private long startTime;

    /**
     * 结束时间
     */
    @Getter
    @Setter
    private long endTime;

    @Getter
    @Setter
    private long totalStock;

    @Getter
    @Setter
    private long allStock;

    /**
     * 快抢商品关注数
     */
    @Getter
    @Setter
    private int noticeNum;

    @Getter
    @Setter
    private int leftUser;

    @Getter
    @Setter
    private String activityId;

    @Getter
    @Setter
    private boolean isNewComerItem;

    @Getter
    @Setter
    private boolean isNewComerUser;

    /**
     * 是否已经设置提醒
     */
    @Getter
    @Setter
    private boolean isFollowed;

    /**
     * 快抢进度条比例。由快抢侧根据销量、库存等数据计算而成。
     * 文档见：https://mogu.feishu.cn/docs/doccnrRCUUmI8DcL6R16fFOR4Xe
     */
    @Getter
    @Setter
    private Double progressBar;

    @Getter
    @Setter
    private Map<String, Object> extra;

    /**
     * 限购数量
     */
    @Getter
    @Setter
    private int limitNum;

    /**
     * 对应氛围的麦田id
     * 不同的快抢类型有一整套不同的渲染素材，其对应关系配置在metabase
     */
    @Getter
    @Setter
    private Integer maitId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof FastbuyDO)) return false;

        FastbuyDO fastbuyDO = (FastbuyDO) o;

        if (getState() != fastbuyDO.getState()) return false;
        if (getStartTime() != fastbuyDO.getStartTime()) return false;
        if (getEndTime() != fastbuyDO.getEndTime()) return false;
        if (getTotalStock() != fastbuyDO.getTotalStock()) return false;
        if (getAllStock() != fastbuyDO.getAllStock()) return false;
        if (getLeftUser() != fastbuyDO.getLeftUser()) return false;
        if (getNoticeNum() != fastbuyDO.getNoticeNum()) return false;
        return getActivityId() != null ? getActivityId().equals(fastbuyDO.getActivityId()) : fastbuyDO.getActivityId() == null;

    }

    @Override
    public int hashCode() {
        int result = getState();
        result = 31 * result + (int) (getStartTime() ^ (getStartTime() >>> 32));
        result = 31 * result + (int) (getEndTime() ^ (getEndTime() >>> 32));
        result = 31 * result + (int) (getTotalStock() ^ (getTotalStock() >>> 32));
        result = 31 * result + (int) (getAllStock() ^ (getAllStock() >>> 32));
        result = 31 * result + getLeftUser();
        result = 31 * result + getNoticeNum();
        result = 31 * result + (getActivityId() != null ? getActivityId().hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "FastbuyDO{" +
                "state=" + state +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", totalStock=" + totalStock +
                ", allStock=" + allStock +
                ", noticeNum=" + noticeNum +
                ", leftUser=" + leftUser +
                ", activityId='" + activityId + '\'' +
                ", isNewComerItem=" + isNewComerItem +
                ", isNewComerUser=" + isNewComerUser +
                ", isFollowed=" + isFollowed +
                ", progressBar=" + progressBar +
                ", extra=" + extra +
                ", maitId=" + maitId +
                '}';
    }
}
