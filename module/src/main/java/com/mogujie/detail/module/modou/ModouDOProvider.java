package com.mogujie.detail.module.modou;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.SwitchKey;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.module.modou.domain.ModouDO;
import com.mogujie.service.tagcenter.api.read.ItemTagReadService;
import com.mogujie.service.tagcenter.domain.entity.query.ItemTagQueryOption;
import com.mogujie.service.tagcenter.domain.entity.result.BaseResultDO;
import com.mogujie.service.tagcenter.domain.entity.result.ItemTagDO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import com.mogujie.tesla.common.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * 蘑豆数据(超值购)
 * Created by anshi on 17/3/20.
 */
@Module(name = "modou")
public class ModouDOProvider implements IModuleDOProvider<ModouDO> {
    private static final Logger logger = LoggerFactory.getLogger(ModouDOProvider.class);

    private Gson gson = new Gson();

    private static final String MODOU_TAG_KEY = "modouItem";

    private ItemTagReadService itemTagReadService;

    @Autowired
    private CommonSwitchUtil commonSwitchUtil;

    private static final Logger LOGGER = LoggerFactory.getLogger(ModouDOProvider.class);

    /**
     * key: modouItem
     * value: {"15ovtc": {"ma": "1","dp":"10.1","st": "**********","et": "**********"}}
     *
     * @param context
     * @return
     */
    @Override
    public ModouDO emit(DetailContext context) {
        DetailItemDO itemDO = context.getItemDO();
        String activityId = context.getParam("activityId");
        ModouDO modouDO = new ModouDO();
        try {
            String extra = null;
            ItemTagQueryOption options = new ItemTagQueryOption();
            options.setTagKey(MODOU_TAG_KEY);
            options.setItemId(itemDO.getItemId());
            options.setQueryNewDB(true);
            BaseResultDO<List<ItemTagDO>> resultBase = itemTagReadService.queryItemTag(options);
            if (resultBase != null && resultBase.isSuccess()
                    && !CollectionUtils.isEmpty(resultBase.getResult())) {
                extra = resultBase.getResult().get(0).getTagValue();
            }
            if (StringUtils.isBlank(extra)) {
                logger.error("modou extra is empty, iid: {}", itemDO.getItemId());
                modouDO.setState(3);
                return modouDO;
            }
            Map<String, Object> modouMap = gson.fromJson(extra, new TypeToken<Map<String, Object>>() {
            }.getType());
            if (modouMap == null || null == modouMap.get(activityId)) {
                logger.error("no such modou activity, itemId: {}, acitvityId: {}", itemDO.getItemId(), activityId);
                modouDO.setState(3);
                return modouDO;
            }
            String modouInfoStr = modouMap.get(activityId).toString();
            Map<String, String> modouInfoMap = gson.fromJson(modouInfoStr, new TypeToken<Map<String, String>>() {
            }.getType());

            if (null != modouInfoMap) {
                String startTimeStr = modouInfoMap.get("st");
                String endTimeStr = modouInfoMap.get("et");
                int startTime = null != startTimeStr ? Integer.valueOf(startTimeStr) : 0;
                int endTime = null != endTimeStr ? Integer.valueOf(endTimeStr) : 0;
                int nowTime = (int) (System.currentTimeMillis() / 1000);
                modouDO.setStartTime(startTime);
                modouDO.setEndTime(endTime);

                String offsetPrice = modouInfoMap.get("dp");
                modouDO.setOffsetPrice(offsetPrice);
                modouDO.setType(1);

                String modouAmountStr = modouInfoMap.get("ma");
                modouDO.setModouAmount(null != modouAmountStr ? Integer.valueOf(modouAmountStr) : 0);
                modouDO.setTotalStock(itemDO.getTotalStock());

                if (startTime <= nowTime && nowTime < endTime) {
                    //活动中
                    modouDO.setState(1);
                } else if (nowTime < startTime) {
                    //未开始
                    modouDO.setState(0);
                } else if (nowTime > endTime) {
                    //结束
                    modouDO.setState(2);
                }
                return modouDO;
            }
        } catch (Exception e) {
            logger.error("modou info get failed.{}", e);
        }
        modouDO.setState(3);
        return modouDO;
    }

    @Override
    public void init() throws DetailException {

        try {
            itemTagReadService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(ItemTagReadService.class);
        } catch (Exception e) {
            LOGGER.error("init itemTagReadService failed: {}", e);
        }

    }
}
