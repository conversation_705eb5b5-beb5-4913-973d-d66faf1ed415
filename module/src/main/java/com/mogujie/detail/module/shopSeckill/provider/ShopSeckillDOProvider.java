package com.mogujie.detail.module.shopSeckill.provider;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.module.shopSeckill.domain.ShopSeckillDO;
import com.mogujie.detail.module.shopSeckill.domain.SkTagInfo;
import com.mogujie.service.item.domain.basic.ItemDO;

/**
 * Created by anshi on 17/12/19.
 */
@Module(name = "shopSeckill")
public class ShopSeckillDOProvider implements IModuleDOProvider<ShopSeckillDO> {
    @Override
    public ShopSeckillDO emit(DetailContext context) {
        DetailItemDO itemDO = context.getItemDO();
        Object obj = context.getContext("shopSeckillInfo");
        if (null != obj) {
            SkTagInfo skTagInfo = (SkTagInfo) obj;
            ShopSeckillDO shopSeckillDO = new ShopSeckillDO();
            Integer nowTime = (int) (System.currentTimeMillis() / 1000);
            if (skTagInfo.getStartTime() > nowTime) {
                shopSeckillDO.setState(0);
            } else if (skTagInfo.getEndTime() > nowTime) {
                shopSeckillDO.setState(1);
            } else {
                shopSeckillDO.setState(2);
            }
            shopSeckillDO.setActivityId(skTagInfo.getActivityId());
            if (itemDO.getActOriginalTotalStock() != null) {
                shopSeckillDO.setOriginTotalStock(itemDO.getActOriginalTotalStock());
            } else {
                shopSeckillDO.setOriginTotalStock(itemDO.getTotalStock());
            }
            shopSeckillDO.setStartTime(skTagInfo.getStartTime());
            shopSeckillDO.setEndTime(skTagInfo.getEndTime());
            return shopSeckillDO;
        }
        return null;
    }

    @Override
    public void init() throws DetailException {

    }
}
