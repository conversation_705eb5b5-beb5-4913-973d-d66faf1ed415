package com.mogujie.detail.module.activity.domain;

import java.util.Map;

/**
 * Created by anshi on 16/8/17.
 */
public class EventResource {
    /**
     * 倒计时背景图
     */
    private String countdownBg;

    /**
     * 倒计时文案
     */
    private String countdownText;

    /**
     * 活动Logo
     */
    private String actLogo;

    /**
     * 预热文案
     */
    private String warmUpTitle;

    /**
     * 预热价图标
     */
    private String prePriceIcon;

    /**
     * 预热价格文案
     */
    private String prePriceText;

    /**
     * 预热价格颜色
     */
    private String prePriceColor;

    /**
     * 预热价格背景框颜色
     */
    private String prePriceBgColor;

    /**
     * 预热价格链接
     */
    private String prePriceLink;

    /**
     * 正式活动价格标签,如616价
     */
    private String priceTagText;

    /**
     * 正式活动价格颜色
     */
    private String priceColor;

    /**
     * 正式活动价格保证
     */
    private String priceGuarantee;

    public EventResource(Map<String, String> propertyMap) {
        this.countdownBg = propertyMap.get("countdown_bg");
        this.countdownText = propertyMap.get("countdown_text");
        this.actLogo = propertyMap.get("act_logo");
        this.warmUpTitle = propertyMap.get("warmUpTitle");
        this.prePriceIcon = propertyMap.get("pre_price_icon");
        this.prePriceText = propertyMap.get("pre_price_text");
        this.prePriceColor = propertyMap.get("pre_price_color");
        this.prePriceBgColor = propertyMap.get("pre_price_bg_color");
        this.prePriceLink = propertyMap.get("pre_price_link");
        this.priceTagText = propertyMap.get("price_tag_text");
        this.priceColor = propertyMap.get("price_color");
        this.priceGuarantee = propertyMap.get("priceGuarantee");
    }

    public String getCountdownBg() {
        return countdownBg;
    }

    public void setCountdownBg(String countdownBg) {
        this.countdownBg = countdownBg;
    }

    public String getCountdownText() {
        return countdownText;
    }

    public void setCountdownText(String countdownText) {
        this.countdownText = countdownText;
    }

    public String getActLogo() {
        return actLogo;
    }

    public void setActLogo(String actLogo) {
        this.actLogo = actLogo;
    }

    public String getPrePriceText() {
        return prePriceText;
    }

    public void setPrePriceText(String prePriceText) {
        this.prePriceText = prePriceText;
    }

    public String getPrePriceColor() {
        return prePriceColor;
    }

    public void setPrePriceColor(String prePriceColor) {
        this.prePriceColor = prePriceColor;
    }

    public String getPriceTagText() {
        return priceTagText;
    }

    public void setPriceTagText(String priceTagText) {
        this.priceTagText = priceTagText;
    }

    public String getPriceColor() {
        return priceColor;
    }

    public void setPriceColor(String priceColor) {
        this.priceColor = priceColor;
    }

    public String getPrePriceIcon() {
        return prePriceIcon;
    }

    public void setPrePriceIcon(String prePriceIcon) {
        this.prePriceIcon = prePriceIcon;
    }

    public String getPrePriceBgColor() {
        return prePriceBgColor;
    }

    public void setPrePriceBgColor(String prePriceBgColor) {
        this.prePriceBgColor = prePriceBgColor;
    }

    public String getPrePriceLink() {
        return prePriceLink;
    }

    public void setPrePriceLink(String prePriceLink) {
        this.prePriceLink = prePriceLink;
    }

    public String getPriceGuarantee() {
        return priceGuarantee;
    }

    public void setPriceGuarantee(String priceGuarantee) {
        this.priceGuarantee = priceGuarantee;
    }

    public String getWarmUpTitle() {
        return warmUpTitle;
    }

    public void setWarmUpTitle(String warmUpTitle) {
        this.warmUpTitle = warmUpTitle;
    }
}
