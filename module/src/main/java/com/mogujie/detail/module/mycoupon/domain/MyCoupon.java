package com.mogujie.detail.module.mycoupon.domain;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * Created by anshi on 2018/8/14.
 */
public class MyCoupon implements Serializable {

    /**
     * 优惠券id
     */
    @Getter
    @Setter
    private Long couponId;

    /**
     * 优惠名称
     */
    @Getter
    @Setter
    private String name;

    /**
     * 满xx元
     */
    @Getter
    @Setter
    private Integer limitPrice;

    /**
     * 减xx元
     */
    @Getter
    @Setter
    private Integer cutPrice;

    /**
     * 折扣券: 950表示9.5折
     */
    @Getter
    @Setter
    private Integer discount;

    /**
     * 最高可抵扣（单位分）
     * 跟着discount走,折扣力度过大的情况下,使用最高可减
     */
    @Getter
    @Setter
    private Long maxDecrease;

    /**
     * 优惠券终端: App专享、女装小程序专享
     */
    @Getter
    @Setter
    private String terminal;

    /**
     * 描述
     */
    @Getter
    @Setter
    private String decorate;

    /**
     * 优惠券有效期开始时间
     */
    @Getter
    @Setter
    private Integer startTime;

    /**
     * 优惠券有效期结束时间
     */
    @Getter
    @Setter
    private Integer endTime;
}
