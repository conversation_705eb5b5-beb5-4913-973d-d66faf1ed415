package com.mogujie.detail.module.live.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import lombok.Getter;
import lombok.Setter;

/**
 * @auther huasheng
 * @time 19/4/16 15:47
 */
public class LiveRewardPointDO implements ModuleDO {

    public static int status_unstart=0;
    public static int status_instart=1;
    public static int status_end=2;


    /**
     * 活动原始总库存
     */
    @Getter
    @Setter
    private Long actOriginalTotalStock=0l;

    /**
     * 活动总的剩余库存
     */
    @Getter
    @Setter
    private Long remindTotalStock=0l;

    /**
     * 活动总的己用库存
     */
    @Getter
    @Setter
    private Long usedTotalStock=0l;

    @Getter
    @Setter
    private long startTime;
    @Getter
    @Setter
    private long endTime;
    @Getter
    @Setter
    private int status;
    @Getter
    @Setter
    private int score;

}
