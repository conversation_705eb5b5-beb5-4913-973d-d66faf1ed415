package com.mogujie.detail.module.auction.provider;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.NumUtil;
import com.mogujie.detail.module.auction.domain.AuctionDO;
import com.mogujie.marketing.auction.dto.AuctionDto;
import com.mogujie.marketing.auction.enums.AuctionStatus;
import com.mogujie.service.item.domain.basic.ItemSkuDO;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @AUTUOR: lengshan
 * @DESCRIPTION: 拍卖
 * @DATE: 2019/8/12 下午2:02
 */
@Module(name = "auction")
public class AuctionDOProvider implements IModuleDOProvider<AuctionDO> {

    private static final Logger LOGGER = LoggerFactory.getLogger(AuctionDOProvider.class);

    @Override
    public void init() throws DetailException {

    }

    @Override
    public AuctionDO emit(DetailContext context) {

        Long loginUserId = context.getLoginUserId();
        AuctionDto auctionDto = getAuctionInfo(context);
        AuctionDO auctionDO = new AuctionDO();
        if (null != auctionDto) {
            DetailItemDO item = context.getItemDO();
            auctionDO.setUserId(auctionDto.getUserId());
            auctionDO.setBiddingTime(auctionDto.getBiddingTime());
            auctionDO.setStatus(auctionDto.getStatus().getCode());
            auctionDO.setExpectTime(auctionDto.getExpectTime());
            auctionDO.setHaveOrderQual(auctionDto.isHaveOrderQual());

            if (auctionDto.getBiddingPrice() != null){
                String realPriceStr = NumUtil.formatNum(auctionDto.getBiddingPrice() / 100D);
                auctionDO.setBiddingPrice(realPriceStr);
            }
            if (loginUserId != null && auctionDto.getUserId() != null && loginUserId.equals(auctionDto.getUserId())){
                auctionDO.setIsWinner(true);
            }
            if (CollectionUtils.isNotEmpty(item.getItemSkuDOList())){
                auctionDO.setQuantity(item.getTotalStock() == null ? 0 : item.getTotalStock().intValue());
            }
        }else{
            Long realPrice = getRealPrice(context);
            if (realPrice != null){
                String realPriceStr = NumUtil.formatNum(realPrice / 100D);
                auctionDO.setBiddingPrice(realPriceStr);
            }
        }

        return auctionDO;
    }

    private AuctionDto getAuctionInfo(DetailContext context) {
        return null;
    }


    /**
     * 拍卖价格设定
     *
     * @param auctionDto
     */
    private void decorateAuctionPrice(DetailContext context, AuctionDto auctionDto) {
        Long loginUserId = context.getLoginUserId();

        DetailItemDO item = context.getItemDO();
        Long realPrice = null;
        if (auctionDto.getStatus() == AuctionStatus.NOT_STARTED && auctionDto.getItem() != null && auctionDto.getItem().getFloorPrice() != null){
            // 待拍卖：取起拍价
            realPrice = auctionDto.getItem().getFloorPrice().longValue();
        }else if (auctionDto.getStatus() == AuctionStatus.ONGOING && auctionDto.getBiddingPrice() == null
                && auctionDto.getItem() != null && auctionDto.getItem().getFloorPrice() != null){
            // 拍卖中：并且当前拍卖价为null，取起拍价
            realPrice = auctionDto.getItem().getFloorPrice().longValue();
        }else if (auctionDto.getStatus() == AuctionStatus.FINISH_UNPAID && auctionDto.isHaveOrderQual()){
            if (loginUserId != null && auctionDto.getUserId() != null && loginUserId.equals(auctionDto.getUserId())){
                // 拍中未付款且有资格且为中拍者：取促销价格
                realPrice = item.getLowNowPriceVal();
            }
        }else if(auctionDto.getStatus() == AuctionStatus.UNSOLD && auctionDto.getItem() != null && auctionDto.getItem().getFloorPrice() != null){
            // 流拍：取起拍价
            realPrice = auctionDto.getItem().getFloorPrice().longValue();
        }

        if (realPrice != null){
            auctionDto.setBiddingPrice(realPrice.intValue());
        }

    }

    /**
     * @DESCRIPTION: 取原价
     * @DATE: 2019/8/19 下午5:59
     * @param
     * @return
     */
    private Long getRealPrice(DetailContext context) {
        DetailItemDO item = context.getItemDO();
        Long realPrice = null;
        for (ItemSkuDO sku : item.getItemSkuDOList()) {
            Long nowPrice = sku.getPrice();
            if (nowPrice == null){
                continue;
            }
            if (null == realPrice || realPrice > nowPrice) {
                realPrice = nowPrice;
            }
        }

        return realPrice;
    }


}
