//package com.mogujie.detail.module.detail.domain;
//
//import java.io.Serializable;
//import java.util.List;
//
///**
// * Created by <PERSON><PERSON><PERSON> on 15/12/4.
// */
//public class DetailModule implements Serializable{
//
//    private static final long serialVersionUID = 1845977688724689864L;
//
//    /**
//     * 模块描述玩呢安
//     */
//    private String desc;
//    /**
//     * 模块名
//     */
//    private String key;
//
//    /**
//     * 模块的key,用于pc详情页,右侧导航栏的锚点使用
//     */
//    private String anchor;
//    /**
//     * 模块的图片
//     */
//    private List<String> list;
//
//    public DetailModule() {
//    }
//
//    public List<String> getList() {
//        return list;
//    }
//
//    public void setList(List<String> list) {
//        this.list = list;
//    }
//
//    public String getKey() {
//        return key;
//    }
//
//    public void setKey(String key) {
//        this.key = key;
//    }
//
//    public String getDesc() {
//        return desc;
//    }
//
//    public void setDesc(String desc) {
//        this.desc = desc;
//    }
//
//    public String getAnchor() {
//        return anchor;
//    }
//
//    public void setAnchor(String anchor) {
//        this.anchor = anchor;
//    }
//
//    @Override
//    public String toString() {
//        return "DetailModule{" +
//                "desc='" + desc + '\'' +
//                ", key='" + key + '\'' +
//                ", list=" + list +
//                ", anchor='" + anchor + '\'' +
//                '}';
//    }
//}
