package com.mogujie.detail.module.shop.util;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.RouteInfo;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.BizType;
import com.mogujie.detail.core.constant.Platform;
import com.mogujie.service.item.domain.basic.ItemDO;
import com.mogujie.service.item.domain.basic.ItemPreSaleDO;
import com.mogujie.service.waitress.platform.api.PlatformItemServiceService;
import com.mogujie.service.waitress.platform.common.PlatformEnum;
import com.mogujie.service.waitress.platform.common.ServiceDetailEnum;
import com.mogujie.service.waitress.platform.common.TradeSourceEnum;
import com.mogujie.service.waitress.platform.domain.ResultSupport;
import com.mogujie.service.waitress.platform.domain.entity.ItemServiceDetail;
import com.mogujie.service.waitress.platform.domain.query.ItemServiceQuery;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.List;

/**
 * Created by anshi on 2019/1/16.
 */
public class WaitressUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(WaitressUtil.class);

    private static PlatformItemServiceService platformItemServiceService;

    static {
        try {
            platformItemServiceService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(PlatformItemServiceService.class);
        } catch (Exception e) {
            LOGGER.error("init PlatformItemServiceService error!", e);
        }
    }

    /**
     * 是否包含30天发货服务
     *
     * @param context
     * @return
     */
    public static boolean contains30dayDelivery(DetailContext context) {
        List<ItemServiceDetail> serviceDetails = getItemServices(context);
        if (CollectionUtils.isEmpty(serviceDetails)) {
            return false;
        }
        return serviceDetails.stream()
                .anyMatch(itemServiceDetail -> itemServiceDetail.getServiceDetailId() == ServiceDetailEnum.FH_470.getDetailId());
    }

    public static List<ItemServiceDetail> getItemServices(DetailContext context) {
        try {
            ItemServiceQuery itemServiceQuery = buildItemServiceQuery(context);
            ResultSupport<List<ItemServiceDetail>> result = platformItemServiceService.getItemService(itemServiceQuery);
            if (result != null && result.isSuccess() && CollectionUtils.isNotEmpty(result.getModule())) {
                return result.getModule();
            }
        } catch (Throwable e) {
            LOGGER.error("get item waitress services error!", e);
        }
        return Collections.EMPTY_LIST;
    }

    private static ItemServiceQuery buildItemServiceQuery(DetailContext context) {
        ItemDO item = context.getItemDO();
        ItemServiceQuery itemServiceQuery = new ItemServiceQuery();
        itemServiceQuery.setPlatformId(getPlatformId(context));

        Long tradeItemId = item.getItemId();
        itemServiceQuery.setTradeItemId(tradeItemId);

        String cids = formatCid(item.getCids());
        itemServiceQuery.setCids(cids);

        Long shopId = item.getShopId();
        itemServiceQuery.setShopId(shopId);

        //type 0:pc端 5:h5/小程序/app 9:良品
        itemServiceQuery.setType(5);

        itemServiceQuery.setTradeSource(getSource(context));
        itemServiceQuery.setItemTag(item.getJsonExtra());
        return itemServiceQuery;
    }

    private static Integer getPlatformId(DetailContext context) {
        try {
            App app = context.getRouteInfo().getApp();
            Platform platform = context.getRouteInfo().getPlatform();
            if (null == app) {
                return null;
            }
            switch (app) {
                case MGJ:
                    return Platform.XCX.equals(platform) ? PlatformEnum.SMALLPROGRAM.getType() : PlatformEnum.MOGUJIE.getType();
                case BH:
                    return PlatformEnum.BAIHUO.getType();
                case XCX:
                    return PlatformEnum.SMALLPROGRAM.getType();
                case MLS:
                    return PlatformEnum.MEILISHUO.getType();
                default:
                    return null;
            }
        } catch (Throwable e) {
            LOGGER.warn("getPlatformId failed : ", e);
        }
        return null;
    }

    private static Integer getSource(DetailContext context) {
        ItemDO item = context.getItemDO();
        BizType bizType = context.getRouteInfo().getBizType();
        RouteInfo routeInfo = context.getRouteInfo();
        App app = routeInfo.getApp();
        if (null != bizType) {
            if (bizType == BizType.CHANNEL) {
                switch (routeInfo.getChannelType()) {
                    case "livelottery":
                        return TradeSourceEnum.LIVE_LOTTERY.getType();
                }
            } else {
                switch (bizType) {
                    case SECKILL:
                        return TradeSourceEnum.MIAO_SHA.getType();
                    case PINTUAN:
                        return TradeSourceEnum.PING_TUAN.getType();
                    case FASTBUY:
                        return TradeSourceEnum.KUAI_QIANG.getType();
                    case ONECENT:
                        return TradeSourceEnum.CHOUJ_IANG.getType();
                    case DIAMOND:
                        return TradeSourceEnum.HUANG_ZUAN.getType();
                }
            }
        }
        if (null != app && app == App.MD) {
            return TradeSourceEnum.MODOU.getType();
        }

        try {
            ItemPreSaleDO preSaleDO = item.getItemPreSaleDO();
            if (null != preSaleDO) {
                long nowTime = System.currentTimeMillis() / 1000;
                if (preSaleDO.getStart() < nowTime && nowTime < preSaleDO.getEnd()) {
                    return TradeSourceEnum.YUS_HOU.getType();
                }
            }
        } catch (Exception e) {
            LOGGER.debug("This item do not have tag! itemId: " + item.getItemId());
        }
        return TradeSourceEnum.NORMAL.getType();
    }

    /**
     * 格式化cid，将 "#3856# #1734# #999# #2946#"  转为： "3856,1734,999,2946"
     *com.mogujie.pay.mailo.api.MaiLoInstallmentAp
     * @param cids
     * @return
     */
    private static String formatCid(String cids) {
        if (StringUtils.isBlank(cids)) {
            return "";
        }

        if (!cids.contains("#")) {
            return cids;
        }

        String newCids = cids.replace("# #", ",");
        return newCids.replace("#", "");
    }
}
