package com.mogujie.detail.module.seo.provider;

import com.meili.service.shopcenter.domain.entity.ShopInfo;
import com.meili.service.shopcenter.util.ShopTagId;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.constant.App;
import com.mogujie.detail.core.constant.SwitchKey;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.CommonSwitchUtil;
import com.mogujie.detail.core.util.ImageUtil;
import com.mogujie.detail.module.seo.domain.SeoDO;
import com.mogujie.service.category2.api.CategoryService;
import com.mogujie.service.category2.domain.entity.Category;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import com.mogujie.tesla.common.CollectionUtils;
import com.mogujie.traffic.constant.TrafficRequestConstant;
import com.mogujie.traffic.core.TrafficResult;
import com.mogujie.traffic.core.impl.ProductDetailTrafficService;
import com.mogujie.traffic.request.ProductDetailTrafficRequest;
import com.mogujie.traffic.util.ResultUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * Created by xiaoyao on 17/4/19.
 */
@Module(name = "seo")
public class SeoDOProvider implements IModuleDOProvider<SeoDO> {

    @Autowired
    private ProductDetailTrafficService trafficService;

    @Autowired
    private CommonSwitchUtil commonSwitchUtil;

    private CategoryService categoryService;

    private static final Logger LOGGER = LoggerFactory.getLogger(SeoDOProvider.class);

    @Override
    public SeoDO emit(DetailContext context) {
        try {
            DetailItemDO itemDO = context.getItemDO();
            ProductDetailTrafficRequest seoRequest = new ProductDetailTrafficRequest();
            seoRequest.setCategorys(getItemCids(itemDO.getCids()));
            seoRequest.setItemDescription(itemDO.getDescription());
            seoRequest.setItemTitle(itemDO.getTitle());
            seoRequest.setItemId(itemDO.getItemId());
            if (context.getRouteInfo().getApp() == App.MLS) {
                seoRequest.setTrafficType(TrafficRequestConstant.RequestType.MEILISHUO_REQUEST);
            }
            ShopInfo shopInfo = itemDO.getShopInfo();
            seoRequest.setShopName(null == shopInfo ? "" : shopInfo.getName());
            ShopTagId shopTagId = ShopTagId.getCode(shopInfo.getTagId());
            if (shopTagId != null) {
                seoRequest.setShopCategory(shopTagId.getTagName());
            }else {
                seoRequest.setShopCategory("未分配类目");
            }
            String tags = itemDO.getTags();
            if (!StringUtils.isEmpty(tags)) {
                seoRequest.setItemKeywords(Arrays.asList(itemDO.getTags().split(",")));
            } else {
                seoRequest.setItemKeywords(null);
            }
            TrafficResult result = trafficService.execute(seoRequest);
            Map<String, String> map = ResultUtil.toMap(result);
            if (ImageUtil.useHttps()) {
                String wapUrl = map.get("wap_url");
                if (StringUtils.isNotBlank(wapUrl) && StringUtils.startsWith(wapUrl, "http://")) {
                    map.put("wap_url", wapUrl.replaceFirst("http:", ""));
                }
                String alternate = map.get("alternate");
                if (StringUtils.isNotBlank(alternate) && StringUtils.startsWith(alternate, "http://")) {
                    map.put("alternate", alternate.replaceFirst("http:", ""));
                }
                String canonical = map.get("canonical");
                if (StringUtils.isNotBlank(canonical) && StringUtils.startsWith(canonical, "http://")) {
                    map.put("canonical", canonical.replaceFirst("http:", ""));
                }
            }
            SeoDO seoDO = new SeoDO();
            seoDO.putAll(map);
            return seoDO;
        } catch (Throwable e) {
            LOGGER.error("get seo info failed : ", e);
        }
        return null;
    }

    private List<String> getItemCids(final String cids) {
        if (!commonSwitchUtil.isOn(SwitchKey.CATEGORYV2_CACHED_CATEGORY)) {
            return null;
        }
        if (StringUtils.isEmpty(cids)) {
            return null;
        }
        String[] cidStrs = StringUtils.split(cids, " ");
        List<Integer> cidList = new ArrayList<>();
        for (String cidStr : cidStrs) {
            cidList.add(Integer.parseInt(cidStr.replaceAll("#", "")));
        }
        List<Category> categories = categoryService.listCachedCategory(cidList);
        if (!CollectionUtils.isEmpty(categories)) {
            List<String> cidNames = new ArrayList<>();
            for (Category category : categories) {
                cidNames.add(category.getName());
            }
            return cidNames;
        }
        return null;
    }

    @Override
    public void init() throws DetailException {
        try {
            categoryService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(CategoryService.class);
        } catch (Exception e) {
            LOGGER.error("init service failed : ", e);
        }
    }
}