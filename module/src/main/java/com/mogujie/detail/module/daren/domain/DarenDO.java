package com.mogujie.detail.module.daren.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import lombok.Data;

import java.util.List;

/**
 * 达人信息
 * Created by nanyang on 19/7/15.
 */

@Data
public class DarenDO implements ModuleDO {
    /**
     * 达人头像
     */
    private String avatar;

    /**
     * 预估收益  只有登录用户是达人才有数据
     */
    private Integer earn;

    /**
     * 达人用户名
     */
    private String name;

    /**
     * 登录用户是否是达人
     */
    private Boolean isDaren;

    /**
     * 关联商品
     */
    private List<RelatedGoods> RelatedGoods;
}
