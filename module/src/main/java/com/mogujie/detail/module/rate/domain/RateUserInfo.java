package com.mogujie.detail.module.rate.domain;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * Created by y<PERSON><PERSON> on 15/11/24.
 */
public class RateUserInfo implements Serializable {

    private static final long serialVersionUID = -8108265471333677117L;

    @Getter
    @Setter
    private String uid;

    @Getter
    @Setter
    private String uname;

    @Getter
    @Setter
    private String avatar;

    @Getter
    @Setter
    private String profileUrl;

    @Getter
    @Setter
    private String tagIndex;

    @Getter
    @Setter
    private Integer userLevel;

    @Getter
    @Setter
    private Boolean isAgainUser;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof RateUserInfo)) return false;

        RateUserInfo that = (RateUserInfo) o;

        if (getUid() != null ? !getUid().equals(that.getUid()) : that.getUid() != null) return false;
        if (getUname() != null ? !getUname().equals(that.getUname()) : that.getUname() != null) return false;
        if (getAvatar() != null ? !getAvatar().equals(that.getAvatar()) : that.getAvatar() != null) return false;
        if (getProfileUrl() != null ? !getProfileUrl().equals(that.getProfileUrl()) : that.getProfileUrl() != null)
            return false;
        return getTagIndex() != null ? getTagIndex().equals(that.getTagIndex()) : that.getTagIndex() == null;

    }

    @Override
    public int hashCode() {
        int result = getUid() != null ? getUid().hashCode() : 0;
        result = 31 * result + (getUname() != null ? getUname().hashCode() : 0);
        result = 31 * result + (getAvatar() != null ? getAvatar().hashCode() : 0);
        result = 31 * result + (getProfileUrl() != null ? getProfileUrl().hashCode() : 0);
        result = 31 * result + (getTagIndex() != null ? getTagIndex().hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "RateUserInfo{" +
                "uid='" + uid + '\'' +
                ", uname='" + uname + '\'' +
                ", avatar='" + avatar + '\'' +
                ", profileUrl='" + profileUrl + '\'' +
                ", tagIndex='" + tagIndex + '\'' +
                '}';
    }
}