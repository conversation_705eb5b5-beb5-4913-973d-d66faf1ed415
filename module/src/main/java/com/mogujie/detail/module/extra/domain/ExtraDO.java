package com.mogujie.detail.module.extra.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by <PERSON><PERSON>oya<PERSON> on 16/8/26.
 */
public class ExtraDO implements ModuleDO {

    /**
     * 销量
     * 预售期间为预售销量；其他情况下为商品90天内支付销量（其他情况改为历史销量）
     */
    @Setter
    @Getter
    private Long sales;

    /**
     * 直播供应链模板商品的"累计供货量"
     * 对应extraDO中的supplyStockNum字段
     */
    @Setter
    @Getter
    private Long supplyStockNum;

    /**
     * 直播供应链模板商品的"佣金比例"
     * 对应extraDO中的supplierCommission字段
     * 10 -> 10%
     */
    @Getter
    @Setter
    private String supplierCommission;

    /**
     * 供应链商家信息（联系方式等）
     */
    @Getter
    @Setter
    private SupplierShopInfo supplierShopInfo;

    /**
     * 是否包邮
     */
    @Getter
    @Setter
    private boolean isFreePost;

    /**
     * 邮费
     */
    @Getter
    @Setter
    private Integer postPrice;

    /**
     * 用户在详情页展示的收获地址
     */
    @Getter
    @Setter
    private String recvAddress;

    /**
     * 用户收获地址详细信息
     */
    @Getter
    @Setter
    private UserRecvAddressInfo userRecvAddressInfo;

    /**
     * 默认快递/全国包邮/包邮包税
     */
    @Getter
    @Setter
    private String express;

    /**
     * 小店app发布商品时的地址
     */
    @Getter
    @Setter
    private String xiaodianAdress;

    /**
     * 小店后台选择的商品默认发货地址
     */
    @Setter
    @Getter
    private String address;

    /**
     * 红人数据，已废弃
     */
    @Setter
    @Getter
    @Deprecated
    private CelebrityInfo celebrityInfo;

    /**
     * 待开售时间
     */
    @Setter
    @Getter
    private Long onSaleTime;

    /**
     * 前半小时立减详情页满减文案
     */
    @Getter
    @Setter
    private String crossStoreDiscount;

    /**
     * 蘑豆抵扣
     */
    @Getter
    @Setter
    private String modouDiscount;

    /**
     * 达人数据，根据标和麦田配置对应
     */
    @Getter
    @Setter
    private DarenInfo darenInfo;

    /**
     * 商品关联的商品讲解视频列表
     */
    @Getter
    @Setter
    private List<ExplainInfo> explainInfos;

    /**
     * 该商品目前是否在直播秒杀中
     * 实际是根据直播打的一个标判断
     */
    @Getter
    @Setter
    private boolean isLiveSeckill;

    /**
     * 闪购信息.
     */
    @Getter
    @Setter
    private FlashBuyInfo flashBuyInfo;

    /**
     * 1460班车热卖榜单信息
     */
    @Getter
    @Setter
    private HotSaleRankInfo hotSaleRankInfo;

    /**
     * 购买用户列表
     */
    @Getter
    @Setter
    private List<BuyerInfo> buyerInfoList;

    /**
     * 平台补贴文案，从麦田里取的
     */
    @Getter
    @Setter
    private String platformAllowanceMsg;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ExtraDO extraDO = (ExtraDO) o;

        if (isFreePost != extraDO.isFreePost) return false;
        if (sales != null ? !sales.equals(extraDO.sales) : extraDO.sales != null) return false;
        if (postPrice != null ? !postPrice.equals(extraDO.postPrice) : extraDO.postPrice != null) return false;
        if (express != null ? !express.equals(extraDO.express) : extraDO.express != null) return false;
        if (xiaodianAdress != null ? !xiaodianAdress.equals(extraDO.xiaodianAdress) : extraDO.xiaodianAdress != null)
            return false;
        if (address != null ? !address.equals(extraDO.address) : extraDO.address != null) return false;
        if (celebrityInfo != null ? !celebrityInfo.equals(extraDO.celebrityInfo) : extraDO.celebrityInfo != null)
            return false;
        if (onSaleTime != null ? !onSaleTime.equals(extraDO.onSaleTime) : extraDO.onSaleTime != null) return false;
        if (crossStoreDiscount != null ? !crossStoreDiscount.equals(extraDO.crossStoreDiscount) : extraDO.crossStoreDiscount != null)
            return false;
        if (darenInfo != null ? !darenInfo.equals(extraDO.darenInfo) : extraDO.darenInfo != null) return false;
        return modouDiscount != null ? modouDiscount.equals(extraDO.modouDiscount) : extraDO.modouDiscount == null;
    }

    @Override
    public int hashCode() {
        int result = sales != null ? sales.hashCode() : 0;
        result = 31 * result + (isFreePost ? 1 : 0);
        result = 31 * result + (postPrice != null ? postPrice.hashCode() : 0);
        result = 31 * result + (express != null ? express.hashCode() : 0);
        result = 31 * result + (xiaodianAdress != null ? xiaodianAdress.hashCode() : 0);
        result = 31 * result + (address != null ? address.hashCode() : 0);
        result = 31 * result + (celebrityInfo != null ? celebrityInfo.hashCode() : 0);
        result = 31 * result + (onSaleTime != null ? onSaleTime.hashCode() : 0);
        result = 31 * result + (crossStoreDiscount != null ? crossStoreDiscount.hashCode() : 0);
        result = 31 * result + (modouDiscount != null ? modouDiscount.hashCode() : 0);
        result = 31 * result + (darenInfo != null ? darenInfo.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "ExtraDO{" +
                "sales=" + sales +
                ", isFreePost=" + isFreePost +
                ", postPrice=" + postPrice +
                ", express='" + express + '\'' +
                ", xiaodianAdress='" + xiaodianAdress + '\'' +
                ", address='" + address + '\'' +
                ", celebrityInfo=" + celebrityInfo +
                ", onSaleTime=" + onSaleTime +
                ", crossStoreDiscount='" + crossStoreDiscount + '\'' +
                ", modouDiscount='" + modouDiscount + '\'' +
                ", darenInfo='" + darenInfo + '\'' +
                '}';
    }
}
