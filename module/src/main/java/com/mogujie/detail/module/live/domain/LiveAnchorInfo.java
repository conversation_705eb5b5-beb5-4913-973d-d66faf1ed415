package com.mogujie.detail.module.live.domain;

import com.mogujie.detail.core.util.ImageUtil;
import com.mogujie.item.domain.ActorLivingInfo;
import com.mogujie.mogulive.domain.LivingActorInfo;
import com.mogujie.service.rate.domain.tag.RateTag;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 主播信息
 * Created by anshi on 17/12/6.
 */
public class LiveAnchorInfo {

    public LiveAnchorInfo() {
    }

    //入参为原直播特卖所用的主播信息
    public LiveAnchorInfo(LivingActorInfo actor) {
        this.setAvatar(ImageUtil.img(actor.getAvatar()));
        this.setLiveUrl(actor.getLiveUrl());
        this.setXcxLiveUrl(actor.getXcxLiveUrl());
        this.setH5LiveUrl(actor.getH5LiveUrl());
        this.setName(actor.getName());
        this.setUserId(actor.getUserId());
        this.setFansNum(actor.getFansNum());
        this.setViewNum(actor.getViewNum());
    }

    //入参为直播供应链所用的主播信息
    public LiveAnchorInfo(ActorLivingInfo actorInfo) {
        this.setAvatar(ImageUtil.img(actorInfo.getAvatar()));
        this.setName(actorInfo.getName());
        this.setUserId(actorInfo.getUserId());
        this.setHeight(actorInfo.getHeight() == null ? null : actorInfo.getHeight().doubleValue());
        this.setWeight(actorInfo.getWeight() == null ? null : actorInfo.getWeight().doubleValue());
        this.setFansNum(actorInfo.getFans());
        this.setViewNum(actorInfo.getViewers());
        this.setLiving(actorInfo.getLiving() && actorInfo.getItemOnSale());
        this.setLiveUrl(actorInfo.getLiveUrl());
        this.setH5LiveUrl(actorInfo.getH5LiveUrl());
        this.setXcxLiveUrl(actorInfo.getXcxLiveUrl());
    }

    /**
     * 主播名
     */
    @Getter
    @Setter
    private String name;

    /**
     * 主播头像
     */
    @Getter
    @Setter
    private String avatar;

    /**
     * 主播用户id
     */
    @Getter
    @Setter
    private long userId;

    /**
     * 主播直播间跳转链接
     */
    @Getter
    @Setter
    private String liveUrl;

    /**
     * 小程序直播间跳转链接
     */
    @Getter
    @Setter
    private String xcxLiveUrl;

    /**
     * h5直播间跳转链接
     */
    @Getter
    @Setter
    private String h5LiveUrl;

    /**
     * 粉丝数
     */
    @Getter
    @Setter
    private int fansNum;

    /**
     * 观众数
     */
    @Getter
    @Setter
    private int viewNum;

    /**
     * 身高
     */
    @Getter
    @Setter
    private Double height;

    /**
     * 体重
     */
    @Getter
    @Setter
    private Double weight;

    /**
     * 是否正在直播
     */
    @Getter
    @Setter
    private boolean isLiving;

    /**
     * 主播标签
     */
    @Getter
    @Setter
    private List<TagInfo> tags;

    @Getter
    @Setter
    private List<RateTag> rateTags;

    /**
     * 主播DSR信息
     */
    @Getter
    @Setter
    private String dsr;

    /**
     * DSR标签，中等、一般、差这些
     */
    @Getter
    @Setter
    private String dsrLevel;

    /**
     * 月销量
     */
    @Getter
    @Setter
    private Integer monthlySales;
}
