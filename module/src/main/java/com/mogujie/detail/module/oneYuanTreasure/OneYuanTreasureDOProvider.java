package com.mogujie.detail.module.oneYuanTreasure;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.module.oneYuanTreasure.domain.OneYuanTreasureDO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by houan on 18/7/3.
 */
@Module(name = "oneyuantreasure")
public class OneYuanTreasureDOProvider implements IModuleDOProvider<OneYuanTreasureDO> {

    @Override
    public void init() throws DetailException {

    }

    @Override
    public OneYuanTreasureDO emit(DetailContext context) {
        return null;
    }

}
