package com.mogujie.detail.module.groupbuying.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import com.mogujie.detail.module.groupbuying.constants.TuanBizType;
import com.mogujie.detail.module.groupbuying.constants.TuanStatus;
import com.mogujie.detail.module.groupbuying.constants.TuanType;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by xiaoyao on 16/10/26.
 */
public class GroupbuyingDO implements ModuleDO {

    @Getter
    @Setter
    private Long startTime;

    @Getter
    @Setter
    private Long endTime;

    @Getter
    @Setter
    private TuanType type;

    @Getter
    @Setter
    private TuanBizType bizType;

    @Getter
    @Setter
    private TuanStatus status;

    @Getter
    @Setter
    private String price;

    @Getter
    @Setter
    private Long activityId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        GroupbuyingDO that = (GroupbuyingDO) o;

        if (startTime != null ? !startTime.equals(that.startTime) : that.startTime != null) return false;
        if (endTime != null ? !endTime.equals(that.endTime) : that.endTime != null) return false;
        if (type != that.type) return false;
        if (bizType != that.bizType) return false;
        if (status != that.status) return false;
        return price != null ? price.equals(that.price) : that.price == null;

    }

    @Override
    public int hashCode() {
        int result = startTime != null ? startTime.hashCode() : 0;
        result = 31 * result + (endTime != null ? endTime.hashCode() : 0);
        result = 31 * result + (type != null ? type.hashCode() : 0);
        result = 31 * result + (bizType != null ? bizType.hashCode() : 0);
        result = 31 * result + (status != null ? status.hashCode() : 0);
        result = 31 * result + (price != null ? price.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "GroupbuyingDO{" +
                "startTime=" + startTime +
                ", endTime=" + endTime +
                ", type=" + type +
                ", bizType=" + bizType +
                ", status=" + status +
                ", price='" + price + '\'' +
                '}';
    }
}
