package com.mogujie.detail.module.live;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meili.service.shopcenter.domain.entity.ShopInfo;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.ContextUtil;
import com.mogujie.detail.core.util.MetabaseTool;
import com.mogujie.detail.module.live.domain.*;
import com.mogujie.item.api.LiveItemOnSaleService;
import com.mogujie.item.domain.ActorLivingInfo;
import com.mogujie.item.domain.LiveItemResponse;
import com.mogujie.live.fendi.api.enums.FendiItemInfoSourceEnum;
import com.mogujie.live.fendi.api.request.FendiLiveItemInfoRequest;
import com.mogujie.live.fendi.api.response.FendiLiveItemInfoResponse;
import com.mogujie.live.fendi.api.result.FendiRpcResult;
import com.mogujie.live.fendi.api.service.FendiLiveItemInfoService;
import com.mogujie.market.common.util.CollectionUtil;
import com.mogujie.mars.api.domain.MarsResult;
import com.mogujie.mars.api.domain.entity.seller.SellerBusinessInfoDTO;
import com.mogujie.mars.api.seller.SellerBusinessInfoService;
import com.mogujie.mogulive.domain.LivingActorInfo;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * Created by anshi on 17/12/6.
 */
@Module(name = "live")
public class LiveDOProvider implements IModuleDOProvider<LiveDO> {
    private static final Logger logger = LoggerFactory.getLogger(LiveDOProvider.class);

    private LiveItemOnSaleService liveItemOnSaleService;

    private SellerBusinessInfoService sellerBusinessInfoService;

    private FendiLiveItemInfoService fendiLiveItemInfoService;

    private static final String LIVE_SHOP_TAG = "330860";

    private static final String LIVE_ANCHOR_ID_TAG = "liveAnchorId";

    private static final String FOR_ZHU_LI_ANCHOR_ID = "forZhuLiAnchorId";

    private static final String SWT_SELLER_BUSINESS_INFO = "supplier_seller_business_info";

    @Override
    public void init() throws DetailException {
        try {
            liveItemOnSaleService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(LiveItemOnSaleService.class);
            sellerBusinessInfoService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(SellerBusinessInfoService.class);
            fendiLiveItemInfoService = TeslaServiceConsumerFactory.getTeslaServiceConsumer(FendiLiveItemInfoService.class);
        } catch (Exception e) {
            logger.error("live/seller service init error.", e);
        }
    }

    @Override
    public LiveDO emit(DetailContext context) {
        if (ContextUtil.isLiveSupplyShadowItem(context.getItemDO())) {
            //直播供应链影子商品的业务逻辑
            return getSupplierItemLiveDO(context);
        } else {
            // 以下为老的直播特卖业务
            return getOldLiveDO(context);
        }
    }

    /**
     * 获取直播供应链商品的LiveDO
     *
     * @param context
     * @return
     */
    private LiveDO getSupplierItemLiveDO(DetailContext context) {
        try {
            DetailItemDO itemDO = context.getItemDO();
            ShopInfo shopInfo = itemDO.getShopInfo();
            LiveDO liveDO = new LiveDO();
            liveDO.setLiveType(LiveType.LIVE_SUPPLY_CHAIN);
            //设置供货商信息
            if (shopInfo != null) {
                liveDO.setProviderName(shopInfo.getName());
                //设置供应商营业执照
                if (MetabaseTool.isOn(SWT_SELLER_BUSINESS_INFO, true) && !context.isDyn()) {
                    MarsResult<SellerBusinessInfoDTO> marsResult = sellerBusinessInfoService.getAuditSimpleSellerBusinessByUserId(shopInfo.getUserId());
                    if (marsResult != null && marsResult.isSuccess() && marsResult.getResult() != null) {
                        SellerBusinessInfoDTO sellerBusinessInfoDTO = marsResult.getResult();
                        liveDO.setLicenseImage(sellerBusinessInfoDTO.getCompanyLicense());
                    }
                }
            }
            //主播信息
            Map<String, String> features = itemDO.getFeatures();
            if (!isForZhuLiAnchorId(context) && features != null && features.get(LIVE_ANCHOR_ID_TAG) != null) {
                LiveItemResponse<ActorLivingInfo> livingInfoLiveItemResponse = liveItemOnSaleService.queryLivingActorInfo(Long.parseLong(features.get(LIVE_ANCHOR_ID_TAG)), itemDO.getItemId());
                if (livingInfoLiveItemResponse != null && livingInfoLiveItemResponse.isSuccess() && livingInfoLiveItemResponse.getResult() != null) {
                    ActorLivingInfo actorInfo = livingInfoLiveItemResponse.getResult();
                    LiveAnchorInfo liveAnchorInfo = new LiveAnchorInfo(actorInfo);
                    liveAnchorInfo.setTags(assembleTagInfos(actorInfo.getTags()));
                    liveDO.setLiveAnchorInfos(Arrays.asList(liveAnchorInfo));
                }
            }
            else if (isForZhuLiAnchorId(context) && features != null && features.get(FOR_ZHU_LI_ANCHOR_ID) != null) {
                LiveItemResponse<ActorLivingInfo> livingInfoLiveItemResponse = liveItemOnSaleService.queryLivingActorInfo(Long.parseLong(features.get(FOR_ZHU_LI_ANCHOR_ID)), itemDO.getItemId());
                if (livingInfoLiveItemResponse != null && livingInfoLiveItemResponse.isSuccess() && livingInfoLiveItemResponse.getResult() != null) {
                    ActorLivingInfo actorInfo = livingInfoLiveItemResponse.getResult();
                    LiveAnchorInfo liveAnchorInfo = new LiveAnchorInfo(actorInfo);
                    liveAnchorInfo.setTags(assembleTagInfos(actorInfo.getTags()));
                    liveDO.setLiveAnchorInfos(Arrays.asList(liveAnchorInfo));
                }
            }

            return liveDO;
        } catch (Throwable e) {
            logger.error("get supplier live do error!", e);
        }
        return null;
    }

    /**
     * 获取原直播特卖的LiveDO
     * 商详进房 获取商品对应的一个或多个直播间信息 https://mogu.feishu.cn/docs/doccnHHeQAlyZ7ICsYiLx9EYoog
     *
     * @param context
     * @return
     */
    private LiveDO getOldLiveDO(DetailContext context) {
        LiveDO liveDO = new LiveDO();
        liveDO.setLiveType(LiveType.OLD_LIVE_PROMOTION);
        //主播信息
        try {
            String actUserId = context.getParam("actUserId");
            //从直播间过来的，不需要有直播信息
            if (StringUtils.isNotBlank(actUserId)) {
                liveDO.setLiveItemInfos(Lists.newArrayList());
                return liveDO;
            }
            Map<Long, FendiLiveItemInfoRequest> itemAndActorUserIds = Maps.newHashMap();
            FendiLiveItemInfoRequest request = new FendiLiveItemInfoRequest();
            request.setItemId(context.getItemId());
            request.setActUserId(StringUtils.isNotBlank(actUserId) ? IdConvertor.urlToId(actUserId) : null);
            itemAndActorUserIds.put(context.getItemId(), request);
            FendiRpcResult<Map<Long, FendiLiveItemInfoResponse>> fendiRpcResult = fendiLiveItemInfoService.fetchItemInfoByItemIds(itemAndActorUserIds,  FendiItemInfoSourceEnum.ITEM_DETAIL.getSourceId());
            if(fendiRpcResult == null || !fendiRpcResult.isSuccess() || null == fendiRpcResult.getValue()){
                return null;
            }
            FendiLiveItemInfoResponse liveItemInfoResponse = fendiRpcResult.getValue().get(context.getItemId());
            if(liveItemInfoResponse == null){
                return null;
            }
            List<LiveItemInfo>liveItemInfos = new ArrayList<>();
            LiveItemInfo liveItemInfo = LiveItemInfo.convert(liveItemInfoResponse);
            liveItemInfos.add(liveItemInfo);
            liveDO.setLiveItemInfos(liveItemInfos);
        } catch (Throwable e) {
            logger.error("call liveItemService error.", e);
        }
        return liveDO;
    }


    private boolean isForZhuLiAnchorId(DetailContext context){
        if(context.getRouteInfo().getChannelType() != null && context.getRouteInfo().getChannelType().equals("livelottery")){
            return  true;
        } else {
            return false;
        }
    }

    private List<TagInfo> assembleTagInfos(List<ActorLivingInfo.TagInfo> tags) {
        if (CollectionUtil.isEmpty(tags)){
            return null;
        }
        List<TagInfo> tagInfos = new ArrayList<>();
        for (ActorLivingInfo.TagInfo tag : tags){
            TagInfo tagInfo = new TagInfo();
            tagInfo.setId(tag.getId());
            tagInfo.setName(tag.getName());
            tagInfo.setTimestamp(tag.getTimestamp());

            tagInfos.add(tagInfo);
        }
        return tagInfos;
    }

    private List<TagInfo> assembleTagInfosByLiveItem(List<LivingActorInfo.TagInfo> tags) {
        if (CollectionUtil.isEmpty(tags)){
            return null;
        }
        List<TagInfo> tagInfos = new ArrayList<>();
        for (LivingActorInfo.TagInfo tag : tags){
            TagInfo tagInfo = new TagInfo();
            tagInfo.setId(tag.getId());
            tagInfo.setName(tag.getName());
            tagInfo.setTimestamp(tag.getTimestamp());

            tagInfos.add(tagInfo);
        }
        return tagInfos;
    }
}
