package com.mogujie.detail.module.live;

import com.mogujie.detail.core.adt.ChannelTag;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.core.util.StringUtils;
import com.mogujie.detail.module.live.domain.LiveDO;
import com.mogujie.detail.module.live.domain.LiveRewardPointDO;
import com.mogujie.themis.camp.config.CampTagConfigClient;

/**
 * 直播积分兑换
 * <p>
 * 标签：zbdh     属性  st：活动开始时间   "et";//活动结束时间  "ws";//预热开始时间
 * "we";//预热结束时间  "ai";//活动ID,对应招商的报名ID   "pp";//促销价    "pr";//价格比例
 *
 * @auther huasheng
 * @time 19/4/16 15:34
 */
@Module(name = "liveRewardPoint")
public class LiveRewardPointProvider implements IModuleDOProvider<LiveRewardPointDO> {

    @Override
    public LiveRewardPointDO emit(DetailContext context) {
        DetailItemDO detailItemDO= context.getItemDO();
        ChannelTag channelTag = context.getChannelTag();
        if (channelTag == null) {
            return null;
        }
        LiveRewardPointDO liveRewardPointDO = new LiveRewardPointDO();
        int price = StringUtils.objToInteger(channelTag.getPrice());
        float priceRate = StringUtils.objToFloat(channelTag.getExtraMap().get("pr"));
        int score = calScore(price, priceRate);
        liveRewardPointDO.setScore(score);

        //库存
        liveRewardPointDO.setActOriginalTotalStock(detailItemDO.getActOriginalTotalStock());
        liveRewardPointDO.setRemindTotalStock(detailItemDO.getRemindTotalStock());
        liveRewardPointDO.setUsedTotalStock(detailItemDO.getActOriginalTotalStock()-detailItemDO.getRemindTotalStock());

        liveRewardPointDO.setStartTime(channelTag.getStartTime());
        liveRewardPointDO.setEndTime(channelTag.getEndTime());
        long nowTime = CampTagConfigClient.fakeCurrentTime();
        if (channelTag.getStartTime() > nowTime) {
            liveRewardPointDO.setStatus(LiveRewardPointDO.status_unstart);
        } else if (channelTag.getEndTime() > nowTime) {
            liveRewardPointDO.setStatus(LiveRewardPointDO.status_instart);
        } else {
            liveRewardPointDO.setStatus(LiveRewardPointDO.status_end);
        }
        return liveRewardPointDO;
    }


    private static int calScore(float price, float priceRate) {
        int score = (int) Math.ceil((price * priceRate) / 100f);
        return score;
    }


    @Override
    public void init() throws DetailException {

    }
}
