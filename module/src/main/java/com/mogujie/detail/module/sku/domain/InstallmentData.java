package com.mogujie.detail.module.sku.domain;

import lombok.Data;

/**
 * Created by anshi on 17/2/10.
 */
@Data
public class InstallmentData {

    /**
     * 用户应付单期分期金额（单期本金 + 单期手续费 - 单期减免手续费）
     */
    private int perPrice;
    /**
     * 分期数
     */
    private int num;
    /**
     * 用户应付单期手续费（单期手续费 - 单期减免手续费）
     */
    private int fee;
    /**
     * 最大免息期数
     */
    private int freeMaxNum;
    /**
     * 免息/优惠标链接
     */
    private String icon;

    private String feeTitle;

}
