package com.mogujie.detail.module.extra.domain;

import com.mogujie.detail.core.util.ImageUtil;
import com.mogujie.detail.core.util.NumUtil;
import com.mogujie.detail.core.util.TagUtil;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * Created by eryi
 * Date: 2020/8/6
 * Time: 2:14 PM
 * Introduction: 闪购标信息
 * Document: 商品shango kv标解析之后的对象.shango标原始格式：st:1596420000|et:1596765598|ws:1596333600|we:1596419999|ai:22298112|uid:22298112|at:0|pp:4500|fimg:|npx:/mlcdn/776a41/200731_0f0i5l25819f1b0ddgjeil56f7ga7_750x1125.jpg|npx2:/mlcdn/776a41/200731_244de94c6k8bjg5l2h2kc06596eg9_750x1125.jpg|qx:/mlcdn/776a41/200731_7e4b0f86hkd40b1k0k89ggia32ebf_750x456.jpg|tp:1|ds:5300|nbt:81|bn:
 * Actions:
 */
@Data
public class FlashBuyInfo {

    /**
     * 正式开始时间
     */
    private Long st;

    /**
     * 正式结束时间
     */
    private Long et;

    /**
     * 预热开始时间
     */
    private Long ws;

    /**
     * 预热结束时间
     */
    private Long we;

    /**
     * 方图
     */
    private String fImg;

    /**
     * 牛皮鲜促销条图
     */
    private String npx;

    /**
     * 牛皮鲜促销条图不等高版本
     */
    private String npx2;

    /**
     * 折扣类型 1：一口价  2：打折
     */
    private Integer tp;

    /**
     * 折扣值
     */
    private String ds;

    /**
     * 品牌特卖商品的品牌名
     */
    private String bn;

    /**
     * 外网价 如9850
     */
    private Integer wwj;

    /**
     * 外网价 如：98.5
     */
    private String outNetPrice;


    /**
     * 将shango标的value转换为FlashBuyInfo对象
     *
     * @param kvTagStr shango标的value。
     * @return
     */
    public static FlashBuyInfo convert(String kvTagStr) {
        if (StringUtils.isBlank(kvTagStr)) {
            return null;
        }
        Map<String, String> kvTags = TagUtil.parseKVTags(kvTagStr);
        if (MapUtils.isEmpty(kvTags)) {
            return null;
        }
        FlashBuyInfo flashBuyInfo = new FlashBuyInfo();
        String st = MapUtils.getString(kvTags, "st");
        if (StringUtils.isNotBlank(st) || StringUtils.isNumeric(st)) {
            flashBuyInfo.setSt(Long.valueOf(st));
        }
        String et = MapUtils.getString(kvTags, "et");
        if (StringUtils.isNotBlank(et) || StringUtils.isNumeric(et)) {
            flashBuyInfo.setEt(Long.valueOf(et));
        }
        String ws = MapUtils.getString(kvTags, "ws");
        if (StringUtils.isNotBlank(ws) || StringUtils.isNumeric(ws)) {
            flashBuyInfo.setWs(Long.valueOf(ws));
        }
        String we = MapUtils.getString(kvTags, "we");
        if (StringUtils.isNotBlank(we) || StringUtils.isNumeric(we)) {
            flashBuyInfo.setWe(Long.valueOf(we));
        }
        String fImg = MapUtils.getString(kvTags, "fimg");
        if (StringUtils.isNotBlank(fImg)) {
            flashBuyInfo.setFImg(ImageUtil.img(fImg));
        }

        String npx = MapUtils.getString(kvTags, "npx");
        if (StringUtils.isNotBlank(npx)) {
            flashBuyInfo.setNpx(ImageUtil.img(npx));
        }
        String npx2 = MapUtils.getString(kvTags, "npx2");
        if (StringUtils.isNotBlank(npx2)) {
            flashBuyInfo.setNpx2(ImageUtil.img(npx2));
        }
        String tp = MapUtils.getString(kvTags, "tp");
        if (StringUtils.isNotBlank(tp) || StringUtils.isNumeric(tp)) {
            flashBuyInfo.setTp(Integer.valueOf(tp));
        }
        String ds = MapUtils.getString(kvTags, "ds");
        if (StringUtils.isNotBlank(tp)) {
            flashBuyInfo.setDs(ds);
        }
        String bn = MapUtils.getString(kvTags, "bn");
        if (StringUtils.isNotBlank(bn)) {
            flashBuyInfo.setBn(bn);
        }
        String wwj = MapUtils.getString(kvTags, "wwj");
        if (StringUtils.isNotBlank(wwj) || StringUtils.isNumeric(wwj)) {
            Long oPrice = Long.valueOf(wwj);
            flashBuyInfo.setWe(oPrice);
            flashBuyInfo.setOutNetPrice(NumUtil.formatNum(oPrice / 100d));
        }
        return flashBuyInfo;
    }


}
