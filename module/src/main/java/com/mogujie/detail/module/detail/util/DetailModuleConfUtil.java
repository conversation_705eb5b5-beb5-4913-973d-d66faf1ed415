package com.mogujie.detail.module.detail.util;

import com.mogujie.metabase.utils.StringUtils;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 16/1/7.
 */
public class DetailModuleConfUtil {
    /**
     * 根据类目获取详情模块
     *
     * @param path
     * @return mixed
     */
    public static Map<String, String> getCategoryModules(String path) {
        if (StringUtils.isBlank(path)) {
            return Collections.emptyMap();
        }

        CategoryTypeUtil categoryType = new CategoryTypeUtil(path);

        String code = "衣服";
        if (categoryType.isClothes()) {
            code = "衣服";
        } else if (categoryType.isShoes()) {
            code = "鞋子";
        } else if (categoryType.isBags()) {
            code = "包包";
        } else if (categoryType.isAccessories()) {
            code = "配饰";
        } else if (categoryType.isHome()) {
            code = "家居";
        } else if (categoryType.isDigital()) {
            code = "数码";
        } else if (categoryType.isHousehold()) {
            code = "家电";
        } else if (categoryType.isVirtual()) {
            code = "虚拟";
        } else if (categoryType.isBeauty()) {
            code = "美妆";
        } else if (categoryType.isFood()) {
            code = "食品";
        } else if (categoryType.isCar()) {
            code = "汽车";
        } else if (categoryType.isTravel()) {
            code = "旅游";
        } else if (categoryType.isHotel()) {
            code = "酒店";
        }

        Map<String, String> moduleInfoMap = new LinkedHashMap<>();

        List<String> keys = DetailModuleResources.config.get(code);
        for (String key : keys) {
            moduleInfoMap.put(key, DetailModuleResources.modules.get(key));
        }
        return moduleInfoMap;
    }
}
