package com.mogujie.detail.module.wrapper;

import com.alibaba.fastjson.JSONObject;
import com.mogujie.commons.utils.IdConvertor;
import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.pagani.api.model.PaganiQuery;
import com.mogujie.pagani.api.model.SearchResult;
import com.mogujie.pagani.api.model.SearchResultData;
import com.mogujie.pagani.api.service.PaganiApiService;
import com.mogujie.search.ara.abtest.common.utils.MoguUuidUtil;
import com.mogujie.session.SessionContextHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by eryi
 * Date: 2020/10/23
 * Time: 3:46 PM
 * Introduction: 图墙查询包装类
 * Document:
 * Actions:
 */
@Component
public class PaganiSearchWrapper {

    private static final Logger logger = LoggerFactory.getLogger(PaganiSearchWrapper.class);

    /**
     * 1460班车品类榜单图墙
     */
    private static final String CKEY_FOR_ITEM_RANKING = "item-ranking-list-rank";

    private static final String CKEY_FOR_ITEM_COLLOCATION = "cross_store_collocation_itemCollocation";

    private  static  final String CKEY_FOR_BUYER_SHOP_RECOMMEND = "item-detail-buyer-shop-1500";

    @Autowired
    private PaganiApiService paganiApiService;

    /**
     * 通过商品id查询品类榜单数据
     *
     * @param itemId 商品id
     * @return
     */
    public JSONObject getRankListBanner(Long itemId) {
        if (itemId == null || itemId <= 0) {
            return null;
        }
        PaganiQuery paganiQuery = new PaganiQuery();
        paganiQuery.setCKey(CKEY_FOR_ITEM_RANKING);
        paganiQuery.put("iid", IdConvertor.idToUrl(itemId));
        List<JSONObject> dataList = this.searchData(paganiQuery);
        return CollectionUtils.isEmpty(dataList) ? null : dataList.get(0);
    }

    /**
     * 通过商品id查询商品搭配数据
     *
     * @param itemId 商品id
     * @param shopId 店铺id
     * @return
     */
    public JSONObject getItemCollocationData(Long itemId, Long shopId) {
        if (itemId == null || itemId <= 0) {
            return null;
        }
        PaganiQuery paganiQuery = new PaganiQuery();
        paganiQuery.setCKey(CKEY_FOR_ITEM_COLLOCATION);
        paganiQuery.put("itemIds", String.valueOf(itemId));
        paganiQuery.put("shopIds", String.valueOf(shopId));
        List<JSONObject> dataList = this.searchData(paganiQuery);
        return CollectionUtils.isEmpty(dataList) ? null : dataList.get(0);
    }

    /**
     * 查询买手店商品推荐列表
     *
     * @param actorId 主播id，url形式
     * @param detailChildCid    商品详情页的商品cid
     * @return
     */
    public List<JSONObject> getliveItemRecommendData(String actorId, String detailChildCid, DetailContext context){
        PaganiQuery paganiQuery = new PaganiQuery();
        paganiQuery.setCKey(CKEY_FOR_BUYER_SHOP_RECOMMEND);
        paganiQuery.put("actorId", actorId);
        paganiQuery.put("detailChildCid", detailChildCid);
        paganiQuery.put("page", String.valueOf(1));
        paganiQuery.put("_not_itemId", context.getItemId());

        String mwTTid = context.getParam("mwTTid");
        String mwDid = context.getParam("mwDid");
        String uuid = SessionContextHolder.getUUID();
        if (StringUtils.isBlank(uuid)) {
            uuid = MoguUuidUtil.getMobileMgjuuid(mwTTid, mwDid);
        }
        paganiQuery.setUuid(uuid);
        paganiQuery.setDid(mwDid);
        if (context.getLoginUserId() != null) {
            paganiQuery.setUserId(IdConvertor.idToUrl(context.getLoginUserId()));
        }
        List<JSONObject> dataList = this.searchData(paganiQuery);
        return CollectionUtils.isEmpty(dataList) ? null : dataList;
    }

    /**
     * @param query pagani请求对象
     * @return
     */
    private List<JSONObject> searchData(PaganiQuery query) {
        try {
            if (query == null) {
                return null;
            }
            query.setVersion("33554429");
            SearchResult searchResult = paganiApiService.search(query);
            if (searchResult == null || !searchResult.isSuccess()) {
                logger.error("searchData error from pagani! paganiQuery:" + query);
                return null;
            }
            SearchResultData data = searchResult.getData();
            Object dataList = data.get("list");
            if (dataList == null) {
                return null;
            }
            return (List<JSONObject>) dataList;
        } catch (Exception e) {
            logger.error("searchData exception from pagani! paganiQuery:" + query, e);
            return null;
        }
    }


}
