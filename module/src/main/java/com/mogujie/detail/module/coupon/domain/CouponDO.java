package com.mogujie.detail.module.coupon.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by <PERSON><PERSON>oya<PERSON> on 16/10/24.
 */
public class CouponDO implements ModuleDO {

    /**
     * 1110以前版本的app在用这个字段（只包括部分商品平台券）
     * 改版后字段废弃
     */
    @Getter
    @Setter
    @Deprecated
    private List<PlatformCoupon> platformCoupon;

    /**
     * 1110版本后的平台券字段(券包、多张券)
     */
    @Getter
    @Setter
    private List<PlatformCouponV2> platformCouponV2;

    /**
     * 优惠券配置类型（配了"券包"还是"多张券"）
     */
    @Getter
    @Setter
    private CouponConfigType couponConfigType;

    /***********************/
    /**      券包部分      **/
    /***********************/

    /**
     * 券包标题：品牌周专享
     */
    @Getter
    @Setter
    private String couponPkgTitle;

    /**
     * 券包图片
     */
    @Getter
    @Setter
    private String couponPkgImg;

    /**
     * 调用券包接口，领券包时需要传入的参数
     */
    @Getter
    @Setter
    private String relationKey;


    /***********************/
    /**     多张券部分     **/
    /***********************/

    /**
     * 优惠券背景图
     */
    @Getter
    @Setter
    private String couponBgImg;

    /**
     * 优惠券角标图片
     */
    @Getter
    @Setter
    private String couponTagImg;


    /************************/
    /** 跨店满减相关（津贴）**/
    /************************/

    /**
     * 跨店满减优惠的背景图
     */
    @Getter
    @Setter
    private String crossShopDiscountBgImg;

    /**
     * 跨店满减优惠领取按钮文案
     */
    @Getter
    @Setter
    private String crossShopDiscountBtnText;

    /**
     * 跨店满减信息（购物金：平台出钱）
     */
    @Getter
    @Setter
    private CrossShopDiscount crossShopDiscount;

    /**
     * 新版跨店满减（商家出钱）：https://mogu.feishu.cn/docs/doccnmVTUUPqZNjPzkFVGeCGwXg
     * 不是在优惠券信息的那快，在商品标题的上方有个类似banner的条。
     */
    @Getter
    @Setter
    private CrossShopDiscountBanner crossShopDiscountBanner;

}
