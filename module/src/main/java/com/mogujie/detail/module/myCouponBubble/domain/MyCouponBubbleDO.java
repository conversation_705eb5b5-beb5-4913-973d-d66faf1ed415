package com.mogujie.detail.module.myCouponBubble.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import lombok.Data;

/**
 * Created by nanyang on 2019/8/6.
 */
@Data
public class MyCouponBubbleDO implements ModuleDO {

    /**
     * 优惠券id
     */
    private Long couponId;

    /**
     * 优惠名称
     */
    private String name;

    /**
     * 满xx元
     */
    private Integer limitPrice;

    /**
     * 减xx元
     */
    private Integer cutPrice;

    /**
     * 折扣券: 950表示9.5折
     */
    private Integer discount;

    /**
     * 最高可抵扣（单位分）
     * 跟着discount走,折扣力度过大的情况下,使用最高可减
     */
    private Long maxDecrease;

    /**
     * 优惠券终端: App专享、女装小程序专享
     */
    private String terminal;

    /**
     * 描述
     */
    private String decorate;

    /**
     * 用户领取的优惠劵的开始时间
     */
    private Integer startTime;

    /**
     * 用户领取的优惠劵的结束时间
     */
    private Integer endTime;


    /**
     * 气泡背景图
     */
    private String dubbleBgImg;


    /**
     * 优惠文案
     */
    private String couponDesc;


    /**
     * 跳转链接
     */
    private String jumpUrl;


    /**
     * 气泡类型 0 是 1270优化人群的倒计时气泡 1 是1280是领券气泡
     */
    private Integer type;


}
