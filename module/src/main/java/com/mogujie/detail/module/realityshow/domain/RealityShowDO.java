package com.mogujie.detail.module.realityshow.domain;

import com.mogujie.detail.core.adt.ModuleDO;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by xy on 2017/7/10.
 */
public class RealityShowDO implements ModuleDO {

    private Integer cid;

    private List<RealityShow> showList;

    public Integer getCid() {
        return cid;
    }

    public void setCid(Integer cid) {
        this.cid = cid;
    }

    public List<RealityShow> getShowList() {
        return showList;
    }

    public void setShowList(List<RealityShow> showList) {
        this.showList = showList;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        RealityShowDO that = (RealityShowDO) o;

        if (cid != null ? !cid.equals(that.cid) : that.cid != null) return false;
        return showList != null ? showList.equals(that.showList) : that.showList == null;
    }

    @Override
    public int hashCode() {
        int result = cid != null ? cid.hashCode() : 0;
        result = 31 * result + (showList != null ? showList.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "RealityShowDO{" +

                "cid=" + cid +
                ", showList=" + showList +
                '}';
    }
}
