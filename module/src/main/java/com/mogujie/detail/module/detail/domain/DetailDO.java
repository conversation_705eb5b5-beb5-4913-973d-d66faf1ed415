package com.mogujie.detail.module.detail.domain;

import com.mogujie.detail.core.adt.ModuleDO;
import com.mogujie.service.tangram.domain.entity.DetailModule;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by xiaoyao on 16/8/16.
 */
public class DetailDO implements ModuleDO {

    /**
     * 图片是否显示间距
     */
    @Getter
    @Setter
    private boolean splitDetailImage = true;

    /**
     * 商品描述
     */
    @Getter
    @Setter
    private String desc;

    /**
     * 详情页装修
     */
    @Getter
    @Setter
    private ShopDecorate shopDecorate;

    /**
     * 详情视频
     */
    @Getter
    @Setter
    private DetailVideo video;


    @Getter
    @Setter
    private CertInfo certInfo;

    /**
     * 图文详情模块
     */
    @Getter
    @Setter
    private List<DetailModule> detailImage;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        DetailDO detailDO = (DetailDO) o;

        if (desc != null ? !desc.equals(detailDO.desc) : detailDO.desc != null) return false;
        if (shopDecorate != null ? !shopDecorate.equals(detailDO.shopDecorate) : detailDO.shopDecorate != null)
            return false;
        if (video != null ? !video.equals(detailDO.video) : detailDO.video != null) return false;
        return detailImage != null ? detailImage.equals(detailDO.detailImage) : detailDO.detailImage == null;

    }

    @Override
    public int hashCode() {
        int result = desc != null ? desc.hashCode() : 0;
        result = 31 * result + (shopDecorate != null ? shopDecorate.hashCode() : 0);
        result = 31 * result + (video != null ? video.hashCode() : 0);
        result = 31 * result + (detailImage != null ? detailImage.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "DetailDO{" +
                "desc='" + desc + '\'' +
                ", shopDecorate=" + shopDecorate +
                ", video=" + video +
                ", detailImage=" + detailImage +
                '}';
    }
}
