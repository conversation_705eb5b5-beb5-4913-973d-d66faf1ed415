package com.mogujie.detail.module.live.domain;

import com.alibaba.fastjson.JSONObject;
import com.mogujie.detail.core.adt.ModuleDO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 19/5/6.
 */
public class LiveSimpleDO implements ModuleDO {
    /**
     * 主播小窗信息
     */
    @Getter
    @Setter
    private LiveAnchorInfo actUserInfo;

    /**
     * 商品讲解信息
     */
    @Getter
    @Setter
    private ExplainInfo explainInfo;

    /**
     * 直播商品进图强，挑选的商品讲解信息。
     */
    @Getter
    @Setter
    private ExplainInfo pickedExplainInfo;

    /**
     * 最新版的视频讲解弹窗信息：https://mogu.feishu.cn/docs/doccnNtfDS5LvMq20gzJlXQiW5e
     */
    @Getter
    @Setter
    private ExplainWindowInfo explainWindowInfo;

    /**
     * 店铺自播弹窗信息：https://mogu.feishu.cn/docs/doccnNtfDS5LvMq20gzJlXQiW5e
     */
    @Getter
    @Setter
    private ShopLiveInfo shopLiveInfo;

    /**
     * 买手店商品推荐信息：https://mogu.feishu.cn/docs/doccnEOYVmIwgEckR0s2B6BAyIq
     */
    @Getter
    @Setter
    private List<LiveItemRecommendInfo> liveItemRecommendInfos;
}
