package com.mogujie.detail.module.presale.provider;

import com.mogujie.detail.core.adt.DetailContext;
import com.mogujie.detail.core.adt.DetailItemDO;
import com.mogujie.detail.core.adt.IModuleDOProvider;
import com.mogujie.detail.core.annotation.Module;
import com.mogujie.detail.core.exception.DetailException;
import com.mogujie.detail.module.presale.domain.PreSaleResouce;
import com.mogujie.detail.module.presale.domain.PresaleDO;
import com.mogujie.detail.module.presale.domain.RuleDesc;
import com.mogujie.detail.module.presale.util.PreSaleUtil;
import com.mogujie.modou.modoubusiness.api.MoDouRuleService;
import com.mogujie.service.item.domain.basic.ItemPreSaleDO;
import com.mogujie.tesla.client.api.TeslaServiceConsumerFactory;
import org.mogujie.cayenne.timeclient.TimeClient;
import org.mogujie.cayenne.timeclient.TimeClientFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by xiaoyao on 16/10/24.
 */
@Module(name = "presale")
public class PresaleDOProvider implements IModuleDOProvider<PresaleDO>{
    private static final Logger logger = LoggerFactory.getLogger(PresaleDOProvider.class);

    protected TimeClient timeClient = null;

    @Override
    public PresaleDO emit(DetailContext context) {
        return this.getPreSaleInfo(context);
    }

    private PresaleDO getPreSaleInfo(DetailContext context) {
        DetailItemDO itemDO = context.getItemDO();
        ItemPreSaleDO itemPreSale = itemDO.getItemPreSaleDO();
        if (itemPreSale == null) {
            return null;
        }

        int nowTime = (int)(timeClient.fake() / 1000);

        if (nowTime < itemPreSale.getStart() || nowTime > itemPreSale.getEnd()) {
            return null;
        }

        PreSaleResouce preSaleResouce = PreSaleUtil.getPreResources();
        if (preSaleResouce == null) {
            return null;
        }

        PresaleDO preSale = new PresaleDO();
        preSale.setRule(preSaleResouce.getRuleTitle());
        preSale.setPresaleDesc("注: " + preSaleResouce.getDeclare());
        preSale.setTitleIcon(preSaleResouce.getPreIconV2());

        // 订金
        preSale.setDeposit(PreSaleUtil.formatPrice(itemPreSale.getDeposit()));
        //膨胀金
        if (itemPreSale.getExpandMoney() != 0) {
            preSale.setExpandMoney(PreSaleUtil.formatPrice(itemPreSale.getExpandMoney()));
        }
        // 总价
        preSale.setTotalPrice(PreSaleUtil.formatPrice(itemPreSale.getPrice()));
        // 时间
        preSale.setPresaleDate(getPreSaleDate(itemPreSale));
        // 尾款时间
        preSale.setPresaleEndDate(getPreSaleEndDate(itemPreSale));
        preSale.setStartTime(itemPreSale.getStart());
        preSale.setEndTime(itemPreSale.getEnd());
        preSale.setPayStartTime(itemPreSale.getPriceStart());
        preSale.setPayEndTime(itemPreSale.getPriceEnd());

        RuleDesc ruleDesc = new RuleDesc();
        ruleDesc.setImg(preSaleResouce.getRuleIcon());
        ruleDesc.setRules(preSaleResouce.getRuleDesc());
        preSale.setRuleDesc(ruleDesc);
        return preSale;
    }

    /**
     * 定金日期
     *
     * @param itemPreSale
     * @return
     */
    String getPreSaleDate(ItemPreSaleDO itemPreSale) {
        int start = itemPreSale.getStart();
        int end = itemPreSale.getEnd();
        return "定金时间：" + PreSaleUtil.formateFullDate(start) + "-" + PreSaleUtil.formateFullDate(end);
    }

    /**
     * 尾款日期
     *
     * @return
     */
    String getPreSaleEndDate(ItemPreSaleDO itemPreSale) {
        int start = itemPreSale.getPriceStart();
        int over = itemPreSale.getPriceEnd();
        return "尾款时间：" + PreSaleUtil.formateFullDate(start) + "-" + PreSaleUtil.formateFullDate(over);
    }

    @Override
    public void init() throws DetailException {
        try {
            timeClient = TimeClientFactory.client("cayenne");
        } catch (Exception e) {
            logger.error("init service failed : {}", e);
        }
    }
}
